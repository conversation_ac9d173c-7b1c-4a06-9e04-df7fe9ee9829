template: go
name: Agentsphere CI
trigger:
  change:
    paths:
      - ".codebase/pipelines/agentsphere_ci.yaml"
      - "agentsphere/**"
      - "api/idl/agentsphere/**"
      - "resources/dbschema/agentsphere/**"
go_version: "1.23"
cache_key_prefix: "0423-"
commands:
  - export CI_TEST_ES_URL=http://localhost:9200
  - go test -v -gcflags="all=-l -N" -coverpkg=./agentsphere/... -coverprofile=coverage.out ./agentsphere/...
services:
  - id: es
    image: hub.byted.org/ee/elasticsearch:7.8.0
    envs:
      "discovery.type": "single-node"