package hertz_handler

import (
	"context"
	"encoding/json"
	"net/http"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/hertz-contrib/sse"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/codeai"
	llmstackapi "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/llmstack"
	codeaitoolservice "code.byted.org/devgpt/kiwis/codeai/agentictool/service"
	"code.byted.org/devgpt/kiwis/codeai/chat/service"
	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/handler"
	chatentity "code.byted.org/devgpt/kiwis/copilotstack/entity/chat"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/port/codebase"
)

type Handler struct {
	AuthMD             *handler.AuthMiddleware
	ChatService        service.ChatService
	CodebaseCli        codebase.Client
	AgenticToolService codeaitoolservice.ToolService
}

type GetAgenticMessageRequest struct {
	ID string `path:"id"`
}

func (h *Handler) GetAgenticMessage(ctx context.Context, c *app.RequestContext) {
	recordNetwork := hertz.RecordNetworkCost(ctx, c)
	defer recordNetwork()
	req := hertz.BindValidate[GetAgenticMessageRequest](ctx, c)
	if req == nil {
		return
	}

	resp, err := h.ChatService.GetAgenticMessage(ctx, req.ID)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, 0, "failed to get agentic message: "+err.Error())
		c.Abort()
		return
	}
	if resp == nil {
		hertz.JSONMessage(c, http.StatusNotFound, 0, "agentic message not found")
		c.Abort()
		return
	}

	// return resp
	c.String(http.StatusOK, "%s", resp.Content.Content)
}

func (h *Handler) AgenticAsk(ctx context.Context, c *app.RequestContext) {
	recordNetwork := hertz.RecordNetworkCost(ctx, c)
	defer recordNetwork()
	req := hertz.BindValidate[codeai.AgenticAskRequest](ctx, c)
	if req == nil {
		return
	}

	if !req.Stream {
		// not implement
		hertz.JSONMessage(c, http.StatusInternalServerError, 0, "not implement")
		c.Abort()
		return
	}

	account, _ := h.AuthMD.GetAccount(ctx, c)

	var (
		repoName string
		revision string
	)
	// extract repo name and revision
	variables := parseObject(ctx, lo.FromPtr(req.IntentVariables))
	if variables != nil {
		repoName = conv.DefaultAny[string](variables["repo_name"])
		revision = conv.DefaultAny[string](variables["revision"])
	}

	if revision == "" {
		revision = "master"
	}
	if repoName == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, 0, "repo_name is required")
		c.Abort()
		return
	}

	permission, err := h.CodebaseCli.GetRepoUserRole(ctx, account.Username, repoName)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, 0, "failed to get user role of current repo: "+err.Error())
		c.Abort()
		return
	}
	if permission == "" {
		hertz.JSONMessage(c, http.StatusForbidden, 0, "no permission to access current repo")
		c.Abort()
		return
	}

	log.V1.CtxInfo(ctx, "agentic ask request: %v", req)

	completeChan := make(chan interface{})
	res, err := h.ChatService.AgenticAsk(ctx, service.AgenticAskOption{
		SessionKey: lo.FromPtr(req.SessionKey),
		Input:      req.Input,
		RepoName:   repoName,
		Revision:   revision,
		History: lo.Map(req.History, func(item *codeai.ChatMessage, _ int) *chatentity.SimpleMessage {
			return &chatentity.SimpleMessage{
				Role:    item.Role,
				Content: item.Content,
			}
		}),
		Stream:       req.Stream,
		RawInput:     req.RawInput,
		Variables:    variables,
		CompleteChan: completeChan,
		Account:      account,
		ModelName:    req.Model,
		Debug:        req.Debug,
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, 0, "failed to agentic ask: "+err.Error())
		c.Abort()
		return
	}

	// process streaming response
	var (
		s          = sse.NewStream(c)
		firstChunk = true
	)

loop:
	for {
		select {
		case err, ok := <-res.StreamChannel.ErrorChannel:
			if !ok || err == nil {
				continue loop
			}
			log.V1.CtxWarn(ctx, "got error while streaming agentic ask response: %v", err)

			_ = s.Publish(&sse.Event{
				Event: "error",
				Data:  []byte(err.Error()),
			})
			res.StreamChannel.Close()
			break loop
		case event, ok := <-res.StreamChannel.DataChannel:
			if !ok {
				_ = s.Publish(&sse.Event{
					Event: "done",
					Data:  []byte(""),
				})
				log.V1.CtxInfo(ctx, "agentic ask data channel is closed, exited")
				break loop
			}
			var publishErr error
			switch {
			case event.Progress != nil:
				publishErr = s.Publish(&sse.Event{
					Event: "progress",
					Data:  getJSON(codeai.AgenticAskResponseSSEProgress{Content: *event.Progress}),
				})
			case event.Message != nil:
				delta := &codeai.ChatMessage{
					Role:    string(event.Message.Role),
					Content: event.Message.Content,
				}

				if event.Message.FunctionCall != nil {
					delta.FunctionCall = &codeai.FunctionCall{
						Name:      event.Message.FunctionCall.FunctionID,
						Arguments: event.Message.FunctionCall.Parameters,
					}
				}

				publishErr = s.Publish(&sse.Event{
					Event: "message_chunk",
					Data: getJSON(codeai.ChatEventMessageChunk{
						Delta:        delta,
						FinishReason: lo.ToPtr(""), // todo(John) what to put here?
					}),
				})
			case event.MessageChunk != nil:
				if firstChunk {
					recordNetwork()
					/* 记录第一个 token 的耗时
					_ = metrics.XX.FirstTokenLatency.WithTags(&metrics.FTLTag{
						Method:     "agentic_ask",
						FunctionID: "agentic_ask",
					}).Observe(float64(time.Since(start).Milliseconds()))
					*/
					firstChunk = false
				}
				publishErr = s.Publish(&sse.Event{
					Event: "message_chunk",
					Data: getJSON(codeai.AgenticAskResponseSSEMessageChunk{
						Id:      lo.FromPtr(req.SessionKey),
						Content: *event.MessageChunk,
					}),
				})
			case event.FunctionCall != nil:
				publishErr = s.Publish(&sse.Event{
					Event: "function_call",
					Data:  getJSON(event.FunctionCall),
				})
			case event.MessageTips != nil:
				publishErr = s.Publish(&sse.Event{
					Event: "message_tips",
					Data:  getJSON(event.MessageTips),
				})
			}
			if publishErr != nil {
				// 通常是因为用户中断了生成过程（例如点击了“停止生成”按钮）
				log.V1.CtxWarn(ctx, "got error while publishing agentic ask response to sse: %v", publishErr)
				res.StreamChannel.Close()
				break
			}
		}
	}

	log.V1.CtxInfo(ctx, "finishing streaming agentic ask response")
	return
}

func getJSON(v any) []byte {
	data, _ := json.Marshal(v)
	return data
}

type DebugToolCallOption struct {
	RepoName     string `json:"repo_name"`
	Revision     string `json:"revision"`
	MaxResLength int    `json:"max_res_length"`
	ToolName     string `json:"tool_name"`
	Arguments    string `json:"arguments"`
}

func (h *Handler) DebugToolCall(ctx context.Context, c *app.RequestContext) {
	account, _ := h.AuthMD.GetAccount(ctx, c)

	req := hertz.BindValidate[DebugToolCallOption](ctx, c)
	if req == nil {
		return
	}

	res, err := h.AgenticToolService.DoFunctionCall(ctx, codeaitoolservice.DoFunctionCallOption{
		Account:      account,
		Call:         llmstackapi.FunctionCall{Name: req.ToolName, Arguments: req.Arguments},
		RepoName:     req.RepoName,
		Revision:     req.Revision,
		MaxResLength: 0,
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, 0, "failed to debug tool call: %s", err.Error())
		c.Abort()
		return
	}
	hertz.JSONMessage(c, http.StatusOK, 0, "success: %s", res)
}
