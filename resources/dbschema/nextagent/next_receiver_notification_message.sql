CREATE TABLE `next_receiver_notification_message` (
          `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
          `receive_type` VARCHAR(64) NOT NULL COMMENT '接收类型： personal 个人, department 部门，all 全体',
          `receiver` VARCHAR(256) DEFAULT '' COMMENT '接收者',
          `message_id` VARCHAR(64) NOT NULL COMMENT '消息的唯一标识符',
          `status` VARCHAR(64) NOT NULL COMMENT '消息状态：unread 未读, read 已读, recalled 已撤回',
          `created_at` datetime NOT NULL COMMENT '创建时间 (Unix 时间戳)',
          `updated_at` datetime NOT NULL COMMENT '更新时间 (Unix 时间戳)',
          PRIMARY KEY (`id`),
          KEY `idx_message_id_receiver` (`message_id`, `receiver`),
          KEY `idx_receiver_status` (`receiver`, `receive_type`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人员消息关联表';