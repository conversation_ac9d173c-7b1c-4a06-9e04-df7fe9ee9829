CREATE TABLE `next_knowledge_task` (
           `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
           `uid` varchar(40) NOT NULL COMMENT 'uid',
           `task_type` varchar(40) NOT NULL COMMENT '导入 wiki 等',
           `dataset_id` varchar(40) NOT NULL COMMENT '知识库 id',
           `task_status` varchar(40) NOT NULL COMMENT '任务状态',
           `params` text  COMMENT '任务操作参数',
           `creator` varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
           `failed_count` INT NOT NULL DEFAULT 0 COMMENT '失败次数',
           `created_at` datetime NOT NULL  COMMENT '创建时间',
           `updated_at` datetime NOT NULL  COMMENT '更新时间',
           `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
           PRIMARY KEY (`id`),
           UNIQUE KEY `uk_uid` (`uid`),
           KEY `idx_dataset_id` (`dataset_id`),
           KEY `idx_status` (`task_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库异步任务表'