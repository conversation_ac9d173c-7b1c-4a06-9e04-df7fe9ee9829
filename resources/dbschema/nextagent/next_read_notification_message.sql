CREATE TABLE `next_read_notification_message` (
          `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
          `receiver` VARCHAR(256) DEFAULT '' COMMENT '接收者',
          `message_id` VARCHAR(64) NOT NULL COMMENT '消息的唯一标识符',
          `created_at` datetime NOT NULL COMMENT '创建时间 (Unix 时间戳)',
          PRIMARY KEY (`id`),
          KEY `idx_message_id_receiver` (`message_id`, `receiver`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='已读消息关联表';