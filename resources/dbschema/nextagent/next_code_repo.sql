CREATE TABLE `next_code_repo` (
          `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
          `space_id` varchar(40)  NOT NULL COMMENT '空间 ID',
          `creator` varchar(64)  NOT NULL COMMENT '创建人',
          `repo_name` varchar(256)  NOT NULL DEFAULT '' COMMENT '仓库名称',
          `repo_id` varchar(256)  NOT NULL DEFAULT '' COMMENT '仓库 ID',
          `created_at` datetime NOT NULL  COMMENT '创建时间',
          `updated_at` datetime NOT NULL  COMMENT '更新时间',
          `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
          PRIMARY KEY (`id`),
          KEY `idx_space_id` (`space_id`),
          KEY `idx_repo_name` (`repo_name`),
          KEY `idx_repo_id` (`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='code repo'