CREATE TABLE `next_notification_message` (
         `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
         `uid` VARCHAR(64) NOT NULL COMMENT '消息的唯一标识符',
         `title` VARCHAR(256) NOT NULL COMMENT '消息标题',
         `content` VARCHAR(256) NOT NULL COMMENT '通知内容',
         `link` VARCHAR(256) NOT NULL COMMENT '跳转链接',
         `link_name` VARCHAR(256) NOT NULL COMMENT '跳转按钮名称',
         `type` VARCHAR(64) NOT NULL DEFAULT 'notification' COMMENT '消息类型: 通知notification/邀请invitation',
         `creator` VARCHAR(64) NOT NULL COMMENT '消息创建者',
         `is_top` tinyint(1) DEFAULT '0'  COMMENT '消息是否置顶',
         `created_at` datetime NOT NULL COMMENT '创建时间 (Unix 时间戳)',
         `updated_at` datetime NOT NULL COMMENT '更新时间 (Unix 时间戳)',
         PRIMARY KEY (`id`),
         UNIQUE KEY `uniq_idx_uid` (`uid`)
) ENGINE=InnoDB
    DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息通知表';

