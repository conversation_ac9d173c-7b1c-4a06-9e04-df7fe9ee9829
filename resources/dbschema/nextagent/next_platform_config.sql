CREATE TABLE `next_platform_config` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `space_id` varchar(40)  NOT NULL COMMENT '空间 ID',
        `creator` varchar(64)  NOT NULL COMMENT '创建人',
        `meego_space_list` json DEFAULT NULL COMMENT '关联的 meego 空间链接列表',
        `created_at` datetime NOT NULL  COMMENT '创建时间',
        `updated_at` datetime NOT NULL  COMMENT '更新时间',
        `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `uk_space_id` (`space_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='platform config'