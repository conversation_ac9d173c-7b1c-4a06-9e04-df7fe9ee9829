CREATE TABLE `next_dev_service` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `space_id` varchar(40)  NOT NULL COMMENT '空间 ID',
        `creator` varchar(64)  NOT NULL COMMENT '创建人',
        `biz_id` varchar(256)  NOT NULL DEFAULT '' COMMENT '服务唯一ID',
        `type` varchar(64)  NOT NULL DEFAULT '' COMMENT '服务类型',
        `name` varchar(256)  NOT NULL DEFAULT '' COMMENT '服务名称',
        `control_plane` varchar(64)  NOT NULL DEFAULT '' COMMENT '服务控制面',
        `created_at` datetime NOT NULL  COMMENT '创建时间',
        `updated_at` datetime NOT NULL  COMMENT '更新时间',
        `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
        PRIMARY KEY (`id`),
        KEY `idx_space_id_type` (`space_id`, `type`),
        KEY `idx_biz_id` (`biz_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='service'