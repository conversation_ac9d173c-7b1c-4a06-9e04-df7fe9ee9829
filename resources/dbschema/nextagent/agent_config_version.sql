CREATE TABLE `next_agent_config_version` -- next_agent_config_version
(
    `id`              BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`             VARCHAR(40)                                                           NOT NULL COMMENT 'uniq id',
    `agent_config_id` VARCHAR(40)                                                           NOT NULL COMMENT 'agent config id',
    `creator`         VARCHAR(128)                                                          NOT NULL COMMENT 'creator username',
    `version`         INT                                                                   NOT NULL COMMENT 'agent_config_id + version',
    `status`          VARCHAR(32)                                                           NOT NULL COMMENT 'status prepared, online, rollback',
    `enabled`         INT                                                                   NOT NULL DEFAULT 0 COMMENT '对应 Agent 版本的状态，1 开启 or 0 关闭',
    `description`     VARCHAR(256)                                                          NOT NULL COMMENT 'description',
    `runtime_config`  JSON                                                                  NULL COMMENT 'runtime 配置信息',
    `prompt_config`   JSON                                                                  NULL COMMENT 'prompt 配置信息',
    `knowledgeset_config` JSON                                                              NULL COMMENT 'knowledgeset_config 配置信息',
    `custom_config`   JSON                                                                  NULL COMMENT '自定义配置信息',
    `created_at`      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP                             NOT NULL COMMENT 'create timestamp',
    `updated_at`      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`      BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    `create_source`   varchar(256)                                                         NULL COMMENT "创建来源信息",
    UNIQUE KEY `uk_uid` (`uid`) COMMENT 'get agent config version by uid',
    UNIQUE KEY `uk_agent_config_id_version` (`agent_config_id`, `version`) COMMENT 'get agent config version by agent_config_id and version',
    KEY `idx_agent_config_id_enabled` (`agent_config_id`, `enabled`) COMMENT 'get agent config version by agent_config_id and enabled',
    KEY `idx_agent_config_id_status` (`agent_config_id`, `status`) COMMENT 'get agent config version by agent_config_id and status'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='agent config version manage';