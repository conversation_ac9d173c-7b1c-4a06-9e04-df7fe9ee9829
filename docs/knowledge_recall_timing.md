# 知识召回总耗时统计实现

## 概述

本文档描述了在aime agent执行过程中统计所有知识召回所花费的总耗时的实现方案。该方案正确处理了并发知识召回的场景，避免了简单累加导致的耗时高估问题。

## 实现方案

### 1. 在AgentRunContext中添加计时字段

在 `agentsphere/agents/iris/context.go` 中添加了以下字段：

```go
// Knowledge recall timing tracking
knowledgeRecallStartTime    time.Time
knowledgeRecallEndTime      time.Time
activeKnowledgeRecallCount  int
knowledgeRecallMutex        sync.Mutex
```

### 2. 添加计时管理方法

在 `AgentRunContext` 中添加了以下方法：

- `StartKnowledgeRecallOperation()` - 标记知识召回操作开始
- `EndKnowledgeRecallOperation()` - 标记知识召回操作结束
- `GetTotalKnowledgeRecallTime()` - 获取总知识召回耗时
- `ResetKnowledgeRecallTiming()` - 重置知识召回计时

### 3. 并发处理逻辑

**核心思想**：统计从第一个知识召回操作开始到最后一个知识召回操作结束的总时间，而不是累加每个操作的耗时。

- 当第一个知识召回操作开始时，记录开始时间
- 维护活跃的知识召回操作计数
- 每个操作结束时更新结束时间
- 总耗时 = 最后结束时间 - 最初开始时间

### 4. 核心打点位置

**只在 `RetrieveKnowledge` 方法中进行打点**

在 `agentsphere/agents/knowledges/knowledge.go` 的 `RetrieveKnowledge` 方法中：
- 在方法开始时调用 `StartKnowledgeRecallOperation()`
- 在defer中调用 `EndKnowledgeRecallOperation()`

这样可以确保：
- 所有通过 `RetrieveKnowledge` 的知识召回都被统计
- 包括直接调用、缓存调用、工具调用等所有场景
- 正确处理并发调用的时间重叠问题

### 5. 添加总耗时打点

#### 5.1 新增telemetry事件

在 `agentsphere/agents/iris/telemetry/events.go` 中：
- 添加了 `EventKnowledgeRecallTotal` 事件常量
- 添加了 `EmitKnowledgeRecallTotal` 函数

#### 5.2 在Agent执行完成后上报

在 `agentsphere/agents/runtime/runtime.go` 中：
- 在Agent执行开始时重置计时
- 在Agent执行完成后获取总耗时并上报

## 打点数据格式

### 单次知识召回打点（原有）
```go
telemetry.EmitKnowledgeRecall(c, "recalled", agent, scenario, tools, strategy, category, count, duration)
```

### 总知识召回耗时打点（新增）
```go
telemetry.EmitKnowledgeRecallTotal(run, agentName, totalDuration)
```

事件数据格式：
```json
{
  "agent": "agent_name",
  "total_duration": 1234  // 毫秒
}
```

## 并发处理示例

### 场景1：顺序调用
```
时间轴: |--召回1--|--召回2--|--召回3--|
总耗时: 召回1耗时 + 召回2耗时 + 召回3耗时
```

### 场景2：并发调用
```
时间轴: |--召回1--|
        |--召回2-----|
        |--召回3----|
总耗时: max(召回1, 召回2, 召回3)的结束时间 - min(召回1, 召回2, 召回3)的开始时间
```

## 实现优势

1. **简单可靠**: 只在核心方法 `RetrieveKnowledge` 中打点，避免遗漏
2. **自动覆盖**: 所有知识召回路径都会经过 `RetrieveKnowledge`，无需在每个调用点单独处理
3. **并发安全**: 正确处理并发调用，避免耗时高估
4. **维护简单**: 集中在一个地方进行计时，易于维护和调试

## 使用场景

1. **性能监控**: 监控不同Agent的知识召回总耗时
2. **性能优化**: 识别知识召回耗时过长的Agent
3. **资源分析**: 分析知识召回在整个Agent执行中的时间占比
4. **并发效果评估**: 评估并发知识召回的性能提升效果

## 注意事项

1. **线程安全**: 计时是线程安全的，使用了mutex保护
2. **生命周期**: 每次Agent执行开始时会重置计时
3. **完整覆盖**: 所有通过 `RetrieveKnowledge` 的知识召回都会被统计
4. **并发正确性**: 正确处理并发调用，避免耗时高估
5. **计数准确性**: 通过活跃操作计数确保开始/结束时间的准确记录

## 验证方法

可以通过查看telemetry日志中的 `aime_runtime_knowledge_recall_total` 事件来验证功能是否正常工作。对于并发场景，总耗时应该明显小于各个召回操作耗时的简单累加。
