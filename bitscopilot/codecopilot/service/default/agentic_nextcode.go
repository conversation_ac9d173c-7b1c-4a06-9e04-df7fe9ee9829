package defaultcodecopilotservice

import (
	"bytes"
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"text/template"
	"time"

	"code.byted.org/codebase/sdk/v2/types/repository"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/bytedance/gopkg/cloud/metainfo"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/nextcode/kitex_gen/agent"
	copilot "code.byted.org/nextcode/kitex_gen/copilot"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/knowledgebase"
	"code.byted.org/devgpt/kiwis/bitscopilot/entity"
	skillservice "code.byted.org/devgpt/kiwis/bitscopilot/skill/service"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/port/codebase"
	"code.byted.org/devgpt/kiwis/port/eventcollector"
	"code.byted.org/devgpt/kiwis/port/llmops"
	"code.byted.org/devgpt/kiwis/port/nextcode"

	sdkaccount "code.byted.org/codebase/sdk/v2/types/account"
)

//go:embed deepwiki_context_instruction.tmpl
var deepwikiContextInstructionTemplate string

//go:embed bitsai_context_instruction.tmpl
var bitsaiContextInstructionTemplate string

//go:embed context_variables_instruction.tmpl
var contextVariablesInstructionTemplate string

const (
	// User ID key for metainfo
	// GRPC streaming: https://bytedance.larkoffice.com/wiki/NRWbwmEAjitSohkup4CcUFApnEh#FMZgd2FbNoPnUFxb1nrlLcNcgWb
	metaKeyUserIDGRPC = "CODE_USER_ID"

	AgenticWikiChatFunction    = "agentic_wiki_chat"
	AgenticExplainCodeFunction = "agentic_explain_code"
	AnswerAboutRepoFunction    = "answer_about_repo"
	AnswerAboutCodeFunction    = "answer_about_code"

	// DeepWiki 场景的格式
	ToolCallMarkDownFormat    = "\n\u003cfont color='grey'\u003e\n\n%s  %s (%s)\n\n\u003c/font\u003e\n\n"
	MCPToolCallMarkDownFormat = "\n\u003cfont color='grey'\u003e\n\n%s  %s\n\n\u003c/font\u003e\n\n"

	// BitsAI 场景的格式
	BitsAIToolCallFormat    = "\n\u003cfont color='grey'\u003e\n\n→  %s (%s)\n\n\u003c/font\u003e\n\n"
	BitsAIMCPToolCallFormat = "\n\u003cfont color='grey'\u003e\n\n•  %s\n\n\u003c/font\u003e\n\n"

	ThoughtSVG = "<svg width=\"14.4\" height=\"14.4\" viewBox=\"0 0 13 12\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\" style=\"display: inline-block; vertical-align: middle; margin: 0 4px 0 0;\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M3.98871 7.89121C2.84225 6.74237 2.25 5.65364 2.25 4.61029C2.25 2.20536 4.14949 0.25 6.5 0.25C8.8505 0.25 10.75 2.20536 10.75 4.61029C10.75 5.65364 10.1578 6.74237 9.01128 7.89121C8.68388 8.21929 8.5 8.66386 8.5 9.12737V9.24265C8.5 9.65773 8.16751 10 7.75 10H5.25C4.83249 10 4.5 9.65773 4.5 9.24265V9.12736C4.5 8.66386 4.31612 8.21929 3.98871 7.89121ZM8.30345 7.18483C9.28049 6.20577 9.75 5.34267 9.75 4.61029C9.75 2.75126 8.29163 1.25 6.5 1.25C4.70837 1.25 3.25 2.75126 3.25 4.61029C3.25 5.34267 3.71951 6.20577 4.69655 7.18483C5.18098 7.67026 5.46546 8.31797 5.49705 9H7.50295C7.53454 8.31797 7.81902 7.67026 8.30345 7.18483ZM5 10.5C4.86193 10.5 4.75 10.6119 4.75 10.75V11.25C4.75 11.3881 4.86193 11.5 5 11.5H8C8.13807 11.5 8.25 11.3881 8.25 11.25V10.75C8.25 10.6119 8.13807 10.5 8 10.5H5Z\" fill=\"currentColor\"/></svg>"
	ViewSVG    = "<svg height=\"14.4\" width=\"14.4\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"display: inline-block; vertical-align: middle; margin: 0 4px 0 0;\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M40 4c1.105 0 2 .853 2 1.905v36.19C42 43.147 41.105 44 40 44H8c-1.105 0-2-.853-2-1.905V5.905C6 4.853 6.895 4 8 4h32zm-2 4H10v32h28V8zm-5 22a1 1 0 011 1v2a1 1 0 01-1 1H15a1 1 0 01-1-1v-2a1 1 0 011-1h18zm0-8a1 1 0 011 1v2a1 1 0 01-1 1H15a1 1 0 01-1-1v-2a1 1 0 011-1h18zm0-8a1 1 0 011 1v2a1 1 0 01-1 1H15a1 1 0 01-1-1v-2a1 1 0 011-1h18z\" fill=\"#000\"/></svg>"
	SearchSVG  = "<svg width=\"14.4\" height=\"14.4\" viewBox=\"0 0 12 12\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\" style=\"display: inline-block; vertical-align: middle; margin: 0 4px 0 0;\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M5.78186 2.11719C3.75825 2.11719 2.1178 3.75764 2.1178 5.78125C2.1178 7.80486 3.75825 9.44531 5.78186 9.44531C7.80547 9.44531 9.44592 7.80486 9.44592 5.78125C9.44592 3.75764 7.80547 2.11719 5.78186 2.11719ZM1.13342 5.78125C1.13342 3.21399 3.2146 1.13281 5.78186 1.13281C8.34912 1.13281 10.4303 3.21399 10.4303 5.78125C10.4303 8.34851 8.34912 10.4297 5.78186 10.4297C3.2146 10.4297 1.13342 8.34851 1.13342 5.78125Z\" fill=\"currentColor\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M9.15258 9.15197C9.34479 8.95976 9.65643 8.95976 9.84864 9.15197L10.7236 10.027C10.9159 10.2192 10.9159 10.5308 10.7236 10.723C10.5314 10.9152 10.2198 10.9152 10.0276 10.723L9.15258 9.84803C8.96037 9.65582 8.96037 9.34418 9.15258 9.15197Z\" fill=\"currentColor\"/></svg>"
	MCPSVG     = "<svg width=\"14.4\" height=\"14.4\" viewBox=\"0 0 12 12\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\" style=\"display: inline-block; vertical-align: middle; margin: 0 4px 0 0;\"><g clip-path=\"url(#svg_4705c82c6a__clip0_3589_250347)\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M9.25 0.5C10.2165 0.5 11 1.2835 11 2.25C11 3.2165 10.2165 4 9.25 4C8.45734 4 7.78777 3.473 7.57255 2.75026L3.875 2.75C3.11561 2.75 2.5 3.36561 2.5 4.125C2.5 4.88439 3.11561 5.5 3.875 5.5H8.125C9.43668 5.5 10.5 6.56332 10.5 7.875C10.5 9.18668 9.43668 10.25 8.125 10.25L4.42745 10.2503C4.21223 10.973 3.54266 11.5 2.75 11.5C1.7835 11.5 1 10.7165 1 9.75C1 8.7835 1.7835 8 2.75 8C3.54275 8 4.21239 8.52712 4.42753 9.24999L8.125 9.25C8.88439 9.25 9.5 8.63439 9.5 7.875C9.5 7.11561 8.88439 6.5 8.125 6.5H3.875C2.56332 6.5 1.5 5.43668 1.5 4.125C1.5 2.81332 2.56332 1.75 3.875 1.75L7.57247 1.74999C7.78761 1.02712 8.45725 0.5 9.25 0.5ZM2.75 9C2.33579 9 2 9.33579 2 9.75C2 10.1642 2.33579 10.5 2.75 10.5C3.16421 10.5 3.5 10.1642 3.5 9.75C3.5 9.33579 3.16421 9 2.75 9ZM8.5 2.25C8.5 1.83579 8.83579 1.5 9.25 1.5C9.66421 1.5 10 1.83579 10 2.25C10 2.66421 9.66421 3 9.25 3C8.83579 3 8.5 2.66421 8.5 2.25Z\" fill=\"currentColor\"/></g><defs><clipPath id=\"svg_4705c82c6a__clip0_3589_250347\"><path fill=\"#fff\" d=\"M0 0H12V12H0z\"/></clipPath></defs></svg>"

	// User-facing warning messages
	TimeoutWarningMessage = `<div style="color: #999; font-size: 80%; margin: 8px 0; padding: 8px; background: #f8f9fa; border-left: 3px solid #ffc107; border-radius: 4px; line-height: 1.3;">
任务处理时间较长，您可以继续等待或新开对话。持续失败请反馈或发起oncall。<br/>
<small style="color: #666; font-size: 85%; line-height: 1.2;">Processing is taking longer than expected. Please continue waiting or start a new conversation.<br/>If issues persist, please provide feedback or initiate an oncall.</small>
</div>`

	DisconnectionWarningMessage = `<div style="color: #d73027; font-size: 80%; margin: 8px 0; padding: 8px; background: #fff2f0; border-left: 3px solid #d73027; border-radius: 4px; line-height: 1.3;">
连接异常，请开启新对话或反馈问题。<br/>
<small style="color: #999; font-size: 85%; line-height: 1.2;">Connection error occurred. Please start a new conversation or report the issue.</small>
</div>`
)

var _ skillservice.FunctionInvoker = &AgenticNextCodeFunctionInvoker{}

type AgenticNextCodeFunctionInvoker struct {
	NextCodeClient nextcode.Client
	CodebaseCli    codebase.Client
	LLMOpsClient   llmops.Client
	FunctionID     string
	EventCollector eventcollector.Client
}

func (ci *AgenticNextCodeFunctionInvoker) Call(ctx context.Context, opt *skillservice.FunctionInvokeOption) (skillservice.FunctionOutput, error) {
	return nil, errors.Errorf("not implemented")
}

func (ci *AgenticNextCodeFunctionInvoker) CallStream(ctx context.Context, opt *skillservice.FunctionInvokeOption) (*stream.RecvChannel[skillservice.FunctionOutput], error) {
	log.V1.CtxInfo(ctx, fmt.Sprintf("nextcode function call stream option: %v", opt))

	// get UserID
	userID, err := ci.GetUserID(ctx, opt.Account)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get user ID: %v", err)
		return nil, errors.WithMessage(err, "failed to get user ID")
	}
	ctx = WithUserID(ctx, userID)

	// create stream channel
	send, recv := stream.NewChannel[skillservice.FunctionOutput](10)
	promptCompletionID := time.Now().UnixNano()

	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.V1.CtxError(ctx, "nextcode function foreach panic %v", err)
			}
		}()
		defer send.Close()

		// Helper function to safely publish messages
		safePublish := func(data map[string]any) {
			defer func() {
				if r := recover(); r != nil {
					log.V1.CtxError(ctx, "warning message publish panic: %v", r)
				}
			}()
			if !send.Stopped() {
				send.Publish(data)
			}
		}

		// Helper function to send timeout warning when appropriate
		sendTimeoutWarning := func() {
			if !send.Stopped() {
				safePublish(map[string]any{
					"result":               TimeoutWarningMessage,
					"prompt_completion_id": promptCompletionID,
				})
			}
		}

		// Helper function to send disconnection warning when appropriate
		sendDisconnectionWarning := func() {
			if !send.Stopped() {
				safePublish(map[string]any{
					"result":               DisconnectionWarningMessage,
					"prompt_completion_id": promptCompletionID,
				})
			}
		}

		// similar logic of processing wiki chat, migrate from CallStream of code_functions.go
		// ↓↓↓↓↓↓↓↓↓↓↓
		// Agentic NextCode Chat Do not need pre-extract relavant file contents

		// get repo_name and revision from context
		repoName := conv.DefaultAny[string](opt.Context.Variables[keyRepoName])
		revision := conv.DefaultAny[string](opt.Context.Variables[keyRevision])

		ci.reportWikiAskEvent(ctx, opt, repoName, revision)

		repoInfo, err := ci.NextCodeClient.GetRepository(ctx, repository.GetRepositoryRequest{
			Path: lo.ToPtr(repoName),
		})
		if err != nil {
			log.V1.CtxError(ctx, "Wiki Ask Failed: failed to get repository: %v", err)
			// 上报wiki ask failed事件
			ci.reportWikiAskFailedEvent(ctx, opt, "get_repository_failed", err, repoName, revision)
			send.PublishError(errors.WithMessage(err, "failed to get repository"), false)
			return
		}
		if repoInfo == nil || repoInfo.Repository == nil {
			log.V1.CtxError(ctx, "Wiki Ask Failed: repository info is nil")
			// 上报wiki ask failed事件
			ci.reportWikiAskFailedEvent(ctx, opt, "repository_info_nil", errors.New("repository info is nil"), repoName, revision)
			send.PublishError(errors.New("repository info is nil"), false)
			return
		}
		repoId := repoInfo.Repository.Id

		// build NextCode chat request
		req, err := ci.buildCompleteChatRequest(ctx, opt, repoId, revision)
		if err != nil {
			log.V1.CtxError(ctx, "Wiki Ask Failed: build chat request: %v", err)
			// 上报wiki ask failed事件
			ci.reportWikiAskFailedEvent(ctx, opt, "build_chat_request_failed", err, repoName, revision)
			send.PublishError(errors.WithMessage(err, "failed to build chat request"), false)
			return
		}

		// stream response process variables
		start := time.Now()
		isFirstChunk := true
		response := ""
		// reference process related variables
		var refBuffer string
		var collectingRef bool = false
		var refStarted bool = false
		// thought process related variables
		var lastMessageTime time.Time = time.Now()
		var collectingThought bool = false

		// Create a completion signal to ensure all processing is done
		completed := make(chan bool, 1)
		streamErr := make(chan error, 1)

		// Add timeout monitoring
		timeoutDuration := time.Minute // 1 minute timeout
		timeoutTimer := time.NewTimer(timeoutDuration)
		timeoutWarningShown := false

		// Start a goroutine to monitor timeout
		go func() {
			for {
				select {
				case <-timeoutTimer.C:
					if !timeoutWarningShown && !send.Stopped() {
						timeoutWarningShown = true
						// Send timeout warning message (shorter and more polite)
						sendTimeoutWarning()
						log.V1.CtxWarn(ctx, "NextCode processing timeout warning sent after %v", timeoutDuration)
					}
					// Continue monitoring but don't show warning again until new content resets the flag
				case <-completed:
					timeoutTimer.Stop()
					return
				case <-streamErr:
					timeoutTimer.Stop()
					return
				case <-ctx.Done():
					timeoutTimer.Stop()
					return
				}
			}
		}()

		// Start NextCode stream processing in a separate goroutine
		go func() {
			var processingErr error
			defer func() {
				if r := recover(); r != nil {
					log.V1.CtxError(ctx, "NextCode stream processing panic: %v", r)
					processingErr = fmt.Errorf("stream processing panic: %v", r)
				}

				// Send only one signal based on final state
				if processingErr != nil {
					streamErr <- processingErr
				} else {
					completed <- true
				}
			}()

			// Helper function to safely publish to channel
			safePublish := func(data map[string]any) error {
				defer func() {
					if r := recover(); r != nil {
						log.V1.CtxError(ctx, "send.Publish panic: %v", r)
					}
				}()

				if send.Stopped() {
					return fmt.Errorf("channel is already stopped")
				}

				send.Publish(data)

				// Reset timeout timer when any content is published
				if !timeoutTimer.Stop() {
					select {
					case <-timeoutTimer.C:
					default:
					}
				}
				timeoutTimer.Reset(timeoutDuration)
				timeoutWarningShown = false // Reset warning flag when new content arrives

				return nil
			}

			// use CompleteChatWithNewClient to do stream chat
			streamProcessErr := nextcode.CompleteChatWithNewClient(ctx, req, func(resp *agent.CompleteChatResponse) error {
				// process each stream response
				if resp.Event == nil {
					return nil
				}

				// Check if we need to end thought collection for non-PartialMessage events
				if resp.Event.Type != copilot.EventTypePartialMessage && collectingThought {
					// End thought collection for any non-PartialMessage event
					collectingThought = false
					if err := safePublish(map[string]any{
						"result":               "\n\n</div>\n</details>\n\n",
						"prompt_completion_id": promptCompletionID,
					}); err != nil {
						log.V1.CtxError(ctx, "failed to publish thought end: %v", err)
						return fmt.Errorf("failed to publish thought end: %w", err)
					}
					lastMessageTime = time.Now()
				}

				// process different type of events
				output := ci.mapEventToFunctionOutput(ctx, resp.Event, promptCompletionID)
				if output != nil {
					// special process message event, add reference extract logic
					// only process partial message for now, full message will be ignored
					if resp.Event.Type == copilot.EventTypePartialMessage {
						content, isThought := ci.extractMessageContent(resp.Event)

						if isFirstChunk {
							isFirstChunk = false
							// record first token latency
							log.V1.CtxInfo(ctx, "first token latency: %v", time.Since(start))
						}

						// 处理thought状态，不管content是否为空
						if isThought {
							if !collectingThought {
								// start new thought part
								collectingThought = true

								// calculate duration since last message
								duration := time.Since(lastMessageTime)

								// send thought start mark with improved styling
								var thoughtStart string
								if ci.isDeepWikiScenario() {
									thoughtStart = fmt.Sprintf("\n\n<details>\n<summary>"+ThoughtSVG+" Thought for %.1f seconds</summary>\n<div style=\"color: #666; font-size: 0.85em; margin: 8px 0; line-height: 1.6;\">\n\n", duration.Seconds())
								} else {
									thoughtStart = fmt.Sprintf("\n\n<details>\n<summary>Thought for %.1f seconds</summary>\n<div style=\"color: #666; font-size: 0.85em; margin: 8px 0; line-height: 1.6;\">\n\n", duration.Seconds())
								}
								if err := safePublish(map[string]any{
									"result":               thoughtStart,
									"prompt_completion_id": promptCompletionID,
								}); err != nil {
									log.V1.CtxError(ctx, "failed to publish thought start: %v", err)
									return fmt.Errorf("failed to publish thought start: %w", err)
								}
							}

							// send thought content only if not empty
							if content != "" {
								if err := safePublish(map[string]any{
									"result":               content,
									"prompt_completion_id": promptCompletionID,
								}); err != nil {
									log.V1.CtxError(ctx, "failed to publish thought content: %v", err)
									return fmt.Errorf("failed to publish thought content: %w", err)
								}
							}
							return nil
						} else {
							// 非thought内容，如果正在收集thought则结束
							if collectingThought {
								// thought end, send end mark
								collectingThought = false
								if err := safePublish(map[string]any{
									"result":               "\n\n</div>\n</details>\n\n",
									"prompt_completion_id": promptCompletionID,
								}); err != nil {
									log.V1.CtxError(ctx, "failed to publish thought end: %v", err)
									return fmt.Errorf("failed to publish thought end: %w", err)
								}
								lastMessageTime = time.Now()
							}

							// process normal message content only if not empty
							if content != "" {
								// if need reference extract
								if NeedReferenceExtract {
									result := ProcessContentForReference(content, collectingRef, refStarted, refBuffer, &response)

									collectingRef = result.Continue
									refStarted = result.RefStarted
									refBuffer = result.RefBuffer

									if result.PreContent != "" {
										if err := safePublish(map[string]any{
											"result":               result.PreContent,
											"prompt_completion_id": promptCompletionID,
										}); err != nil {
											log.V1.CtxError(ctx, "failed to publish pre content: %v", err)
											return fmt.Errorf("failed to publish pre content: %w", err)
										}
									}

									if result.Reference != nil {
										if err := safePublish(map[string]any{
											"reference": result.Reference,
										}); err != nil {
											log.V1.CtxError(ctx, "failed to publish reference: %v", err)
											return fmt.Errorf("failed to publish reference: %w", err)
										}

										if result.PostContent != "" {
											if err := safePublish(map[string]any{
												"result":               result.PostContent,
												"prompt_completion_id": promptCompletionID,
											}); err != nil {
												log.V1.CtxError(ctx, "failed to publish post content: %v", err)
												return fmt.Errorf("failed to publish post content: %w", err)
											}
										}
										return nil
									}

									if !collectingRef && result.PreContent == "" {
										if err := safePublish(map[string]any{
											"result":               content,
											"prompt_completion_id": promptCompletionID,
										}); err != nil {
											log.V1.CtxError(ctx, "failed to publish content: %v", err)
											return fmt.Errorf("failed to publish content: %w", err)
										}
									}
									return nil
								} else {
									// no reference extract, just return content
									response += content
									if err := safePublish(map[string]any{
										"result":               content,
										"prompt_completion_id": promptCompletionID,
									}); err != nil {
										log.V1.CtxError(ctx, "failed to publish content: %v", err)
										return fmt.Errorf("failed to publish content: %w", err)
									}
									return nil
								}
							}
						}
					} else if resp.Event.Type == copilot.EventTypeBuiltinToolCall {
						// process builtin tool call event, format to markdown and send
						if resp.Event.Detail != nil && resp.Event.Detail.BuiltinToolCall != nil {
							toolCall := resp.Event.Detail.BuiltinToolCall
							// extract tool call params and icon
							icon, toolParams := ci.extractToolCallParams(toolCall)

							// only send if icon is not empty (meaning this tool is recognized)
							if icon != "" {
								var formattedContent string
								if ci.isDeepWikiScenario() {
									formattedContent = fmt.Sprintf(ToolCallMarkDownFormat, icon, toolCall.ToolType, toolParams)
								} else {
									formattedContent = fmt.Sprintf(BitsAIToolCallFormat, toolCall.ToolType, toolParams)
								}

								// send as normal incremental message
								if err := safePublish(map[string]any{
									"result":               formattedContent,
									"prompt_completion_id": promptCompletionID,
								}); err != nil {
									log.V1.CtxError(ctx, "failed to publish tool call: %v", err)
									return fmt.Errorf("failed to publish tool call: %w", err)
								}
							}
						}
						return nil
					} else if resp.Event.Type == copilot.EventTypeMCPToolCall {
						// process mcp tool call event, format to markdown and send
						if resp.Event.Detail != nil && resp.Event.Detail.MCPToolCall != nil {
							toolCall := resp.Event.Detail.MCPToolCall
							var formattedContent string
							if ci.isDeepWikiScenario() {
								formattedContent = fmt.Sprintf(MCPToolCallMarkDownFormat, MCPSVG, toolCall.ToolName)
							} else {
								formattedContent = fmt.Sprintf(BitsAIMCPToolCallFormat, toolCall.ToolName)
							}
							if err := safePublish(map[string]any{
								"result":               formattedContent,
								"prompt_completion_id": promptCompletionID,
							}); err != nil {
								log.V1.CtxError(ctx, "failed to publish mcp tool call: %v", err)
								return fmt.Errorf("failed to publish mcp tool call: %w", err)
							}
						}
						return nil
					}

					// send other type of events
					if err := safePublish(output); err != nil {
						log.V1.CtxError(ctx, "failed to publish event output: %v", err)
						return fmt.Errorf("failed to publish event output: %w", err)
					}
				}
				return nil
			})

			if streamProcessErr != nil {
				processingErr = streamProcessErr
			}
		}()

		// Wait for stream processing to complete, similar to ForwardWithFilterNil
		select {
		case <-completed:
			log.V1.CtxInfo(ctx, "NextCode stream processing completed successfully")
			// Check for any unfinished reference content, similar to code_functions.go
			if collectingRef && len(refBuffer) > 0 {
				log.V1.CtxWarn(ctx, "reference processing timeout, no complete reference found: %s", refBuffer)
				response += refBuffer
				// Safely publish unfinished buffer content
				safePublish(map[string]any{
					"result":               refBuffer,
					"prompt_completion_id": promptCompletionID,
				})
			}
		case streamProcessingErr := <-streamErr:
			log.V1.CtxError(ctx, "Wiki Ask Failed: NextCode stream processing error: %v", streamProcessingErr)

			// Check if this could be due to downstream disconnection
			// Send warning to user before reporting the error
			sendDisconnectionWarning()
			log.V1.CtxWarn(ctx, "Stream processing error detected - user warning sent before error report")

			// 上报wiki ask failed事件
			ci.reportWikiAskFailedEvent(ctx, opt, "stream_processing_failed", streamProcessingErr, repoName, revision)
			send.PublishError(errors.WithMessage(streamProcessingErr, "nextcode chat stream failed"), false)
		case <-ctx.Done():
			log.V1.CtxWarn(ctx, "NextCode stream processing cancelled due to context cancellation")

			// Context cancellation often indicates downstream disconnection
			// Send warning to user before reporting cancellation
			sendDisconnectionWarning()
			log.V1.CtxWarn(ctx, "Context cancellation detected - user warning sent before cancellation report")

			send.PublishError(errors.New("stream processing cancelled"), false)
		}
	}()

	return recv, nil
}

// shouldInjectRepoInfo checks if the function requires repo info injection
func (ci *AgenticNextCodeFunctionInvoker) shouldInjectRepoInfo() bool {
	repoInfoFunctions := []string{
		AgenticWikiChatFunction,
		AnswerAboutRepoFunction,
		AnswerAboutCodeFunction,
		AgenticExplainCodeFunction,
	}

	for _, funcID := range repoInfoFunctions {
		if ci.FunctionID == funcID {
			return true
		}
	}
	return false
}

// isDeepWikiScenario 判断是否为DeepWiki场景
func (ci *AgenticNextCodeFunctionInvoker) isDeepWikiScenario() bool {
	return ci.FunctionID == AgenticWikiChatFunction
}

// buildCompleteChatRequest 构建 NextCode 聊天请求
func (ci *AgenticNextCodeFunctionInvoker) buildCompleteChatRequest(
	ctx context.Context, opt *skillservice.FunctionInvokeOption, repoId string, revision string) (*agent.CompleteChatRequest, error) {

	// check nil history messages
	var historyEvents []*copilot.Event
	if opt.Msgs != nil {
		historyEvents = make([]*copilot.Event, 0, len(opt.Msgs))

		// limit history messages to 5
		msgs := opt.Msgs
		if len(msgs) > 5 {
			msgs = msgs[len(msgs)-5:]
		}

		for i, msg := range msgs {
			if msg == nil {
				// ignore nil message
				continue
			}

			event := &copilot.Event{
				Type:      copilot.EventTypeFullMessage,
				Id:        opt.SessionID,
				CreatedAt: time.Now().Format(time.RFC3339),
				Detail: &copilot.EventDetail{
					FullMessage: &copilot.FullMessageEvent{
						Message: &copilot.Message{
							Id:   fmt.Sprintf("msg-%d", i),
							Role: ci.mapRole(msg.Role),
							Parts: []*copilot.Part{
								{
									Text: &copilot.TextPart{Text: msg.Content},
								},
							},
						},
					},
				},
			}
			historyEvents = append(historyEvents, event)
		}
	}

	// WA: handle all json.Number to float64, prevent template error
	var variables map[string]any
	if opt.Context != nil && opt.Context.Variables != nil {
		variables = make(map[string]any)
		for k, v := range opt.Context.Variables {
			// special handle CodeAssistVariable type, ensure field name match template
			if codeUserMessage, ok := v.([]*knowledgebase.CodeAssistVariable); ok {
				// use json serialization/deserialization to automatically convert field name(from Go field name to JSON tag name)
				// so CodeChunk -> code_chunk, Text -> text, etc.
				variableMap := map[string]any{
					k: codeUserMessage,
				}
				variableMap = conv.MapStringAny(conv.JSONString(variableMap))
				variables[k] = variableMap[k]
			} else {
				variables[k] = convertJSONNumbersToFloat64(v)
			}
		}
	} else {
		variables = make(map[string]any)
	}

	// make final history messages
	finalHistory := make([]*copilot.Event, 0)
	if len(historyEvents) > 0 {
		finalHistory = append(finalHistory, historyEvents[len(historyEvents)-1])
	}

	// replace last message with repo info (simplified condition)
	if variables["repo_info"] != nil && ci.shouldInjectRepoInfo() {
		repoInfo := variables["repo_info"].(string)
		// insert repo info as user message
		repoInfoMessage := &copilot.Event{
			Type:      copilot.EventTypeFullMessage,
			Id:        opt.SessionID,
			CreatedAt: time.Now().Format(time.RFC3339),
			Detail: &copilot.EventDetail{
				FullMessage: &copilot.FullMessageEvent{
					Message: &copilot.Message{
						Id:   "repo-info-msg",
						Role: copilot.RoleUser,
						Parts: []*copilot.Part{
							{
								Text: &copilot.TextPart{Text: repoInfo},
							},
						},
					},
				},
			},
		}
		finalHistory = append(finalHistory, repoInfoMessage)
	}

	// compose current user input in variables, considering wiki_select in variables
	userInput, ok := variables["user_input"]
	if !ok {
		log.V1.CtxError(ctx, "user_input not found in variables")
		return nil, errors.New("user_input not found in variables")
	}

	if wikiSelect, ok := variables["wiki_select"]; ok {
		userInput = userInput.(string) + "\n" + wikiSelect.(string)
	}

	if userInput != "" {
		finalHistory = append(finalHistory, &copilot.Event{
			Type:      copilot.EventTypeFullMessage,
			Id:        opt.SessionID,
			CreatedAt: time.Now().Format(time.RFC3339),
			Detail: &copilot.EventDetail{
				FullMessage: &copilot.FullMessageEvent{
					Message: &copilot.Message{
						Id:   "user-input",
						Role: copilot.RoleUser,
						Parts: []*copilot.Part{
							{
								Text: &copilot.TextPart{Text: userInput.(string)},
							},
						},
					},
				},
			},
		})
	}

	// render context instruction template
	wiki_output_instruction, err := ci.renderContextInstructionTemplate(variables)
	if err != nil {
		log.V1.CtxError(ctx, "failed to render context instruction template: %v", err)
		return nil, errors.WithMessage(err, "failed to render context instruction template")
	}

	// render context variables instruction template
	context_variables_instruction, err := ci.renderContextVariablesInstructionTemplate(ctx, variables, opt.Account)
	if err != nil {
		log.V1.CtxError(ctx, "failed to render context variables instruction template: %v", err)
		return nil, errors.WithMessage(err, "failed to render context variables instruction template")
	}

	// 构建禁用的工具列表
	disabledToolNames := []string{
		copilot.BuiltinToolTypeTask,
		copilot.BuiltinToolTypeWrite,
		copilot.BuiltinToolTypeEdit,
		copilot.BuiltinToolTypeBash,
		// hardcoded builtin tools
		"multi_edit",

		// lark tools
		"mcp__lark__GetDocument",
		"mcp__lark__docx_v1_document_rawContent",
		"mcp__lark__wiki_v2_space_getNode",

		// === Codebase MCP 写操作工具 (ADK 格式) ===
		"mcp__codebase__CreateBranch",               // 创建分支
		"mcp__codebase__CreateOrUpdateFiles",        // 创建/更新文件
		"mcp__codebase__CreateMergeRequest",         // 创建 MR
		"mcp__codebase__MergeMergeRequest",          // 合并 MR (最高危)
		"mcp__codebase__UpdateMergeRequest",         // 更新 MR
		"mcp__codebase__CreateMergeRequestComment",  // 创建评论
		"mcp__codebase__CreateMergeRequestComments", // 创建多评论
		"mcp__codebase__PublishDraftComments",       // 发布评论
	}

	// 只在非 deepwiki 场景下禁用 semantic_symbol_search 工具
	if ci.FunctionID != AgenticWikiChatFunction {
		disabledToolNames = append(disabledToolNames, "semantic_symbol_search")
	}

	req := &agent.CompleteChatRequest{
		History:                finalHistory,
		RepoId:                 lo.ToPtr(repoId),
		CommitId:               lo.ToPtr(revision),
		DisabledToolNames:      disabledToolNames,
		AdditionalInstructions: []string{wiki_output_instruction, context_variables_instruction},
	}

	return req, nil
}

// convertJSONNumbersToFloat64 转换函数
func convertJSONNumbersToFloat64(v any) any {
	switch x := v.(type) {
	case map[string]any:
		for k, v := range x {
			x[k] = convertJSONNumbersToFloat64(v)
		}
		return x
	case []any:
		for i, v := range x {
			x[i] = convertJSONNumbersToFloat64(v)
		}
		return x
	case json.Number:
		f, err := x.Float64()
		if err != nil {
			return v
		}
		return f
	default:
		return v
	}
}

// mapRole 映射角色类型
func (ci *AgenticNextCodeFunctionInvoker) mapRole(role string) string {
	switch role {
	case "user":
		return copilot.RoleUser
	case "assistant", "agent":
		return copilot.RoleAgent
	default:
		return copilot.RoleUser
	}
}

// mapEventToFunctionOutput 将 NextCode 事件映射为 FunctionOutput
func (ci *AgenticNextCodeFunctionInvoker) mapEventToFunctionOutput(ctx context.Context, event *copilot.Event, promptCompletionID int64) skillservice.FunctionOutput {
	if event == nil {
		return nil
	}

	switch event.Type {
	case copilot.EventTypePartialMessage:
		// 处理部分消息事件
		content, isThought := ci.extractMessageContent(event)
		if content != "" {
			return map[string]any{
				"prompt_completion_id": promptCompletionID,
				"result":               content,
				"event_type":           "partial_message",
				"event_id":             event.Id,
				"last_chunk":           event.Detail.PartialMessage.LastChunk,
				"is_thought":           isThought,
			}
		}

	case copilot.EventTypeBuiltinToolCall:
		// 处理内置工具调用事件
		if event.Detail != nil && event.Detail.BuiltinToolCall != nil {
			toolCall := event.Detail.BuiltinToolCall
			return map[string]any{
				"prompt_completion_id": promptCompletionID,
				"event_type":           "builtin_tool_call",
				"event_id":             event.Id,
				"tool_type":            toolCall.ToolType,
				"tool_call_id":         toolCall.Id,
				"message_id":           toolCall.MessageId,
			}
		}

	case copilot.EventTypeBuiltinToolCallOutput:
		// 处理内置工具调用输出事件
		if event.Detail != nil && event.Detail.BuiltinToolCallOutput != nil {
			output := event.Detail.BuiltinToolCallOutput
			outputData := ci.extractToolOutput(output)
			return map[string]any{
				"prompt_completion_id": promptCompletionID,
				"event_type":           "builtin_tool_call_output",
				"event_id":             event.Id,
				"tool_type":            output.ToolType,
				"builtin_tool_call_id": output.BuiltinToolCallId,
				"tool_output":          outputData,
			}
		}

	case copilot.EventTypeTaskStatusUpdate:
		// 处理任务状态更新事件
		if event.Detail != nil && event.Detail.TaskStatusUpdate != nil {
			statusUpdate := event.Detail.TaskStatusUpdate
			return map[string]any{
				"prompt_completion_id": promptCompletionID,
				"event_type":           "task_status_update",
				"event_id":             event.Id,
				"status":               statusUpdate.Status,
				"final":                statusUpdate.Final,
			}
		}
	case copilot.EventTypeMCPToolCall:
		// 处理MCP工具调用事件
		if event.Detail != nil && event.Detail.MCPToolCall != nil {
			toolCall := event.Detail.MCPToolCall
			return map[string]any{
				"prompt_completion_id": promptCompletionID,
				"event_type":           "mcp_tool_call",
				"event_id":             event.Id,
				"tool_type":            toolCall.ToolName,
				"tool_call_id":         toolCall.Id,
				"message_id":           toolCall.MessageId,
			}
		}
	case copilot.EventTypeMCPToolCallOutput:
		// 处理MCP工具调用输出事件
		if event.Detail != nil && event.Detail.MCPToolCallOutput != nil {
			output := event.Detail.MCPToolCallOutput
			outputData := ci.extractMCPToolOutput(output)
			return map[string]any{
				"prompt_completion_id": promptCompletionID,
				"event_type":           "mcp_tool_call_output",
				"event_id":             event.Id,
				"tool_type":            output.ToolName,
				"mcp_tool_call_id":     output.MCPToolCallId,
				"tool_output":          outputData,
			}
		}
	}

	// 未知事件类型，返回原始数据
	log.V1.CtxInfo(ctx, "unknown event type: %v", event)
	eventData, _ := json.Marshal(event)
	log.V1.CtxInfo(ctx, "event raw: %v", eventData)
	return map[string]any{
		"prompt_completion_id": promptCompletionID,
		"event_type":           "unknown",
		"event_id":             event.Id,
		"raw_event":            string(eventData),
	}
}

// extractToolOutput 提取工具输出内容
func (ci *AgenticNextCodeFunctionInvoker) extractToolOutput(output *copilot.BuiltinToolCallOutputEvent) map[string]any {
	result := make(map[string]any)

	if output.Output.Grep != nil {
		result["grep"] = map[string]any{
			"total_files": output.Output.Grep.TotalFiles,
			"files":       output.Output.Grep.Files,
			"truncated":   output.Output.Grep.Truncated,
		}
	} else if output.Output.GetRead() != nil {
		result["read"] = map[string]any{
			"offset": output.Output.GetRead().Offset,
			"lines":  output.Output.GetRead().Lines,
		}
	} else if output.Output.Glob != nil {
		result["glob"] = map[string]any{
			"files":     output.Output.Glob.Files,
			"truncated": output.Output.Glob.Truncated,
		}
	} else if output.Output.Ls != nil {
		result["ls"] = map[string]any{
			"path":      output.Output.Ls.Path,
			"tree":      output.Output.Ls.Tree,
			"truncated": output.Output.Ls.Truncated,
		}
	} else if output.Output.Task != nil {
		// TaskOutput.Result_ is []*Part, extract text content like MCP tool output
		textParts := make([]string, 0)
		for _, part := range output.Output.Task.Result_ {
			if part != nil && part.Text != nil {
				textParts = append(textParts, part.Text.Text)
			}
		}
		if len(textParts) > 0 {
			result["task"] = map[string]any{
				"text_content": textParts,
			}
		}
		// Also provide raw output for debugging if needed
		result["task_raw"] = map[string]any{
			"result_parts": output.Output.Task.Result_,
		}
	}

	return result
}

// extractMCPToolOutput 提取MCP工具输出内容
func (ci *AgenticNextCodeFunctionInvoker) extractMCPToolOutput(output *copilot.MCPToolCallOutputEvent) map[string]any {
	result := make(map[string]any)

	if len(output.Output) > 0 {
		// 提取所有Part中的文本内容
		textParts := make([]string, 0)
		for _, part := range output.Output {
			if part != nil && part.Text != nil {
				textParts = append(textParts, part.Text.Text)
			}
		}
		if len(textParts) > 0 {
			result["text_content"] = textParts
		}

		// 保留原始output结构供调试
		result["raw_output"] = output.Output
	}

	return result
}

// extractToolCallParams 提取工具调用的关键参数信息和对应图标
func (ci *AgenticNextCodeFunctionInvoker) extractToolCallParams(toolCall *copilot.BuiltinToolCallEvent) (string, string) {
	var icon, params string

	switch toolCall.ToolType {
	case copilot.BuiltinToolTypeRead:
		icon = ViewSVG
		params = fmt.Sprintf("viewing %s", toolCall.Input.GetRead().FilePath)
	case copilot.BuiltinToolTypeGlob:
		icon = SearchSVG
		params = fmt.Sprintf("searching path %s with pattern %s", lo.FromPtr(toolCall.Input.Glob.Path), toolCall.Input.Glob.Pattern)
	case copilot.BuiltinToolTypeGrep:
		icon = SearchSVG
		params = fmt.Sprintf("searching file %s having content %s", lo.FromPtr(toolCall.Input.Grep.Path), toolCall.Input.Grep.Pattern)
	case copilot.BuiltinToolTypeLs:
		icon = SearchSVG
		params = fmt.Sprintf("list files in %s", toolCall.Input.Ls.Path)
	case copilot.BuiltinToolTypeTask:
		icon = SearchSVG
		params = fmt.Sprintf("sub agent task - may take several minutes - %s", toolCall.Input.Task.Prompt)
	}

	return icon, params
}

// extractMessageContent 从事件中提取消息内容，返回内容和是否为思考内容
func (ci *AgenticNextCodeFunctionInvoker) extractMessageContent(event *copilot.Event) (string, bool) {
	if event == nil {
		return "", false
	}

	switch event.Type {
	case copilot.EventTypePartialMessage:
		if event.Detail != nil && event.Detail.PartialMessage != nil &&
			event.Detail.PartialMessage.Message != nil {

			// 先检查是否有思考内容
			if len(event.Detail.PartialMessage.Message.Thought) > 0 &&
				event.Detail.PartialMessage.Message.Thought[0] != nil &&
				event.Detail.PartialMessage.Message.Thought[0].Text != nil {
				return event.Detail.PartialMessage.Message.Thought[0].Text.Text, true
			}

			// 再检查普通文本内容
			if len(event.Detail.PartialMessage.Message.Parts) > 0 &&
				event.Detail.PartialMessage.Message.Parts[0] != nil &&
				event.Detail.PartialMessage.Message.Parts[0].Text != nil {
				return event.Detail.PartialMessage.Message.Parts[0].Text.Text, false
			}
		}
	case copilot.EventTypeFullMessage:
		if event.Detail != nil && event.Detail.FullMessage != nil &&
			event.Detail.FullMessage.Message != nil {

			// 先检查是否有思考内容
			if len(event.Detail.FullMessage.Message.Thought) > 0 &&
				event.Detail.FullMessage.Message.Thought[0] != nil &&
				event.Detail.FullMessage.Message.Thought[0].Text != nil {
				return event.Detail.FullMessage.Message.Thought[0].Text.Text, true
			}

			// 再检查普通文本内容
			if len(event.Detail.FullMessage.Message.Parts) > 0 &&
				event.Detail.FullMessage.Message.Parts[0] != nil &&
				event.Detail.FullMessage.Message.Parts[0].Text != nil {
				return event.Detail.FullMessage.Message.Parts[0].Text.Text, false
			}
		}
	}
	return "", false
}

// renderContextInstructionTemplate 渲染上下文指令模板
func (ci *AgenticNextCodeFunctionInvoker) renderContextInstructionTemplate(variables map[string]any) (string, error) {
	// 根据场景选择不同的模板
	var templateContent string
	if ci.isDeepWikiScenario() {
		templateContent = deepwikiContextInstructionTemplate
	} else {
		templateContent = bitsaiContextInstructionTemplate
	}

	// 创建模板并解析
	tmpl, err := template.New("context_instruction").Parse(templateContent)
	if err != nil {
		return "", errors.WithMessage(err, "failed to parse context instruction template")
	}

	// 使用variables渲染模板
	var buf bytes.Buffer
	err = tmpl.Execute(&buf, variables)
	if err != nil {
		return "", errors.WithMessage(err, "failed to execute context instruction template")
	}

	return buf.String(), nil
}

// renderContextVariablesInstructionTemplate 渲染上下文变量指令模板
func (ci *AgenticNextCodeFunctionInvoker) renderContextVariablesInstructionTemplate(ctx context.Context, variables map[string]any, account *authentity.Account) (string, error) {
	// enhance variables with MR information if change_id exists
	if changeID, exists := variables["change_id"]; exists {
		if repoNameVar, repoExists := variables["repo_name"]; repoExists {
			if repoNameStr, ok := repoNameVar.(string); ok && repoNameStr != "" {
				if changeIDFloat, ok := changeID.(float64); ok {
					changeIDInt := int64(changeIDFloat)
					log.V1.CtxInfo(ctx, "fetching MR details for change_id: %d", changeIDInt)

					// Get change detail from Codebase API
					changeDetail, err := ci.CodebaseCli.GetChangeDetail(ctx, repoNameStr, changeIDInt, false, codebaseWithJWT(account))
					if err != nil {
						log.V1.CtxWarn(ctx, "failed to get change detail for change_id %d: %v", changeIDInt, err)
					} else if changeDetail != nil {
						// Add MR number to variables
						if changeDetail.ExternalID > 0 {
							variables["mr_number"] = changeDetail.ExternalID
							log.V1.CtxInfo(ctx, "found MR number: %d for change_id: %d", changeDetail.ExternalID, changeIDInt)
						}
						// Add MR URL if available
						if changeDetail.ExternalUrl != "" {
							variables["mr_url"] = changeDetail.ExternalUrl
						}
					}
				}
			}
		}
	}

	// 创建模板并解析
	tmpl, err := template.New("context_variables_instruction").Parse(contextVariablesInstructionTemplate)
	if err != nil {
		return "", errors.WithMessage(err, "failed to parse context variables instruction template")
	}

	// 使用variables渲染模板
	var buf bytes.Buffer
	err = tmpl.Execute(&buf, variables)
	if err != nil {
		return "", errors.WithMessage(err, "failed to execute context variables instruction template")
	}

	return buf.String(), nil
}

func (ci *AgenticNextCodeFunctionInvoker) GetUserID(ctx context.Context, account *authentity.Account) (string, error) {
	if account == nil || account.Username == "" {
		return "", errors.New("account is nil or username is empty")
	}

	userBriefs, err := ci.NextCodeClient.ListUserBriefs(ctx, sdkaccount.ListUserBriefsRequest{
		Usernames: []string{account.Username},
	})
	if err != nil {
		return "", err
	}
	if len(userBriefs.Users) == 0 {
		return "", errors.New("user not found")
	}
	return userBriefs.Users[0].Id, nil
}

// WithUserID adds user ID to context using metainfo
func WithUserID(ctx context.Context, userID string) context.Context {
	return metainfo.WithValue(ctx, metaKeyUserIDGRPC, userID)
}

// reportWikiAskFailedEvent 上报wiki ask失败事件
func (ci *AgenticNextCodeFunctionInvoker) reportWikiAskFailedEvent(
	ctx context.Context,
	opt *skillservice.FunctionInvokeOption,
	errorType string,
	err error,
	repoName, revision string,
) {
	// 只有在wiki场景下才上报wiki ask failed事件
	if ci.FunctionID != AgenticWikiChatFunction {
		return
	}

	// 从context获取logID
	logID := ctxvalues.LogIDDefault(ctx)

	// 构建事件数据并上报
	wikiAskFailedEventMap := map[string]interface{}{
		"repo_name":       repoName,
		"revision":        revision,
		"conversation_id": opt.ConversationID,
		"message_id":      opt.MessageID,
		"error_type":      errorType,
		"error_message":   err.Error(),
		"log_id":          logID,
	}
	ci.EventCollector.EmitEvent(ctx, entity.EventBitsAIWikiAskFailed, wikiAskFailedEventMap, opt.Account.Username)
}

func (ci *AgenticNextCodeFunctionInvoker) reportWikiAskEvent(
	ctx context.Context,
	opt *skillservice.FunctionInvokeOption,
	repoName, revision string,
) {
	// 只有在wiki场景下才上报wiki ask事件
	if ci.FunctionID != AgenticWikiChatFunction {
		return
	}

	wikiAskEventMap := map[string]interface{}{
		"repo_name":       repoName,
		"revision":        revision,
		"conversation_id": opt.ConversationID,
		"message_id":      opt.MessageID,
	}
	ci.EventCollector.EmitEvent(ctx, entity.EventBitsAIWikiAsk, wikiAskEventMap, opt.Account.Username)
}
