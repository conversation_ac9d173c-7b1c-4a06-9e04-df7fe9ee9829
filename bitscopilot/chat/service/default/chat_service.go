package defaultchatservice

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	bytetrace "code.byted.org/bytedtrace/interface-go"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	llmstackgen "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/llmstack"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/utgen/codeverseunittestservice"
	codeaiservice "code.byted.org/devgpt/kiwis/apps/codeai/service"
	agententity "code.byted.org/devgpt/kiwis/bitscopilot/agent/entity"
	agentserivce "code.byted.org/devgpt/kiwis/bitscopilot/agent/service"
	appservice "code.byted.org/devgpt/kiwis/bitscopilot/app/service"
	"code.byted.org/devgpt/kiwis/bitscopilot/chat/dal"
	"code.byted.org/devgpt/kiwis/bitscopilot/chat/service"
	"code.byted.org/devgpt/kiwis/bitscopilot/codecopilot/entity"
	skillservice "code.byted.org/devgpt/kiwis/bitscopilot/skill/service"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	appentity "code.byted.org/devgpt/kiwis/copilotstack/entity/app"
	chatentity "code.byted.org/devgpt/kiwis/copilotstack/entity/chat"
	"code.byted.org/devgpt/kiwis/copilotstack/entity/skill"
	skillentity "code.byted.org/devgpt/kiwis/copilotstack/entity/skill"
	journalservice "code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/lib/apierror"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/lib/tokenizer"
	"code.byted.org/devgpt/kiwis/port/ab"
	"code.byted.org/devgpt/kiwis/port/llmstack"
	"code.byted.org/devgpt/kiwis/port/tos"
)

var _ service.ChatService = &ChatService{}

type ChatService struct {
	DAO             dal.ChatDAO
	SDK             llmstack.Client
	IDGenerator     IDGenerator
	FunctionCaller  agentserivce.FunctionCallAgent
	ToolAgent       agentserivce.BitsToolAgent
	AppService      appservice.AppService
	SkillService    skillservice.SkillService
	JournalService  journalservice.JournalService
	UTClient        codeverseunittestservice.Client `optional:"true"`
	IntentService   codeaiservice.IntentService
	WikiChatStorage tos.Client
}

func (s *ChatService) CreateConversation(ctx context.Context, opt service.CreateConversationOption) (*service.CreateConversationResult, error) {
	c, err := s.DAO.CreateConversation(ctx, dal.CreateConversationOption{
		AppID:   opt.AppID,
		ID:      s.IDGenerator.NewID(),
		Title:   lo.Ternary(len(opt.Title) != 0, opt.Title, fmt.Sprintf("Copilot Chat @ %s", time.Now().Format(time.DateTime))),
		Creator: opt.Creator,
		Context: opt.Context,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create conversation record")
	}

	var initialMessages []*chatentity.Message
	if len(opt.InitialMessages) != 0 {
		createMsgOpts := make([]dal.CreateMessageOption, len(opt.InitialMessages))
		for idx := range opt.InitialMessages {
			createMsgOpts[idx] = dal.CreateMessageOption{
				ID:                s.IDGenerator.NewID(),
				ConversationID:    c.ID,
				Username:          opt.InitialMessages[idx].Username,
				Role:              opt.InitialMessages[idx].Role,
				Type:              opt.InitialMessages[idx].Type,
				Prompt:            opt.InitialMessages[idx].Prompt,
				Content:           opt.InitialMessages[idx].Content,
				SkillFunctionCall: opt.InitialMessages[idx].FunctionCall,
				CreatedAt:         opt.InitialMessages[idx].CreatedAt,
			}
		}
		initialMessages, err = s.DAO.CreateMessages(ctx, createMsgOpts...)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create initial messages")
		}
		_, err := s.LogByMessages(ctx, opt.AppID, opt.Creator, createMsgOpts...)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to log by messages: %v", err)
		}
	}

	return &service.CreateConversationResult{
		Conversation:    c,
		InitialMessages: lo.Reverse(initialMessages),
	}, nil
}

func (s *ChatService) GetConversation(ctx context.Context, opt service.GetConversationOption) (*chatentity.ConversationWithMessages, error) {
	c, err := s.DAO.GetConversation(ctx, opt.AppID, opt.ID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get conversation record")
	}

	msgs, count, err := s.DAO.ListMessages(ctx, dal.ListMessagesOption{
		ConversationID: &opt.ID,
		StartTime:      opt.StartTime,
		Offset:         opt.Offset,
		Limit:          opt.Limit,
		Type:           opt.Type,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list messages")
	}

	reverseOrders := false
	for _, msg := range msgs {
		if msg.Type == "event_stream" {
			reverseOrders = true
			// read content wiki storage
			eventStreamInfo, err := s.WikiChatStorage.GetObject(ctx, msg.Content)
			if err != nil {
				log.V1.CtxError(ctx, "failed to read content from wiki storage: %v", err)
				continue
			}
			streamContent, err := io.ReadAll(eventStreamInfo.R)
			if err != nil {
				log.V1.CtxError(ctx, "failed to read content from wiki storage: %v", err)
				continue
			}
			log.V1.CtxInfo(ctx, "successfully reload event stream from wiki storage"+
				" for message %s, conversation %s, storage key %s", msg.ID, c.ID, msg.Content)
			msg.Content = string(streamContent)
		}
	}
	if reverseOrders {
		msgs = lo.Reverse(msgs)
	}

	return &chatentity.ConversationWithMessages{
		Conversation:  c,
		Messages:      msgs,
		TotalMessages: count,
	}, nil
}

func (s *ChatService) GetMessage(ctx context.Context, messageId string) (*chatentity.Message, error) {
	return s.DAO.GetMessage(ctx, messageId)
}

func (s *ChatService) UpdateMessage(ctx context.Context, messageID string, opt service.UpdateMessageOption) (*chatentity.Message, error) {
	return s.DAO.UpdateMessage(ctx, messageID, dal.UpdateMessageOption{
		IsCanceled: opt.IsCanceled,
	})
}

func (s *ChatService) ListMessages(ctx context.Context, opt service.ListMessagesOption) ([]*chatentity.Message, int64, error) {
	return s.DAO.ListMessages(ctx, dal.ListMessagesOption{
		ConversationID: opt.ConversationID,
		SessionID:      opt.SessionID,
		StartTime:      opt.StartTime,
		Offset:         opt.Offset,
		Limit:          opt.Limit,
	})
}

func (s *ChatService) DeleteConversation(ctx context.Context, appID, id string) error {
	deleted, err := s.DAO.DeleteConversation(ctx, appID, id)
	if err != nil {
		return err
	}

	if deleted != 1 {
		return errors.New("no conversation is deleted, maybe a mismatched id is given")
	}

	return nil
}

func (s *ChatService) ListConversations(ctx context.Context, opt service.ListConversationsOption) ([]*chatentity.Conversation, int64, error) {
	if len(opt.Creator) == 0 {
		return nil, 0, errors.New("creator username is required")
	}

	cs, count, err := s.DAO.ListConversations(ctx, dal.ListConversationsOption{
		AppID:    opt.AppID,
		Creator:  &opt.Creator,
		RepoName: conv.DefaultAnyPtr[string](opt.CodeContext["repo_name"]),
		Offset:   opt.Offset,
		Limit:    opt.Limit,
	})
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to list conversations for user")
	}

	return cs, count, nil
}

func (s *ChatService) generateChatSummary(ctx context.Context, c *chatentity.Conversation, lastMessage *chatentity.Message, model string) (err error) {
	span, ctx := bytetrace.StartCustomSpan(ctx, "chat", "generateChatSummary")
	defer span.Finish()
	start := time.Now()
	defer func() {
		_ = metrics.CM.StepLatency.WithTags(&metrics.StepTag{
			Step: "generate_chat_title",
			Err:  metrics.BoolTag(err != nil),
		}).Observe(float64(time.Since(start).Milliseconds()))
	}()
	resp, _, err := s.SDK.Chat(ctx,
		llmstackgen.ChatRequest{
			Model: model,
			Messages: []*llmstackgen.Message{
				{
					Role: llmstack.RoleSystem,
					Content: `You are an AI bot which is good at summarizing message sentences, and you knows many languages.
You must summarize the given sentences to a short statement phrase using the same language as the sentence.
You should output the summary directly, with no prefix or suffix.
Examples:

Message: "你好，今天天气是不是不好"
Summary: 天气情况

Message: "How many people in the US?"
Summary: US population
`,
				},
				{
					Role: llmstack.RoleUser,
					Content: fmt.Sprintf(
						"Summarize this message:\nMessage: %q",
						lastMessage.Content,
					),
				},
			},
			N:           lo.ToPtr(int32(1)),
			Stream:      lo.ToPtr(false),
			Temperature: lo.ToPtr(0.2),
		},
		llmstack.WithByteCloudServiceJWT(),
	)
	if err != nil {
		return errors.WithMessage(err, "failed to use AI to summary user intent")
	}

	title := strings.Trim(strings.TrimPrefix(resp.Choices[0].Message.Content, "Summary:"), " \t\n\r")

	c, err = s.DAO.UpdateConversation(ctx, dal.UpdateConversationOption{
		AppID: c.AppID,
		ID:    c.ID,
		Title: &title,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update conversation title to user question summary")
	}
	log.V1.CtxInfo(ctx, "updated conversation %s title to user question summary: %s", c.ID, title)

	return nil
}

const (
	// retain several paired messages, such as [...user, assistant, user]:
	// the last message must be user role.
	defaultContextMaxHistoryMessages = 9

	failedMessageNotice = "Sorry, failed to generate response due to some errors."
)

func askEventProgress(progress string) *service.AskEvent {
	return &service.AskEvent{Progress: &progress}
}

func getAgentMessages(msgs []*chatentity.Message) []*agententity.Message {
	return lo.Reverse(lo.Map(msgs, func(item *chatentity.Message, index int) *agententity.Message {
		role := item.Role
		if role == chatentity.RoleTypeCopilot {
			role = "assistant"
		}
		return &agententity.Message{
			Role:    string(role),
			Name:    item.Username,
			Content: item.Content,
			Time:    item.CreatedAt,
		}
	}))
}

// 新增辅助函数用于收集事件数据
func collectEventData(event *service.AskEvent) (eventType string, eventData interface{}) {
	if event.MessageChunk != nil {
		return "message_chunk", *event.MessageChunk
	} else if event.Progress != nil {
		return "progress", *event.Progress
	} else if event.FunctionCall != nil {
		return "function_call", event.FunctionCall
	} else if event.MessageTips != nil {
		return "message_tips", event.MessageTips
	} else if event.FileContents != nil {
		return "file_contents", event.FileContents
	} else if event.Reference != nil {
		return "reference", event.Reference
	} else if event.Done != nil {
		return "done", nil
	}
	return "", nil
}

// 新增辅助函数用于创建快照消息
func (s *ChatService) createSnapshotMessage(ctx context.Context, eventCollection []map[string]interface{}, opt service.AskOption) {
	if len(eventCollection) == 0 {
		return
	}

	snapshotID := s.IDGenerator.NewID()
	snapshotContent, err := json.Marshal(eventCollection)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to marshal event collection: %v", err)
		return
	}

	snapShotKey := fmt.Sprintf("%s/%s.json", opt.ConversationID, snapshotID)
	// calculate size and create a io.Reader of snapshotContent
	size := int64(len(snapshotContent))
	reader := bytes.NewReader(snapshotContent)
	err = s.WikiChatStorage.PutObject(ctx, snapShotKey, size, reader)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to save snapshot content to local file: %v", err)
		return
	}

	snapshotMsg := dal.CreateMessageOption{
		ID:             snapshotID,
		ConversationID: opt.ConversationID,
		SessionID:      opt.SessionID,
		Username:       chatentity.DefaultCopilotUsername,
		Role:           chatentity.RoleType("user"),
		Type:           chatentity.MessageType("event_stream"),
		Content:        snapShotKey, // only save the key, not the content
	}

	_, err = s.DAO.CreateMessages(ctx, snapshotMsg)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to create snapshot message: %v", err)
	} else {
		log.V1.CtxInfo(ctx, "created snapshot message: %s", snapshotID)
	}
}

func (s *ChatService) getAskResponseStream(
	ctx context.Context,
	opt service.AskOption,
	userMessage *chatentity.Message,
	history *chatentity.ConversationWithMessages,
	channel *stream.SendChannel[*service.AskEvent],
	app *appentity.App,
) error {
	defer func() {
		if e := recover(); e != nil {
			log.V1.CtxError(ctx, "panic occurred while get ask stream response: %v, %s",
				e, string(debug.Stack()))
		}
	}()

	channel.DataChannel <- &service.AskEvent{Message: userMessage}
	if len(history.Messages) == 1 {
		go func() {
			if err := s.generateChatSummary(ctx, history.Conversation, history.Messages[0], app.Model.Model); err != nil {
				log.V1.CtxWarn(ctx, "failed to generate conversation title: %v", err)
			}
		}()
	}

	// 判断是否为 wiki_chat 功能调用
	isWikiChat := false
	if opt.Command != nil && (strings.Contains(*opt.Command, "/wiki_chat") || strings.Contains(*opt.Command, "/agentic_wiki_chat")) {
		isWikiChat = true
		log.V1.CtxInfo(ctx, "Detected wiki_chat function, will store event snapshot")
	}

	// 用于收集事件的切片
	var eventCollection []map[string]interface{}
	if isWikiChat {
		eventCollection = make([]map[string]interface{}, 0, 20)

		// 仅记录用户消息的role和content
		eventMap := make(map[string]interface{})
		eventMap["type"] = "message"
		eventMap["data"] = map[string]interface{}{
			"id":       userMessage.ID,
			"username": userMessage.Username,
			"role":     userMessage.Role,
			"content":  userMessage.Content,
		}
		eventCollection = append(eventCollection, eventMap)
	}

	responseMessageID := s.IDGenerator.NewID()
	plan, err := s.plan(ctx, agentserivce.PlanOption{
		AppID:        opt.AppID,
		Input:        opt.Input,
		History:      getAgentMessages(history.Messages),
		Context:      opt.Context,
		Functions:    opt.Functions,
		Command:      opt.Command,
		Account:      opt.Account,
		Conversation: history.Conversation,
		Channel:      channel,
		MessageID:    responseMessageID,
		SessionID:    opt.SessionID,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to plan: %v", err)
		channel.DataChannel <- askEventProgress(fmt.Sprintf("Failed to generate plan: %v", err))
	}

	if plan != nil {
		// Add summarized user intent in variable.
		if opt.Context == nil {
			opt.Context = &skill.Context{}
		}
		if opt.Context.Variables == nil {
			opt.Context.Variables = make(map[string]any)
		}
		opt.Context.Variables[skill.IntentContextVariable] = plan.UserIntentSummary
		opt.Context.Variables[skill.UserInputContextVariable] = plan.UserInput
		// Update conversation to agent mode if necessary.
		if plan.PredefinedIntent != nil && plan.PredefinedIntent.Handler.Agent != nil && history.Conversation.State == "" {
			state := appentity.GetConversationAgentStateFromIntent(plan.PredefinedIntent)
			_, err = s.DAO.UpdateConversation(ctx, dal.UpdateConversationOption{
				AppID: app.ID,
				ID:    history.Conversation.ID,
				State: &state,
			})
			if err != nil {
				return errors.WithMessage(err, "failed to update conversation state")
			}
			log.V1.CtxInfo(ctx, "updated conversation %s to state: %s", history.Conversation.ID, state)
		}
	}

	if plan != nil && plan.PredefinedIntent != nil && plan.PredefinedIntent.Handler.Agent != nil {
		return s.getAgentResponseStream(ctx, opt, plan, history, channel, app, responseMessageID)
	}
	span, ctx := bytetrace.StartCustomSpan(ctx, "chat", "Execute")
	defer span.Finish()
	res, err := s.execute(ctx, agentserivce.ExecuteOption{
		AppID:          opt.AppID,
		ConversationID: opt.ConversationID,
		SessionID:      opt.SessionID,
		MessageID:      responseMessageID,
		Input:          opt.Input,
		Plan:           plan,
		History:        getAgentMessages(history.Messages),
		Account:        opt.Account,
		Context:        opt.Context,
		Stream:         opt.Stream,
	})
	if err != nil {
		channel.ErrorChannel <- errors.WithMessage(err, "failed to generate response")
		channel.Close()
		return errors.WithMessage(err, "failed to execute")
	}

	var prompt = "LogID: " + ctxvalues.LogIDDefault(ctx) + "\n"
	if plan != nil {
		prompt += "***Plan Prompt***\n" + plan.Prompt + "\n***Execute Prompt***\n" + res.Prompt
	} else {
		prompt += "***Execute Prompt***\n" + res.Prompt
	}

	var functionCall *chatentity.MessageFunctionCall
	var variables map[string]any
	if opt.Context != nil {
		variables = opt.Context.Variables
	}
	if res.FunctionCall != nil {
		functionCall = &chatentity.MessageFunctionCall{
			Type:             res.FunctionCall.Type,
			Rationale:        res.FunctionCall.Rationale,
			FunctionID:       res.FunctionCall.FunctionID,
			Parameters:       conv.JSONString(res.FunctionCall.Inputs),
			Response:         conv.JSONString(res.FunctionCall.Outputs),
			Intent:           res.FunctionCall.Intent,
			ContextVariables: variables,
		}
	}

	responseMessage := &chatentity.Message{
		ID:             responseMessageID,
		ConversationID: opt.ConversationID,
		SessionID:      opt.SessionID,
		Username:       chatentity.DefaultCopilotUsername,
		Role:           chatentity.RoleTypeCopilot,
		Type:           chatentity.MessageTypeMessage,
		Prompt:         &prompt,
		FunctionCall:   functionCall,
		Content:        "",
		CreatedAt:      time.Now(),
	}

	channel.DataChannel <- &service.AskEvent{Message: responseMessage}
	createMsgOpt := dal.CreateMessageOption{
		ID:                responseMessage.ID,
		ConversationID:    responseMessage.ConversationID,
		SessionID:         responseMessage.SessionID,
		Username:          responseMessage.Username,
		Role:              responseMessage.Role,
		Type:              responseMessage.Type,
		Prompt:            responseMessage.Prompt,
		SkillFunctionCall: responseMessage.FunctionCall,
	}
	msgContent := strings.Builder{}
	defer func() {
		if msgContent.Len() == 0 {
			log.V1.CtxWarn(ctx, "empty message content, not to save")
			msgContent.WriteString(failedMessageNotice)
		}
		createMsgOpt.Content = msgContent.String()
		_, err = s.DAO.CreateMessages(ctx, createMsgOpt)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to create message record: %v", err)
			return
		}
		log.V1.CtxInfo(ctx, "created message record: %s", responseMessage.ID)

		// 如果是wiki_chat，创建快照消息
		if isWikiChat && len(eventCollection) > 0 {
			s.createSnapshotMessage(ctx, eventCollection, opt)
		}

		if opt.CompleteChan != nil {
			close(opt.CompleteChan)
		}
		_, err = s.LogByMessages(ctx, opt.AppID, opt.Account.Username, createMsgOpt)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to log by messages: %v", err)
		}
	}()

	return stream.Forward(ctx, res.ResultChannel, channel, func(event *service.AskEvent) *service.AskEvent {
		// message whole content
		if event.MessageChunk != nil {
			msgContent.WriteString(*event.MessageChunk)
		}

		// Event stream recording snapshot
		if isWikiChat {
			eventType, eventData := collectEventData(event)
			if eventType != "" && eventData != nil {
				eventMap := make(map[string]interface{})
				eventMap["type"] = eventType

				// for message_chunk type, restore with id and conversation id
				if eventType == "message_chunk" {
					messageChunkData := service.MessageChunkData{
						Content:        eventData.(string),
						MessageID:      responseMessage.ID,
						ConversationID: opt.ConversationID,
					}
					eventMap["data"] = messageChunkData
				} else if eventType == "file_contents" {
					// 正确处理FileContents类型
					eventData := eventData.(*chatentity.FileContents)
					fileContentsData := &chatentity.FileContents{ // 使用指针类型
						RepoName: eventData.RepoName,
						FilePath: eventData.FilePath,
						Content:  "", // 保持为空字符串
						CommitID: eventData.CommitID,
					}
					eventMap["data"] = fileContentsData // 存储指针而不是值
				} else if eventType == "done" {
					eventMap["data"] = map[string]interface{}{}
				} else {
					eventMap[eventType] = eventData
				}

				eventCollection = append(eventCollection, eventMap)
			}
		}

		return event
	})
}

func (s *ChatService) getAskResponse(
	ctx context.Context,
	opt service.AskOption,
	history *chatentity.ConversationWithMessages,
	app *appentity.App,
) (*chatentity.Message, error) {
	defer func() {
		if opt.CompleteChan != nil {
			close(opt.CompleteChan)
		}
	}()
	// Generate conversation title automatically, if this is the first message sent from user.
	if len(history.Messages) == 1 {
		go func() {
			if err := s.generateChatSummary(ctx, history.Conversation, history.Messages[0], app.Model.Model); err != nil {
				log.V1.CtxWarn(ctx, "failed to generate conversation title: %v", err)
			}
		}()
	}

	plan, err := s.plan(ctx, agentserivce.PlanOption{
		AppID:        opt.AppID,
		Input:        opt.Input,
		History:      getAgentMessages(history.Messages),
		Context:      opt.Context,
		Functions:    opt.Functions,
		Command:      opt.Command,
		Account:      opt.Account,
		Conversation: history.Conversation,
		SessionID:    opt.SessionID,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to plan: %v", err)
	}

	if plan != nil {
		// Add summarized user intent in variable.
		if opt.Context == nil {
			opt.Context = &skill.Context{}
		}
		if opt.Context.Variables == nil {
			opt.Context.Variables = make(map[string]any)
		}
		opt.Context.Variables["intent"] = plan.UserIntentSummary
		opt.Context.Variables["user_input"] = plan.UserInput
		// Update conversation to agent mode if necessary.
		if plan.PredefinedIntent != nil && plan.PredefinedIntent.Handler.Agent != nil && history.Conversation.State == "" {
			state := appentity.GetConversationAgentStateFromIntent(plan.PredefinedIntent)
			_, err = s.DAO.UpdateConversation(ctx, dal.UpdateConversationOption{
				AppID: app.ID,
				ID:    history.Conversation.ID,
				State: &state,
			})
			if err != nil {
				return nil, errors.WithMessage(err, "failed to update conversation state")
			}
			log.V1.CtxInfo(ctx, "updated conversation %s to state: %s", history.Conversation.ID, state)
		}
	}
	messageID := s.IDGenerator.NewID()
	if plan != nil && plan.PredefinedIntent != nil && plan.PredefinedIntent.Handler.Agent != nil {
		return s.getAgentResponse(ctx, opt, plan, history, app, messageID)
	}

	res, err := s.execute(ctx, agentserivce.ExecuteOption{
		AppID:          opt.AppID,
		ConversationID: opt.ConversationID,
		SessionID:      opt.SessionID,
		MessageID:      messageID,
		Input:          opt.Input,
		Plan:           plan,
		History:        getAgentMessages(history.Messages),
		Account:        opt.Account,
		Context:        opt.Context,
		Stream:         opt.Stream,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to execute plan")
	}

	var prompt = "LogID: " + ctxvalues.LogIDDefault(ctx) + "\n"
	if plan != nil {
		prompt += "***Plan Prompt***\n" + plan.Prompt + "\n***Execute Prompt***\n" + res.Prompt
	} else {
		prompt += "***Execute Prompt***\n" + res.Prompt
	}

	var functionCall *chatentity.MessageFunctionCall
	var variables map[string]any
	if opt.Context != nil {
		variables = opt.Context.Variables
	}
	if res.FunctionCall != nil {
		functionCall = &chatentity.MessageFunctionCall{
			Type:             res.FunctionCall.Type,
			Rationale:        res.FunctionCall.Rationale,
			FunctionID:       res.FunctionCall.FunctionID,
			Parameters:       conv.JSONString(res.FunctionCall.Inputs),
			Response:         conv.JSONString(res.FunctionCall.Outputs),
			Intent:           res.FunctionCall.Intent,
			ContextVariables: variables,
		}
	}

	createMsgOpt := dal.CreateMessageOption{
		ID:                messageID,
		ConversationID:    opt.ConversationID,
		SessionID:         opt.SessionID,
		Username:          chatentity.DefaultCopilotUsername,
		Role:              chatentity.RoleTypeCopilot,
		Type:              chatentity.MessageTypeMessage,
		Prompt:            &prompt,
		Content:           res.Result,
		SkillFunctionCall: functionCall,
	}

	// If not in stream mode, return result directly.
	msg, err := s.DAO.CreateMessages(ctx, createMsgOpt)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create copilot message record")
	}
	if opt.CompleteChan != nil {
		close(opt.CompleteChan)
	}
	_, err = s.LogByMessages(ctx, opt.AppID, opt.Account.Username, createMsgOpt)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to log by messages: %v", err)
	}

	return msg[0], nil
}

func (s *ChatService) Ask(ctx context.Context, opt service.AskOption) (*service.AskResult, error) {
	app, err := s.AppService.GetApp(ctx, opt.AppID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get app")
	}

	// revise cmd from expressions(to display for user) -> real handler function
	if opt.Command != nil && *opt.Command != "" {
		// Handle metadata name to handler ID mapping first
		for _, h := range app.Handlers {
			if h.Metadata == nil {
				continue
			}
			if h.Metadata.Name != nil && "/"+*h.Metadata.Name == *opt.Command {
				opt.Command = lo.ToPtr("/" + h.ID)
				log.V1.CtxInfo(ctx, "replace ask cmd to builtin: %s", opt.Command)
				break
			}
		}

		if *opt.Command == "/wiki_chat" {
			log.V1.CtxInfo(ctx, "replace ask cmd to agentic_wiki_chat")
			opt.Command = lo.ToPtr("/agentic_wiki_chat")
			// won't replace input with command for wiki chat, re-assign input after merge with conversation context
		} else {
			// try best to avoid empty input considering some model doesn't allow an empty input.
			if opt.Input == "" {
				opt.Input = lo.FromPtr(opt.Command)
			}
		}
	}

	// Replace explain_code_v2 with agentic_explain_code for ask interface (advanced mode)
	if opt.Command != nil && *opt.Command == "/explain_code_v2" {
		log.V1.CtxInfo(ctx, "replace ask cmd to agentic_explain_code")
		opt.Command = lo.ToPtr("/agentic_explain_code")
	}

	const (
		customIntentPrefix = "/custom_intent"
		intentIDKey        = "intent_id"
		AppIDKey           = "app_id"
	)

	if opt.Context != nil && opt.Context.Variables != nil {
		opt.Context.Variables[AppIDKey] = opt.AppID
	}

	// Get the message history.
	history, err := s.GetConversation(ctx, service.GetConversationOption{
		AppID: opt.AppID,
		ID:    opt.ConversationID,
		Limit: -1,
		// only need message for history, exclude other types: event_stream etc.
		Type: lo.ToPtr("message"),
	})
	if err != nil {
		return nil, err
	}

	// merge the conversation context with the request context
	opt.Context = s.MergeWithConversationContext(ctx, opt.Context, history.Conversation.Context)
	// reassign input with variables' input k-v
	if opt.Context != nil && opt.Context.Variables != nil {
		// check if input is in variables
		if inputValue, exist := opt.Context.Variables["input"]; exist {
			if inputStr, ok := inputValue.(string); ok {
				opt.Input = inputStr
				log.V1.CtxInfo(ctx, "reassign input with merged variables' input k-v: %s", opt.Input)
			} else {
				log.V1.CtxWarn(ctx, "input variable exists but is not a string type: %T", inputValue)
			}
		}
	}

	// remove once-used(customized field) variables from conversation context
	err = s.RemoveConversationCtx(ctx, opt.AppID, opt.ConversationID, history.Conversation.Context)
	if err != nil {
		log.V1.CtxError(ctx, "failed to remove conversation context: %v", err)
	}
	// log before and after merging
	log.V1.CtxInfo(ctx, "before merging, request context: %v, conversation context: %v", opt.Context, history.Conversation.Context)
	log.V1.CtxInfo(ctx, "after merging, request context: %v, conversation context: %v", opt.Context, history.Conversation.Context)

	// Purpose: Similar as in invoke.
	// FIXME(John): Also need to replace function's base prompt after it is editable at user-end(future).
	if opt.Command != nil && strings.HasPrefix(*opt.Command, customIntentPrefix) {
		customIntentID := uint64(0) // invalid by default
		log.V1.CtxInfo(ctx, "detect a customized intent request.")
		if opt.Context == nil || opt.Context.Variables == nil {
			return nil, errors.New("no context is given")
		}

		if _, exist := opt.Context.Variables[intentIDKey]; exist {
			val, _ := opt.Context.Variables[intentIDKey].(json.Number)
			intValue, err := val.Int64()
			if err != nil {
				log.V1.CtxError(ctx, "failed to get intent_id from context variables:%v", err)
				return nil, err
			}
			customIntentID = uint64(intValue)
		}

		if customIntentID != 0 {
			// valid customIntentID
			customIntent, err := s.IntentService.GetIntent(ctx, codeaiservice.GetIntentOption{
				AppID: opt.AppID,
				ID:    customIntentID,
			})
			if err != nil {
				log.V1.CtxError(ctx, "Cannot resolve custom intent entity with passing intent_id: %v", err)
				return nil, errors.WithMessage(err, "Unknown custom intent")
			}
			if customIntent == nil {
				return nil, errors.New("Get nil custom intent")
			}

			builtinIntent, err := s.IntentService.GetIntent(ctx, codeaiservice.GetIntentOption{
				AppID: opt.AppID,
				ID:    customIntent.IntentBase.OriginIntentId,
			})
			if err != nil {
				log.V1.CtxError(ctx, "Cannot resolve builtin intent according to custom intent_id: %v", err)
				return nil, errors.WithMessage(err, "Custom intent with unknown builtin intent")
			}

			// Mapping prompt pipeline with correct builtin function id.
			if builtinIntent != nil && builtinIntent.IntentBase != nil {
				for _, h := range app.Handlers {
					if h.Metadata == nil || h.Metadata.IntentID == nil {
						// not each handler has Metadata configured.
						continue
					}
					if *h.Metadata.IntentID == builtinIntent.IntentBase.ID {
						opt.Command = lo.ToPtr("/" + h.ID)
						log.V1.CtxInfo(ctx, "replace ask cmd to builtin: %s", opt.Command)
						break
					}
				}
			}
		}
	}

	// sessionID record messages of one user-interact with this function - usually two messages.
	sessionID := s.IDGenerator.NewID()
	opt.SessionID = sessionID
	askRes := new(service.AskResult)
	// Save the user's message first.

	createMsgOpt := dal.CreateMessageOption{
		ID:             s.IDGenerator.NewID(),
		ConversationID: opt.ConversationID,
		SessionID:      opt.SessionID,
		Username:       opt.Account.Username,
		Role:           chatentity.RoleTypeUser,
		Type:           chatentity.MessageTypeMessage,
		Prompt:         opt.RawInput, // Temporarily stored in the message prompt field.
		Content:        opt.Input,
	}
	userMessage, err := s.DAO.CreateMessages(ctx, createMsgOpt)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create user message record")
	}

	_, err = s.LogByMessages(ctx, opt.AppID, opt.Account.Username, createMsgOpt)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to log by messages: %v", err)
	}

	askRes.InputMessage = userMessage[0]

	// Limit max history messages passed to AI.
	if len(history.Messages) > defaultContextMaxHistoryMessages {
		history.Messages = history.Messages[:defaultContextMaxHistoryMessages]
	}

	if opt.Stream {
		send, recv := stream.NewChannel[*service.AskEvent](10)
		askRes.StreamChannel = recv

		go func() {
			if err := s.getAskResponseStream(ctx, opt, askRes.InputMessage, history, send, app); err != nil {
				log.V1.CtxWarn(ctx, "failed to get ask response in stream mode: %v", err)
			}
		}()

		return askRes, nil
	}
	askRes.ResponseMessage, err = s.getAskResponse(ctx, opt, history, app)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get ask response")
	}

	return askRes, nil
}

func (s *ChatService) Invoke(ctx context.Context, opt service.InvokeOption) (*service.InvokeResult, error) {
	var (
		sender, receiver = stream.NewChannel[*service.InvokeEvent](10)
		functionID       = opt.FunctionID
		customIntentID   = uint64(0) // invalid by default
	)
	const (
		CustomIntentPrefix = "custom_intent"
		IntentIDKey        = "intent_id"
		AppIDKey           = "app_id"
		FunctionIDKey      = "function_id"
	)

	locale := entity.LocaleChinese
	if opt.Context != nil {
		locale = entity.Locale(conv.DefaultAny[string](opt.Context.Variables[skill.LocaleContextVariable]))
	}

	if opt.Account.Locale != "" {
		log.V1.CtxInfo(ctx, "enhanced local prediction, locale = %s", opt.Account.Locale)
		locale = entity.Locale(opt.Account.Locale)
		opt.Context.Variables[skill.LocaleContextVariable] = opt.Account.Locale
	}

	app, err := s.AppService.GetApp(ctx, opt.AppID)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get app '%s'", opt.AppID)
	}

	if opt.Context != nil && opt.Context.Variables != nil {
		opt.Context.Variables[AppIDKey] = opt.AppID
		// save as context to diverse code processing logic in codebase context.
		opt.Context.Variables[FunctionIDKey] = functionID
	}
	// Purpose: Resolve customized-intent & builtin-function/intent with given "customized" intent_id.
	// after this logic, we always pass builtin-function-id to reuse configured resolvers(which is not part of customizing).
	// Check both parameters and variables of context in-case client-end changes passing logic.
	// FIXME(John): Also need to replace function's base prompt after it is editable at user-end(future).
	if strings.HasPrefix(opt.FunctionID, CustomIntentPrefix) {
		log.V1.CtxInfo(ctx, "detect a customized intent request.")
		if opt.Parameters != nil {
			if _, exist := opt.Parameters[IntentIDKey]; exist {
				val, _ := opt.Parameters[IntentIDKey].(json.Number)
				intValue, err := val.Int64()
				if err != nil {
					log.V1.CtxError(ctx, "failed to get intent_id from params:%v", err)
					return nil, err
				}
				customIntentID = uint64(intValue)
				if opt.Context != nil && opt.Context.Variables != nil && customIntentID != 0 {
					// used as query params
					opt.Context.Variables[IntentIDKey] = customIntentID
				}
			}
		}
		if customIntentID == 0 {
			if _, exist := opt.Context.Variables[IntentIDKey]; exist {
				val, _ := opt.Context.Variables[IntentIDKey].(json.Number)
				intValue, err := val.Int64()
				if err != nil {
					log.V1.CtxError(ctx, "failed to get intent_id from context variables:%v", err)
					return nil, err
				}
				customIntentID = uint64(intValue)
			}
		}

		log.V1.CtxInfo(ctx, "parsed IntentID is %d", customIntentID)
		if customIntentID != 0 {
			// valid customIntentID
			customIntent, err := s.IntentService.GetIntent(ctx, codeaiservice.GetIntentOption{
				AppID: opt.AppID,
				ID:    customIntentID,
			})
			if err != nil {
				log.V1.CtxError(ctx, "Cannot resolve custom intent entity with passing intent_id: %v", err)
				return nil, errors.WithMessage(err, "Unknown custom intent")
			}
			if customIntent == nil {
				return nil, errors.New("Get nil custom intent")
			}
			log.V1.CtxInfo(ctx, "detect a valid customized intent.")

			builtinIntent, err := s.IntentService.GetIntent(ctx, codeaiservice.GetIntentOption{
				AppID: opt.AppID,
				ID:    customIntent.IntentBase.OriginIntentId,
			})
			if err != nil {
				log.V1.CtxError(ctx, "Cannot resolve builtin intent according to custom intent_id: %v", err)
				return nil, errors.WithMessage(err, "Custom intent with unknown builtin intent")
			}

			// Mapping prompt pipeline with correct builtin function id.
			if builtinIntent != nil && builtinIntent.IntentBase != nil {
				for _, h := range app.Handlers {
					if h.Metadata == nil || h.Metadata.IntentID == nil {
						// not each handler has Metadata configured.
						continue
					}
					if *h.Metadata.IntentID == builtinIntent.IntentBase.ID {
						functionID = h.ID
						log.V1.CtxInfo(ctx, "replace function id to builtin: %s", functionID)
						break
					}
				}
			}
		} else {
			log.V1.CtxWarn(ctx, "Seems a customized intent case but not successfully detect intent_id, continue")
		}
	}

	function, err := s.SkillService.GetFunctionInvoker(ctx, opt.AppID, functionID, locale, opt.Context)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get function invoker")
	}

	// FIXME(zk): frontend currently does not support progress event.
	// sender.DataChannel <- &service.InvokeEvent{
	//	Progress: lo.ToPtr("Resolving context..."),
	// }
	c, err := s.SkillService.ResolveContext(ctx, opt.Account, opt.AppID, functionID, opt.Context, false)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to resolve context")
	}

	if opt.Parameters == nil {
		opt.Parameters = make(map[string]any)
	}
	opt.Parameters["function_id"] = functionID

	if opt.SessionID == "" {
		// sessionID record messages of one user-interact with this function - usually two messages.
		opt.SessionID = s.IDGenerator.NewID()
	}

	// Save the user's message first.
	var variables map[string]any
	if opt.Context != nil {
		variables = opt.Context.Variables
	}
	functionCall := &chatentity.MessageFunctionCall{
		Type:             "code",
		Rationale:        "Predefined intent, resolving with the corresponding function",
		FunctionID:       functionID,
		Parameters:       conv.JSONString(opt.Parameters),
		Response:         "",
		Intent:           functionID, // invoke function represents intent.
		ContextVariables: variables,
	}
	createMsgOpt := dal.CreateMessageOption{
		ID:                s.IDGenerator.NewID(),
		ConversationID:    "", // invoke message and corresponding response is isolated and a "conversation" by themself.
		SessionID:         opt.SessionID,
		Username:          opt.Account.Username,
		Role:              chatentity.RoleTypeUser,
		Type:              chatentity.MessageTypeMessage,
		Prompt:            nil,
		Content:           opt.FunctionID, // function id of invoke represents user's input.
		SkillFunctionCall: functionCall,
	}

	// create a solo message, and not care about user message returned - we won't put it in response message prompt.
	_, err = s.DAO.CreateMessages(ctx, createMsgOpt)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create user message record")
	}

	var intentName string
	// invoke requires a valid function handler, find in handler if no intent matched.
	intent, ok := lo.Find(app.Intents, func(item appentity.Intent) bool {
		return item.Handler.ID == functionID
	})
	if !ok {
		// not found in app.Intent, check app.Handler.
		for _, h := range app.Handlers {
			if functionID == h.ID {
				if h.Metadata != nil && h.Metadata.Name != nil {
					intentName = *h.Metadata.Name
				} else {
					intentName = h.ID
				}
				break
			}
		}
		if intentName == "" {
			return nil, errors.Errorf("failed to find intent '%s' for app '%s'", functionID, opt.AppID)
		}
	} else {
		intentName = intent.Intent
	}
	if !opt.Stream {
		outputs, err := function.Call(ctx, &skillservice.FunctionInvokeOption{
			AppID:     opt.AppID,
			SessionID: opt.SessionID,
			Account:   opt.Account,
			Context:   c,
			Msgs:      nil,
			Input:     opt.Parameters,
			Intent:    intentName,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to call function")
		}

		return &service.InvokeResult{
			Outputs: outputs,
		}, nil
	}

	res, err := function.CallStream(ctx, &skillservice.FunctionInvokeOption{
		AppID:     opt.AppID,
		Account:   opt.Account,
		SessionID: opt.SessionID,
		Context:   c,
		Msgs:      nil,
		Input:     opt.Parameters,
		Intent:    intentName,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call function")
	}

	go stream.Forward(ctx, res, sender, func(item skillservice.FunctionOutput) *service.InvokeEvent {
		if prompt, ok := item["prompts"]; ok {
			return &service.InvokeEvent{
				Prompts: lo.ToPtr(prompt.(string)),
			}
		}
		if ranges, ok := item["ranges"]; ok {
			return &service.InvokeEvent{
				Ranges: lo.ToPtr(ranges.(string)),
			}
		}
		return &service.InvokeEvent{
			ResultChunk: item,
		}
	})

	return &service.InvokeResult{
		StreamChannel: receiver,
	}, nil
}

func (s *ChatService) UpdateConversation(ctx context.Context, opt service.UpdateConversationOption) (*chatentity.Conversation, error) {
	c, err := s.DAO.UpdateConversation(ctx, dal.UpdateConversationOption{
		AppID:   opt.AppID,
		ID:      opt.ConversationID,
		Title:   opt.Title,
		Context: opt.Context,
		State:   opt.State,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update conversation")
	}

	return c, nil
}

func adjustPrompts(ctx context.Context, opt service.ChatOption) ([]*llmstackgen.Message, int32, error) {
	var (
		model = lo.FromPtr(opt.Model)
	)
	// 计算每一部分的 token 数量
	start := time.Now()
	if *opt.Model == "seed_25b_stream" {
		adjustSeedMessages(ctx, &opt)
	}
	var (
		intentPrompts = lo.Map(opt.Context.IntentPrompts, func(item skill.IntentPrompt, index int) tokenData[skill.IntentPrompt] {
			return calcToken(model, item, item.ResolvedContext)
		})
		msgs = lo.Map(opt.Messages, func(item chatentity.SimpleMessage, index int) tokenData[chatentity.SimpleMessage] {
			return calcToken(model, item, item.Content)
		})
		input = calcToken(model, opt.Input, opt.Input)
	)
	log.V1.CtxInfo(ctx, "initial counting tokens cost %dms", time.Since(start).Milliseconds())

	countTokens := func() int {
		var tokens int
		for idx := range intentPrompts {
			tokens += intentPrompts[idx].Tokens + 1 // Role 占 1 token
		}
		for idx := range msgs {
			tokens += msgs[idx].Tokens + 1 // Role 占 1 token
		}
		tokens += input.Tokens + 1 // Role 占 1 token
		return tokens + 3          // 最后给模型回复时还要加上 3 token
	}

	tokenLimit, ok := inputTokenLimit[model]
	log.V1.CtxInfo(ctx, "model %s token limit %d, tokens: %d", model, tokenLimit, countTokens())
	// 没有的话，不做限制
	if !ok {
		log.V1.CtxInfo(ctx, "no token limit or limit is meet for model %s, skip adjusting",
			lo.FromPtr(opt.Model))
		return composeMessages(ctx, getDataSlice(msgs), getDataSlice(intentPrompts), input.Data, opt), int32(countTokens()), nil
	}

	// 根据 score 从低到高排序（相同的时候长的在前面），先删 score 低的
	// 假设传进来的是按重要性从高到低排序的，这里先反过来，方便从前面删
	intentPrompts = lo.Reverse(intentPrompts)
	var (
		tokens = countTokens()
		idx    int
	)
	// 超过 token 限制，开始裁剪
	for tokens > tokenLimit && idx < 20 { // 防止死循环，理论上不会出现
		idx++
		tokens = countTokens()
		// 先裁 chat history
		var removed bool

		msgs, removed = removeFirstTurn(msgs)
		// 裁了
		if removed {
			continue
		}

		// 再裁 intent prompt
		intentPrompts, removed = removeIntentPrompts(ctx, intentPrompts, model, tokens-tokenLimit)
		if removed {
			continue
		}

		// 裁下 input
		charDiff := int(float32(tokens-tokenLimit) * float32(tokenizer.CharsPerToken))
		if charDiff > 0 && len(input.Data) > charDiff {
			log.V1.CtxInfo(ctx, "shorten input %d => %d", len(input.Data), charDiff)
			input.Data = input.Data[:len(input.Data)-charDiff]
			continue
		}

		// 都裁不动了，只能退出
		break
	}

	tokens = countTokens()
	log.V1.CtxInfo(ctx, "done adjusting prompt, tokens: %d", tokens)

	return composeMessages(ctx, getDataSlice(msgs), lo.Reverse(getDataSlice(intentPrompts)), input.Data, opt), int32(tokens), nil
}

const (
	// IDEAppID 是 AI IDE 的 app id，方便特殊处理。。
	IDEAppID       = "b070098d-978d-4274-ae6b-b76da2570112"
	IDEIntentUTGen = "generate_unittest"
)

func (s *ChatService) ChatCompletion(ctx context.Context, opt service.ChatCompletionOption) (*stream.Result[service.ChatResult], error) {
	// 1. check if valid APPID
	app, err := s.AppService.GetApp(ctx, opt.AppID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get app")
	}

	if opt.Model == nil || len(*opt.Model) == 0 {
		opt.Model = &app.Model.Model
	}

	// 2. Choose the specific model name by model alias name.
	opt.Model = lo.ToPtr(getChatModelFromAlias(*opt.Model))
	log.V1.CtxInfo(ctx, "chat using model %s", *opt.Model)

	msgs := make([]*llmstackgen.Message, 0)
	for _, msg := range opt.Messages {
		msgs = append(msgs, &llmstackgen.Message{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	prompt := lo.ToPtr(llmstack.GetMessagesPromptString(msgs))
	// Create log.
	llmLog, err := s.JournalService.CreateLog(ctx, journalservice.CreateLogOption{
		Username:  opt.Account.Username,
		ModelName: *opt.Model,
		Prompt:    prompt,                    // join all messages together
		Status:    journalservice.StatusFail, // Set to failed first.
		AppId:     lo.ToPtr(app.ID),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create LLM log")
	}
	token, _ := tokenizer.CountModelToken(*prompt, *opt.Model)
	token = token + 3 // add extra 3 tokens for each message.

	var (
		requestMetadata = &journalservice.PromptCompletionRequestMetadata{
			Stream: lo.ToPtr(opt.Stream),
			Usage: &journalservice.Usage{
				PromptTokens:     token,
				CompletionTokens: 0,
				TotalTokens:      token,
			},
			LogID:      nil,
			FunctionID: &app.Name,
		}
		updateLog = journalservice.UpdateLogOption{
			ID:              llmLog.ID,
			Status:          nil,
			RequestMetadata: requestMetadata,
		}
		requestModelStart = time.Now()
	)
	updateLogFunc := func() {
		updateLog.RequestMetadata.Latency = lo.ToPtr(time.Since(requestModelStart))
		if err != nil {
			updateLog.RequestMetadata.Error = lo.ToPtr(err.Error())
			updateLog.Status = lo.ToPtr(journalservice.StatusFail)
		} else {
			updateLog.Status = lo.ToPtr(journalservice.StatusSuccess)
		}
		// Not to update if it is stream call here, because it will be handled in the new goroutine.
		if err == nil && updateLog.ContentRaw == nil {
			return
		}
		if _, logErr := s.JournalService.UpdateLog(ctx, updateLog); logErr != nil {
			log.V1.CtxWarn(ctx, "failed to update LLM log: %v", logErr)
		}
	}

	defer updateLogFunc()

	if strings.HasPrefix(*opt.Model, "seed") || strings.HasPrefix(*opt.Model, "skylark") {
		msgs = completeMessageRoles(msgs, *opt.Model)
	}

	span, ct := bytetrace.StartCustomSpan(ctx, "chatCompletion", fmt.Sprintf("Chat:%s", *opt.Model))
	defer span.Finish()

	chatRequest := llmstackgen.ChatRequest{
		Model:           *opt.Model,
		Messages:        msgs,
		Temperature:     lo.ToPtr(0.1),
		Stream:          &opt.Stream,
		RetryTimes:      lo.ToPtr(int32(2)),
		DisableAntidirt: opt.DisableAntiDirt,
		// TODO(cyx): increase `max_tokens` if needed.
	}

	var (
		res *llmstackgen.ChatResult
		ch  *stream.RecvChannel[*llmstackgen.ChatChunk]
	)

	start := time.Now()
	if len(opt.Functions) > 0 && opt.FunctionCall != nil {
		var functionCall any
		functionCall = chatentity.DefaultFunctionCall
		// if it's a direct function call, not "auto"
		if *opt.FunctionCall != chatentity.DefaultFunctionCall {
			functionCall = lo.ToPtr(service.FunctionName{Name: *opt.FunctionCall})
		}

		res, ch, err = s.SDK.ChatWithFunctionCall(ctx, llmstack.ChatCompletionRequest{
			ChatRequest:  chatRequest,
			FunctionCall: functionCall,
			Functions: lo.Map(opt.Functions, func(item service.FunctionDefinition, index int) *llmstack.Function {
				return &llmstack.Function{
					Name:        item.Name,
					Description: item.Description,
					Parameters:  json.RawMessage(item.Parameters),
				}
			}),
		}, llmstack.WithByteCloudServiceJWT())
	} else {
		res, ch, err = s.SDK.Chat(ctx, chatRequest, llmstack.WithByteCloudServiceJWT())
	}

	if err != nil {
		bytetrace.AddEvents(ct, bytetrace.NewErrorLogEvent("failed to chat "+err.Error()))
		if llmstack.IsMatchAntiDirt(err) {
			updateLog.ContentRaw = lo.ToPtr(defaultAntiDirtResponse)
			fakeRes := &stream.Result[service.ChatResult]{
				Stream: nil,
				Result: nil,
			}
			fakeMsg := service.ChatResult{
				Message: &chatentity.SimpleMessage{
					Role:    "assistant",
					Content: defaultAntiDirtResponse,
				},
				FinishReason: lo.ToPtr("sensitive_content"),
				Prompt:       prompt,
			}
			if !opt.Stream {
				fakeRes.Result = &fakeMsg
			} else {
				fakeRes.Stream = stream.SliceToChannel[service.ChatResult](10, []any{
					fakeMsg,
				})
			}
			return fakeRes, nil
		}
		return nil, apierror.ErrModelReqFail.WithMessage(err.Error())
	}

	var result = new(stream.Result[service.ChatResult])

	if !opt.Stream {
		updateLog.RequestMetadata.ID = &res.Id
		var usage *chatentity.TokenUsage
		if res.Usage != nil && res.Usage.TotalTokens > 0 {
			updateLog.RequestMetadata.Usage = &journalservice.Usage{
				PromptTokens:     int(res.Usage.PromptTokens),
				CompletionTokens: int(res.Usage.CompletionTokens),
				TotalTokens:      int(res.Usage.TotalTokens),
			}
			usage = &chatentity.TokenUsage{
				PromptTokens:     res.Usage.PromptTokens,
				CompletionTokens: res.Usage.CompletionTokens,
				TotalTokens:      res.Usage.TotalTokens,
			}
			log.V1.CtxInfo(ctx, "token usage, prompt: %d, completion: %d, total: %d",
				res.Usage.PromptTokens, res.Usage.CompletionTokens, res.Usage.TotalTokens)
		}
		var choice *llmstackgen.ChatChoice
		if len(res.Choices) == 0 || res.Choices[0].Message == nil {
			return nil, apierror.ErrModelReqFail.WithMessage("no available choice returned from LLM")
		}
		choice = res.Choices[0]
		result.Result = &service.ChatResult{
			Message: &chatentity.SimpleMessage{
				Role:    "assistant",
				Content: choice.Message.Content,
			},
			Prompt:             prompt,
			PromptCompletionId: lo.ToPtr(llmLog.ID),
		}

		if choice.Message.FunctionCall != nil {
			result.Result.Message.FunctionCall = &chatentity.FunctionCall{
				Name:      choice.Message.FunctionCall.Name,
				Arguments: choice.Message.FunctionCall.Arguments,
			}
		}

		if len(choice.FinishReason) > 0 {
			result.Result.FinishReason = &choice.FinishReason
			updateLog.RequestMetadata.FinishReason = &choice.FinishReason
		}
		result.Result.Usage = usage
		result.Result.Model = opt.Model
		updateLog.Status = lo.ToPtr(journalservice.StatusSuccess)
		updateLog.ContentRaw = &choice.Message.Content

		return result, nil
	}
	send, recv := stream.NewChannel[service.ChatResult](10)
	result.Stream = recv

	send.DataChannel <- service.ChatResult{
		Prompt: prompt,
		Usage: &chatentity.TokenUsage{
			PromptTokens:     int32(token),
			CompletionTokens: 0,
			TotalTokens:      int32(token),
		},
		PromptCompletionId: lo.ToPtr(llmLog.ID),
		Model:              opt.Model,
	}

	go func() {
		span2, _ := bytetrace.StartCustomSpan(ctx, "chat", fmt.Sprintf("ChatStream:%s", *opt.Model))
		defer func() {
			span2.Finish()
			updateLogFunc()
			if e := recover(); e != nil {
				log.V1.CtxError(ctx, "panic occurred while forward chat stream response: %+v, %s",
					e, string(debug.Stack()))
				send.ErrorChannel <- errors.Errorf("internal error occurred: %+v", e)
				send.Close()
			}
		}()
		response := ""
		isFirstToken := true
		isFirstChunk := true
		err = stream.Forward(ctx, ch, send, func(item *llmstackgen.ChatChunk) service.ChatResult {
			if isFirstToken {
				isFirstToken = false
				log.V1.CtxInfo(ctx, "chat first token latency: %dms", time.Since(start).Milliseconds())
			}
			chunk := service.ChatResult{
				Message:      nil,
				FinishReason: nil,
			}
			if len(item.Id) > 0 {
				updateLog.RequestMetadata.ID = &item.Id
			}
			if item.Usage != nil && item.Usage.CompletionTokens > 0 {
				log.V1.CtxInfo(ctx, "token usage, prompt: %d, completion: %d, total: %d",
					item.Usage.PromptTokens, item.Usage.CompletionTokens, item.Usage.TotalTokens)
				updateLog.RequestMetadata.Usage = &journalservice.Usage{
					PromptTokens:     int(item.Usage.PromptTokens),
					CompletionTokens: int(item.Usage.CompletionTokens),
					TotalTokens:      int(item.Usage.TotalTokens),
				}
				chunk.Usage = &chatentity.TokenUsage{
					PromptTokens:     item.Usage.PromptTokens,
					CompletionTokens: item.Usage.CompletionTokens,
					TotalTokens:      item.Usage.TotalTokens,
				}
			}
			if len(item.Choices) > 0 && item.Choices[0].Delta != nil {
				if len(item.Choices[0].FinishReason) > 0 {
					updateLog.RequestMetadata.FinishReason = &item.Choices[0].FinishReason
					chunk.FinishReason = &item.Choices[0].FinishReason
				}
				chunk.Message = &chatentity.SimpleMessage{
					Role:    "assistant",
					Content: item.Choices[0].Delta.Content,
				}
				// Fix typo code block symbol if it is seed_25b_stream.
				// Only fix the first chunk and the last chunk.
				if *opt.Model == "seed_25b_stream" && (isFirstChunk || chunk.FinishReason != nil) {
					isFirstChunk = false
					chunk.Message.Content = fixSeed25bResponse(ctx, chunk.Message.Content)
				}
				if item.Choices[0].Delta.FunctionCall != nil {
					chunk.Message.FunctionCall = &chatentity.FunctionCall{
						Name:      item.Choices[0].Delta.FunctionCall.Name,
						Arguments: item.Choices[0].Delta.FunctionCall.Arguments,
					}
				}
				response += item.Choices[0].Delta.Content
			} else {
				// Not sure if this is possible.
				log.V1.CtxWarn(ctx, "no available choices in chat chunk, fallback to empty message")
				chunk.Message = &chatentity.SimpleMessage{
					Role:    "assistant",
					Content: "",
				}
			}
			return chunk
		})
		updateLog.ContentRaw = &response

		if err != nil {
			log.V1.CtxWarn(ctx, "failed to forward chat stream response: %v", err)
		}
	}()
	return result, nil
}

func (s *ChatService) Chat(ctx context.Context, opt service.ChatOption) (*stream.Result[service.ChatResult], error) {
	app, err := s.AppService.GetApp(ctx, opt.AppID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get app")
	}

	if opt.ConversationId != nil && len(*opt.ConversationId) != 0 {
		createMsgOpt := dal.CreateMessageOption{
			ID:             s.IDGenerator.NewID(),
			ConversationID: *opt.ConversationId,
			Username:       opt.Account.Username,
			Role:           chatentity.RoleTypeUser,
			Type:           chatentity.MessageTypeMessage,
			Content:        opt.Input,
		}
		_, err := s.DAO.CreateMessages(ctx, createMsgOpt)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create user message record")
		}
	}

	span, ct := bytetrace.StartCustomSpan(ctx, "chat", "ResolveContext")
	start := time.Now()
	// TODO(cyx): 因为太慢，暂时给 IDE 禁用这俩 resolver
	if opt.AppID == IDEAppID {
		intentPrompts := make([]skill.IntentPrompt, 0, len(opt.Context.IntentPrompts))
		for _, r := range opt.Context.IntentPrompts {
			if r.ResolverID == skill.ResolverRelatedSymbolsServer || r.ResolverID == skill.ResolverKeyFilesContentServer {
				log.V1.CtxInfo(ctx, "disabled intent prompt for IDE temporarily: %s", r.ResolverID)
				continue
			}
			intentPrompts = append(intentPrompts, r)
		}
		opt.Context.IntentPrompts = intentPrompts
	}

	opt.Context.Input = opt.Input
	opt.Context, err = s.SkillService.ResolveContext(ctx, opt.Account, opt.AppID, "none", opt.Context, true)
	if err != nil {
		bytetrace.AddEvents(ct, bytetrace.NewErrorLogEvent("failed to resolve context "+err.Error()))
		span.Finish()
		return nil, errors.WithMessage(err, "failed to resolve context")
	}
	span.Finish()
	log.V1.CtxInfo(ctx, "resolving server context cost %dms", time.Since(start).Milliseconds())

	if opt.Model == nil || len(*opt.Model) == 0 {
		opt.Model = &app.Model.Model
	}

	// 针对 IDE Go 语言单测的特殊处理。。因为 Go 单测生成有个特殊模型，不是标准 chat 接口
	// 其他语言正常走 seed.
	if lo.FromPtr(opt.IntentName) == IDEIntentUTGen && lo.FromPtr(opt.Model) == modelIDEInternal {
		res, err := s.genUTWithIESModel(ctx, opt)
		if err != nil {
			return nil, err
		}
		if res != nil {
			return res, nil
		}
		log.V1.CtxInfo(ctx, "fallback to normal chat model ut gen")
	}

	// Choose the specific model name by model alias name.
	opt.Model = lo.ToPtr(getChatModelFromAlias(*opt.Model))
	log.V1.CtxInfo(ctx, "chat using model %s", *opt.Model)

	span, ct = bytetrace.StartCustomSpan(ctx, "chat", "AdjustPrompt")
	start = time.Now()
	msgs, tokens, err := adjustPrompts(ctx, opt)
	if err != nil {
		bytetrace.AddEvents(ct, bytetrace.NewErrorLogEvent("failed to adjust prompt "+err.Error()))
		span.Finish()
		return nil, errors.WithMessage(err, "failed to adjust prompt")
	}
	span.Finish()
	log.V1.CtxInfo(ctx, "adjusting prompt tokens cost %dms", time.Since(start).Milliseconds())

	if strings.HasPrefix(*opt.Model, "seed") || strings.HasPrefix(*opt.Model, "skylark") {
		msgs = completeMessageRoles(msgs, *opt.Model)
	}

	// if model is gpt-4, choose from gpt-4-32k-0613 and gpt-4-0613 according to token size
	if strings.HasPrefix(*opt.Model, "gpt-4") {
		// if token > 6k, use gpt-4-32k-0613
		if int(tokens) > inputTokenLimit["gpt-4-0613"] {
			opt.Model = lo.ToPtr("gpt-4-32k-0613")
		} else {
			opt.Model = lo.ToPtr("gpt-4-0613")
		}
	}

	_ = metrics.CM.ModelCounter.WithTags(&metrics.CopilotModelTag{
		Name:  *opt.Model,
		Type:  "",
		AppID: opt.AppID,
	}).Incr(1)

	_ = metrics.CM.ModelRateCounter.WithTags(&metrics.CopilotModelTag{
		Name:  *opt.Model,
		Type:  "",
		AppID: opt.AppID,
	}).Incr(1)

	prompt := lo.ToPtr(llmstack.GetMessagesPromptString(msgs))
	// Create log.
	llmLog, err := s.JournalService.CreateLog(ctx, journalservice.CreateLogOption{
		Username:  opt.Account.Username,
		ModelName: *opt.Model,
		Prompt:    prompt,                    // join all messages together
		Status:    journalservice.StatusFail, // Set to failed first.
		AppId:     lo.ToPtr(app.ID),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create LLM log")
	}

	var (
		updateLog = journalservice.UpdateLogOption{
			ID:         llmLog.ID,
			ContentRaw: nil,
			Status:     nil,
			RequestMetadata: &journalservice.PromptCompletionRequestMetadata{
				Stream:           nil,
				MaxTokens:        nil,
				Temperature:      nil,
				TopP:             nil,
				TopK:             nil,
				N:                nil,
				FrequencyPenalty: nil,
				PresencePenalty:  nil,
				ID:               nil,
				Usage: &journalservice.Usage{
					PromptTokens:     int(tokens),
					CompletionTokens: 0,
					TotalTokens:      int(tokens),
				},
				Latency:             nil,
				FirstTokenLatency:   nil,
				Error:               nil,
				FinishReason:        nil,
				LogID:               nil,
				ContextVariables:    nil,
				UserInputTokens:     nil,
				IntentName:          nil,
				ProgrammingLanguage: nil,
			},
		}
		requestModelStart = time.Now()
	)

	updateLogFunc := func() {
		updateLog.RequestMetadata.Latency = lo.ToPtr(time.Since(requestModelStart))
		updateLog.RequestMetadata.LogID = lo.ToPtr(ctxvalues.LogIDDefault(ctx))
		if err != nil {
			updateLog.RequestMetadata.Error = lo.ToPtr(err.Error())
			updateLog.Status = lo.ToPtr(journalservice.StatusFail)
		} else {
			updateLog.Status = lo.ToPtr(journalservice.StatusSuccess)
		}
		// Not to update if it is stream call here, because it will be handled in the new goroutine.
		if err == nil && updateLog.ContentRaw == nil {
			return
		}
		if _, logErr := s.JournalService.UpdateLog(ctx, updateLog); logErr != nil {
			log.V1.CtxWarn(ctx, "failed to update LLM log: %v", logErr)
		}
	}

	defer updateLogFunc()

	span, ct = bytetrace.StartCustomSpan(ctx, "chat", fmt.Sprintf("Chat:%s", *opt.Model))
	defer span.Finish()

	chatRequest := llmstackgen.ChatRequest{
		Model:           *opt.Model,
		Messages:        msgs,
		Temperature:     lo.ToPtr(0.1),
		Stream:          &opt.Stream,
		RetryTimes:      lo.ToPtr(int32(2)),
		DisableAntidirt: opt.DisableAntiDirt,
		MaxTokens:       lo.ToPtr(int32(getMaxTokens(int(tokens), *opt.Model))),
	}

	var (
		res *llmstackgen.ChatResult
		ch  *stream.RecvChannel[*llmstackgen.ChatChunk]
	)
	start = time.Now()
	if len(opt.Functions) > 0 && opt.FunctionCall != nil {
		var functionCall any
		functionCall = chatentity.DefaultFunctionCall
		// if it's a direct function call, not "auto"
		if *opt.FunctionCall != chatentity.DefaultFunctionCall {
			functionCall = lo.ToPtr(service.FunctionName{Name: *opt.FunctionCall})
		}

		res, ch, err = s.SDK.ChatWithFunctionCall(ctx, llmstack.ChatCompletionRequest{
			ChatRequest:  chatRequest,
			FunctionCall: functionCall,
			Functions: lo.Map(opt.Functions, func(item service.FunctionDefinition, index int) *llmstack.Function {
				return &llmstack.Function{
					Name:        item.Name,
					Description: item.Description,
					Parameters:  json.RawMessage(item.Parameters),
				}
			}),
		}, llmstack.WithByteCloudServiceJWT())
	} else {
		res, ch, err = s.SDK.Chat(ctx, chatRequest, llmstack.WithByteCloudServiceJWT())
	}

	if err != nil {
		bytetrace.AddEvents(ct, bytetrace.NewErrorLogEvent("failed to chat "+err.Error()))
		if llmstack.IsMatchAntiDirt(err) {
			updateLog.ContentRaw = lo.ToPtr(defaultAntiDirtResponse)
			fakeRes := &stream.Result[service.ChatResult]{
				Stream: nil,
				Result: nil,
			}
			fakeMsg := service.ChatResult{
				Message: &chatentity.SimpleMessage{
					Role:    "assistant",
					Content: defaultAntiDirtResponse,
				},
				FinishReason: lo.ToPtr("sensitive_content"),
				Prompt:       prompt,
			}
			if !opt.Stream {
				fakeRes.Result = &fakeMsg
			} else {
				fakeRes.Stream = stream.SliceToChannel[service.ChatResult](10, []any{
					fakeMsg,
				})
			}
			return fakeRes, nil
		}
		return nil, apierror.ErrModelReqFail.WithMessage(err.Error())
	}

	var result = new(stream.Result[service.ChatResult])

	if !opt.Stream {
		updateLog.RequestMetadata.ID = &res.Id
		var usage *chatentity.TokenUsage
		if res.Usage != nil && res.Usage.TotalTokens > 0 {
			updateLog.RequestMetadata.Usage = &journalservice.Usage{
				PromptTokens:     int(res.Usage.PromptTokens),
				CompletionTokens: int(res.Usage.CompletionTokens),
				TotalTokens:      int(res.Usage.TotalTokens),
			}
			usage = &chatentity.TokenUsage{
				PromptTokens:     res.Usage.PromptTokens,
				CompletionTokens: res.Usage.CompletionTokens,
				TotalTokens:      res.Usage.TotalTokens,
			}
			log.V1.CtxInfo(ctx, "token usage, prompt: %d, completion: %d, total: %d",
				res.Usage.PromptTokens, res.Usage.CompletionTokens, res.Usage.TotalTokens)
		}
		var choice *llmstackgen.ChatChoice
		if len(res.Choices) == 0 || res.Choices[0].Message == nil {
			return nil, apierror.ErrModelReqFail.WithMessage("no available choice returned from LLM")
		}
		choice = res.Choices[0]
		result.Result = &service.ChatResult{
			Message: &chatentity.SimpleMessage{
				Role:    "assistant",
				Content: choice.Message.Content,
			},
			Prompt:             prompt,
			PromptCompletionId: lo.ToPtr(llmLog.ID),
		}

		if choice.Message.FunctionCall != nil {
			result.Result.Message.FunctionCall = &chatentity.FunctionCall{
				Name:      choice.Message.FunctionCall.Name,
				Arguments: choice.Message.FunctionCall.Arguments,
			}
		}

		if len(choice.FinishReason) > 0 {
			result.Result.FinishReason = &choice.FinishReason
			updateLog.RequestMetadata.FinishReason = &choice.FinishReason
		}

		_ = metrics.CM.ChunkThroughput.WithTags(&metrics.CopilotModelTag{
			Name:  *opt.Model,
			Type:  "",
			AppID: opt.AppID,
		}).Incr(1)

		result.Result.Usage = usage
		result.Result.Model = opt.Model
		updateLog.Status = lo.ToPtr(journalservice.StatusSuccess)
		updateLog.ContentRaw = &choice.Message.Content

		if opt.ConversationId != nil && len(*opt.ConversationId) != 0 {
			go s.updateHistory(ctx, opt, *opt.Model, choice.Message.Content, prompt)
		}

		return result, nil
	}

	send, recv := stream.NewChannel[service.ChatResult](10)
	result.Stream = recv

	send.DataChannel <- service.ChatResult{
		Prompt: prompt,
		Usage: &chatentity.TokenUsage{
			PromptTokens:     tokens,
			CompletionTokens: 0,
			TotalTokens:      tokens,
		},
		PromptCompletionId: lo.ToPtr(llmLog.ID),
		Model:              opt.Model,
	}

	go func() {
		span2, _ := bytetrace.StartCustomSpan(ctx, "chat", fmt.Sprintf("ChatStream:%s", *opt.Model))
		defer func() {
			span2.Finish()
			updateLogFunc()
			if e := recover(); e != nil {
				log.V1.CtxError(ctx, "panic occurred while forward chat stream response: %+v, %s", e, string(debug.Stack()))
				send.ErrorChannel <- errors.Errorf("internal error occurred: %+v", e)
				send.Close()
			}
		}()
		response := ""
		isFirstToken := true
		isFirstChunk := true
		err = stream.Forward(ctx, ch, send, func(item *llmstackgen.ChatChunk) service.ChatResult {
			if isFirstToken {
				isFirstToken = false
				log.V1.CtxInfo(ctx, "chat first token latency: %dms", time.Since(start).Milliseconds())
			}
			chunk := service.ChatResult{
				Message:      nil,
				FinishReason: nil,
			}
			if len(item.Id) > 0 {
				updateLog.RequestMetadata.ID = &item.Id
			}
			if item.Usage != nil && item.Usage.CompletionTokens > 0 {
				log.V1.CtxInfo(ctx, "token usage, prompt: %d, completion: %d, total: %d",
					item.Usage.PromptTokens, item.Usage.CompletionTokens, item.Usage.TotalTokens)
				updateLog.RequestMetadata.Usage = &journalservice.Usage{
					PromptTokens:     int(item.Usage.PromptTokens),
					CompletionTokens: int(item.Usage.CompletionTokens),
					TotalTokens:      int(item.Usage.TotalTokens),
				}
				chunk.Usage = &chatentity.TokenUsage{
					PromptTokens:     item.Usage.PromptTokens,
					CompletionTokens: item.Usage.CompletionTokens,
					TotalTokens:      item.Usage.TotalTokens,
				}
			}
			if len(item.Choices) > 0 && item.Choices[0].Delta != nil {
				if len(item.Choices[0].FinishReason) > 0 {
					chunk.FinishReason = &item.Choices[0].FinishReason
					updateLog.RequestMetadata.FinishReason = &item.Choices[0].FinishReason
				}
				chunk.Message = &chatentity.SimpleMessage{
					Role:    "assistant",
					Content: item.Choices[0].Delta.Content,
				}
				// Fix typo code block symbol if it is seed_25b_stream.
				// Only fix the first chunk and the last chunk.
				if *opt.Model == "seed_25b_stream" && (isFirstChunk || chunk.FinishReason != nil) {
					isFirstChunk = false
					chunk.Message.Content = fixSeed25bResponse(ctx, chunk.Message.Content)
				}
				if item.Choices[0].Delta.FunctionCall != nil {
					chunk.Message.FunctionCall = &chatentity.FunctionCall{
						Name:      item.Choices[0].Delta.FunctionCall.Name,
						Arguments: item.Choices[0].Delta.FunctionCall.Arguments,
					}
				}
				response += item.Choices[0].Delta.Content

				_ = metrics.CM.ChunkThroughput.WithTags(&metrics.CopilotModelTag{
					Name:  *opt.Model,
					Type:  "",
					AppID: opt.AppID,
				}).Incr(1)
			} else {
				// Not sure if this is possible.
				log.V1.CtxWarn(ctx, "no available choices in chat chunk, fallback to empty message")
				chunk.Message = &chatentity.SimpleMessage{
					Role:    "assistant",
					Content: "",
				}
			}
			return chunk
		})
		updateLog.ContentRaw = &response

		if opt.ConversationId != nil && len(*opt.ConversationId) != 0 {
			go s.updateHistory(ctx, opt, *opt.Model, response, prompt)
		}

		if err != nil {
			log.V1.CtxWarn(ctx, "failed to forward chat stream response: %v", err)
		}
	}()
	return result, nil
}

func (s *ChatService) updateHistory(ctx context.Context, opt service.ChatOption, model, response string, prompt *string) {
	history, err := s.GetConversation(ctx, service.GetConversationOption{
		AppID: opt.AppID,
		ID:    *opt.ConversationId,
		Limit: -1,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get conversation: %v", err)
		return
	}

	if len(history.Messages) == 1 {
		if err := s.generateChatSummary(ctx, history.Conversation, history.Messages[0], model); err != nil {
			log.V1.CtxWarn(ctx, "failed to generate conversation title: %v", err)
		}
	}

	createMsgOpt := dal.CreateMessageOption{
		ID:             s.IDGenerator.NewID(),
		ConversationID: *opt.ConversationId,
		Username:       chatentity.DefaultCopilotUsername,
		Role:           chatentity.RoleTypeCopilot,
		Type:           chatentity.MessageTypeMessage,
		Prompt:         prompt,
		Content:        response,
	}

	_, err = s.DAO.CreateMessages(ctx, createMsgOpt)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to create message: %v", err)
	}
}

func isInternalIntentModel(s string) bool {
	return s == SkylarkLiteIntentModelV2 || s == SkylarkLiteIntentModel
}

func (s *ChatService) IntentDetect(ctx context.Context, opt chatentity.IntentDetectOption) (*chatentity.IntentDetectResult, error) {
	app, err := s.AppService.GetApp(ctx, opt.AppID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get app")
	}

	originalModel := lo.FromPtr(opt.Model)

	if opt.Model == nil || len(*opt.Model) == 0 {
		opt.Model = lo.ToPtr(app.Model.PlanModel)
	}
	opt.Model = lo.ToPtr(getIntentDetectModelFromAlias(*opt.Model))
	log.V1.CtxInfo(ctx, "intent detect using model %s", *opt.Model)

	app = app.Clone()
	app.Intents = opt.Intents
	app.Model.PlanModel = *opt.Model

	wg := sync.WaitGroup{}

	// environment description
	envDesc := ""
	if opt.Context != nil && opt.Context.Variables != nil &&
		conv.DefaultAny[bool](opt.Context.Variables["is_code_selected"]) {
		// 如果是专用模型，设置适合该模型的 prompt
		if isInternalIntentModel(app.Model.PlanModel) {
			envDesc += "当前光标选中了一段代码"
		} else {
			envDesc += "User has provided code."
		}
	} else if isInternalIntentModel(app.Model.PlanModel) {
		envDesc += "当前光标没有选中任何代码"
	}

	if isInternalIntentModel(app.Model.PlanModel) {
		app.Intents = adjustIntentsForSkylarkLiteIntent(app.Intents)
	}

	// adjust input length.
	// Fix max input Limit to 1K.
	adjustedInput := TrimInputLength(opt.Input, 2000)

	var contexts []agentserivce.ContextResolver
	wg.Add(1)
	go func() {
		span, ct := bytetrace.StartCustomSpan(ctx, "intent", "GetContextSelection")
		defer span.Finish()
		defer wg.Done()
		var err error
		start := time.Now()
		app := app.Clone()
		app.Model.PlanModel = getContextSelectModelFromAlias(originalModel)
		log.V1.CtxInfo(ctx, "choosing context resolver using model: %s", app.Model.PlanModel)
		contexts, err = s.GetContextSelection(ctx, agentserivce.ContextSelectOption{
			App:       app,
			UserInput: adjustedInput,
			AvailableContexts: lo.Map(opt.ContextResolvers, func(item chatentity.ContextResolver, index int) agentserivce.ContextResolver {
				return agentserivce.ContextResolver{
					ResolverID:  item.ResolverID,
					Description: item.Description,
				}
			}),
			Username: opt.Account.Username,
		})
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to choose context types: %v", err)
			bytetrace.AddEvents(ct, bytetrace.NewErrorLogEvent("failed to choose context types "+err.Error()))
		}
		log.V1.CtxInfo(ctx, "choosing context costs %dms", time.Since(start).Milliseconds())
	}()

	var intent *appentity.Intent

	if len(lo.FromPtr(opt.IntentName)) != 0 {
		intent = &appentity.Intent{
			Intent:      lo.FromPtr(opt.IntentName),
			Description: "",
		}
		for _, i := range opt.Intents {
			if i.Intent == intent.Intent {
				intent.Description = i.Description
			}
		}
	} else {
		span, ct := bytetrace.StartCustomSpan(ctx, "intent", "getUserIntent")
		historyMessage := make([]*agententity.Message, 0)
		if opt.Context != nil {
			historyMessage = lo.Map(opt.Context.Messages, getMessageFromSimpleMessage)
		}

		start := time.Now()
		_, intent, _, _, err = s.getUserIntent(ctx, agentserivce.GetIntentOption{
			App:              app,
			UserInput:        adjustedInput,
			History:          historyMessage,
			EnvDesc:          envDesc,
			GenerateKeywords: false,
			Username:         opt.Account.Username,
		})
		if err != nil {
			bytetrace.AddEvents(ct, bytetrace.NewErrorLogEvent("failed to do intent detection "+err.Error()))
			span.Finish()
			return nil, errors.WithMessage(err, "failed to do intent detection")
		}
		span.Finish()
		log.V1.CtxInfo(ctx, "getting user intent costs %dms", time.Since(start).Milliseconds())
		if isInternalIntentModel(app.Model.PlanModel) && intent != nil {
			intent.Intent = getOriginalIntent(intent.Intent)
		}
	}

	if intent != nil {
		log.V1.CtxInfo(ctx, "detect user intent successfully: %s", intent.Intent)
	}

	wg.Wait()

	return &chatentity.IntentDetectResult{
		Intent: intent,
		ContextResolvers: lo.Map(contexts, func(item agentserivce.ContextResolver, index int) chatentity.ContextResolver {
			return chatentity.ContextResolver{
				ResolverID:  item.ResolverID,
				Description: item.Description,
			}
		}),
		Model: *opt.Model,
	}, nil
}

func getMessageFromSimpleMessage(m *chatentity.SimpleMessage, _ int) *agententity.Message {
	if m == nil {
		return nil
	}
	return &agententity.Message{
		Role:    m.Role,
		Content: m.Content,
	}
}

func TrimInputLength(input string, inputLimit int) string {
	adjustedInput := input
	rs := []rune(input)
	if len(rs) > inputLimit {
		adjustedInput = string(rs[:inputLimit/2]) + string(rs[(len(rs)-inputLimit/2):])
	}

	return adjustedInput
}

func (s *ChatService) ListUserModels(ctx context.Context, appID string, account *authentity.Account) (*service.ListUserModelsResult, error) {
	app, err := s.AppService.GetApp(ctx, appID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get app")
	}

	defaultModelAlias := getAliasFromModel(app.Model.Model)

	res := &service.ListUserModelsResult{
		DefaultModel:    defaultModelAlias,
		AvailableModels: []string{modelIDEInternal},
	}

	hit, abRes, err := ab.GetABVersion(ctx, "", account.Username, account.Department, appID)
	if err != nil || !hit {
		log.V1.CtxInfo(ctx, "%s AB test not hit", account.Username)
	} else {
		res.AvailableModels = abRes.Models
		res.DefaultModel = abRes.ChatModel
	}

	return res, nil
}

func (s *ChatService) Embeddings(ctx context.Context, req llmstackgen.EmbeddingsRequest, account *authentity.Account) (*llmstackgen.EmbeddingsResponse, error) {
	const chunkSize = 4
	const maxConcurrent = 25
	// use errGroup
	g, ctxErrGroup := errgroup.WithContext(ctx)
	g.SetLimit(maxConcurrent)

	req.Model = getEmbeddingModelFromAlias(req.Model)

	log.V1.CtxInfo(ctx, fmt.Sprintf("[Copilot Embedding] original req input is %s", lo.FromPtr(req.Input)))
	// After supporting batch processing, downstream API deprecate single input string.
	if req.Model == "unixcoder" {
		if req.Input != nil && *req.Input != "" {
			log.V1.CtxInfo(ctx, "[Copilot Embedding] put single input into array for unix coder model")
			req.Inputs = append(req.Inputs, *req.Input)
			req.Input = nil
		}
	}
	var inputLen = len(req.Inputs)

	// Downstream api of unixcoder is restricted in response length, set to 5 according to local testing.
	if req.Model == "unixcoder" && inputLen > chunkSize {
		// request the first one and keep metadata as final response.
		firstReq := llmstackgen.EmbeddingsRequest{
			Model:             req.Model,
			Input:             req.Input,
			Inputs:            req.Inputs[:chunkSize],
			FallbackModelList: req.FallbackModelList,
			RetryTimes:        req.RetryTimes,
		}
		res, err := s.SDK.Embeddings(ctx, firstReq, llmstack.WithByteCloudServiceJWT())
		if err != nil {
			return nil, err
		}
		if res == nil {
			log.V1.CtxWarn(ctx, "[Copilot Embedding] get empty embedding response")
			return nil, nil
		}

		subRes := make([]*llmstackgen.Embedding, inputLen)

		for i := chunkSize; i < inputLen; i += chunkSize {
			endIndex := i + chunkSize
			if endIndex > inputLen {
				endIndex = inputLen
			}
			subReq := llmstackgen.EmbeddingsRequest{
				Model:             req.Model,
				Input:             req.Input,
				Inputs:            req.Inputs[i:endIndex],
				FallbackModelList: req.FallbackModelList,
				RetryTimes:        req.RetryTimes,
			}
			// Capture range variables
			curI := i
			curEndIndex := endIndex

			g.Go(func() error {
				r, subErr := s.SDK.Embeddings(ctxErrGroup, subReq, llmstack.WithByteCloudServiceJWT())
				if subErr != nil {
					log.V1.CtxError(ctxErrGroup, fmt.Sprintf("[Copilot Embedding] sub request of copilot embedding failed, err = %v", subErr))
					return subErr
				} else {
					if r != nil {
						for j := curI; j < curEndIndex; j++ {
							subRes[j] = r.Data[j-curI]
						}
					}
				}
				return nil
			})
		}

		if err = g.Wait(); err != nil {
			return nil, err
		}

		for i := chunkSize; i < inputLen; i++ {
			res.Data = append(res.Data, subRes[i])
		}
		return res, nil
	} else {
		res, err := s.SDK.Embeddings(ctx, req, llmstack.WithByteCloudServiceJWT())
		if err != nil {
			log.V1.CtxError(ctx, "[Copilot Embedding] failed, err: %v", err)
			return nil, err
		}
		return res, nil
	}
}

func (s *ChatService) LogByMessages(ctx context.Context, appId, username string, opts ...dal.CreateMessageOption) ([]*journalservice.PromptCompletion, error) {
	pcs := make([]*journalservice.PromptCompletion, 0)
	for _, opt := range opts {
		// Only record copilot's response
		if opt.Role == chatentity.RoleTypeCopilot {
			res, err := s.JournalService.CreateLog(ctx, journalservice.CreateLogOption{
				AppId:           lo.ToPtr(appId),
				Username:        username,
				ModelName:       "",
				Status:          journalservice.StatusSuccess,
				SessionId:       opt.SessionID,
				Prompt:          opt.Prompt,
				Type:            journalservice.TypeChatCompletion,
				ContentRaw:      lo.ToPtr(opt.Content),
				RequestMetadata: journalservice.PromptCompletionRequestMetadata{},
				ContextVariable: journalservice.PromptCompletionContextVariable{},
			})
			if err != nil {
				return nil, errors.WithMessage(err, "failed to create log")
			}
			pcs = append(pcs, res)
		}
	}
	return pcs, nil
}

func (s *ChatService) RemoveConversationCtx(ctx context.Context, appID string, conversationID string, conversationContext *skillentity.Context) error {
	if conversationContext == nil {
		return nil
	}

	// if context stores "wiki_select", remove it
	if conversationContext.Variables != nil {
		delete(conversationContext.Variables, "wiki_select")
	}

	log.V1.CtxInfo(ctx, "remove conversation context for conversation %s", conversationID)

	// update conversation with new context
	_, err := s.DAO.UpdateConversation(ctx, dal.UpdateConversationOption{
		AppID:   appID,
		ID:      conversationID,
		Context: conversationContext,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update conversation: %v", err)
		return err
	}
	return nil
}

// MergeWithConversationContext merge the request context with the conversation context
// if the key is the same, the request context will be used, and the conversation context will not be modified
func (s *ChatService) MergeWithConversationContext(ctx context.Context, requestContext *skillentity.Context, conversationContext *skillentity.Context) *skillentity.Context {
	logID := ctxvalues.LogIDDefault(ctx)

	// record the information before merging
	requestVarsCount := 0
	conversationVarsCount := 0

	if requestContext != nil && requestContext.Variables != nil {
		requestVarsCount = len(requestContext.Variables)
	}
	if conversationContext != nil && conversationContext.Variables != nil {
		conversationVarsCount = len(conversationContext.Variables)
	}

	log.V1.CtxInfo(ctx, "[%s] start merging context, request variables: %d, conversation variables: %d",
		logID, requestVarsCount, conversationVarsCount)

	if requestContext == nil {
		if conversationContext == nil {
			log.V1.CtxInfo(ctx, "[%s] both request and conversation context are empty, return nil", logID)
			return nil
		}
		// if the request has no context, but the conversation has, copy the conversation context
		result := &skillentity.Context{}
		if conversationContext.Variables != nil {
			result.Variables = make(map[string]any)
			for k, v := range conversationContext.Variables {
				result.Variables[k] = v
			}
		}
		if len(conversationContext.IntentPrompts) > 0 {
			result.IntentPrompts = make([]skillentity.IntentPrompt, len(conversationContext.IntentPrompts))
			copy(result.IntentPrompts, conversationContext.IntentPrompts)
		}
		log.V1.CtxInfo(ctx, "[%s] request context is empty, use conversation context, variables: %d",
			logID, len(result.Variables))
		return result
	}

	// create a copy of the request context
	result := &skillentity.Context{
		Input: requestContext.Input, // ensure the Input is also copied
	}
	if requestContext.Variables != nil {
		result.Variables = make(map[string]any)
		for k, v := range requestContext.Variables {
			result.Variables[k] = v
		}
	}
	if len(requestContext.IntentPrompts) > 0 {
		result.IntentPrompts = make([]skillentity.IntentPrompt, len(requestContext.IntentPrompts))
		copy(result.IntentPrompts, requestContext.IntentPrompts)
	}

	// if the conversation has no context, return the request context
	if conversationContext == nil {
		log.V1.CtxInfo(ctx, "[%s] conversation context is empty, use request context, variables: %d",
			logID, len(result.Variables))
		return result
	}

	// the number of variables before merging
	beforeMergeVarsCount := 0
	if result.Variables != nil {
		beforeMergeVarsCount = len(result.Variables)
	}

	// the number of variables before merging
	var mergedCount int
	if conversationContext.Variables != nil {
		if result.Variables == nil {
			result.Variables = make(map[string]any)
		}
		for k, v := range conversationContext.Variables {
			// only use the value from the conversation if the key does not exist in the request
			if _, exists := result.Variables[k]; !exists {
				result.Variables[k] = v
				mergedCount++
			}
		}
	}

	// the number of IntentPrompts before merging
	beforeMergePromptsCount := len(result.IntentPrompts)

	// merge IntentPrompts
	// strategy: keep all IntentPrompts from the request, and add the IntentPrompts from the conversation that are not in the request
	var mergedPromptsCount int
	if len(conversationContext.IntentPrompts) > 0 {
		if len(result.IntentPrompts) == 0 {
			// if the request has no IntentPrompts, use the IntentPrompts from the conversation
			result.IntentPrompts = make([]skillentity.IntentPrompt, len(conversationContext.IntentPrompts))
			copy(result.IntentPrompts, conversationContext.IntentPrompts)
			mergedPromptsCount = len(conversationContext.IntentPrompts)
		} else {
			// create a map of ResolverID to index
			resolverIDMap := make(map[string]bool)
			for _, prompt := range result.IntentPrompts {
				resolverIDMap[prompt.ResolverID] = true
			}

			// add the IntentPrompts from the conversation that are not in the request
			for _, prompt := range conversationContext.IntentPrompts {
				if !resolverIDMap[prompt.ResolverID] {
					result.IntentPrompts = append(result.IntentPrompts, prompt)
					resolverIDMap[prompt.ResolverID] = true
					mergedPromptsCount++
				}
			}
		}
	}

	log.V1.CtxInfo(ctx, "[%s] Context merged. Variables: %d -> %d(merged %d), IntentPrompts: %d -> %d(merged %d)",
		logID, beforeMergeVarsCount, len(result.Variables), mergedCount,
		beforeMergePromptsCount, len(result.IntentPrompts), mergedPromptsCount)

	return result
}
