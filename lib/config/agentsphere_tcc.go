package config

import (
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
)

type AgentSphereTCCConfig struct {
	ModelAuthConfig                  *libtcc.GenericConfig[ModelDispatchConfig]              `tcc:"key:model_auth_config;format:yaml;space:default"`
	CORSConfig                       *libtcc.GenericConfig[CORSConfig]                       `tcc:"key:cors_config;format:yaml;space:default"`
	ServiceJWT                       *libtcc.GenericConfig[CodebaseServiceJWTConfig]         `tcc:"key:codebase_service_jwt;format:string;space:default"`
	TOSConfig                        *libtcc.GenericConfig[TOSConfig]                        `tcc:"key:tos_config;format:yaml;space:default"`
	AgentList                        *libtcc.GenericConfig[[]AgentConfig]                    `tcc:"key:agent_config;format:yaml;space:default"`
	GitlabToken                      *libtcc.GenericConfig[GitlabTokenConfig]                `tcc:"key:gitlab_ci_token;format:string;space:default"`
	StratoCubeConfig                 *libtcc.GenericConfig[StratoCubeConfig]                 `tcc:"key:stratocube_config;format:yaml;space:default"`
	AchaProxyConfig                  *libtcc.GenericConfig[AchaProxyConfig]                  `tcc:"key:acha_proxy_config;format:yaml;space:default"`
	AGSPRelayConfig                  *libtcc.GenericConfig[AGSPRelayConfig]                  `tcc:"key:agsp_relay_config;format:yaml;space:default"`
	AGSPUserFeaturesConfig           *libtcc.GenericConfig[AGSPUserFeaturesConfig]           `tcc:"key:user_features_config;format:yaml;space:default"`
	LarkAppConfig                    *libtcc.GenericConfig[LarkAppConfig]                    `tcc:"key:lark_app_config;format:yaml;space:default" name:"global"`
	ByteCloudSecret                  *libtcc.GenericConfig[ByteCloudSKConfig]                `tcc:"key:bytecloud_service_secret;format:string;space:default"`
	NextAgentUserFeaturesConfig      *libtcc.GenericConfig[NextAgentUserFeaturesConfig]      `tcc:"key:next_agent_user_features_config;format:yaml;space:default"`
	NextAgentUserRoleConfig          *libtcc.GenericConfig[NextAgentUserRoleConfig]          `tcc:"key:next_agent_user_role_config;format:yaml;space:default"`
	NextAgentShowcaseLocalizedConfig *libtcc.GenericConfig[NextAgentShowcaseLocalizedConfig] `tcc:"key:next_agent_showcase_localized_config;format:yaml;space:default"`
	NeumaLarkAppConfig               *libtcc.GenericConfig[LarkAppConfig]                    `tcc:"key:neuma_lark_app_config;format:yaml;space:default" name:"neuma"`
	ImageXConfig                     *libtcc.GenericConfig[ImageXTCCConfig]                  `tcc:"key:imagex_config;format:json;space:default"`
	NextAgentShareConfig             *libtcc.GenericConfig[NextAgentShareConfig]             `tcc:"key:next_agent_share_config;format:yaml;space:default"`
	NextAgentSessionCollectionConfig *libtcc.GenericConfig[NextAgentSessionCollectionConfig] `tcc:"key:next_agent_session_collection_config;format:yaml;space:default"`
	NextAgentSendLarkMessageConfig   *libtcc.GenericConfig[NextAgentSendLarkMessageConfig]   `tcc:"key:next_agent_send_lark_message_config;format:yaml;space:default"`
	NextAgentUploadLarkFileConfig    *libtcc.GenericConfig[NextAgentUploadLarkFileConfig]    `tcc:"key:next_agent_upload_lark_file_config;format:yaml;space:default"`
	NextAgentGroupConfig             *libtcc.GenericConfig[NextAgentGroupConfig]             `tcc:"key:next_agent_group_config;format:yaml;space:default"`
	NextAgentGroupRuleConfig         *libtcc.GenericConfig[NextAgentGroupRuleConfig]         `tcc:"key:next_agent_group_rule_config;format:yaml;space:default"`
	NextAgentRuleConfig              *libtcc.GenericConfig[NextAgentRuleConfig]              `tcc:"key:next_agent_rule_config;format:yaml;space:default"`
	NextAgentHulkCloudConfig         *libtcc.GenericConfig[NextAgentHulkCloudConfig]         `tcc:"key:next_agent_hulkcloud_config;format:yaml;space:default"`
	NextAgentSessionMonitorConfig    *libtcc.GenericConfig[NextAgentSessionMonitorConfig]    `tcc:"key:next_agent_session_monitor_config;format:yaml;space:default"`
	AMapConfig                       *libtcc.GenericConfig[AMapConfig]                       `tcc:"key:amap_config;format:yaml;space:default"`
	TaskTemplatesConfig              *libtcc.GenericConfig[TaskTemplatesConfig]              `tcc:"key:task_templates_config;format:yaml;space:default"`
	NextCodeConfig                   *libtcc.GenericConfig[NextCodeConfig]                   `tcc:"key:next_code_config;format:yaml;space:default"`
	RoleAgentConfig                  *libtcc.GenericConfig[RoleAgentConfig]                  `tcc:"key:role_agent_config;format:yaml;space:default"`
	NextAgentActivityConfig          *libtcc.GenericConfig[NextAgentActivityConfig]          `tcc:"key:next_agent_activity_config;format:yaml;space:default"`
	NextAgentGrantAccessConfig       *libtcc.GenericConfig[NextAgentGrantAccessConfig]       `tcc:"key:next_agent_grant_access_config;format:yaml;space:default"`
	NextGlobalSessionLimitConfig     *libtcc.GenericConfig[NextGlobalSessionLimitConfig]     `tcc:"key:next_global_session_limit_config;format:yaml;space:default"`
	DeepWikiConfig                   *libtcc.GenericConfig[DeepWikiConfig]                   `tcc:"key:deepwiki_config;format:yaml;space:default"`
	NextAgentKnowledgeConfig         *libtcc.GenericConfig[NextAgentKnowledgebaseConfig]     `tcc:"key:next_agent_knowledge_config;format:yaml;space:default"`
	PermissionActionConfig           *libtcc.GenericConfig[PermissionActionConfig]           `tcc:"key:permission_action_config;format:yaml;space:default"`
	NextOncallConfig                 *libtcc.GenericConfig[NextOncallConfig]                 `tcc:"key:next_oncall_config;format:yaml;space:default"`
	NextKnowledgesetMetadata         *libtcc.GenericConfig[NextKnowledgesetMetadata]         `tcc:"key:next_knowledgeset_metadata;format:json;space:default"`
	NextAimeAbConfig                 *libtcc.GenericConfig[NextAimeAbConfig]                 `tcc:"key:next_aime_ab_config;format:json;space:default"`
	NextAgentDeploymentConfig        *libtcc.GenericConfig[NextAgentDeploymentConfig]        `tcc:"key:next_agent_deployment_config;format:yaml;space:default"`
	NextSessionConfig                *libtcc.GenericConfig[NextSessionConfig]                `tcc:"key:next_session_config;format:yaml;space:default"`
	TQSConfig                        *libtcc.GenericConfig[TQSConfig]                        `tcc:"key:tqs_config;format:yaml;space:default"`
	NextAimeAccountConfig            *libtcc.GenericConfig[NextAimeAccountConfig]            `tcc:"key:next_aime_account;format:yaml;space:default"`
	NextDeployReviewConfig           *libtcc.GenericConfig[NextDeployReviewConfig]           `tcc:"key:next_deploy_review;format:yaml;space:default"`
	NextToolI18nConfig               *libtcc.GenericConfig[NextToolI18nConfig]               `tcc:"key:next_tool_i18n_config;format:yaml;space:default"`
	NextAgentScenarioDetectionConfig *libtcc.GenericConfig[NextAgentScenarioDetectionConfig] `tcc:"key:next_agent_scenario_detection_config;format:yaml;space:default"`
}

type RoleAgentConfigMetadata struct {
	Name    string `yaml:"name"`
	AgentID string `yaml:"agent_id"`
}

type RoleAgentConfig struct {
	Roles map[string]RoleAgentConfigMetadata `yaml:"roles"`
}

type TaskTemplatesConfig struct {
	TaskTemplates []TaskTemplate `yaml:"TaskTemplates"`
}

type TaskTemplate struct {
	ID               string             `yaml:"ID"`
	Category         string             `yaml:"Category"`
	Name             string             `yaml:"Name"`
	Prompt           string             `yaml:"Prompt"`
	Variables        []TemplateVariable `yaml:"Variables"`
	Showcase         NextAgentShowcase  `yaml:"Showcase"`
	UseInternalTool  *bool              `yaml:"UseInternalTool"`
	EstimatedMinutes EstimatedMinutes   `yaml:"EstimatedMinutes"`
	SupportRoles     []string           `yaml:"SupportRoles"`
}

type TemplateVariable struct {
	Key         string `yaml:"Key"`
	Required    bool   `yaml:"Required"`
	Description string `yaml:"Description"`
	Type        string `yaml:"Type"`
	Placeholder string `yaml:"Placeholder"`
}

type EstimatedMinutes struct {
	Min int `yaml:"Min"`
	Max int `yaml:"Max"`
}

type ByteCloudSKConfig string

type GitlabTokenConfig string

type I18nText struct {
	En string `yaml:"en"`
	Zh string `yaml:"zh"`
}

type AgentConfig struct {
	Agent           string   `yaml:"Agent"`       // agent series, e.g. `panic_fix`, frontend will display corresponding form
	Version         string   `yaml:"Version"`     // an agent series may have multiple versions, empty string will be used as default version for users
	VersionName     I18nText `yaml:"VersionName"` // version name displayed to users
	Description     I18nText `yaml:"Description"` // description for the specific version of agent
	DefaultPlan     string   `yaml:"DefaultPlan"`
	DefaultTerminal string   `yaml:"DefaultTerminal"`
	RuntimeAgentID  string   `yaml:"RuntimeAgentID"` // runtime agent id, if not empty, will be used as agent id for runtime, otherwise will use Agent

	OrchestrationConfig OrchestrationConfig `yaml:"OrchestrationConfig"`
	RuntimeConfig       RuntimeConfig       `yaml:"RuntimeConfig"`
}

type ResourceQuantity struct {
	Requests string `yaml:"Requests"`
	Limits   string `yaml:"Limits"`
}

// Container resource quota, only available in stratocube
type RuntimeResourceQuota struct {
	CPU    *ResourceQuantity `yaml:"CPU"`
	Memory *ResourceQuantity `yaml:"Memory"`
	// whether to persist workspace dir and how much space to allocate
	// Only Requests is supported, format: `8Gi`
	PersistWorkspace *ResourceQuantity `yaml:"PersistWorkspace"`
	// workspace path to persist, default: /workspace
	WorkspacePath *string `yaml:"WorkspacePath"`
	StorageClass  *string `yaml:"StorageClass"`
}

type RuntimeConfig struct {
	PSM               string               `yaml:"PSM"`
	Type              string               `yaml:"Type"`
	InjectOpenAIToken bool                 `yaml:"InjectOpenAIToken"`
	Image             string               `yaml:"Image"`
	Envs              map[string]string    `yaml:"Envs"`
	Port              int                  `yaml:"Port"`
	Quota             RuntimeResourceQuota `yaml:"Quota"`
	AgentRunConfig    AgentRunConfig       `yaml:"AgentRunConfig"`
	// BinarySource indicates which binary is used to start the runtime process.
	// AgentSphere agent runtime is used by default.
	// Format: scm://{repo_name}?version={version} or file://{local_path}
	BinarySource string `yaml:"BinarySource"`
}

type OrchestrationConfig struct {
	// default timeout for agent run in time.ParseDuration format, e.g. `1h10m10s`, `10m`, default: 2h
	Timeout string `yaml:"Timeout"`
	// timeout for container recycle in time.ParseDuration format, default: 14d
	RecycleTimeout string `yaml:"RecycleTimeout"`
	// resource pool key to limit concurrency, or "agent@version" by default
	ResourceKey string `yaml:"PoolKey"`
	// max number of concurrent containers shared by ALL USERS, no limit if 0. new tasks will be queued until one is available
	// NOTE: if this is enabled and there are running agent, disable it will cause issues
	MaxConcurrency int `yaml:"MaxConcurrency"`

	// Number of pre-created containers, if drained, new containers will be created
	PoolSize int `yaml:"PoolSize"`

	// RuntimeStopTimeout for stop container
	RuntimeStopTimeout string `yaml:"RuntimeStopTimeout"`
	// RuntimeDeleteTimeout for delete container
	RuntimeDeleteTimeout string `yaml:"RuntimeDeleteTimeout"`
}

type AgentRunConfig struct {
	Model              AgentRunLLMConfig   `yaml:"Model"`
	MaxSteps           int                 `yaml:"MaxSteps"`
	ModelScenesConfig  AgentModelConfig    `yaml:"ModelScenesConfig"`
	AgentVariantConfig AgentVariantConfig  `yaml:"AgentVariantConfig"`
	CondensedModel     AgentCondensedModel `yaml:"CondensedModel"`
	// Temporary option to indicate if it needs user confirmation to submit MR.
	// Enabled by default.
	// This is used by the automated benchmark which involves no real user input, so the confirmation should be skipped.
	DisableUserConfirmation bool `yaml:"DisableUserConfirmation"`

	// Extra options for the agent run, schemaless.
	// See the following AgentRunOption for the supported options.
	ExtraOptions map[string]any `yaml:"ExtraOptions"`
}

const (
	// common agent run extra options
	AgentRunOptionDisablePushToRemote = "disable_push_to_remote" // bool
	AgentRunOptionDetectV2            = "detect_v2"              // bool
)

type AgentRunLLMConfig struct {
	Model       string          `yaml:"Model"`
	Temperature float32         `yaml:"Temperature"`
	MaxTokens   int32           `yaml:"MaxTokens"`
	TopP        float32         `yaml:"TopP"`
	Thinking    *ThinkingConfig `yaml:"Thinking"`
}

type AgentVariantDetail struct {
	Variant string   `yaml:"Variant"`
	Scenes  []string `yaml:"Scenes"`
}

type AgentVariantConfig struct {
	Default  AgentVariantDetail   `yaml:"Default"`
	Variants []AgentVariantDetail `yaml:"Variants"`
}

type AgentModelConfig struct {
	SceneSpecifications []ScenesModelConfig `yaml:"SceneSpecifications"`
	Default             ModelDetailConfig   `yaml:"Default"`
}

type AgentCondensedModel struct {
	ModelConfig             ModelDetailConfig `yaml:"ModelConfig"`
	CondensedTokenThreshold int               `yaml:"CondensedTokenThreshold"`
}

type ScenesModelConfig struct {
	Scenes      []string          `yaml:"Scenes"`
	ModelConfig ModelDetailConfig `yaml:"ModelConfig"`
}

type AchaProxyConfig struct {
	OnlineProxyURL string   `yaml:"OnlineProxyURL"`
	Tokens         []string `yaml:"Tokens"`
	AllowedPSMs    []string `yaml:"AllowedPSMs"`
	AllowedRepos   []string `yaml:"AllowedRepos"`
}

// 从线上访问 BOE 容器的 WebSocket Relay 配置
type AGSPRelayConfig struct {
	RelayURL string `yaml:"RelayURL"`
}

type StratoCubeTenantConfig struct {
	TenantKey       string `yaml:"TenantKey"`
	TenantName      string `yaml:"TenantName"`
	TenantID        int    `yaml:"TenantID"`
	Secret          string `yaml:"Secret"`
	FileServerToken string `yaml:"FileServerToken"`
}

type StratoCubeConfig struct {
	BaseURL      string                   `yaml:"BaseURL"`
	Tenant       string                   `yaml:"Tenant"`
	TenantID     int                      `yaml:"TenantID"`
	Secret       string                   `yaml:"Secret"`
	PoolSize     int                      `yaml:"PoolSize"`
	TenantConfig []StratoCubeTenantConfig `yaml:"TenantConfig"`
}

type GrayUsers struct {
	Users       []string `yaml:"Users"`
	Departments []string `yaml:"Departments"`
}

type AgentGrayConfig struct {
	Agent        string    `yaml:"Agent"`
	Version      string    `yaml:"Version"`
	EnabledUsers GrayUsers `yaml:"EnabledUsers"`
	Developers   []string  `yaml:"Developers"`
}

// 按部门/邮箱前缀灰度用户
type AGSPUserFeaturesConfig struct {
	GrayUsers       []string `yaml:"Users"`
	GrayDepartments []string `yaml:"Departments"`
	Admins          []string `yaml:"Admins"`
	Developers      []string `yaml:"Developers"` // users who can access developer apis

	Agents []AgentGrayConfig `yaml:"Agents"`
}

type NextAgentUserFeaturesConfig struct {
	GrayUsers       []string        `yaml:"Users"` // 超级白名单
	GrayDepartments []string        `yaml:"Departments"`
	Developers      []string        `yaml:"Developers"`
	NormalGrayUsers map[string]bool `yaml:"NormalGrayUsers"` // 正常白名单

	MaxRunningAgents  int `yaml:"MaxRunningAgents"`  // 暂时弃用
	MaxSessionsPerDay int `yaml:"MaxSessionsPerDay"` // 每天可以创建的 Session 数量, 凌晨 4 点后计算
	MaxMessageLimit   int `yaml:"MaxMessageLimit"`   // 每个 Session 最大对话数量
	MaxMessageWarning int `yaml:"MaxMessageWarning"` // 超过某个数值后开始提醒用户
}

type NextAgentUserRoleConfig struct {
	Roles      []RoleConfig      `yaml:"Roles"`
	UserGroups []UserGroupConfig `yaml:"UserGroups"`
}

type RoleConfig struct {
	Name        string   `yaml:"Name"`
	Users       []string `yaml:"Users"`
	Departments []string `yaml:"Departments"`
	UserGroups  []string `yaml:"UserGroups"`
}

type UserGroupConfig struct {
	Name        string   `yaml:"Name"`
	Users       []string `yaml:"Users"`
	Departments []string `yaml:"Departments"`
}

type NextAgentShowcaseLocalizedConfig struct {
	ShowcasesEN []NextAgentShowcase `yaml:"ShowcasesEN"`
	ShowcasesZH []NextAgentShowcase `yaml:"ShowcasesZH"`
}

type NextAgentShowcase struct {
	ReplayID    string              `yaml:"ReplayID"`
	Title       string              `yaml:"Title"`
	Description string              `yaml:"Description"`
	ImageURL    string              `yaml:"ImageURL"`
	TotalSteps  int64               `yaml:"TotalSteps"`
	Duration    int64               `yaml:"Duration"`
	Artifacts   []ShowcaseArtifacts `yaml:"Artifacts"`
	Category    string              `yaml:"Category"`
}

type NextAgentShareConfig struct {
	TemplateID string `yaml:"TemplateID"`
	URLTitle   string `yaml:"URLTitle"`
}

type ShowcaseArtifacts struct {
	Type string `yaml:"Type"`
	Name string `yaml:"Name"`
}

type NextAgentSessionCollectionConfig struct {
	SheetToken                string                      `yaml:"SheetToken"`
	SheetID                   string                      `yaml:"SheetID"`
	Domain                    string                      `yaml:"Domain"`
	MaxRunning                int                         `yaml:"MaxRunning"`
	DownloadSheetMaxRows      int                         `yaml:"DownloadSheetMaxRows"`
	LarkNotificationTemplates []*LarkNotificationTemplate `yaml:"LarkNotificationTemplates"`
}

type LarkNotificationTemplate struct {
	ID      string `yaml:"ID"`
	Title   string `yaml:"Title"`
	Content string `yaml:"Content"`
}

type NextAgentSendLarkMessageConfig struct {
	GroupID                   string `yaml:"GroupID"`                   // aime内测群
	DeployNotificationGroupID string `yaml:"DeployNotificationGroupID"` // agent 发车群
	SessionMonitorGroupID     string `yaml:"SessionMonitorGroupID"`     // session 巡检监控群
}

type NextAgentUploadLarkFileConfig struct {
	FolderToken      string `yaml:"FolderToken"`
	TraceFolderToken string `yaml:"TraceFolderToken"`
	RLFolderToken    string `yaml:"RLFolderToken"`
}

/*
数据定义:
 1. 存在一个 Group Map, Key 是 Group Name, Value 是这个的 Group 的用户和部门数据
 2. 存在一个 Rule Map，Key 是 Rule Name, Value 是这个 Rule 对应的具体规则
 3. 存在一个 Group Rule Map, 可以是 Group Name, Value 是 Rule Name
查询逻辑：
 1. 先从 GroupMap 里面获取这个用户数据哪个 Group，可能存在一个用户属于多个 Group
 2. 从 GroupRuleMap 里面获取这个用户所在 Group 对应的 Rule
 3. 从获取的所有 Rule 里面取并集
*/

type NextAgentGroup struct {
	Users       map[string]bool `yaml:"Users"`       // 确定的用户名，直接 Map 获取
	Departments []string        `yaml:"Departments"` // 存在包含关系，需要逐个判断
}

type NextAgentGroupConfig struct {
	Groups map[string]NextAgentGroup `yaml:"Groups"` // 对应不同 Group 的规则，key 是 group name
}

type NextAgentRule struct {
	Role                 int    `yaml:"Role"` // 每个规则对应的角色
	AllowUseInternalTool bool   `yaml:"AllowUseInternalTool"`
	MaxSessionsPerDay    *int64 `yaml:"MaxSessionsPerDay"` // 每天可以创建的 Session 数量, 凌晨 4 点后计算
	MaxMessageLimit      *int64 `yaml:"MaxMessageLimit"`   // 每个 Session 最大对话数量
	MaxMessageWarning    *int64 `yaml:"MaxMessageWarning"` // 超过某个数值后开始提醒用户
}

type NextAgentRuleConfig struct {
	Rules map[string][]NextAgentRule `yaml:"Rules"`
}

type NextAgentGroupRuleConfig struct {
	GroupRules map[string][]string `yaml:"GroupRules"` // key 是 group name, value 是 rule list
}

type NextAgentHulkCloudConfig struct {
	BaseURL string `yaml:"BaseURL"`
	Token   string `yaml:"Token"`
	Service string `yaml:"Service"`
}

type NextAgentSessionMonitorConfig struct {
	EnableMonitor              bool  `yaml:"EnableMonitor"`              // 开启巡检
	MonitorIntervalMinute      int64 `yaml:"MonitorIntervalMinute"`      // session 巡检间隔
	NotificationIntervalMinute int64 `yaml:"NotificationIntervalMinute"` // session 巡检通知间隔
	MaxCheckTimeoutMinute      int64 `yaml:"MaxCheckTimeoutMinute"`      // session 巡检最大超时时间，超过后终止巡检
	StayTimeoutMinute          int64 `yaml:"StayTimeoutMinute"`          // session 运行超时时间
	FirstWaitMessageMinute     int64 `yaml:"FirstWaitMessageMinute"`     // session 第一条消息未回复时间
	FirstWaitMessageMaxMinute  int64 `yaml:"FirstWaitMessageMaxMinute"`  // session 超过最大时间未回复，则修改状态为 error, 需要保证大于 FirstWaitMessageMinute
}

type ActivityConfig struct {
	ID                  string `yaml:"ID"`
	Activited           bool   `yaml:"Activited"`
	StartTime           string `yaml:"StartTime"`
	EndTime             string `yaml:"EndTime"`
	LuckyEggsPerTask    int    `yaml:"LuckyEggsPerTask"`
	LuckyEggRandomN     int    `yaml:"LuckyEggRandomN"`
	InviteTargetViewNum int    `yaml:"InviteTargetViewNum"`
}

type NextAgentActivityConfig struct {
	CurrentActivityID string           `yaml:"CurrentActivityID"`
	Activities        []ActivityConfig `yaml:"Activities"`
}

type NextAgentGrantAccessConfig struct {
	Enabled    bool                    `yaml:"Enabled"`    // 是否启用直接获取内测资格
	ActivityID string                  `yaml:"ActivityID"` // 内测资格活动 ID
	CodeOwner  string                  `yaml:"CodeOwner"`  // 内测资格代码拥有者（以邀请码绑定的方式发放资格，需要一个Owner）
	BlackList  []*GrantAccessBlackList `yaml:"BlackList"`  // 黑名单用户列表
}

type GrantAccessBlackList struct {
	Department  string `yaml:"Department"`
	Sequence    string `yaml:"Sequence"`
	EmailSuffix string `yaml:"EmailSuffix"`
}

type RoleLimit struct {
	Role               int  `yaml:"Role"`               // 角色对应的数字标识
	Enabled            bool `yaml:"Enabled"`            // 是否启用该角色的限制
	NewSessionOnly     bool `yaml:"NewSessionOnly"`     // 是否只对新创建的 Session 生效
	RecentHoursToCheck int  `yaml:"RecentHoursToCheck"` // 检查最近多少小时内的 Session 数量
	MaxRunningSessions int  `yaml:"MaxRunningSessions"` // 每个角色最大运行的 Session 数量
}

type DeveloperLimit struct {
	Enabled bool `yaml:"Enabled"`
}

type NextGlobalSessionLimitConfig struct {
	RolesLimit     []RoleLimit    `yaml:"Roles"`
	DeveloperLimit DeveloperLimit `yaml:"Developer"`
}

type DeepWikiConfig struct {
	NextCodeAppID      string `yaml:"NextCodeAppID"`
	NextCodeAppSecret  string `yaml:"NextCodeAppSecret"`
	CodebaseServiceJWT string `yaml:"CodebaseServiceJWT"`
}

type NextAgentKnowledgebaseConfig struct {
	VikingToken                string `yaml:"VikingToken"`
	VikingIndex                string `yaml:"VikingIndex"`
	ElasticSearchDocumentIndex string `yaml:"ElasticSearchDocumentIndex"`
	ForceUpdate                bool   `yaml:"ForceUpdate"`
}
type PermissionActionConfig struct {
	Roles map[string][]string `yaml:"Roles"`
}

type NextOncallConfig struct {
	OncallTernantID       string          `yaml:"OncallTernantID"`       // oncall 租户ID
	OncallBaseUrl         string          `yaml:"OncallBaseUrl"`         // oncall url
	OncallLarkFolderToken string          `yaml:"OncallLarkFolderToken"` // 记录用户反馈内容的飞书文档文件夹 token
	OncallCardID          string          `yaml:"OncallCardID"`          // oncall 卡片ID
	AppLinkBaseUrl        string          `yaml:"AppLinkBaseUrl"`        // applink URL
	ChatIDs               map[string]bool `yaml:"ChatIDs"`               // 监听用户反馈消息的飞书群聊 ID
}

type NextKnowledgesetMetadata struct {
	KnowledgeTag          map[string][]string             `json:"knowledge_tags"`
	KnowledgesetTypes     []NextKnowledgesetTypesMeta     `json:"knowledge_set_types"`
	KnowledgesetTag       map[string][]string             `json:"knowledge_set_tags"`
	KnowledgeRecallMethod []NextKnowledgeRecallMethodMeta `json:"knowledge_recall_method"`
}
type NextKnowledgesetTypesMeta struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type NextKnowledgeRecallMethodMeta struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type NextAimeAbConfig struct {
	AppID string `json:"appid"`
	Token string `json:"token"`
}

type NextAgentDeploymentConfig struct {
	FaaSToken   string `yaml:"FaaSToken"`   // 服务账号 token for deployment
	FaaSBaseURL string `yaml:"FaaSBaseURL"` // FaaS base url for deployment
	FaaSRegion  string `yaml:"FaaSRegion"`  // FaaS region for deployment
	FaaSCluster string `yaml:"FaaSCluster"` // FaaS cluster for deployment
}

type NextSessionConfig struct {
	GenerateTitleModel string `yaml:"GenerateTitleModel"`
}
type NextAimeAccountConfig struct {
	BPMToken             string `yaml:"BPMToken"`
	AimeAccountSecretKey string `yaml:"AimeAccountSecretKey"`
	AimeICMAccount       string `yaml:"AimeICMAccount"`
	AimeICMToken         string `yaml:"AimeICMToken"`
}

type NextCanaryVersionRationConfig struct {
	Succeed          int64   `yaml:"Succeed"`
	Failed           int64   `yaml:"Failed"`
	Running          int64   `yaml:"Running"`
	RunningWeight    float64 `yaml:"RunningWeight"`
	CanaryProbeCount int64   `yaml:"CanaryProbeCount"`
}

type NextAgentCanaryVersionRationConfig struct {
	OldVersion NextCanaryVersionRationConfig `yaml:"OldVersion"`
	NewVersion NextCanaryVersionRationConfig `yaml:"NewVersion"`
}

type NextDeployReviewConfig struct {
	InspectInterval             int                                           `yaml:"InspectInterval"`
	FailLimit                   int64                                         `yaml:"FailLimit"`
	DeployUser                  []string                                      `yaml:"DeployUser"`
	CanaryVersionRationConfig   map[string]NextAgentCanaryVersionRationConfig `yaml:"CanaryVersionRationConfig"`
	BaseAllowCopyAgentConfigIDs []string                                      `yaml:"BaseAllowCopyAgentConfigID"`
	BaseAllowCopyAll            bool                                          `yaml:"BaseAllowCopyAll"`
}

type NextToolI18nConfig struct {
	EN map[string]string `yaml:"EN"`
	ZH map[string]string `yaml:"ZH"`
}

type NextAgentScenarioDetectionModelConfig struct {
	Model          string   `yaml:"Model"`
	Temperature    float32  `yaml:"Temperature"`
	MaxTokens      int      `yaml:"MaxTokens"`
	Tag            string   `yaml:"Tag"`
	FallbackModels []string `yaml:"FallbackModels"`
}
type NextAgentScenarioDetectionConfig struct {
	Enabled                bool                                  `yaml:"Enabled"`                // 是否启用场景检测
	DetectionModelConfig   NextAgentScenarioDetectionModelConfig `yaml:"DetectionModelConfig"`   // 用于检测的模型
	LarkChatID             string                                `yaml:"LarkChatID"`             // 飞书群ID
	ScenarioCardTemplateID string                                `yaml:"ScenarioCardTemplateID"` // 场景检测卡片模板ID
	ScenarioDetectPrompt   string                                `yaml:"ScenarioDetectPrompt"`   // 场景检测提示词
}
