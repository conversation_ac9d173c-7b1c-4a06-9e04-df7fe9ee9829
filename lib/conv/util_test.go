package conv

import (
	"reflect"
	"testing"
	"time"

	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
	"gorm.io/plugin/soft_delete"
	"gotest.tools/v3/assert"
)

func TestSoftDeleteToTime(t *testing.T) {
	require.Nil(t, SoftDeleteToTime(soft_delete.DeletedAt(0)))
	now := time.Now()
	require.Equal(t, (&now).Format(time.DateTime), SoftDeleteToTime(soft_delete.DeletedAt(now.UnixNano())).Format(time.DateTime))
}

func TestDefaultAny(t *testing.T) {
	require.Equal(t, "123", DefaultAny[string](any("123")))
	require.Equal(t, "", DefaultAny[string](any(123)))

	require.Equal(t, lo.To<PERSON>tr("123"), DefaultAnyPtr[string]("123"))
	require.Equal(t, (*string)(nil), DefaultAnyPtr[string](123))
}

func TestJSONString(t *testing.T) {
	require.Equal(t, `{"abc":"123","cde":123}`, JSONString(map[string]any{
		"abc": "123",
		"cde": 123,
	}))
}

func TestJSONBytes(t *testing.T) {
	require.Equal(t, []byte(`{"abc":"123","cde":123}`), JSONBytes(map[string]any{
		"abc": "123",
		"cde": 123,
	}))
}

func TestDerefDefaultAny(t *testing.T) {
	require.Equal(t, "", DerefDefaultAny[string](nil))
	require.Equal(t, "", DerefDefaultAny[string](123))
	require.Equal(t, "", DerefDefaultAny[string](lo.ToPtr(123)))
	require.Equal(t, "123", DerefDefaultAny[string](lo.ToPtr("123")))
}

func TestMapStringAny(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want map[string]any
	}{
		{
			name: "Empty string",
			args: args{s: ""},
			want: map[string]any{},
		},
		{
			name: "Valid JSON string",
			args: args{s: `{"key1":"value1","key2":2}`},
			want: map[string]any{"key1": "value1", "key2": 2.0},
		},
		{
			name: "Invalid JSON string",
			args: args{s: `{"key1":"value1",`},
			want: map[string]any{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MapStringAny(tt.args.s); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MapStringAny() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapToStruct(t *testing.T) {
	type Person struct {
		Name string
		Age  int
	}

	var testCases = []struct {
		name     string
		inputMap map[string]interface{}
		expected Person
	}{
		{
			"map with valid keys and values",
			map[string]interface{}{
				"Name": "John",
				"Age":  30,
			},
			Person{Name: "John", Age: 30},
		},
		{
			"map with incomplete keys",
			map[string]interface{}{
				"Name": "Jane",
			},
			Person{Name: "Jane"},
		},
		{
			"map with invalid keys",
			map[string]interface{}{
				"First Name": "Alice",
			},
			Person{},
		},
		{
			"empty map",
			map[string]interface{}{},
			Person{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := MapToStruct[Person](tc.inputMap)

			if result != tc.expected {
				t.Errorf("Unexpected result - expected %+v, but got %+v", tc.expected, result)
			}
		})
	}
}

func TestPtrIfNotZero(t *testing.T) {
	require.Nil(t, PtrIfNotZero(0))
	require.Nil(t, PtrIfNotZero(0.0))
	require.Nil(t, PtrIfNotZero(""))
	require.Equal(t, 123, *PtrIfNotZero(123))
	require.Equal(t, 123.1, *PtrIfNotZero(123.1))
	require.Equal(t, "123", *PtrIfNotZero("123"))
}

func TestMapToStructByJSONTag(t *testing.T) {
	type Person struct {
		Name string `json:"name"`
		Age  int    `json:"age"`
	}

	var testCases = []struct {
		name     string
		inputMap map[string]interface{}
		expected Person
	}{
		{
			"map with valid keys and values",
			map[string]interface{}{
				"name": "John",
				"age":  30,
			},
			Person{Name: "John", Age: 30},
		},
		{
			"map with incomplete keys",
			map[string]interface{}{
				"name": "Jane",
			},
			Person{Name: "Jane"},
		},
		{
			"map with invalid keys",
			map[string]interface{}{
				"First Name": "Alice",
			},
			Person{},
		},
		{
			"empty map",
			nil,
			Person{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := MapToStructByJSONTag[Person](tc.inputMap)
			assert.NilError(t, err)

			if result != tc.expected {
				t.Errorf("Unexpected result - expected %+v, but got %+v", tc.expected, result)
			}
		})
	}
}

func TestConvertFloatToInt(t *testing.T) {
	data := map[string]any{
		"key1": 1.5,
		"key2": []any{2.5, 3.5},
		"key3": map[string]any{
			"subKey": 4.5,
		},
	}
	ConvertFloatToInt(data)
	assert.Equal(t, 1, data["key1"])
	assert.DeepEqual(t, []any{2.5, 3.5}, data["key2"])
	assert.Equal(t, 4.5, data["key3"].(map[string]any)["subKey"])
}

// 定义一个辅助结构体用于测试
type TestOutput struct {
	Time time.Time
}

func Test_DecodeMapstructureWithTimeStringEnabled(t *testing.T) {
	now := time.Now()
	nowFormated := now.Format(time.RFC3339Nano)
	t.Run("NewDecoder fails", func(t *testing.T) {
		input := map[string]string{"Time": nowFormated}
		output := &TestOutput{}
		err := DecodeMapstructureWithTimeStringEnabled(input, *output)
		require.Error(t, err, "Expected an error when NewDecoder fails")
	})

	t.Run("mapstructure.Decode fails", func(t *testing.T) {
		input := map[string]string{"Time": nowFormated}
		output := &TestOutput{}
		err := mapstructure.Decode(input, output)
		require.Error(t, err, "Expected an error when mapstructure.Decode fails")
	})

	t.Run("Normal call", func(t *testing.T) {
		input := map[string]string{"Time": nowFormated}
		output := &TestOutput{}
		err := DecodeMapstructureWithTimeStringEnabled(input, output)
		require.NoError(t, err, "Expected no error for normal call")
		require.True(t, now.Equal(output.Time))
	})
}

type TestFrontMatter struct {
	Title string   `yaml:"title"`
	Tags  []string `yaml:"tags"`
	Date  string   `yaml:"date"`
	Draft bool     `yaml:"draft"`
}

func TestParseYAMLFrontMatter(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    *TestFrontMatter
		expectedDoc string
		expectError bool
	}{
		{
			name: "Valid front matter",
			input: `---
title: Test Article
tags:
  - golang
  - yaml
date: 2023-01-01
draft: false
---
This is the content of the article.
With multiple lines.`,
			expected: &TestFrontMatter{
				Title: "Test Article",
				Tags:  []string{"golang", "yaml"},
				Date:  "2023-01-01",
				Draft: false,
			},
			expectedDoc: "\nThis is the content of the article.\nWith multiple lines.",
			expectError: false,
		},
		{
			name: "Missing closing delimiter",
			input: `---
title: Incomplete
tags:
  - golang
This is content without proper delimiter`,
			expected:    nil,
			expectedDoc: "",
			expectError: true,
		},
		{
			name:        "Empty input",
			input:       "",
			expected:    nil,
			expectedDoc: "",
			expectError: true,
		},
		{
			name: "Invalid YAML",
			input: `---
title: "Unclosed quote
---
Content after invalid YAML`,
			expected:    nil,
			expectedDoc: "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, doc, err := ParseYAMLFrontMatter[TestFrontMatter](tt.input)

			if tt.expectError {
				require.Error(t, err)
				require.Nil(t, result)
				return
			}

			require.NoError(t, err)
			assert.DeepEqual(t, tt.expected, result)
			assert.DeepEqual(t, tt.expectedDoc, doc)
		})
	}
}

func TestDecodeJSON(t *testing.T) {
	type args struct {
		S string
	}
	type T map[string]any // custom map type

	tests := []struct {
		name string
		args args
		typ  any
		want any
	}{
		{
			name: "Valid JSON string",
			args: args{S: `{"key1":"value1","key2":2}`},
			want: T{"key1": "value1", "key2": 2.0},
		},
		{
			name: "Invalid JSON string",
			args: args{S: `{"key1":"value1",`},
			want: T{}, // non nil map
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := DecodeJSON[T](tt.args.S)
			assert.DeepEqual(t, tt.want, got)
		})
	}
	tests2 := []struct {
		name string
		args args
		want any
	}{
		{
			name: "Invalid JSON string",
			args: args{S: `[{"s":"1"}]`},
			want: []args{{S: "1"}},
		},
		{
			name: "Invalid JSON string",
			args: args{S: `[{"s":"1"`},
			want: []args{}, // non nil slice
		},
	}
	for _, tt := range tests2 {
		t.Run(tt.name, func(t *testing.T) {
			got := DecodeJSON[[]args](tt.args.S)
			assert.DeepEqual(t, tt.want, got)
		})
	}

	tests3 := []struct {
		name string
		args args
		want any
	}{
		{
			name: "other json types",
			args: args{S: `"123"`},
			want: "123",
		},
	}
	for _, tt := range tests3 {
		t.Run(tt.name, func(t *testing.T) {
			got := DecodeJSON[string](tt.args.S)
			assert.DeepEqual(t, tt.want, got)
		})
	}
}
