package conv

import (
	"encoding/json"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/lib/mapstructure"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gopkg.in/yaml.v3"
	"gorm.io/plugin/soft_delete"
)

func SoftDeleteToTime(at soft_delete.DeletedAt) *time.Time {
	if at == 0 {
		return nil
	}
	return lo.ToPtr(time.Unix(int64(at/soft_delete.DeletedAt(time.Second)), int64(at%soft_delete.DeletedAt(time.Second))))
}

// DefaultAny tries converting the given v to the type T,
// if not convertable, return the default value of type T.
func DefaultAny[T any](v any) T {
	if res, ok := v.(T); ok {
		return res
	}
	var d T
	return d
}

// DefaultAnyPtr is the same as DefaultAny, but return ptr of the type T.
func DefaultAnyPtr[T any](v any) *T {
	if res, ok := v.(T); ok {
		return &res
	}

	return nil
}

// DerefDefaultAny tries to dereference a ptr into T, and returns zero value of T if v is nil or if it's not *T
func DerefDefaultAny[T any](v any) T {
	res, ok := v.(*T)
	if !ok || res == nil {
		var d T
		return d
	}
	return *res
}

// JSONBytes gets the JSON bytes of the given value, ignoring errors.
func JSONBytes(v any) []byte {
	data, _ := json.Marshal(v)
	return data
}

// JSONString gets the JSON string of the given value, ignoring errors.
func JSONString(v any) string {
	return string(JSONBytes(v))
}

func JSONFormatString(v any) string {
	data, _ := json.MarshalIndent(v, "", "  ")
	return string(data)
}

func DecodeJSON[T any](s string) T {
	var result T
	_ = json.Unmarshal([]byte(s), &result)
	return result
}

// MapStringAny parses the string into map[string]any, ignores any errors.
func MapStringAny(s string) map[string]any {
	r := make(map[string]any)
	_ = json.Unmarshal([]byte(s), &r)
	return r
}

// MapToStruct parses the map into a struct, ignores any errors.
func MapToStruct[T any](v map[string]any) T {
	var result T
	_ = mapstructure.Decode(v, &result)
	return result
}

func MapToStructByJSONTag[T any](v map[string]any) (T, error) {
	var result T
	config := &mapstructure.DecoderConfig{
		Metadata: nil,
		Result:   &result,
		TagName:  "json",
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return result, err
	}

	return result, decoder.Decode(v)
}

// PtrIfNotZero returns a ptr of the given value if the value is not zero.
func PtrIfNotZero[T comparable](v T) *T {
	var zero T
	if v == zero {
		return nil
	}
	return &v
}

// ConvertFloatToInt converts float64 values in the given map to int without recursive.
func ConvertFloatToInt(m map[string]any) map[string]any {
	for k, item := range m {
		switch v := item.(type) {
		case float64:
			m[k] = int(v)
		}
	}
	return m
}

// DecodeMapstructureWithTimeStringEnabled is same as mapstructure.Decode, but enables convert string "2006-01-02T15:04:05.999999999Z07:00" to time.Time.
func DecodeMapstructureWithTimeStringEnabled(input, output any) error {
	decoder, err := mapstructure.NewDecoder(&mapstructure.DecoderConfig{
		DecodeHook: mapstructure.StringToTimeHookFunc(time.RFC3339Nano),
		Result:     output,
	})
	if err != nil {
		return err
	}
	return decoder.Decode(input)
}

func ParseYAMLFrontMatter[T any](s string) (*T, string, error) {
	parts := strings.SplitN(s, "---", 3)
	if len(parts) < 3 {
		return nil, "", errors.New("invalid YAML front matter")
	}
	var result T
	if err := yaml.Unmarshal([]byte(parts[1]), &result); err != nil {
		return nil, "", errors.WithMessage(err, "failed parse yaml")
	}
	return &result, parts[2], nil
}
