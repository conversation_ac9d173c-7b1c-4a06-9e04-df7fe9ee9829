package metrics

import (
	gmetrics "code.byted.org/gopkg/metrics/generic"
)

var AR AgentRuntimeMetricSet

type AgentRuntimeMetricSet struct {
	RunAgentThroughput   gmetrics.DeltaCounterVec[RunAgentTag] `name:"run_agent.throughput"`
	AbortAgentThroughput gmetrics.DeltaCounter                 `name:"abort_agent.throughput"`
	PauseAgentThroughput gmetrics.DeltaCounter                 `name:"pause_agent.throughput"`

	ToolCallThroughput       gmetrics.DeltaCounterVec[ToolCallTag] `name:"tool_call.throughput"`
	ToolCallCost             gmetrics.TimerVec[ToolCallTag]        `name:"tool_call.cost"`
	ToolGitCloneCost         gmetrics.TimerVec[ToolGitCloneTag]    `name:"tool_call.git_clone.cost"`
	ToolBashCmdCost          gmetrics.TimerVec[ToolBashCmdTag]     `name:"tool_call.bash_cmd.cost"`
	ToolCheckFileCost        gmetrics.TimerVec[ToolCheckFileTag]   `name:"tool_call.check_file.cost"`
	ToolCallResultThroughput gmetrics.DeltaCounterVec[ToolCallTag] `name:"tool_call.result.throughput"`

	AgentRunFinishedThroughput gmetrics.DeltaCounterVec[RunAgentTag] `name:"agent_run.finished.throughput"`

	ActorFinishedThroughput gmetrics.DeltaCounterVec[ActorFinishedTag] `name:"actor.finished.throughput"`
	ActorFinishedCost       gmetrics.TimerVec[ActorFinishedTag]        `name:"actor.finished.cost"`

	QueryPreprocessorThroughput    gmetrics.DeltaCounterVec[QueryPreprocessorTag]    `name:"query_preprocessor.throughput"`
	QueryPreprocessorCost          gmetrics.TimerVec[QueryPreprocessorTag]           `name:"query_preprocessor.cost"`
	QueryPreprocessorTimeSaved     gmetrics.TimerVec[QueryPreprocessorTag]           `name:"query_preprocessor.time_saved"`
	AgentKnowledgeRecallThroughput gmetrics.DeltaCounterVec[AgentKnowledgeRecallTag] `name:"agent_knowledge.recall.throughput"`

	PlannerRoundTimeCost gmetrics.Timer `name:"planner.round.time_cost"`
	// total time cost of a user request, excluding the time cost of ask_user
	// user interruption will not be counted
	PlannerTaskTimeCost gmetrics.TimerVec[PlannerTaskTag] `name:"planner.task.time_cost"`

	MemoryCondenserThroughput gmetrics.DeltaCounterVec[MemoryCondenserTag] `name:"memory_condenser.throughput"`
	LLMAbnormalThroughput     gmetrics.DeltaCounterVec[LLMAbnormalTag]     `name:"llm.abnormal.throughput"`

	ActorWebSearcherPhraseCost                    gmetrics.TimerVec[AgentActorWebSearcherPhraseTag]            `name:"actor.websearcher.phrase.cost"`
	ActorWebSearcherLLMCost                       gmetrics.TimerVec[AgentActorWebSearcherLLMTag]               `name:"actor.websearcher.llm.cost"`
	ActorWebSearcherLLMError                      gmetrics.DeltaCounterVec[AgentActorWebSearcherLLMTag]        `name:"actor.websearcher.llm.error"`
	ActorWebSearcherLLMExtract                    gmetrics.DeltaCounterVec[AgentActorWebSearcherLLMExtractTag] `name:"actor.websearcher.llm.extract"`
	ActorWebSearcherReadWeb                       gmetrics.DeltaCounter                                        `name:"actor.websearcher.read_web"`
	ActorWebSearcherReadWebError                  gmetrics.DeltaCounter                                        `name:"actor.websearcher.read_web.error"`
	ActorWebSearcherReadLarkDocMetadata           gmetrics.DeltaCounter                                        `name:"actor.websearcher.read_lark_doc_metadata"`
	ActorWebSearcherReadLarkDocMetadataError      gmetrics.DeltaCounter                                        `name:"actor.websearcher.read_lark_doc_metadata.error"`
	ActorWebSearcherReadLarkDocMetadataRetryError gmetrics.DeltaCounter                                        `name:"actor.websearcher.read_lark_doc_metadata.retry_error"`
}

func InitAgentRuntimeMetric() error {
	// 注册初始化 metricSet 中包含的指标，默认会追加前缀为{P.S.M}.
	// RegisterMetrics would validate the tag keys for metrics.
	err := gmetrics.RegisterMetrics(&AR)
	return err
}

type AgentActorWebSearcherPhraseTag struct {
	Phrase string `key:"phrase"`
}

type AgentActorWebSearcherLLMTag struct {
	Model string `key:"model"`
	Tool  string `key:"tool"`
}

type AgentActorWebSearcherLLMExtractTag struct {
	Conclusion string `key:"conclusion"`
}

type PlannerTaskTag struct {
	FinishReason string `key:"finish_reason"` // idle, interrupted
}

type ToolCallTag struct {
	Tool   string `key:"tool"`
	Status string `key:"status"`
}

type ToolGitCloneTag struct {
	RepoName string `key:"repo_name"`
	Platform string `key:"platform"`
	Shallow  bool   `key:"shallow"`
	Success  bool   `key:"success"`
}

type ToolBashCmdTag struct {
	Command string `key:"command"`
	Timeout int    `key:"timeout"`
	Success bool   `key:"success"`
}

type ToolCheckFileTag struct {
	FileType   string `key:"file_type"`
	ErrorCount int    `key:"error_count"`
}

type LLMAbnormalTag struct {
	Model  string `key:"model"`
	Reason string `key:"reason"` // repetition, token_limit_exceeded, etc.
}

type QueryPreprocessorTag struct {
	Type string `key:"type"` // codebase, lark_doc
}
