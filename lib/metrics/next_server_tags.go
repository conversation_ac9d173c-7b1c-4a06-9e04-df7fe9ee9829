package metrics

type NextServerModule string

const (
	NextServerModuleReplay     NextServerModule = "replay"
	NextServerModuleDeployment NextServerModule = "deployment"
	NextServerModuleArtifact   NextServerModule = "artifact"
	NextServerModuleSession    NextServerModule = "session"
	NextServerModuleTemplate   NextServerModule = "template"
	NextServerModuleActivity   NextServerModule = "activity"
	NextServerModuleLark       NextServerModule = "lark"
	NextServerModuleMCPDebug   NextServerModule = "mcp_debug"
)

type NextServerAgentTag struct {
	Method string `key:"method"`
}

type NextServerPrepareCubeTag struct {
	Role int `key:"role"`
}

type NextServerSessionTag struct {
	Method      string `key:"method"`
	ErrorReason string `key:"error_reason"`
	Username    string `key:"username"`
	FromApp     string `key:"from_app"`
}

type NextServerMCPDebugTag struct {
	Method      string `key:"method"`
	ErrorReason string `key:"error_reason"`
	Username    string `key:"username"`
}

type NextServerSessionStatusTag struct {
	Status string `key:"status"`
}

type NextServerSessionStayTag struct {
	Status   string `key:"status"`
	Duration int    `key:"duration"` // 单位为分钟
}

type NextServerArtifactTag struct {
	Method      string `key:"method"`
	Source      string `key:"source"`
	ErrorReason string `key:"error_reason"`
}

type NextServerModuleTag struct {
	Module      NextServerModule `key:"module"`       // 业务模块
	Method      string           `key:"method"`       // 方法名
	ErrorReason string           `key:"error_reason"` // 错误原因
}

type NextServerEventProcessTag struct {
	Method string `key:"method"`
}

type NextServerDoneEventTag struct {
}

type NextServerDoneEventErrorTag struct {
	ErrorCode int64 `key:"error_code"`
}

type NextServerAgentEventTag struct {
	IsOffline bool   `key:"is_offline"`
	Event     string `key:"event"`
	IsError   bool   `key:"is_error"`
}

type NextServerAgentEventOffsetAbnormalTag struct {
	SessionID string `key:"session_id"`
	Event     string `key:"event"`
	IsOffline bool   `key:"is_offline"`
}

type NextServerConnectAgentTag struct {
	ContainerID string `key:"container_id"`
}

type NextServerPermissionEventTag struct {
	Username string `key:"username"`
	Method   string `key:"method"`
}

type NextServerKnowledgeBaseTag struct {
	IsError       bool                    `key:"is_error"`
	IsNeedUpdated bool                    `key:"is_need_updated"`
	ErrorStage    KnowledgeBaseErrorStage `key:"error_stage"`
}

type KnowledgeBaseErrorStage string

const (
	KnowledgeBaseErrorStageUnknown      KnowledgeBaseErrorStage = ""
	KnowledgeBaseErrorStageSaveToES     KnowledgeBaseErrorStage = "es"
	KnowledgeBaseErrorStageSaveToViking KnowledgeBaseErrorStage = "viking"
	KnowledgeBaseErrorStageSaveToTOS    KnowledgeBaseErrorStage = "tos"
	KnowledgeBaseErrorStageSaveToDB     KnowledgeBaseErrorStage = "db"
	KnowledgeBaseErrorStageHandleData   KnowledgeBaseErrorStage = "data"
)

type NextServerScenarioDetectionTag struct {
	Operation   string `key:"operation"`    // 操作类型：detect, notify
	ErrorReason string `key:"error_reason"` // 错误原因
}
