package cloud_oauth

import (
	"context"
	"time"

	"code.byted.org/paas/cloud-sdk-go/jwt"
	"code.byted.org/paas/oauth2_sdk/client"
	. "code.byted.org/paas/oauth2_sdk/const"
	"code.byted.org/paas/oauth2_sdk/pkg/request"
	"github.com/pkg/errors"

	"code.byted.org/gopkg/logs/v2/log"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/lib/config"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/tcc"
)

type CloudOAuthClientConfig struct {
	ClientID      string `yaml:"client_id"`
	ClientSecret  string `yaml:"client_secret"`
	ServerAccount string `yaml:"server_account"`
	ServerSecret  string `yaml:"server_secret"`
	RedirectUri   string `yaml:"redirect_uri"`
}

// TryNewCloudOAuthClient 尝试启动tccClient并启动OAuthClient
func TryNewCloudOAuthClient() *OAuthClient {
	ctx := context.Background()
	cli, err := tcc.NewClient(config.TCCConfig{PSM: "flow.agentsphere.config"})
	if err != nil {
		log.V1.Error("[TryNewCloudOAuthClient] NewClient failed, err %v", err)
		return nil
	}
	cloudConfig := &CloudOAuthClientConfig{}
	err = cli.GetTCCValue(ctx, "cloud_oauth_config", cloudConfig, libtcc.ConfigFormatYAML)
	if err != nil {
		log.V1.Error("[TryNewCloudOAuthClient] NewClient failed, err %v", err)
		return nil
	}
	return NewCloudOAuthClient(cloudConfig)
}

// NewCloudOAuthClient 可以刷新已过期字节云token的Client，目前写死CN Token
func NewCloudOAuthClient(config *CloudOAuthClientConfig) *OAuthClient {
	host := OAuth2ServerDomainCN
	if iris.CurrentRegion() == iris.RegionI18n {
		host = OAuth2ServerDomainI18N
	}
	return &OAuthClient{
		cli: client.NewClient().WithClientID(config.ClientID).
			WithClientSecret(config.ClientSecret).
			WithScope([]string{"tce", "scm", "faas"}).
			WithRedirectUri(config.RedirectUri). // 用户授权完成回调通知接口
			WithJWTClient(client.NewJwtClient(config.ServerAccount,
				config.ServerSecret,
				request.WithHost(host),
			)).
			WithAuthType(AuthTypeCustom).
			WithLarkAuthReq(false).
			WithRequestOptions(
				request.WithHost(host),
				request.WithTimeout(time.Second*10),
			),
		jwtValidator: jwt.NewValidator([]string{jwt.RegionCN, jwt.RegionI18N}),
	}
}

type OAuthClient struct {
	cli          *client.LarkClient
	jwtValidator jwt.Validator
}

// GenJwtToken 获取用户token 有效期2小时，请不要频繁获取，每次调用都会生成新的token
// https://cloud.bytedance.net/docs/bytecloud/docs/63c4c6df7e9d2a021ec21002/663dd9532ef28e0300d2f00e?x-resource-account=public&x-bc-region-id=bytedance
func (c *OAuthClient) RefreshJwtToken(ctx context.Context, token string) (string, error) {
	playload, err := c.jwtValidator.ValidateSignatureOnly(ctx, token)
	if err != nil {
		return "", errors.Wrapf(err, "failed to validate signature only")
	}
	tokens, err := c.cli.Token(ctx, playload.Username)
	if err != nil {
		return "", err
	}
	return tokens.AccessToken, nil
}
