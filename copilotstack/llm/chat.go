package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"

	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/entity"
	journalentity "code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	customopenai "code.byted.org/devgpt/kiwis/port/openai"
)

// ChatCompletionRequest aligns with OpenAI chat completion API as much as possible.
// Add more params if needed for other LLMs, avoid conflicting.
type ChatCompletionRequest struct {
	// Not all LLM supports these params, check before using.

	// Model name.
	Model string
	// FallbackModels is used for fallback to other models if the first model fails(currently only RateLimit error).
	// If the first model fails, the service will try the fallback models in order.
	// When this feature is used, the invoker should ensure the parameters are also compatible with the fallback models,
	// such as MaxTokens, Prompt token limit, etc.
	//
	// Notice: not all model service supports this, because some model API does not return error code to indicate RateLimit(retriable) error.
	FallbackModels           []string
	RetryTimesBeforeFallback int
	// Messages are messages to be completed by LLM.
	Messages []ChatCompletionMessage
	// MaxTokens indicates the LLM max output tokens, some LLM has a default value, usually 1k.
	MaxTokens   *int // Equal to `MaxNewTokens`.
	Temperature *float32
	TopP        *float32
	TopK        *int
	N           *int
	Stream      bool
	// Stop completion if encounter these strings.
	Stop []string

	FrequencyPenalty *float32 // Not all model services support this.
	PresencePenalty  *float32 // Not all model services support this.

	// MaxNewTokens      *int // Use `MaxTokens`.
	MinNewTokens      *int // Min tokens to generate.
	MaxPromptTokens   *int // Max prompt tokens.
	RepetitionPenalty *float32

	Seed *int // Not all model services support this.

	// psm call by process server
	Psm    string
	PsmEnv string

	// Tools are the tool functions the LLM can use.
	Tools []Tool
	// This can be either a string or an ToolChoice object.
	// Controls which (if any) function is called by the model.
	// none means the model will not call a function and instead generates a message.
	// auto means the model can pick between generating a message or calling a function.
	// Specifying a particular function via {"type": "function", "function": {"name": "my_function"}}
	// forces the model to call that function.
	ToolChoice any

	// Extra options which are not supported by LLM but by this service:

	// SensitiveOpt include config for sensitive function.
	SensitiveOpt SensitiveOpt

	// SessionID is used to associate multiple LLM requests for one user request.
	SessionID string
	// RetryTimes if the first request fails.
	RetryTimes *int
	Account    *authentity.Account
	AppID      string
	// Tag is a string to tag this LLM request.
	Tag string
	// Record indicates if records this request into db for analysis.
	Record bool

	// Variables can be used by some special models, such as IES UT model.
	Variables entity.Variables

	RequestMetadata journalentity.PromptCompletionRequestMetadata
	ContextVariable journalentity.PromptCompletionContextVariable
	EnvMetadata     journalentity.SessionEnvMetadata
	// CustomModelConfig is used to user custom model config.
	// 如果不为空则不会使用 tcc 的配置和模型选择
	CustomModelConfig *config.ModelAuthConfig
	Thinking          *ThinkingOpt

	// ModelArch is used to llm flow
	ModelArch string
}

type ThinkingOpt struct {
	Type            string
	BudgetTokens    int
	IncludeThoughts bool
}

// SensitiveOpt include config for sensitive function.
type SensitiveOpt struct {
	// Scene different scene has different sensitive content filter rules.
	// For example, "generate_unittest" scene will use a different sensitive content filter rule.
	// If not set, the default scene will be used.
	Scene string
	// DisableAntiDirt disables sensitive content filter.
	DisableAntiDirt *bool
	// DisableLLMSecurity disable llm flag intervention
	DisableLLMSecurity *bool

	// EnableAntiDirt4Output enable model output sensitive content filter, default false
	EnableAntiDirt4Output *bool
	// merge chunk size, for AntiDirt and LLMSecurity
	OutputFilterChunkSize *int
	// buffer token size, for AntiDirt and LLMSecurity
	OutputFilterWindowSize *int
}

type CacheControl struct {
	Type string `json:"type"`
}

type ChatMessageImageURL struct {
	URL                string
	Detail             string
	InternalResourceID *string // attention: 非OpenAI协议字段，仅供落库用的 resource_id（一个内部多模态数据存储链接）
}

const (
	ChatMessagePartTypeImageURL = "image_url"
)

type ChatMessagePart struct {
	Type         string
	Text         string
	ImageURL     *ChatMessageImageURL
	CacheControl *CacheControl
}

type ChatCompletionMessage struct {
	Role             string
	Content          string
	ReasoningContent string
	FunctionCall     *FunctionCall

	MultiContent []ChatMessagePart

	// For Role=assistant prompts this may be set to the tool calls generated by the model, such as function calls.
	ToolCalls []ToolCall

	// For Role=tool prompts this should be set to the ID given in the assistant's prior request to call a tool.
	ToolCallID string

	CacheControl *CacheControl
}

type xmlTag struct {
	Name    string
	Attr    []xml.Attr
	Content string
}

func (t *xmlTag) WriteStringBuilder(s *strings.Builder) {
	s.WriteString(fmt.Sprintf("<%s", t.Name))
	for _, attr := range t.Attr {
		s.WriteString(fmt.Sprintf(" %s=%s", attr.Name.Local, strconv.Quote(attr.Value)))
	}
	s.WriteString(">\n")
	s.WriteString(t.Content)
	s.WriteString(fmt.Sprintf("\n</%s>\n", t.Name))
}

// GetMessagesPromptString converts strutured messages to prompt string:
// <system>
// xxxx
// </system>
// <user tool_call_id="xxx">
// </user>
// <assistant with_tool_call="true">
// <content>
// xxxx
// </content>
// <tool_calls>
// []
// </tool_calls>
// </assistant>
// <tools>
// <tool name="xxx">
// <description>
//
//	xxx
//
// </description>
// <parameters>
//
//	{}
//
// </parameters>
// </tool>
// </tools>
func GetMessagesPromptString(msgs []ChatCompletionMessage, tools []Tool, resourceManageService ResourceManageService) string {
	prompt := lo.Reduce(msgs, func(agg *strings.Builder, item ChatCompletionMessage, index int) *strings.Builder {
		tag := xmlTag{
			Name:    item.Role,
			Content: item.Content,
			Attr:    []xml.Attr{},
		}
		if len(item.ToolCallID) > 0 {
			tag.Attr = append(tag.Attr,
				xml.Attr{
					Name:  xml.Name{Local: "with_tool_call"},
					Value: "true",
				},
				xml.Attr{
					Name:  xml.Name{Local: "tool_call_id"},
					Value: item.ToolCallID,
				},
			)
		}
		cacheType := ""
		if item.CacheControl != nil {
			cacheType = item.CacheControl.Type
		}
		for _, part := range item.MultiContent {
			if part.CacheControl != nil {
				cacheType = part.CacheControl.Type
				break
			}
		}
		if len(cacheType) > 0 {
			tag.Attr = append(tag.Attr,
				xml.Attr{
					Name:  xml.Name{Local: "cache_control"},
					Value: cacheType,
				},
			)
		}
		if len(item.MultiContent) > 0 {
			tag.Attr = append(tag.Attr,
				xml.Attr{
					Name:  xml.Name{Local: "type"},
					Value: "multi_content",
				},
			)
			tmpBuilder := strings.Builder{}
			for _, part := range item.MultiContent {
				switch part.Type {
				case "image_url":
					resourceID := ""
					if part.ImageURL.InternalResourceID == nil && resourceManageService != nil {
						// upload image to internal resource storage
						if rid, err := resourceManageService.UploadImage(context.Background(), part.ImageURL.URL); err == nil {
							resourceID = rid
						}
					} else {
						resourceID = lo.FromPtr(part.ImageURL.InternalResourceID)
					}
					tmpBuilder.WriteString(fmt.Sprintf("<image_url>\n[%s]\n</image_url>\n", resourceID))
				case "text":
					tmpBuilder.WriteString(fmt.Sprintf("<text>\n%s\n</text>\n", part.Text))
				}
			}
			tag.Content = tmpBuilder.String()
		}
		if len(item.ToolCalls) > 0 {
			tag.Content = fmt.Sprintf("<content>\n%s\n</content>\n<tool_calls>\n%s\n</tool_calls>", item.Content, conv.JSONFormatString(item.ToolCalls))
		}
		if len(tag.Content) == 0 {
			tag.Attr = append(tag.Attr, xml.Attr{
				Name:  xml.Name{Local: "empty"},
				Value: "true",
			})
		}
		tag.WriteStringBuilder(agg)
		return agg
	}, new(strings.Builder))
	if len(tools) > 0 {
		prompt.WriteString("<tools>\n")
		prompt.WriteString(conv.JSONFormatString(tools) + "\n")
		prompt.WriteString("</tools>")
	}
	return prompt.String()
}

type ToolCall struct {
	// Index is not nil only in chat completion chunk object
	Index    *int         `json:"index,omitempty"`
	ID       string       `json:"id"`
	Type     ToolType     `json:"type"`
	Function FunctionCall `json:"function"`
}

type FunctionCall struct {
	Name string `json:"name"`
	// call function with arguments in JSON format
	Arguments string `json:"arguments"`
}

type Tool struct {
	Type         ToolType           `json:"type"`
	Function     FunctionDefinition `json:"function"`
	CacheControl *CacheControl      `json:"cache_control,omitempty"`
}

type ToolType = string

type ToolChoice struct {
	Type     ToolType     `json:"type"`
	Function ToolFunction `json:"function"`
}

type ToolFunction struct {
	Name string `json:"name"`
}

type FunctionDefinition struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Parameters  any    `json:"parameters"`
}

// Common stop reasons.
const (
	// FinishReasonStop indicates LLM returned full message or caused by the `Stop` sequences given in the request.
	FinishReasonStop = "stop"
	// FinishReasonLength indicates LLM stopped due to the max tokens limit.
	FinishReasonLength = "length"
	// FinishReasonFunctionCall indicates LLM decided to call a tool function.
	FinishReasonFunctionCall = "function_call"
	// FinishReasonContentFilter indicates request contained sensitive message.
	FinishReasonContentFilter         = "content_filter"
	FinishReasonSensitiveContent      = "sensitive_content"
	FinishReasonInputSensitiveContent = "input_sensitive_content"
	FinishReasonPromptTokensOverLimit = "prompt_over_limit"
	FinishInvalidModel                = "invalid_model"
)

type ChatCompletionChoice struct {
	Index   int                   `json:"index"`
	Message ChatCompletionMessage `json:"message"`
	// FinishReason
	// stop: API returned complete message,
	// or a message terminated by one of the stop sequences provided via the stop parameter
	// length: Incomplete model output due to max_tokens parameter or token limit
	// function_call: The model decided to call a function
	// content_filter: Omitted content due to a flag from our content filters
	// null: API response still in progress or incomplete
	FinishReason string `json:"finish_reason"`
}

type ChatCompletionResponse struct {
	ID      string
	Object  string
	Created int64
	Model   string
	Choices []ChatCompletionChoice
	Usage   TokenUsage
}

func (r *ChatCompletionResponse) GetFirstChoiceContent() (res string) {
	if r == nil || len(r.Choices) == 0 {
		return
	}
	return r.Choices[0].Message.Content
}

type ChatCompletionStreamChoiceDelta struct {
	Content          string        `json:"content"`
	ReasoningContent string        `json:"reasoning_content,omitempty"`
	Role             string        `json:"role"`
	FunctionCall     *FunctionCall `json:"function_call,omitempty"`
	ToolCalls        []ToolCall    `json:"tool_calls,omitempty"`
}

type ChatCompletionStreamChoice struct {
	Index        int                             `json:"index"`
	Delta        ChatCompletionStreamChoiceDelta `json:"delta"`
	FinishReason string                          `json:"finish_reason"`
}

type ChatCompletionStreamResponse struct {
	ID      string                       `json:"id"`
	Object  string                       `json:"object"`
	Created int64                        `json:"created"`
	Model   string                       `json:"model"`
	Choices []ChatCompletionStreamChoice `json:"choices"`
	Usage   *TokenUsage                  `json:"usage"`
}

func (r *ChatCompletionStreamResponse) GetFirstChoiceContent() (res string) {
	if r == nil || len(r.Choices) == 0 {
		return
	}
	return r.Choices[0].Delta.Content
}

type TokenUsage struct {
	PromptTokens             int `json:"prompt_tokens"`
	CompletionTokens         int `json:"completion_tokens"`
	ReasoningTokens          int `json:"reasoning_tokens,omitempty"`
	TotalTokens              int `json:"total_tokens"`
	CacheCreationInputTokens int `json:"cache_creation_input_tokens,omitempty"`
	CacheReadInputTokens     int `json:"cache_read_input_tokens,omitempty"`
}

// ChatOpenAIHTTPRequest is the request model that uses OpenAI's protocol but adds other fields.
// 尽可能兼容 OpenAI 协议的同时，支持 prompt cache、reasoning 过程等非 OpenAI 官方协议的字段。
type ChatOpenAIHTTPRequest struct {
	// openai.ChatCompletionRequest
	Model string `json:"model"`
	// MaxTokens The maximum number of tokens that can be generated in the chat completion.
	// This value can be used to control costs for text generated via API.
	// This value is now deprecated in favor of max_completion_tokens, and is not compatible with o1 series models.
	// refs: https://platform.openai.com/docs/api-reference/chat/create#chat-create-max_tokens
	MaxTokens int `json:"max_tokens,omitempty"`
	// MaxCompletionsTokens An upper bound for the number of tokens that can be generated for a completion,
	// including visible output tokens and reasoning tokens https://platform.openai.com/docs/guides/reasoning
	MaxCompletionTokens int                                  `json:"max_completion_tokens,omitempty"`
	Temperature         float32                              `json:"temperature,omitempty"`
	TopP                float32                              `json:"top_p,omitempty"`
	N                   int                                  `json:"n,omitempty"`
	Stream              bool                                 `json:"stream,omitempty"`
	Stop                []string                             `json:"stop,omitempty"`
	PresencePenalty     float32                              `json:"presence_penalty,omitempty"`
	ResponseFormat      *openai.ChatCompletionResponseFormat `json:"response_format,omitempty"`
	Seed                *int                                 `json:"seed,omitempty"`
	FrequencyPenalty    float32                              `json:"frequency_penalty,omitempty"`
	// LogitBias is must be a token id string (specified by their token ID in the tokenizer), not a word string.
	// incorrect: `"logit_bias":{"You": 6}`, correct: `"logit_bias":{"1639": 6}`
	// refs: https://platform.openai.com/docs/api-reference/chat/create#chat/create-logit_bias
	LogitBias map[string]int `json:"logit_bias,omitempty"`
	// LogProbs indicates whether to return log probabilities of the output tokens or not.
	// If true, returns the log probabilities of each output token returned in the content of message.
	// This option is currently not available on the gpt-4-vision-preview model.
	LogProbs bool `json:"logprobs,omitempty"`
	// TopLogProbs is an integer between 0 and 5 specifying the number of most likely tokens to return at each
	// token position, each with an associated log probability.
	// logprobs must be set to true if this parameter is used.
	TopLogProbs int    `json:"top_logprobs,omitempty"`
	User        string `json:"user,omitempty"`
	// Deprecated: use Tools instead.
	Functions []openai.FunctionDefinition `json:"functions,omitempty"`
	// Deprecated: use ToolChoice instead.
	FunctionCall any              `json:"function_call,omitempty"`
	Tools        []ChatOpenAITool `json:"tools,omitempty"`
	// This can be either a string or an ToolChoice object.
	ToolChoice any `json:"tool_choice,omitempty"`
	// Options for streaming response. Only set this when you set stream: true.
	StreamOptions *openai.StreamOptions `json:"stream_options,omitempty"`
	// Disable the default behavior of parallel tool calls by setting it: false.
	ParallelToolCalls any `json:"parallel_tool_calls,omitempty"`

	Messages []ChatOpenAICompletionMessage `json:"messages"`
	// Thinking about the streaming response, we need to store the response in memory.
	Thinking *config.ThinkingConfig `json:"thinking,omitempty"`
}

type ChatOpenAITool struct {
	openai.Tool
	CacheControl *ChatOpenAICacheControl `json:"cache_control,omitempty"`
}

type ChatOpenAICacheControl struct {
	Type string `json:"type"`
}

type ChatOpenAIMessagePart struct {
	openai.ChatMessagePart
	CacheControl *ChatOpenAICacheControl `json:"cache_control,omitempty"`
}

type ChatOpenAICompletionMessage struct {
	Role         string `json:"role"`
	Content      string `json:"content"`
	Refusal      string `json:"refusal,omitempty"`
	MultiContent []ChatOpenAIMessagePart

	// This property isn't in the official documentation, but it's in
	// the documentation for the official library for python:
	// - https://github.com/openai/openai-python/blob/main/chatml.md
	// - https://github.com/openai/openai-cookbook/blob/main/examples/How_to_count_tokens_with_tiktoken.ipynb
	Name string `json:"name,omitempty"`

	FunctionCall *openai.FunctionCall `json:"function_call,omitempty"`

	// For Role=assistant prompts this may be set to the tool calls generated by the model, such as function calls.
	ToolCalls []openai.ToolCall `json:"tool_calls,omitempty"`

	// For Role=tool prompts this should be set to the ID given in the assistant's prior request to call a tool.
	ToolCallID string `json:"tool_call_id,omitempty"`

	CacheControl *ChatOpenAICacheControl `json:"cache_control,omitempty"`

	ReasoningContent string `json:"reasoning_content,omitempty"`
}

func (m ChatOpenAICompletionMessage) MarshalJSON() ([]byte, error) {
	buf := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buf)
	encoder.SetEscapeHTML(false) // disable escaping of HTML entities to avoid encode urls in tool calls and results
	var msg any
	if len(m.MultiContent) > 0 {
		msg = struct {
			Role             string                  `json:"role"`
			Content          string                  `json:"-"`
			Refusal          string                  `json:"refusal,omitempty"`
			MultiContent     []ChatOpenAIMessagePart `json:"content,omitempty"`
			Name             string                  `json:"name,omitempty"`
			FunctionCall     *openai.FunctionCall    `json:"function_call,omitempty"`
			ToolCalls        []openai.ToolCall       `json:"tool_calls,omitempty"`
			ToolCallID       string                  `json:"tool_call_id,omitempty"`
			CacheControl     *ChatOpenAICacheControl `json:"cache_control,omitempty"`
			ReasoningContent string                  `json:"reasoning_content,omitempty"`
		}(m)
	} else {
		msg = struct {
			Role             string                  `json:"role"`
			Content          string                  `json:"content,omitempty"`
			Refusal          string                  `json:"refusal,omitempty"`
			MultiContent     []ChatOpenAIMessagePart `json:"-"`
			Name             string                  `json:"name,omitempty"`
			FunctionCall     *openai.FunctionCall    `json:"function_call,omitempty"`
			ToolCalls        []openai.ToolCall       `json:"tool_calls,omitempty"`
			ToolCallID       string                  `json:"tool_call_id,omitempty"`
			CacheControl     *ChatOpenAICacheControl `json:"cache_control,omitempty"`
			ReasoningContent string                  `json:"reasoning_content,omitempty"`
		}(m)
	}
	err := encoder.Encode(msg)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

func (m *ChatOpenAICompletionMessage) UnmarshalJSON(bs []byte) error {
	msg := struct {
		Role             string `json:"role"`
		Content          string `json:"content,omitempty"`
		Refusal          string `json:"refusal,omitempty"`
		MultiContent     []ChatOpenAIMessagePart
		Name             string                  `json:"name,omitempty"`
		FunctionCall     *openai.FunctionCall    `json:"function_call,omitempty"`
		ToolCalls        []openai.ToolCall       `json:"tool_calls,omitempty"`
		ToolCallID       string                  `json:"tool_call_id,omitempty"`
		CacheControl     *ChatOpenAICacheControl `json:"cache_control,omitempty"`
		ReasoningContent string                  `json:"reasoning_content,omitempty"`
	}{}

	if err := json.Unmarshal(bs, &msg); err == nil {
		*m = ChatOpenAICompletionMessage(msg)
		return nil
	}
	multiMsg := struct {
		Role             string `json:"role"`
		Content          string
		Refusal          string                  `json:"refusal,omitempty"`
		MultiContent     []ChatOpenAIMessagePart `json:"content"`
		Name             string                  `json:"name,omitempty"`
		FunctionCall     *openai.FunctionCall    `json:"function_call,omitempty"`
		ToolCalls        []openai.ToolCall       `json:"tool_calls,omitempty"`
		ToolCallID       string                  `json:"tool_call_id,omitempty"`
		CacheControl     *ChatOpenAICacheControl `json:"cache_control,omitempty"`
		ReasoningContent string                  `json:"reasoning_content,omitempty"`
	}{}
	if err := json.Unmarshal(bs, &multiMsg); err != nil {
		return err
	}
	*m = ChatOpenAICompletionMessage(multiMsg)
	return nil
}

type ChatOpenAIUsage struct {
	openai.Usage
	CacheCreationInputTokens int `json:"cache_creation_input_tokens,omitempty"`
	CacheReadInputTokens     int `json:"cache_read_input_tokens,omitempty"`
	ReasoningTokens          int `json:"reasoning_tokens,omitempty"`
}

// ChatOpenAIHTTPResponse is the response model that uses OpenAI's protocol but adds other fields.
type ChatOpenAIHTTPStreamResponse struct {
	ID                string                   `json:"id"`
	Object            string                   `json:"object,omitempty"`
	Created           int64                    `json:"created"`
	Model             string                   `json:"model"`
	Choices           []ChatOpenAIStreamChoice `json:"choices"`
	SystemFingerprint string                   `json:"system_fingerprint,omitempty"`
	Usage             *ChatOpenAIUsage         `json:"usage,omitempty"`
}

type ChatOpenAIStreamChoice struct {
	Index                int                         `json:"index"`
	Delta                ChatOpenAIStreamChoiceDelta `json:"delta"`
	FinishReason         openai.FinishReason         `json:"finish_reason"`
	ContentFilterResults openai.ContentFilterResults `json:"content_filter_results,omitempty"`
}

type ChatOpenAIStreamChoiceDelta struct {
	Content          string               `json:"content,omitempty"`
	ReasoningContent string               `json:"reasoning_content,omitempty"`
	Role             string               `json:"role,omitempty"`
	FunctionCall     *openai.FunctionCall `json:"function_call,omitempty"`
	ToolCalls        []openai.ToolCall    `json:"tool_calls,omitempty"`
}

type ChatOpenAIHTTPResponse struct {
	ID                string                       `json:"id"`
	Object            string                       `json:"object"`
	Created           int64                        `json:"created"`
	Model             string                       `json:"model"`
	Choices           []ChatOpenAICompletionChoice `json:"choices"`
	Usage             ChatOpenAIUsage              `json:"usage"`
	SystemFingerprint string                       `json:"system_fingerprint"`
}

type ChatOpenAICompletionChoice struct {
	Index   int                         `json:"index"`
	Message ChatOpenAICompletionMessage `json:"message"`
	// FinishReason
	// stop: API returned complete message,
	// or a message terminated by one of the stop sequences provided via the stop parameter
	// length: Incomplete model output due to max_tokens parameter or token limit
	// function_call: The model decided to call a function
	// content_filter: Omitted content due to a flag from our content filters
	// null: API response still in progress or incomplete
	FinishReason openai.FinishReason `json:"finish_reason"`
	LogProbs     *openai.LogProbs    `json:"logprobs,omitempty"`
}

type ChatDeepseekClientRequest struct {
	Model             string    `json:"model"`
	Messages          []Message `json:"messages"`
	IsStream          bool      `json:"is_stream,omitempty"`
	Stream            bool      `json:"stream,omitempty"`
	Temperature       float32   `json:"temperature,omitempty"`
	FrequencyPenalty  float32   `json:"frequency_penalty,omitempty"`
	RepetitionPenalty float32   `json:"repetition_penalty,omitempty"`
	TopP              float32   `json:"top_p,omitempty"`
	TopK              int       `json:"top_k,omitempty"`
	DoSample          bool      `json:"do_sample,omitempty"`
	MaxTokens         int       `json:"max_tokens,omitempty"`
	MaxNewTokens      int       `json:"max_new_tokens,omitempty"`
}

type ChatDeepseekClientResponse struct {
	ID      string   `json:"id"`
	Model   string   `json:"model"`
	Created int      `json:"created"`
	Choices []Choice `json:"choices"`
}

// Choice ...
type Choice struct {
	Message      *Message `json:"message,omitempty"`
	Delta        *Delta   `json:"delta,omitempty"`
	FinishReason string   `json:"finish_reason"`
	FunctionCall string   `json:"function_call"`
}

// Message ...
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type Delta struct {
	Role    *string `json:"role"`
	Content *string `json:"content"`
}

// Convenient functions to convert from OpenAI to our internal model, or vice versa.

func GetChatCompletionsMessagePartFromOpenAI(p openai.ChatMessagePart, _ int) ChatMessagePart {
	part := ChatMessagePart{
		Type:     string(p.Type),
		Text:     p.Text,
		ImageURL: nil,
	}
	if p.ImageURL != nil {
		part.ImageURL = &ChatMessageImageURL{
			URL:    p.ImageURL.URL,
			Detail: string(p.ImageURL.Detail),
		}
	}
	return part
}

func mapNil[T any, R any](collection []T, iteratee func(item T, index int) R) []R {
	if collection == nil {
		return nil
	} else {
		return lo.Map(collection, iteratee)
	}
}

func GetChatCompletionsMessageFromOpenAI(msg openai.ChatCompletionMessage) ChatCompletionMessage {
	m := ChatCompletionMessage{
		Role:         msg.Role,
		Content:      msg.Content,
		FunctionCall: nil,
		MultiContent: mapNil(msg.MultiContent, GetChatCompletionsMessagePartFromOpenAI),
		ToolCalls: lo.Map(msg.ToolCalls, func(item openai.ToolCall, index int) ToolCall {
			tc := ToolCall{
				Index: item.Index,
				ID:    item.ID,
				Type:  ToolType(item.Type),
				Function: FunctionCall{
					Name:      item.Function.Name,
					Arguments: item.Function.Arguments,
				},
			}
			return tc
		}),
		ToolCallID: msg.ToolCallID,
	}
	if msg.FunctionCall != nil {
		m.FunctionCall = &FunctionCall{
			Name:      msg.FunctionCall.Name,
			Arguments: msg.FunctionCall.Arguments,
		}
	}

	return m
}

func GetChatCompletionsRequestFromOpenAI(req openai.ChatCompletionRequest) ChatCompletionRequest {
	r := ChatCompletionRequest{
		Model:          req.Model,
		FallbackModels: nil,
		Messages: lo.Map(req.Messages, func(item openai.ChatCompletionMessage, index int) ChatCompletionMessage {
			return GetChatCompletionsMessageFromOpenAI(item)
		}),
		MaxTokens:        conv.PtrIfNotZero(req.MaxTokens),
		Temperature:      conv.PtrIfNotZero(req.Temperature),
		TopP:             conv.PtrIfNotZero(req.TopP),
		TopK:             nil, // Not supported yet.
		N:                conv.PtrIfNotZero(req.N),
		Stream:           req.Stream,
		Stop:             req.Stop,
		FrequencyPenalty: conv.PtrIfNotZero(req.FrequencyPenalty),
		PresencePenalty:  conv.PtrIfNotZero(req.PresencePenalty),
		Seed:             req.Seed,
		Tools: lo.Map(req.Tools, func(item openai.Tool, index int) Tool {
			t := Tool{
				Type:     ToolType(item.Type),
				Function: FunctionDefinition{},
			}
			if item.Function != nil {
				t.Function = FunctionDefinition{
					Name:        item.Function.Name,
					Description: item.Function.Description,
					Parameters:  item.Function.Parameters,
				}
			}
			return t
		}),
		ToolChoice: req.ToolChoice,
		SensitiveOpt: SensitiveOpt{
			DisableAntiDirt:       lo.ToPtr(true),
			DisableLLMSecurity:    lo.ToPtr(true),
			EnableAntiDirt4Output: lo.ToPtr(false),
		},
		SessionID:       "",
		RetryTimes:      nil,
		Account:         nil,
		AppID:           "",
		Tag:             "",
		Record:          false,
		Variables:       nil,
		RequestMetadata: journalentity.PromptCompletionRequestMetadata{},
		ContextVariable: journalentity.PromptCompletionContextVariable{},
		EnvMetadata:     journalentity.SessionEnvMetadata{},
	}
	return r
}

func GetOpenAIChatCompletionsMessagePart(p ChatMessagePart, _ int) openai.ChatMessagePart {
	part := openai.ChatMessagePart{
		Type: openai.ChatMessagePartType(p.Type),
		Text: p.Text,
	}
	if p.ImageURL != nil {
		part.ImageURL = &openai.ChatMessageImageURL{
			URL:    p.ImageURL.URL,
			Detail: openai.ImageURLDetail(p.ImageURL.Detail),
		}
	}
	return part
}

func GetOpenAIToolCall(t ToolCall, _ int) openai.ToolCall {
	return openai.ToolCall{
		Index: t.Index,
		ID:    t.ID,
		Type:  openai.ToolType(t.Type),
		Function: openai.FunctionCall{
			Name:      t.Function.Name,
			Arguments: t.Function.Arguments,
		},
	}
}

func GetOpenAIChatCompletionsMessage(m ChatCompletionMessage) openai.ChatCompletionMessage {
	msg := openai.ChatCompletionMessage{
		Role:         m.Role,
		Content:      m.Content,
		MultiContent: mapNil(m.MultiContent, GetOpenAIChatCompletionsMessagePart),
		Name:         "",
		FunctionCall: nil,
		ToolCalls:    lo.Map(m.ToolCalls, GetOpenAIToolCall),
		ToolCallID:   m.ToolCallID,
	}
	if m.FunctionCall != nil {
		msg.FunctionCall = &openai.FunctionCall{
			Name:      m.FunctionCall.Name,
			Arguments: m.FunctionCall.Arguments,
		}
	}
	return msg
}

func GetOpenAIChatCompletionsResponse(r ChatCompletionResponse) openai.ChatCompletionResponse {
	resp := openai.ChatCompletionResponse{
		ID:      r.ID,
		Object:  r.Object,
		Created: r.Created,
		Model:   r.Model,
		Choices: lo.Map(r.Choices, func(item ChatCompletionChoice, index int) openai.ChatCompletionChoice {
			return openai.ChatCompletionChoice{
				Index:        item.Index,
				Message:      GetOpenAIChatCompletionsMessage(item.Message),
				FinishReason: openai.FinishReason(item.FinishReason),
			}
		}),
		Usage: openai.Usage{
			PromptTokens:     r.Usage.PromptTokens,
			CompletionTokens: r.Usage.CompletionTokens,
			TotalTokens:      r.Usage.TotalTokens,
		},
		SystemFingerprint: "",
	}
	return resp
}

func GetOpenAIChatCompletionsStreamResponse(r ChatCompletionStreamResponse) openai.ChatCompletionStreamResponse {
	resp := openai.ChatCompletionStreamResponse{
		ID:      r.ID,
		Object:  r.Object,
		Created: r.Created,
		Model:   r.Model,
		Choices: lo.Map(r.Choices, func(item ChatCompletionStreamChoice, index int) openai.ChatCompletionStreamChoice {
			c := openai.ChatCompletionStreamChoice{
				Index: item.Index,
				Delta: openai.ChatCompletionStreamChoiceDelta{
					Content:      item.Delta.Content,
					Role:         item.Delta.Role,
					FunctionCall: nil,
					ToolCalls:    lo.Map(item.Delta.ToolCalls, GetOpenAIToolCall),
				},
				FinishReason:         openai.FinishReason(item.FinishReason),
				ContentFilterResults: openai.ContentFilterResults{},
			}
			if item.Delta.FunctionCall != nil {
				c.Delta.FunctionCall = &openai.FunctionCall{
					Name:      item.Delta.FunctionCall.Name,
					Arguments: item.Delta.FunctionCall.Arguments,
				}
			}
			return c
		}),
		PromptAnnotations: nil,
		Usage:             nil,
	}
	if r.Usage != nil {
		resp.Usage = &openai.Usage{
			PromptTokens:     r.Usage.PromptTokens,
			CompletionTokens: r.Usage.CompletionTokens,
			TotalTokens:      r.Usage.TotalTokens,
		}
	}
	return resp
}

type OpenAIErrorResponse struct {
	Error OpenAIErrorResponseDetail `json:"error"`
}

// ErrorResponse is returned if errors occurs requesting OpenAI API.
// Ref: https://platform.openai.com/docs/guides/error-codes/api-errors
type OpenAIErrorResponseDetail struct {
	Code    any     `json:"code"`
	Message string  `json:"message"`
	Type    string  `json:"type"`
	Param   *string `json:"param"`
}

func GetChatCompletionsMessagePartFromCustomOpenAI(p customopenai.ChatMessagePart, _ int) ChatMessagePart {
	part := ChatMessagePart{
		Type:     p.Type,
		Text:     p.Text,
		ImageURL: nil,
	}
	if p.ImageURL != nil {
		part.ImageURL = &ChatMessageImageURL{
			URL:    p.ImageURL.URL,
			Detail: p.ImageURL.Detail,
		}
	}
	if p.CacheControl != nil {
		part.CacheControl = &CacheControl{
			Type: p.CacheControl.Type,
		}
	}
	return part
}

func GetChatCompletionsMessageFromCustomOpenAI(msg customopenai.ChatCompletionMessage) ChatCompletionMessage {
	m := ChatCompletionMessage{
		Role:             msg.Role,
		Content:          msg.Content,
		ReasoningContent: msg.ReasoningContent,
		FunctionCall:     nil,
		MultiContent:     mapNil(msg.MultiContent, GetChatCompletionsMessagePartFromCustomOpenAI),
		ToolCalls: lo.Map(msg.ToolCalls, func(item customopenai.ToolCall, index int) ToolCall {
			tc := ToolCall{
				Index: item.Index,
				ID:    item.ID,
				Type:  item.Type,
				Function: FunctionCall{
					Name:      item.Function.Name,
					Arguments: item.Function.Arguments,
				},
			}
			return tc
		}),
		ToolCallID: msg.ToolCallID,
	}
	if msg.CacheControl != nil {
		m.CacheControl = &CacheControl{
			Type: msg.CacheControl.Type,
		}
	}

	return m
}

func GetChatCompletionsRequestFromCustomOpenAI(req customopenai.ChatCompletionsRequest) ChatCompletionRequest {
	r := ChatCompletionRequest{
		Model:          req.Model,
		FallbackModels: nil,
		Messages: lo.Map(req.Messages, func(item customopenai.ChatCompletionMessage, index int) ChatCompletionMessage {
			return GetChatCompletionsMessageFromCustomOpenAI(item)
		}),
		MaxTokens:        conv.PtrIfNotZero(req.MaxTokens),
		Temperature:      conv.PtrIfNotZero(req.Temperature),
		TopP:             conv.PtrIfNotZero(req.TopP),
		TopK:             nil, // Not supported yet.
		N:                conv.PtrIfNotZero(req.N),
		Stream:           req.Stream,
		Stop:             req.Stop,
		FrequencyPenalty: conv.PtrIfNotZero(req.FrequencyPenalty),
		PresencePenalty:  conv.PtrIfNotZero(req.PresencePenalty),
		Seed:             req.Seed,
		Tools: lo.Map(req.Tools, func(item customopenai.Tool, index int) Tool {
			t := Tool{
				Type:     item.Type,
				Function: FunctionDefinition{},
			}
			if item.Function != nil {
				t.Function = FunctionDefinition{
					Name:        item.Function.Name,
					Description: item.Function.Description,
					Parameters:  item.Function.Parameters,
				}
			}
			if item.CacheControl != nil {
				t.CacheControl = &CacheControl{
					Type: item.CacheControl.Type,
				}
			}
			return t
		}),
		ToolChoice: req.ToolChoice,
		SensitiveOpt: SensitiveOpt{
			DisableAntiDirt:       lo.ToPtr(true),
			DisableLLMSecurity:    lo.ToPtr(true),
			EnableAntiDirt4Output: lo.ToPtr(false),
		},
		SessionID:       "",
		RetryTimes:      nil,
		Account:         nil,
		AppID:           "",
		Tag:             "",
		Record:          false,
		Variables:       nil,
		RequestMetadata: journalentity.PromptCompletionRequestMetadata{},
		ContextVariable: journalentity.PromptCompletionContextVariable{},
		EnvMetadata:     journalentity.SessionEnvMetadata{},
	}
	if req.Thinking != nil {
		r.Thinking = &ThinkingOpt{
			Type:            req.Thinking.Type,
			BudgetTokens:    req.Thinking.BudgetTokens,
			IncludeThoughts: req.Thinking.IncludeThoughts,
		}
	}
	return r
}

func GetCustomOpenAIChatCompletionsMessagePart(p ChatMessagePart, _ int) customopenai.ChatMessagePart {
	part := customopenai.ChatMessagePart{
		Type: p.Type,
		Text: p.Text,
	}
	if p.ImageURL != nil {
		part.ImageURL = &customopenai.ChatMessageImageURL{
			URL:    p.ImageURL.URL,
			Detail: p.ImageURL.Detail,
		}
	}
	if p.CacheControl != nil {
		part.CacheControl = &customopenai.CacheControl{
			Type: p.CacheControl.Type,
		}
	}
	return part
}

func GetCustomOpenAIToolCall(t ToolCall, _ int) customopenai.ToolCall {
	toolCall := customopenai.ToolCall{
		Index: t.Index,
		ID:    t.ID,
		Type:  t.Type,
		Function: customopenai.FunctionCall{
			Name:      t.Function.Name,
			Arguments: t.Function.Arguments,
		},
	}
	return toolCall
}

func GetCustomOpenAIChatCompletionsMessage(m ChatCompletionMessage) customopenai.ChatCompletionMessage {
	msg := customopenai.ChatCompletionMessage{
		Role:             m.Role,
		Content:          m.Content,
		ReasoningContent: m.ReasoningContent,
		MultiContent:     mapNil(m.MultiContent, GetCustomOpenAIChatCompletionsMessagePart),
		ToolCalls:        lo.Map(m.ToolCalls, GetCustomOpenAIToolCall),
		ToolCallID:       m.ToolCallID,
	}
	if m.CacheControl != nil {
		msg.CacheControl = &customopenai.CacheControl{
			Type: m.CacheControl.Type,
		}
	}
	return msg
}

func GetCustomOpenAIChatCompletionsResponse(r ChatCompletionResponse) customopenai.ChatResponse {
	resp := customopenai.ChatResponse{
		ID:      r.ID,
		Object:  r.Object,
		Created: r.Created,
		Model:   r.Model,
		Choices: lo.Map(r.Choices, func(item ChatCompletionChoice, index int) customopenai.ChatCompletionChoice {
			return customopenai.ChatCompletionChoice{
				Index:        item.Index,
				Message:      GetCustomOpenAIChatCompletionsMessage(item.Message),
				FinishReason: item.FinishReason,
			}
		}),
		Usage: customopenai.Usage{
			PromptTokens:             r.Usage.PromptTokens,
			CompletionTokens:         r.Usage.CompletionTokens,
			TotalTokens:              r.Usage.TotalTokens,
			CacheCreationInputTokens: r.Usage.CacheCreationInputTokens,
			CacheReadInputTokens:     r.Usage.CacheReadInputTokens,
			ReasoningTokens:          r.Usage.ReasoningTokens,
		},
		SystemFingerprint: "",
	}
	return resp
}

func GetCustomOpenAIChatCompletionsStreamResponse(r ChatCompletionStreamResponse) customopenai.ChatStreamResponse {
	resp := customopenai.ChatStreamResponse{
		ID:      r.ID,
		Object:  r.Object,
		Created: r.Created,
		Model:   r.Model,
		Choices: lo.Map(r.Choices, func(item ChatCompletionStreamChoice, index int) customopenai.ChatStreamChoice {
			c := customopenai.ChatStreamChoice{
				Index: item.Index,
				Delta: customopenai.ChatStreamChoiceDelta{
					Content:          item.Delta.Content,
					ReasoningContent: item.Delta.ReasoningContent,
					Role:             item.Delta.Role,
					ToolCalls:        lo.Map(item.Delta.ToolCalls, GetCustomOpenAIToolCall),
				},
				FinishReason: item.FinishReason,
			}
			return c
		}),
		Usage: nil,
	}
	if r.Usage != nil {
		resp.Usage = &customopenai.Usage{
			PromptTokens:             r.Usage.PromptTokens,
			CompletionTokens:         r.Usage.CompletionTokens,
			TotalTokens:              r.Usage.TotalTokens,
			CacheCreationInputTokens: r.Usage.CacheCreationInputTokens,
			CacheReadInputTokens:     r.Usage.CacheReadInputTokens,
			ReasoningTokens:          r.Usage.ReasoningTokens,
		}
	}
	return resp
}
