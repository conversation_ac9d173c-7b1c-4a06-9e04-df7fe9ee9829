package llm

import (
	"bytes"
	"context"
	"encoding/json"
	stderrors "errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"runtime/debug"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cloudwego/hertz/pkg/protocol"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"

	journalservice "code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/stream"
)

type OpenAIHTTPStreamer struct {
	client         *hertz.Client
	req            *ChatCompletionRequest
	streamResponse *stream.RecvChannel[*hertz.Event]
	stats          Stats
	metadata       Metadata
	start          time.Time
	journalService journalservice.JournalService
	httpReqOption  hertz.ReqOption
}

func (a *OpenAIHTTPStreamer) NextChunk(ctx context.Context) (*ChatCompletionStreamResponse, error) {
	var err error
	res := ChatOpenAIHTTPStreamResponse{}

	if a.stats.FirstCountLatency == 0 {
		a.stats.FirstCountLatency = time.Since(a.start)
	}
	a.stats.ChunkCount++

loop:
	for {
		select {
		case event, ok := <-a.streamResponse.DataChannel:
			if !ok {
				return nil, a.error(io.EOF)
			}
			if strings.TrimSpace(string(event.Data)) == "[DONE]" {
				return nil, a.error(io.EOF)
			}
			if err = json.Unmarshal(event.Data, &res); err != nil {
				return nil, a.error(wrapAzureAPIError(err, nil))
			}
			// 只有当有数据时才退出循环
			break loop
		case err, ok := <-a.streamResponse.ErrorChannel:
			if ok && err != nil {
				return nil, a.error(wrapAzureAPIError(err, nil))
			}
		}
	}

	a.stats.TotalTimeCost = time.Since(a.start)
	if res.ID != "" {
		a.stats.ID = res.ID
	}

	result := &ChatCompletionStreamResponse{
		ID:      res.ID,
		Object:  res.Object,
		Created: res.Created,
		Model:   res.Model,
		Choices: lo.Map(res.Choices, func(item ChatOpenAIStreamChoice, index int) ChatCompletionStreamChoice {
			a.stats.ContentRaw += item.Delta.Content
			if len(item.Delta.ReasoningContent) > 0 {
				a.stats.ReasoningContent += item.Delta.ReasoningContent
			}
			if len(item.FinishReason) > 0 {
				a.stats.FinishReason = string(item.FinishReason)
			}
			choice := ChatCompletionStreamChoice{
				Index: item.Index,
				Delta: ChatCompletionStreamChoiceDelta{
					Content:          item.Delta.Content,
					ReasoningContent: item.Delta.ReasoningContent,
					Role:             item.Delta.Role,
				},
				FinishReason: string(item.FinishReason),
			}
			if item.Delta.FunctionCall != nil {
				choice.Delta.FunctionCall = &FunctionCall{
					Name:      item.Delta.FunctionCall.Name,
					Arguments: item.Delta.FunctionCall.Arguments,
				}
			}
			if len(item.Delta.ToolCalls) != 0 {
				choice.Delta.ToolCalls = lo.Map(item.Delta.ToolCalls, func(item openai.ToolCall, index int) ToolCall {
					return ToolCall{
						Index: item.Index,
						ID:    item.ID,
						Type:  ToolType(item.Type),
						Function: FunctionCall{
							Name:      item.Function.Name,
							Arguments: item.Function.Arguments,
						},
					}
				})
			}
			return choice
		}),
	}
	if res.Usage != nil {
		result.Usage = &TokenUsage{
			PromptTokens:             res.Usage.PromptTokens,
			CompletionTokens:         res.Usage.CompletionTokens,
			TotalTokens:              res.Usage.TotalTokens,
			CacheCreationInputTokens: res.Usage.CacheCreationInputTokens,
			CacheReadInputTokens:     res.Usage.CacheReadInputTokens,
			ReasoningTokens:          res.Usage.ReasoningTokens,
		}
		a.stats.PromptTokens = res.Usage.PromptTokens
		a.stats.CompletionTokens = res.Usage.CompletionTokens
		a.stats.TotalTokens = res.Usage.TotalTokens
		a.stats.CacheCreationInputTokens = res.Usage.CacheCreationInputTokens
		a.stats.CacheReadInputTokens = res.Usage.CacheReadInputTokens
		a.stats.ReasoningTokens = res.Usage.ReasoningTokens
	}
	if len(result.Choices) > 0 && len(result.Choices[0].Delta.ToolCalls) > 0 {
		for _, toolCall := range result.Choices[0].Delta.ToolCalls {
			_, idx, ok := lo.FindIndexOf(a.stats.ToolCalls, func(item ToolCall) bool {
				if toolCall.Index != nil && item.Index != nil {
					return *item.Index == *toolCall.Index
				}
				return item.ID == toolCall.ID
			})
			if ok {
				a.stats.ToolCalls[idx].Function.Arguments += toolCall.Function.Arguments
			} else {
				a.stats.ToolCalls = append(a.stats.ToolCalls, toolCall)
			}
		}
	}

	return result, err
}

func (a *OpenAIHTTPStreamer) error(err error) error {
	if err == io.EOF {
		a.stats.TotalTimeCost = time.Since(a.start)
		return io.EOF
	}
	a.stats.Error = err.Error()
	a.stats.Status = journalservice.StatusFail
	a.stats.TotalTimeCost = time.Since(a.start)
	a.stats.FinishReason = FinishReasonStop
	return errors.WithMessage(err, "failed to recv http server response")
}

func parseOpenAICompatibleError(resp *protocol.Response, err error) error {
	if err == nil {
		return nil
	}

	if resp != nil {
		var openaiErr = struct {
			Error *openai.APIError `json:"error,omitempty"`
		}{}
		if resp.StatusCode() == http.StatusTooManyRequests {
			err = stderrors.Join(err, ErrModelRateLimited)
		}
		if parseErr := json.Unmarshal(resp.BodyBytes(), &openaiErr); parseErr == nil && openaiErr.Error != nil {
			return wrapAzureAPIError(stderrors.Join(err, openaiErr.Error), nil)
		}
	}

	return err
}

func (a *OpenAIHTTPStreamer) Aggregation(ctx context.Context) (*ChatCompletionResponse, error) {
	if a.streamResponse != nil {
		return aggregateStream(ctx, a, nil)
	}

	// 不支持流式访问
	body, err := a.makeRequest()
	if err != nil {
		return nil, err
	}
	rawResponse := &ChatOpenAIHTTPResponse{}

	reqOption := a.httpReqOption
	reqOption.Body = &body
	reqOption.Result = &rawResponse
	if resp, err := a.client.DoJSONReq(ctx, http.MethodPost, "", reqOption); err != nil {
		return nil, parseOpenAICompatibleError(resp, err)
	}

	response := &ChatCompletionResponse{
		ID:      rawResponse.ID,
		Object:  rawResponse.Object,
		Created: rawResponse.Created,
		Model:   rawResponse.Model,
		Choices: lo.Map(rawResponse.Choices, func(item ChatOpenAICompletionChoice, index int) ChatCompletionChoice {
			choice := ChatCompletionChoice{
				Index: item.Index,
				Message: ChatCompletionMessage{
					Content:          item.Message.Content,
					ReasoningContent: item.Message.ReasoningContent,
					Role:             item.Message.Role,
				},
				FinishReason: string(item.FinishReason),
			}
			if item.Message.FunctionCall != nil {
				choice.Message.FunctionCall = &FunctionCall{
					Name:      item.Message.FunctionCall.Name,
					Arguments: item.Message.FunctionCall.Arguments,
				}
			}
			if len(item.Message.ToolCalls) != 0 {
				choice.Message.ToolCalls = lo.Map(item.Message.ToolCalls, func(item openai.ToolCall, index int) ToolCall {
					return ToolCall{
						Index: item.Index,
						ID:    item.ID,
						Type:  ToolType(item.Type),
						Function: FunctionCall{
							Name:      item.Function.Name,
							Arguments: item.Function.Arguments,
						},
					}
				})
			}

			return choice
		}),
		Usage: TokenUsage{
			PromptTokens:             rawResponse.Usage.PromptTokens,
			CompletionTokens:         rawResponse.Usage.CompletionTokens,
			TotalTokens:              rawResponse.Usage.TotalTokens,
			CacheCreationInputTokens: rawResponse.Usage.CacheCreationInputTokens,
			CacheReadInputTokens:     rawResponse.Usage.CacheReadInputTokens,
			ReasoningTokens:          rawResponse.Usage.ReasoningTokens,
		},
	}

	if len(response.Choices) > 0 {
		a.stats.ContentRaw = response.Choices[0].Message.Content
		a.stats.ReasoningContent = response.Choices[0].Message.ReasoningContent
		if len(response.Choices[0].FinishReason) > 0 {
			a.stats.FinishReason = response.Choices[0].FinishReason
		}
		if len(response.Choices[0].Message.ToolCalls) > 0 {
			a.stats.ToolCalls = response.Choices[0].Message.ToolCalls
		}
	}

	a.stats.ID = response.ID
	a.stats.FirstCountLatency = time.Since(a.start)
	a.stats.TotalTimeCost = time.Since(a.start)
	a.stats.PromptTokens = response.Usage.PromptTokens
	a.stats.CompletionTokens = response.Usage.CompletionTokens
	a.stats.TotalTokens = response.Usage.TotalTokens
	a.stats.CacheCreationInputTokens = response.Usage.CacheCreationInputTokens
	a.stats.CacheReadInputTokens = response.Usage.CacheReadInputTokens
	a.stats.ReasoningTokens = response.Usage.ReasoningTokens

	a.stats.Status = journalservice.StatusSuccess
	return response, nil
}

func (a *OpenAIHTTPStreamer) Metadata(ctx context.Context) Metadata {
	return a.metadata
}

func (a *OpenAIHTTPStreamer) makeRequest() (*ChatOpenAIHTTPRequest, error) {
	request := &ChatOpenAIHTTPRequest{
		Model: a.req.Model,
		Messages: lo.Map(a.req.Messages, func(item ChatCompletionMessage, index int) ChatOpenAICompletionMessage {
			msg := ChatOpenAICompletionMessage{
				Role:         item.Role,
				Content:      item.Content,
				MultiContent: nil,
				FunctionCall: nil,
				ToolCalls: lo.Map(item.ToolCalls, func(item ToolCall, index int) openai.ToolCall {
					return openai.ToolCall{
						Index: item.Index,
						ID:    item.ID,
						Type:  openai.ToolType(item.Type),
						Function: openai.FunctionCall{
							Name:      item.Function.Name,
							Arguments: item.Function.Arguments,
						},
					}
				}),
				ToolCallID:   item.ToolCallID,
				CacheControl: (*ChatOpenAICacheControl)(item.CacheControl),
			}
			if item.FunctionCall != nil {
				msg.FunctionCall = &openai.FunctionCall{
					Name:      item.FunctionCall.Name,
					Arguments: item.FunctionCall.Arguments,
				}
			}
			if len(item.MultiContent) > 0 {
				msg.MultiContent = lo.Map(item.MultiContent, func(item ChatMessagePart, index int) ChatOpenAIMessagePart {
					part := ChatOpenAIMessagePart{
						ChatMessagePart: openai.ChatMessagePart{
							Type:     openai.ChatMessagePartType(item.Type),
							Text:     item.Text,
							ImageURL: nil,
						},
						CacheControl: (*ChatOpenAICacheControl)(item.CacheControl),
					}
					if item.ImageURL != nil {
						part.ImageURL = &openai.ChatMessageImageURL{
							URL:    item.ImageURL.URL,
							Detail: openai.ImageURLDetail(item.ImageURL.Detail),
						}
					}
					return part
				})
			}
			return msg
		}),
		MaxTokens:        lo.FromPtr(a.req.MaxTokens),
		Temperature:      lo.FromPtr(a.req.Temperature),
		TopP:             lo.FromPtr(a.req.TopP),
		N:                lo.FromPtr(a.req.N),
		Stream:           a.req.Stream,
		Stop:             a.req.Stop,
		Seed:             a.req.Seed,
		PresencePenalty:  lo.FromPtr(a.req.PresencePenalty),
		FrequencyPenalty: lo.FromPtr(a.req.FrequencyPenalty),
		Tools: lo.Map(a.req.Tools, func(item Tool, index int) ChatOpenAITool {
			return ChatOpenAITool{
				Tool: openai.Tool{
					Type: openai.ToolType(item.Type),
					Function: &openai.FunctionDefinition{
						Name:        item.Function.Name,
						Description: item.Function.Description,
						Parameters:  item.Function.Parameters,
					},
				},
				CacheControl: (*ChatOpenAICacheControl)(item.CacheControl),
			}
		}),
		ToolChoice: a.req.ToolChoice,
		// It seems azure GPT models do not support stream options yet, got error if set.
		// StreamOptions: &openai.StreamOptions{
		// 	IncludeUsage: true,
		// },
	}
	if a.req.Thinking != nil {
		request.Thinking = &config.ThinkingConfig{
			Type:            a.req.Thinking.Type,
			BudgetTokens:    a.req.Thinking.BudgetTokens,
			IncludeThoughts: a.req.Thinking.IncludeThoughts,
		}
	}

	return request, nil
}

func (a *OpenAIHTTPStreamer) Close(ctx context.Context) error {
	if a.streamResponse != nil {
		// Consume the remain data to avoid channel leakage.
		go func() {
			defer func() {
				if e := recover(); e != nil {
					log.V1.CtxError(ctx, "panicked while close openai http response: %+v, %s", e, string(debug.Stack()))
				}
			}()
			for range a.streamResponse.DataChannel {
			}
			a.streamResponse.Close()
		}()
	}
	return nil
}

func (a *OpenAIHTTPStreamer) Statistics(ctx context.Context) Stats {
	return a.stats
}

func (s *ModelService) getCustomOpenAIHTTPClient(ctx context.Context, modelConfig *config.ModelAuthConfig) (*hertz.Client, error) {
	modelURL := modelConfig.Endpoint
	cacheKey := getHTTPRequestModelCacheKey(modelConfig)
	cli, ok := s.openaiHTTPCliPool.Get(cacheKey)
	if ok {
		return cli, nil
	}

	parsedURL, err := url.Parse(modelURL)
	if err != nil {
		return nil, errors.WithMessage(err, "invalid model endpoint")
	}

	useSD := false
	sdCluster := ""
	switch parsedURL.Scheme {
	case "http", "https":
	case "sd":
		useSD = true
		q := parsedURL.Query()
		sdCluster = q.Get("cluster")
		q.Del("cluster")
		modelURL = fmt.Sprintf("%s%s", parsedURL.Host, parsedURL.Path)
		if len(q.Encode()) > 0 {
			modelURL += "?" + q.Encode()
		}
	}

	cli, err = hertz.NewClient(modelURL, hertz.NewHTTPClientOption{
		Timeout:      lo.Ternary(modelConfig.Timeout > 0, time.Second*time.Duration(modelConfig.Timeout), 5*time.Minute),
		Debug:        false,
		DisableLog:   false,
		Headers:      nil,
		EnableStream: true,
		UseSD:        useSD,
		SDCluster:    sdCluster,
	})
	if err != nil {
		return nil, err
	}

	s.openaiHTTPCliPool.Add(cacheKey, cli)

	return cli, nil
}

func (s *ModelService) callOpenAICompatibleChatCompletion(
	ctx context.Context,
	req ChatCompletionRequest,
	modelConfig *config.ModelAuthConfig,
) (ChatCompletionStreamResult, error) {
	cli, err := s.getCustomOpenAIHTTPClient(ctx, modelConfig)
	if err != nil {
		return nil, err
	}
	streamer := &OpenAIHTTPStreamer{
		client: cli,
		req:    &req,
		stats: Stats{
			Status: journalservice.StatusSuccess,
		},
		start:          time.Now(),
		journalService: s.journalService,
	}

	header := make(map[string]string)
	if extraHeader := conv.DefaultAny[map[string]string](req.Variables["extra_http_header"]); len(extraHeader) > 0 {
		for k, v := range extraHeader {
			header[k] = v
		}
	}
	if modelConfig.APIKey != "" { // API key for openai API key.
		// openai official way authorization.
		header["Authorization"] = fmt.Sprintf("Bearer %s", modelConfig.APIKey)
	}
	if modelConfig.AccessKey != "" { // AK for azure access key.
		// compatible with azure openai authorization.
		header["API-KEY"] = modelConfig.AccessKey
	}
	body, err := streamer.makeRequest()
	if err != nil {
		return nil, err
	}
	bodyBytes := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(bodyBytes)
	// by default, encoder will escape HTML characters, which will cause urls in prompt being encoded
	// and the model also output the encoded urls, causing user MCP tools to fail.
	encoder.SetEscapeHTML(false)
	encoder.Encode(body)
	streamer.httpReqOption = hertz.ReqOption{
		Body:         bodyBytes.Bytes(),
		Result:       nil,
		Headers:      header,
		ExpectedCode: http.StatusOK,
	}

	if req.Stream {
		resCh, recv := stream.NewChannel[*hertz.Event](respBufferSize)
		streamer.streamResponse = recv
		reqOption := streamer.httpReqOption
		reqOption.Result = resCh

		if resp, err := streamer.client.DoJSONReq(ctx, http.MethodPost, "", reqOption); err != nil {
			return nil, parseOpenAICompatibleError(resp, err)
		}
	}
	return streamer, nil
}
