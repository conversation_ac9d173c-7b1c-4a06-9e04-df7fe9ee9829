package knowledge_graph

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/lib/hertz"
	graph "code.byted.org/overpass/bits_ai_graph/kitex_gen/bits/ai/graph"
	knowledge_graph_rpc "code.byted.org/overpass/bits_ai_graph/rpc/bits_ai_graph"
	"code.byted.org/overpass/common/option/calloption"
)

const (
	searchRepoPath        = "/api/tool/search_repo"
	searchServiceInfoPath = "/api/tool/search_service_info"
)

var defaultBaseURL = "https://bits-graph.byted.org"

// Request-related structs
type RequestArguments struct {
	Query      string   `json:"query"`
	Developers []string `json:"developers"`
}

type RequestMeta struct {
	ProgressToken int `json:"progressToken"`
}

type RequestParams struct {
	Name      string           `json:"name"`
	Arguments RequestArguments `json:"arguments"`
	Meta      RequestMeta      `json:"_meta"`
}

type FullRequest struct {
	Method  string        `json:"method"`
	Params  RequestParams `json:"params"`
	JSONRPC string        `json:"jsonrpc"`
	ID      int           `json:"id"`
}

// Response-related structs
type ResponseResult struct {
	Content []map[string]any `json:"content"`
}

type FullResponse struct {
	JSONRPC string         `json:"jsonrpc"`
	ID      int            `json:"id"`
	Result  ResponseResult `json:"result"`
}

// Client struct holds the hertz client and base URL
type Client struct {
	hertzClient *hertz.Client
}

// NewClient creates and configures a new client for accessing the knowledge graph API.
func NewClient() (*Client, error) {
	c, err := hertz.NewClient(defaultBaseURL, hertz.NewHTTPClientOption{
		Timeout: 10 * time.Minute,
	})
	if err != nil {
		return nil, err
	}
	return &Client{
		hertzClient: c,
	}, nil
}

func (c *Client) doPost(ctx context.Context, apiPath string, req any) (resp *graph.SearchServiceInfoResponse, err error) {
	var fullResponse graph.SearchServiceInfoResponse
	_, err = c.hertzClient.DoJSONReq(ctx, http.MethodPost, apiPath, hertz.ReqOption{
		Body:   req,
		Result: &fullResponse,
	})

	if err != nil {
		return nil, err
	}

	return &fullResponse, nil
}

func (c *Client) SearchServiceInfo(ctx context.Context, spaceID, query, toolName string) (string, error) {
	sceneType, err := graph.SceneTypeFromString(toolName)
	if err != nil {
		return "", err
	}

	req := &graph.SearchServiceInfoRequest{
		AimeSpaceId: spaceID,
		Query:       query,
		Scene:       sceneType,
	}

	resp, err := c.doPost(ctx, searchServiceInfoPath, req)

	if err != nil {
		return "", err
	}

	return resp.GetResult_(), nil
}

func (c *Client) SearchRepo(ctx context.Context, spaceID, query, toolName string, developers []string) (string, error) {
	sceneType, err := graph.SceneTypeFromString(toolName)
	if err != nil {
		return "", err
	}

	req := &graph.SearchRepoRequest{
		AimeSpaceId: spaceID,
		Query:       query,
		Scene:       sceneType,
		Developers:  developers,
	}

	resp, err := c.doPost(ctx, searchRepoPath, req)
	if err != nil {
		return "", err
	}

	return resp.GetResult_(), nil
}

// GetKnowledge sends a query to the knowledge graph API and returns the content from the response.
func (c *Client) GetKnowledge(ctx context.Context, apiPath string, toolName string, query string, developers []string) ([]map[string]any, error) {
	// Construct the request body based on the curl command
	reqBody := FullRequest{
		Method: "tools/call",
		Params: RequestParams{
			Name: toolName,
			Arguments: RequestArguments{
				Query:      query,
				Developers: developers,
			},
			Meta: RequestMeta{
				ProgressToken: 2,
			},
		},
		JSONRPC: "2.0",
		ID:      2,
	}

	var fullResponse FullResponse
	_, err := c.hertzClient.DoJSONReq(ctx, http.MethodPost, apiPath, hertz.ReqOption{
		Body:   &reqBody,
		Result: &fullResponse,
	})

	if err != nil {
		return nil, err
	}

	// Return the content field
	return fullResponse.Result.Content, nil
}

func SearchBam(ctx context.Context, spaceID, query, toolName string) (string, error) {
	sceneType, err := graph.SceneTypeFromString(toolName)
	if err != nil {
		return "", err
	}

	rep := &graph.SearchServiceInfoRequest{
		AimeSpaceId: spaceID,
		Query:       query,
		Scene:       sceneType,
	}

	resp, err := knowledge_graph_rpc.RawCall.SearchServiceInfo(ctx, rep, calloption.WithPSM("bits.ai.graph.service.lf"), calloption.WithRPCTimeout(5*time.Minute))
	if err != nil {
		return "", err
	}

	return resp.GetResult_(), nil
}

func SearchRepo(ctx context.Context, spaceID, query, toolName string, developers []string) (string, error) {
	sceneType, err := graph.SceneTypeFromString(toolName)
	if err != nil {
		return "", err
	}

	rep := &graph.SearchRepoRequest{
		AimeSpaceId: spaceID,
		Query:       query,
		Scene:       sceneType,
		Developers:  developers,
	}

	resp, err := knowledge_graph_rpc.RawCall.SearchRepo(ctx, rep, calloption.WithPSM("bits.ai.graph.service.lf"), calloption.WithRPCTimeout(5*time.Minute))
	if err != nil {
		return "", err
	}

	return resp.GetResult_(), nil
}
