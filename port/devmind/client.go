package devmind

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/lib/hertz"
	"github.com/pkg/errors"
)

const (
	defaultBaseURL = "https://devmind.bytedance.net/api/v1/llm"
	defaultEnv     = "ppe_aime_devmind_0730"
)

// Client is the client for DevMind API
type Client interface {
	// GetStoryMetrics retrieves the list of metrics related to the user
	GetStoryMetrics(ctx context.Context, jwtToken string) (*StoryMetricsData, error)

	// GetBusinessNodes retrieves the list of business line nodes related to the user
	GetBusinessNodes(ctx context.Context, jwtToken string) (*NodesData, error)

	// GetReportNodes retrieves the list of reporting line nodes related to the user
	GetReportNodes(ctx context.Context, jwtToken string) (*NodesData, error)

	// GetCurrentUserNode retrieves the list of people nodes related to the user
	GetCurrentUserNode(ctx context.Context, jwtToken string) (*NodeInfo, error)

	// QueryMetricData retrieves data for a specific metric
	QueryMetricData(ctx context.Context, jwtToken, indexID, timeGranularity, queryDate, nodeID string) (*MetricDataResponseData, error)

	// ManageFavorite creates or deletes a favorite metric
	ManageFavorite(ctx context.Context, jwtToken, collectorID, status string) (*FavoriteResponseData, error)

	// QueryInsightReportData retrieves data for a specific insight report
	QueryInsightReportData(ctx context.Context, jwtToken, indexID, timeGranularity, queryDate string) (*InsightReportDataResponseData, error)

	// QueryPublicDataModel retrieves the list of public data model related to the user
	QueryPublicDataModel(ctx context.Context, jwtToken, sessionID, rawQuery, analysisMetric, analysisDimension, filterCondition string) (*GetPublicDataModelResponse, error)

	// QueryAnalysisMetric retrieves the list of analysis metric related to the user
	QueryAnalysisMetric(ctx context.Context, jwtToken, sessionID, modelID, rawQuery, analysisMetric, analysisDimension, filterCondition string) (*GetAnalysisMetricResponse, error)

	// QueryAnalysisDimension retrieves the list of analysis dimension related to the user
	QueryAnalysisDimension(ctx context.Context, jwtToken, sessionID, modelID, analysisMetric, analysisDimension, filterCondition string) (*GetDimensionByUserResponse, error)

	// QueryChartUrl query charts based on the returned dimensions, metrics, and data models
	QueryChartUrl(ctx context.Context, jwtToken string, request *AimeChartQueryByUser) (*GetChartUrlResponse, error)

	// QueryChartInfo query charts based on the returned dimensions, metrics, and data models
	QueryChartInfo(ctx context.Context, jwtToken, sessionID, url string) (*GetChartInfoByUrlResponse, error)
}

type client struct {
	baseURL string
	env     string
	client  *hertz.Client
}

// ClientOption is the option for creating a new DevMind client
type ClientOption struct {
	BaseURL string
	Env     string
}

// New creates a new DevMind client
func New(opts ...ClientOption) (Client, error) {
	c := &client{
		baseURL: defaultBaseURL,
		//env:     defaultEnv,
	}

	if len(opts) > 0 {
		opt := opts[0]
		if opt.BaseURL != "" {
			c.baseURL = opt.BaseURL
		}
		if opt.Env != "" {
			c.env = opt.Env
		}
	}

	httpClient, err := hertz.NewClient(c.baseURL, hertz.NewHTTPClientOption{
		Timeout: 5 * time.Minute,
		Headers: map[string]string{
			//"x-use-ppe":    "1",
			//"x-tt-env":     c.env,
			"Content-Type": "application/json",
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create HTTP client")
	}

	c.client = httpClient
	return c, nil
}

// NewTestClient creates a new DevMind ppe test client
func NewTestClient(opts ...ClientOption) (Client, error) {
	c := &client{
		baseURL: defaultBaseURL,
		env:     defaultEnv,
	}

	if len(opts) > 0 {
		opt := opts[0]
		if opt.BaseURL != "" {
			c.baseURL = opt.BaseURL
		}
		if opt.Env != "" {
			c.env = opt.Env
		}
	}

	httpClient, err := hertz.NewClient(c.baseURL, hertz.NewHTTPClientOption{
		Timeout: 5 * time.Minute,
		Headers: map[string]string{
			"x-use-ppe":    "1",
			"x-tt-env":     c.env,
			"Content-Type": "application/json",
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create HTTP client")
	}

	c.client = httpClient
	return c, nil
}

// GetStoryMetrics retrieves the list of metrics related to the user
func (c *client) GetStoryMetrics(ctx context.Context, jwtToken string) (*StoryMetricsData, error) {
	response := &StoryMetricsResponse{}
	_, err := c.client.DoJSONReq(ctx, http.MethodGet, "/aime/story", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      time.Minute,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get story metrics")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to get story metrics: %s", response.Errmsg)
	}

	if response.Data == nil {
		return nil, fmt.Errorf("failed to get story metrics: data is nil")
	}

	return response.Data, nil
}

// GetBusinessNodes retrieves the list of business line nodes related to the user
func (c *client) GetBusinessNodes(ctx context.Context, jwtToken string) (*NodesData, error) {
	response := &NodesResponse{}
	_, err := c.client.DoJSONReq(ctx, http.MethodGet, "/aime/node/business", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      time.Minute,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get business nodes")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to get business nodes: %s", response.Errmsg)
	}

	if response.Data == nil {
		return nil, fmt.Errorf("failed to get business nodes: data is nil")
	}

	return response.Data, nil
}

// GetReportNodes retrieves the list of reporting line nodes related to the user
func (c *client) GetReportNodes(ctx context.Context, jwtToken string) (*NodesData, error) {
	response := &NodesResponse{}
	_, err := c.client.DoJSONReq(ctx, http.MethodGet, "/aime/node/report", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      time.Minute,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get report nodes")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to get report nodes: %s", response.Errmsg)
	}

	if response.Data == nil {
		return nil, fmt.Errorf("failed to get report nodes: data is nil")
	}

	return response.Data, nil
}

// GetCurrentUserNode retrieves the list of people nodes related to the user
func (c *client) GetCurrentUserNode(ctx context.Context, jwtToken string) (*NodeInfo, error) {
	response := &NodesResponse{}
	_, err := c.client.DoJSONReq(ctx, http.MethodGet, "/aime/node/people", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      time.Minute,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get people nodes")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to get people nodes: %s", response.Errmsg)
	}

	if response.Data == nil || len(response.Data.NodeList) == 0 {
		return nil, fmt.Errorf("failed to get people nodes: data is nil")
	}

	return &response.Data.NodeList[0], nil
}

// QueryMetricData retrieves data for a specific metric
func (c *client) QueryMetricData(ctx context.Context, jwtToken, indexID, timeGranularity, queryDate, nodeID string) (*MetricDataResponseData, error) {
	response := &MetricDataResponse{}
	_, err := c.client.DoJSONReq(ctx, http.MethodPost, "/aime/tiny_query", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Body: map[string]string{
			"IndexId":         indexID,
			"TimeGranularity": timeGranularity,
			"QueryDate":       queryDate,
			"NodeId":          nodeID,
		},
		Result:  response,
		Timeout: time.Minute,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to query metric data")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to query metric data: %s", response.Errmsg)
	}
	if response.Data == nil {
		return nil, fmt.Errorf("failed to query metric data: data is nil")
	}

	if response.Data.Code != 0 {
		return nil, fmt.Errorf("failed to query metric data: %s", response.Data.Msg)
	}

	return response.Data, nil
}

// ManageFavorite creates or deletes a favorite metric
func (c *client) ManageFavorite(ctx context.Context, jwtToken, collectorID, status string) (*FavoriteResponseData, error) {
	response := &FavoriteResponse{}
	_, err := c.client.DoJSONReq(ctx, http.MethodPost, "/collector/create_or_delete", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Body: map[string]interface{}{
			"version": 1,
			"collector_config": map[string]string{
				"collector_id": collectorID,
				"status":       status,
			},
		},
		Result:  response,
		Timeout: time.Minute,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to manage favorite")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to manage favorite: %s", response.Errmsg)
	}

	if response.Data == nil {
		return nil, fmt.Errorf("failed to manage favorite: data is nil")
	}

	return response.Data, nil
}

// QueryInsightReportData retrieves data for a specific insight report
func (c *client) QueryInsightReportData(ctx context.Context, jwtToken, indexID, timeGranularity, queryDate string) (*InsightReportDataResponseData, error) {
	response := &InsightReportDataResponse{}
	_, err := c.client.DoJSONReq(ctx, http.MethodPost, "/aime/insight_report/query", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Body: map[string]string{
			"IndexId":         indexID,
			"TimeGranularity": timeGranularity,
			"QueryDate":       queryDate,
		},
		Result:  response,
		Timeout: 5 * time.Minute,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to query insight report data")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to query insight report data: %s", response.Errmsg)
	}
	if response.Data == nil {
		return nil, fmt.Errorf("failed to query insight report data: data is nil")
	}

	if response.Data.Code != 0 {
		return nil, fmt.Errorf("failed to query insight report data: %s", response.Data.Msg)
	}

	switch response.Data.ErrType {
	case "query_err":
		return nil, fmt.Errorf("failed to query insight report data: query_err")
	case "no_permission":
		return nil, fmt.Errorf("failed to query insight report data: no_permission")
	case "query_param_err":
		return nil, fmt.Errorf("failed to query insight report data: query_param_err")
	case "user_nil":
		return nil, fmt.Errorf("failed to query insight report data: user_nil")
	}

	return response.Data, nil
}

func (c *client) QueryPublicDataModel(ctx context.Context, jwtToken, sessionID string, rawQuery, analysisMetric, analysisDimension, filterCondition string) (*GetPublicDataModelResponse, error) {
	response := &QueryPublicDataModelResponse{}
	url := fmt.Sprintf("/aime/model")
	_, err := c.client.DoJSONReq(ctx, http.MethodPost, url, hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      5 * time.Minute,
		Body: map[string]string{
			"raw_query":          rawQuery,
			"session_id":         sessionID,
			"analysis_metric":    analysisMetric,
			"analysis_dimension": analysisDimension,
			"filter_condition":   filterCondition,
		},
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to query the public data model")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to query the public data model: %s", response.Errmsg)
	}
	if response.Data == nil {
		return nil, fmt.Errorf("failed to query the public data model: data is nil")
	}

	if response.Data.Code != 0 {
		return nil, fmt.Errorf("failed to query the public data model: %s", response.Data.Msg)
	}

	switch response.Data.ErrType {
	case "query_err":
		return nil, fmt.Errorf("failed to query the public data model: query_err")
	case "no_permission":
		return nil, fmt.Errorf("failed to query the public data model: no_permission")
	case "query_param_err":
		return nil, fmt.Errorf("failed to query the public data model: query_param_err")
	case "user_nil":
		return nil, fmt.Errorf("failed to query the public data model: user_nil")
	}

	return response.Data, nil
}

func (c *client) QueryAnalysisMetric(ctx context.Context, jwtToken, sessionID, modelID, rawQuery, analysisMetric, analysisDimension, filterCondition string) (*GetAnalysisMetricResponse, error) {
	response := &QueryAnalysisMetricResponse{}
	url := "/aime/model/metric"
	_, err := c.client.DoJSONReq(ctx, http.MethodPost, url, hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      5 * time.Minute,
		Body: map[string]string{
			"model_id":           modelID,
			"session_id":         sessionID,
			"analysis_metric":    analysisMetric,
			"analysis_dimension": analysisDimension,
			"filter_condition":   filterCondition,
			"raw_query":          rawQuery,
		},
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to query analysis metric data")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to query analysis metric data: %s", response.Errmsg)
	}
	if response.Data == nil {
		return nil, fmt.Errorf("failed to query analysis metric data: data is nil")
	}

	if response.Data.Code != 0 {
		return nil, fmt.Errorf("failed to query analysis metric data: %s", response.Data.Msg)
	}

	switch response.Data.ErrType {
	case "query_err":
		return nil, fmt.Errorf("failed to query analysis metric: query_err")
	case "no_permission":
		return nil, fmt.Errorf("failed to query analysis metric: no_permission")
	case "query_param_err":
		return nil, fmt.Errorf("failed to query analysis metric data: query_param_err")
	case "user_nil":
		return nil, fmt.Errorf("failed to query analysis metric data: user_nil")
	}

	return response.Data, nil
}

func (c *client) QueryAnalysisDimension(ctx context.Context, jwtToken, sessionID, modelID string, analysisMetric, analysisDimension, filterCondition string) (*GetDimensionByUserResponse, error) {
	response := &QueryAnalysisDimensionResponse{}
	url := fmt.Sprintf("/aime/model/dimension?model_id=%s", modelID)
	_, err := c.client.DoJSONReq(ctx, http.MethodPost, url, hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      5 * time.Minute,
		Body: map[string]string{
			"model_id":           modelID,
			"session_id":         sessionID,
			"analysis_metric":    analysisMetric,
			"analysis_dimension": analysisDimension,
			"filter_condition":   filterCondition,
		},
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to query analysis dimension data")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to query analysis dimension data: %s", response.Errmsg)
	}
	if response.Data == nil {
		return nil, fmt.Errorf("failed to query analysis dimension data: data is nil")
	}

	if response.Data.Code != 0 {
		return nil, fmt.Errorf("failed to query analysis dimension data: %s", response.Data.Msg)
	}

	switch response.Data.ErrType {
	case "query_err":
		return nil, fmt.Errorf("failed to query analysis dimension: query_err")
	case "no_permission":
		return nil, fmt.Errorf("failed to query analysis dimension: no_permission")
	case "query_param_err":
		return nil, fmt.Errorf("failed to query analysis dimension data: query_param_err")
	case "user_nil":
		return nil, fmt.Errorf("failed to query analysis dimension data: user_nil")
	}

	return response.Data, nil
}

func (c *client) QueryChartUrl(ctx context.Context, jwtToken string, request *AimeChartQueryByUser) (*GetChartUrlResponse, error) {
	response := &QueryChartUrlResponse{}
	url := fmt.Sprintf("/aime/chart/query")
	_, err := c.client.DoJSONReq(ctx, http.MethodPost, url, hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      5 * time.Minute,
		Body:         request,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to query chart data")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to query chart data: %s", response.Errmsg)
	}
	if response.Data == nil {
		return nil, fmt.Errorf("failed to query chart data: data is nil")
	}

	if response.Data.Code != 0 {
		return nil, fmt.Errorf("failed to query chart data: %s", response.Data.Msg)
	}

	switch response.Data.ErrType {
	case "query_err":
		return nil, fmt.Errorf("failed to query chart data: query_err")
	case "no_permission":
		return nil, fmt.Errorf("failed to query chart data: no_permission")
	case "query_param_err":
		return nil, fmt.Errorf("failed to query chart data: query_param_err")
	case "user_nil":
		return nil, fmt.Errorf("failed to query chart data: user_nil")
	}

	return response.Data, nil
}

func (c *client) QueryChartInfo(ctx context.Context, jwtToken, sessionID, chartUrl string) (*GetChartInfoByUrlResponse, error) {
	response := &QueryChartInfoResponse{}
	url := fmt.Sprintf("/aime/chart_info?chart_url=%s&session_id=%s", chartUrl, sessionID)
	_, err := c.client.DoJSONReq(ctx, http.MethodGet, url, hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Result:       response,
		Timeout:      5 * time.Minute,
		Headers: map[string]string{
			"x-jwt-token": jwtToken,
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to query chart data")
	}

	if response.Errno != 200 {
		return nil, fmt.Errorf("failed to query chart data: %s", response.Errmsg)
	}
	if response.Data == nil {
		return nil, fmt.Errorf("failed to query chart data: data is nil")
	}

	if response.Data.Code != 0 {
		return nil, fmt.Errorf("failed to query chart data: %s", response.Data.Msg)
	}

	switch response.Data.ErrType {
	case "query_err":
		return nil, fmt.Errorf("failed to query chart data: query_err")
	case "no_permission":
		return nil, fmt.Errorf("failed to query chart data: no_permission")
	case "query_param_err":
		return nil, fmt.Errorf("failed to query chart data: query_param_err")
	case "user_nil":
		return nil, fmt.Errorf("failed to query chart data: user_nil")
	}

	return response.Data, nil
}
