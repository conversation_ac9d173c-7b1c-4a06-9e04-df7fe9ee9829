package devmind

// BaseResponse represents the common response structure from DevMind API
type BaseResponse struct {
	Errno  int    `json:"errno"`
	Errmsg string `json:"errmsg"`
}

// StoryInfo represents a story metric information
type StoryInfo struct {
	StoryID           string   `json:"story_id"`
	StoryType         string   `json:"story_type"`
	Name              string   `json:"name"`
	CnName            string   `json:"cn_name"`
	Description       string   `json:"description"`
	CalcCaliber       string   `json:"calc_caliber"`
	Level             string   `json:"level"`
	AnalysisDirection string   `json:"analysis_direction"`
	RdStageActivity   []string `json:"rd_stage_activity"`
	Owners            []string `json:"Owners"`
}

// StoryMetricsData represents the data field in the story metrics response
type StoryMetricsData struct {
	StoryInfoList []StoryInfo `json:"story_info_list"`
}

// StoryMetricsResponse represents the response from GetStoryMetrics
type StoryMetricsResponse struct {
	BaseResponse
	Data *StoryMetricsData `json:"data"`
}

// NodeInfo represents a node information (business or report)
type NodeInfo struct {
	NodeID       string `json:"node_id"`
	NodeName     string `json:"node_name"`
	NodeType     string `json:"node_type"`
	NodeNamePath string `json:"node_name_path"`
}

// NodesData represents the data field in the nodes response
type NodesData struct {
	NodeList []NodeInfo `json:"node_list"`
}

// NodesResponse represents the response from GetBusinessNodes and GetReportNodes
type NodesResponse struct {
	BaseResponse
	Data *NodesData `json:"data"`
}

// CardData represents the card data in the metric data response
type CardData struct {
	Value              float64 `json:"value"`
	BaselineData       float64 `json:"baseline_data"`
	PeriodDiffValue    float64 `json:"period_diff_value"`
	BaselineConclusion string  `json:"baseline_conclusion"`
	HistoryConclusion  string  `json:"history_conclusion"`
	FormulaDesc        string  `json:"formula_desc"`
	ValueStr           string  `json:"value_str"`
}

// RequestData represents the request data in the metric data response
type RequestData struct {
	IndexName string `json:"index_name"`
	NodeName  string `json:"node_name"`
	QueryDate string `json:"query_date"`
}

// RuleConclusion represents the rule conclusion in the metric data response
type RuleConclusion struct {
	Conclusion string `json:"conclusion"`
}

// ExpertAnalysis represents the expert analysis in the metric data response
type ExpertAnalysis struct {
	Analysis string `json:"analysis"`
}

// ImproveTasks represents the improve tasks in the metric data response
type ImproveTasks struct {
	UnfinishedTasks []ImproveTask `json:"unfinished_tasks"`
	FinishedTasks   []ImproveTask `json:"finished_tasks"`
}

type ImproveTask struct {
	TaskName string `json:"task_name"`
	TaskDesc string `json:"task_desc"`
	//TaskOwners    []string `json:"task_owners"`
	//TaskFollowers []string `json:"task_followers"`
	TaskLevel    string `json:"task_level"`
	ExpStartTime string `json:"exp_start_time"`
	ExpEndTime   string `json:"exp_end_time"`
}

// ContextVariables represents the context variables in the related recommend
type ContextVariables struct {
	IndexID         string `json:"IndexId"`
	TimeGranularity string `json:"TimeGranularity"`
	QueryDate       string `json:"QueryDate"`
	NodeID          string `json:"NodeId"`
}

// RelatedRecommend represents a related recommendation in the metric data response
type RelatedRecommend struct {
	Text             string           `json:"text"`
	ContextVariables ContextVariables `json:"context_variables"`
}

// MetricDataResponseData represents the data field in the metric data response
type MetricDataResponseData struct {
	CardData         CardData           `json:"card_data"`
	RequestData      RequestData        `json:"request_data"`
	RuleConclusion   RuleConclusion     `json:"rule_conclusion"`
	ExpertAnalysis   ExpertAnalysis     `json:"expert_analysis"`
	ImproveTasks     ImproveTasks       `json:"improve_tasks"`
	RelatedRecommend []RelatedRecommend `json:"related_recommend"`
	URL              string             `json:"url"`
	Code             int                `json:"code"`
	Msg              string             `json:"msg"`
}

// MetricDataResponse represents the response from QueryMetricData
type MetricDataResponse struct {
	BaseResponse
	Data *MetricDataResponseData `json:"data"`
}

// FavoriteResponseData represents the data field in the favorite response
type FavoriteResponseData struct {
	CollectorID   string `json:"collector_id"`
	CollectorType string `json:"collector_type"`
	CreateTime    string `json:"create_time"`
	UpdateTime    string `json:"update_time"`
	Operator      string `json:"operator"`
	Status        string `json:"status"`
}

// FavoriteResponse represents the response from ManageFavorite
type FavoriteResponse struct {
	BaseResponse
	Data *FavoriteResponseData `json:"data"`
}

// InsightReportDataResponseData represents the data field in the insight report data response
type InsightReportDataResponseData struct {
	ReportInfo            AimeReportInfo        `json:"report_info"`
	ReportMetricQueryInfo []AimeMetricQueryInfo `json:"report_metric_query_info"`
	Code                  int                   `json:"code"`
	Msg                   string                `json:"msg"`
	ErrType               string                `json:"err_type"`
}

type AimeMetricQueryInfo struct {
	MetricId                string                `json:"metric_id"`
	MetricType              string                `json:"metric_type"`
	MetricName              string                `json:"metric_name"`
	MetricDescription       string                `json:"metric_description"`
	MetricCaliber           string                `json:"metric_caliber"`
	MetricAnalysisDirection string                `json:"metric_analysis_direction"`
	MetricDataUnit          AimeMetricDataUnit    `json:"metric_data_unit,omitempty"`
	MetricQueryDataList     []AimeMetricQueryData `json:"metric_query_data_list"`
}

// AimeReportNodeInfo represents the node info in the report info
type AimeReportNodeInfo struct {
	NodeID   string `json:"node_id"`
	NodeName string `json:"node_name"`
	NodeType string `json:"node_type"`
}

// AimeReportInfo represents the report info in the insight report data response
type AimeReportInfo struct {
	ReportID              string             `json:"report_id"`
	ReportName            string             `json:"report_name"`
	ReportTimeRange       AimeTimeRange      `json:"report_time_range"`
	ReportType            string             `json:"report_type"`
	ReportGranularityType string             `json:"report_granularity_type"`
	ReportManagers        string             `json:"report_managers"`
	ReportGenerateCycle   string             `json:"report_generate_cycle"`
	ReportNodeInfo        AimeReportNodeInfo `json:"report_node_info"`
	ReportURL             string             `json:"report_url"`
}

// AimeTimeRange represents the time range in the report info
type AimeTimeRange struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}

// ReportMetricData represents the metric data in the insight report data response
type ReportMetricData struct {
	CardData       CardData       `json:"card_data"`
	RequestData    RequestData    `json:"request_data"`
	RuleConclusion RuleConclusion `json:"rule_conclusion"`
	ExpertAnalysis ExpertAnalysis `json:"expert_analysis"`
	ImproveTasks   ImproveTasks   `json:"improve_tasks"`
}

// InsightReportDataResponse represents the response from QueryInsightReportData
type InsightReportDataResponse struct {
	BaseResponse
	Data *InsightReportDataResponseData `json:"data"`
}

type AimeMetricQueryData struct {
	CardGraph       *AimeChartData `json:"card_graph,omitempty"`
	LineGraph       *AimeChartData `json:"line_graph,omitempty"`
	ChartSheet      *AimeSheetData `json:"chart_sheet,omitempty"`
	BarGraph        *AimeChartData `json:"bar_graph,omitempty"`
	DoubleAxisGraph *AimeChartData `json:"double_axis_graph,omitempty"`
	PieGraph        []AimeCardData `json:"pie_graph,omitempty"`
	BarRowGraph     *AimeChartData `json:"bar_row_graph,omitempty"`
	GraphType       string         `json:"graph_type"`
}

type AimeCardData struct {
	Name  string  `json:"name"`
	Value float64 `json:"value"`
}

type AimeChartData struct {
	XAxis              []string           `json:"x_axis"`
	DataList           []AimeChartSubData `json:"data_list"`
	BaselineConclusion string             `json:"baseline_conclusion"`
	HistoryConclusion  string             `json:"history_conclusion"`
	AnalysisDimensions *string            `json:"analysis_dimensions"`
}

type AimeChartSubData struct {
	Name              string   `json:"name"`
	SubDataList       []string `json:"sub_data_list"`
	Avg               string   `json:"avg"`
	Sum               string   `json:"sum"`
	BaselineValueList []string `json:"baseline_value_list"`
}

type AimeSheetData struct {
	Titles   []AimeSheetTitle     `json:"titles"`
	DataList [][]AimeSheetSubData `json:"data_list"`
}

type AimeSheetTitle struct {
	Title    string `json:"title"`
	IsMetric bool   `json:"is_metric"`
}

type AimeSheetSubData struct {
	Value           string   `json:"value"`
	YearDiffValue   *float64 `json:"year_diff_value,omitempty"`
	YearDiffRatio   *float64 `json:"year_diff_ratio,omitempty"`
	PeriodDiffValue *float64 `json:"period_diff_value,omitempty"`
	PeriodDiffRatio *float64 `json:"period_diff_ratio,omitempty"`
	ProportionRatio *float64 `json:"proportion_ratio,omitempty"`
	BaselineValue   *float64 `json:"baseline_value,omitempty"`
}

type AimeMetricDataUnit struct {
	DataUnitType string `json:"data_unit_type"`
	Suffix       string `json:"suffix"`
	DecimalPlace int32  `json:"decimal_place"`
}

// QueryAnalysisDimensionResponse represents the response from QueryAnalysisDimension 维度信息
type QueryAnalysisDimensionResponse struct {
	BaseResponse
	Data *GetDimensionByUserResponse `json:"data"`
}

// GetDimensionByUserResponse represents the data field in the analysis dimension response
type GetDimensionByUserResponse struct {
	PublicRecommendDimensionList          []AimeSearchDimension `json:"public_recommend_dimension_list"`
	PublicRecommendConditionDimensionList []AimeSearchDimension `json:"public_recommend_condition_dimension_list"`
	PrivateDimList                        []AimeSearchDimension `json:"private_dim_list"`
	Code                                  int                   `json:"code"`
	Msg                                   string                `json:"msg"`
	ErrType                               string                `json:"err_type"`
	LogID                                 string                `json:"log_id"`
}

// AimeSearchDimension represents an analysis dimension information
type AimeSearchDimension struct {
	DimensionID           string   `json:"dimension_id"`
	Name                  string   `json:"name"`
	CnName                string   `json:"cn_name"`
	Description           string   `json:"description"`
	DimensionType         string   `json:"dimension_type"`
	Sql                   string   `json:"sql"`
	Owners                []string `json:"owners"`
	IsMeasureObj          bool     `json:"is_measure_obj"`
	MeasureObjID          *string  `json:"measure_obj_id"`
	PublicRecommendScore  *float32 `json:"public_recommend_score"`
	PublicRecommendReason *string  `json:"public_recommend_reason"`
	HeatCount             *string  `json:"heat_count,omitempty"`
}

// QueryPublicDataModelResponse represents the response from QueryPublicDataModel 公共数据模型
type QueryPublicDataModelResponse struct {
	BaseResponse
	Data *GetPublicDataModelResponse `json:"data"`
}

// GetPublicDataModelResponse represents the data field in the public data model response
type GetPublicDataModelResponse struct {
	PublicRecommendModelList []AimeSearchModel `json:"public_recommend_model_list"`
	Code                     int               `json:"code"`
	Msg                      string            `json:"msg"`
	ErrType                  string            `json:"err_type"`
	LogID                    string            `json:"log_id"`
}

// AimeSearchModel represents a public data model information
type AimeSearchModel struct {
	ModelID               string   `json:"model_id"`
	Name                  string   `json:"name"`
	CnName                string   `json:"cn_name"`
	Description           string   `json:"description"`
	ModelType             string   `json:"model_type"`
	Owners                []string `json:"owners"`
	PublicRecommendScore  float32  `json:"public_recommend_score"`
	PublicRecommendReason string   `json:"public_recommend_reason"`
	HeatCount             *string  `json:"heat_count,omitempty"`
}

// QueryAnalysisMetricResponse represents the response from QueryAnalysisMetric 分析指标
type QueryAnalysisMetricResponse struct {
	BaseResponse
	Data *GetAnalysisMetricResponse `json:"data"`
}

// GetAnalysisMetricResponse represents the data field in the analysis metric response
type GetAnalysisMetricResponse struct {
	PublicRecommendMetricList []AimeSearchMetric `json:"public_recommend_metric_list"`
	PrivateMetricList         []AimeSearchMetric `json:"private_metric_list"`
	Code                      int                `json:"code"`
	Msg                       string             `json:"msg"`
	ErrType                   string             `json:"err_type"`
	LogID                     string             `json:"log_id"`
}

// AimeSearchMetric represents a analysis metric information
type AimeSearchMetric struct {
	MetricID              string   `json:"metric_id"`
	Name                  string   `json:"name"`
	CnName                string   `json:"cn_name"`
	Description           string   `json:"description"`
	Sql                   string   `json:"sql"`
	DataType              string   `json:"data_type"`
	Owners                []string `json:"owners"`
	PublicRecommendScore  float32  `json:"public_recommend_score"`
	PublicRecommendReason string   `json:"public_recommend_reason"`
	HeatCount             *string  `json:"heat_count,omitempty"`
}

// QueryChartUrlResponse represents the response from QueryChart 查询图表链接
type QueryChartUrlResponse struct {
	BaseResponse
	Data *GetChartUrlResponse `json:"data"`
}

// AimeChartQueryByUser represents the data field in the chart request
type AimeChartQueryByUser struct {
	SessionID         string         `json:"session_id"`
	AnalysisMetric    string         `json:"analysis_metric"`
	AnalysisDimension string         `json:"analysis_dimension"`
	FilterCondition   string         `json:"filter_condition"`
	GetChartByUser    GetChartByUser `json:"query_request"`
}

type GetChartByUser struct {
	ModelID                string                    `json:"model_id"`
	MetricList             []AimeQueryChartMetric    `json:"metric_list"`
	DimensionList          []AimeQueryChartDimension `json:"dimension_list"`
	DimensionConditionList []AimeQueryChartDimension `json:"dimension_condition_list"`

	ChartType string `json:"chart_type,omitempty" description:"sheet, line, bar, bar_stack, line_stack, percent_bar_stack, pie, double_axis, card"`
}

type AimeQueryChartDimension struct {
	DimensionID   string    `json:"dimension_id"`
	Operator      *string   `json:"operator,omitempty"`
	Value         *[]string `json:"value,omitempty" description:"filter value"`
	GranularityId *int      `json:"granularity_id,omitempty" description:"filter time granularity when used with time dimension, Day (id = 1)  Week (id = 2) Month (id = 3) (which is the default Time Granularity if user doesn't specify) BiMonth (id = 4) Quater (id = 49)"`
}

type AimeQueryChartMetric struct {
	MetricID   string  `json:"metric_id"`
	AggType    *string `json:"agg_type,omitempty" description:"If it is a metric type itself, this field is not needed; it is only required when the MetricType is dimension"`
	MetricType *string `json:"metric_type,omitempty" description:"Select from the recommended metrics or dimensions. The selection shall be exactly what is taken from the source, and can only be either \"dimension\" or \"metric\". The default value \"metric\" may be omitted."` // dimension or metric
	Extra      *int    `json:"extra,omitempty" description:"If the aggType is of the quantile type, this value represents the quantile value."`                                                                                                                                       // 分位数用到
}

// GetChartUrlResponse represents the data field in the chart response
type GetChartUrlResponse struct {
	ChartJumpUrl    string `json:"chart_jump_url" description:"chart jump url"`
	ChartPreviewUrl string `json:"chart_preview_url" description:"chart preview url"`
	Code            int    `json:"code"`
	Msg             string `json:"msg"`
	ErrType         string `json:"err_type"`
	LogID           string `json:"log_id"`
}

// QueryChartInfoResponse represents the response from QueryChart 查询图表信息
type QueryChartInfoResponse struct {
	BaseResponse
	Data *GetChartInfoByUrlResponse `json:"data"`
}

// GetChartInfoByUrlResponse represents the data field in the chart response
type GetChartInfoByUrlResponse struct {
	ChartInfo GetChartByUser `json:"chart_info" description:"chart info by url"`
	Code      int            `json:"code"`
	Msg       string         `json:"msg"`
	ErrType   string         `json:"err_type"`
	LogID     string         `json:"log_id"`
}

// AimeChartQueryData 图表查询数据
type AimeChartQueryData struct {
	CardGraph            *[]AimeCardData `json:"card_graph,omitempty"`              // 卡片，card
	LineGraph            *AimeChartData  `json:"line_graph,omitempty"`              // 折线图，line
	ChartSheet           *AimeSheetData  `json:"chart_sheet,omitempty"`             // 表格，sheet
	BarGraph             *AimeChartData  `json:"bar_graph,omitempty"`               // 柱状图，bar
	DoubleAxisGraph      *AimeChartData  `json:"double_axis_graph,omitempty"`       // 双轴图，double_axis
	PieGraph             *[]AimeCardData `json:"pie_graph,omitempty"`               // 饼图，pie，
	LineStackGraph       *AimeChartData  `json:"line_stack_graph,omitempty"`        // 积流图，line_stack
	BarStackGraph        *AimeChartData  `json:"bar_stack_graph,omitempty"`         // 堆叠图，bar_stack
	PercentBarStackGraph *AimeChartData  `json:"percent_bar_stack_graph,omitempty"` // 百分比堆叠图，percent_bar_stack

	ChartType string `json:"chart_type,omitempty"` // 图形类型
}
