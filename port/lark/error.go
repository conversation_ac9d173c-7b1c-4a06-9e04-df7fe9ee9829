package lark

import "github.com/pkg/errors"

var (
	ErrFrequencyLimit          = errors.New("frequency limit")
	ErrPermissionDenied        = errors.New("permission denied")
	ErrLarkAuthFailed          = errors.New("lark auth failed")
	ErrLarkAuthUnauthorized    = errors.New("Unauthorized")
	ErrResourceNotFound        = errors.New("resource not found")
	ErrRefreshTokenAlreadyUsed = errors.New("refresh token already used")
	ErrRefreshTokenRevoked     = errors.New("refresh token has been revoked")
)
