// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"fmt"
)

const (
	ReceiveTypePersonal = "personal"

	ReceiveTypeAll = "all"

	ReceiveTypeDepartment = "department"

	MessageTypeNotification = "notification"

	MessageTypeInvitation = "invitation"

	MessageStatusUnread = "unread"

	MessageStatusRead = "read"

	MessageStatusRecalled = "recalled"
)

type ReceiveType = string

type MessageType = string

type MessageStatus = string

type CreateNotificationMessageRequest struct {
	Title string `thrift:"Title,1,required" json:"title"`
	// 通知内容
	Content       string           `thrift:"Content,2,required" json:"content"`
	ReceiveConfig []*ReceiveConfig `thrift:"ReceiveConfig,3,required" json:"receive_config"`
	// 是否置顶
	IsTop *bool `thrift:"IsTop,4,optional" json:"is_top"`
	// 是否发送 lark 通知
	SendLark *bool       `thrift:"SendLark,5,optional" json:"send_lark"`
	LinkInfo *LinkInfo   `thrift:"LinkInfo,6,optional" json:"link_info"`
	Type     MessageType `thrift:"Type,7,required" json:"type"`
}

func NewCreateNotificationMessageRequest() *CreateNotificationMessageRequest {
	return &CreateNotificationMessageRequest{}
}

func (p *CreateNotificationMessageRequest) InitDefault() {
}

func (p *CreateNotificationMessageRequest) GetTitle() (v string) {
	return p.Title
}

func (p *CreateNotificationMessageRequest) GetContent() (v string) {
	return p.Content
}

func (p *CreateNotificationMessageRequest) GetReceiveConfig() (v []*ReceiveConfig) {
	return p.ReceiveConfig
}

var CreateNotificationMessageRequest_IsTop_DEFAULT bool

func (p *CreateNotificationMessageRequest) GetIsTop() (v bool) {
	if !p.IsSetIsTop() {
		return CreateNotificationMessageRequest_IsTop_DEFAULT
	}
	return *p.IsTop
}

var CreateNotificationMessageRequest_SendLark_DEFAULT bool

func (p *CreateNotificationMessageRequest) GetSendLark() (v bool) {
	if !p.IsSetSendLark() {
		return CreateNotificationMessageRequest_SendLark_DEFAULT
	}
	return *p.SendLark
}

var CreateNotificationMessageRequest_LinkInfo_DEFAULT *LinkInfo

func (p *CreateNotificationMessageRequest) GetLinkInfo() (v *LinkInfo) {
	if !p.IsSetLinkInfo() {
		return CreateNotificationMessageRequest_LinkInfo_DEFAULT
	}
	return p.LinkInfo
}

func (p *CreateNotificationMessageRequest) GetType() (v MessageType) {
	return p.Type
}

func (p *CreateNotificationMessageRequest) IsSetIsTop() bool {
	return p.IsTop != nil
}

func (p *CreateNotificationMessageRequest) IsSetSendLark() bool {
	return p.SendLark != nil
}

func (p *CreateNotificationMessageRequest) IsSetLinkInfo() bool {
	return p.LinkInfo != nil
}

func (p *CreateNotificationMessageRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateNotificationMessageRequest(%+v)", *p)
}

type ReceiveConfig struct {
	// 接收类型：个人，全体等
	ReceiveType ReceiveType `thrift:"ReceiveType,1,required" json:"receive_type"`
	Receivers   []string    `thrift:"Receivers,2,optional" json:"receivers"`
}

func NewReceiveConfig() *ReceiveConfig {
	return &ReceiveConfig{}
}

func (p *ReceiveConfig) InitDefault() {
}

func (p *ReceiveConfig) GetReceiveType() (v ReceiveType) {
	return p.ReceiveType
}

var ReceiveConfig_Receivers_DEFAULT []string

func (p *ReceiveConfig) GetReceivers() (v []string) {
	if !p.IsSetReceivers() {
		return ReceiveConfig_Receivers_DEFAULT
	}
	return p.Receivers
}

func (p *ReceiveConfig) IsSetReceivers() bool {
	return p.Receivers != nil
}

func (p *ReceiveConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReceiveConfig(%+v)", *p)
}

type CreateNotificationMessageResponse struct {
	MessageID string `thrift:"MessageID,1,required" json:"message_id"`
}

func NewCreateNotificationMessageResponse() *CreateNotificationMessageResponse {
	return &CreateNotificationMessageResponse{}
}

func (p *CreateNotificationMessageResponse) InitDefault() {
}

func (p *CreateNotificationMessageResponse) GetMessageID() (v string) {
	return p.MessageID
}

func (p *CreateNotificationMessageResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateNotificationMessageResponse(%+v)", *p)
}

type ListNotificationMessagesRequest struct {
}

func NewListNotificationMessagesRequest() *ListNotificationMessagesRequest {
	return &ListNotificationMessagesRequest{}
}

func (p *ListNotificationMessagesRequest) InitDefault() {
}

func (p *ListNotificationMessagesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListNotificationMessagesRequest(%+v)", *p)
}

// 消息
type NotificationMessage struct {
	MessageID string `thrift:"MessageID,1,required" json:"message_id"`
	Title     string `thrift:"Title,2,required" json:"title"`
	// 通知内容
	Content string `thrift:"Content,3,required" json:"content"`
	// 跳转链接信息
	LinkInfo *LinkInfo `thrift:"LinkInfo,4,optional" json:"link_info"`
	// 消息状态
	Status    MessageStatus `thrift:"Status,5,required" json:"status"`
	IsTop     bool          `thrift:"IsTop,6,required" json:"is_top"`
	CreatedAt int64         `thrift:"CreatedAt,7,required" json:"created_at"`
	Creator   string        `thrift:"Creator,8,required" json:"creator"`
	Type      MessageType   `thrift:"Type,9,required" json:"type"`
}

func NewNotificationMessage() *NotificationMessage {
	return &NotificationMessage{}
}

func (p *NotificationMessage) InitDefault() {
}

func (p *NotificationMessage) GetMessageID() (v string) {
	return p.MessageID
}

func (p *NotificationMessage) GetTitle() (v string) {
	return p.Title
}

func (p *NotificationMessage) GetContent() (v string) {
	return p.Content
}

var NotificationMessage_LinkInfo_DEFAULT *LinkInfo

func (p *NotificationMessage) GetLinkInfo() (v *LinkInfo) {
	if !p.IsSetLinkInfo() {
		return NotificationMessage_LinkInfo_DEFAULT
	}
	return p.LinkInfo
}

func (p *NotificationMessage) GetStatus() (v MessageStatus) {
	return p.Status
}

func (p *NotificationMessage) GetIsTop() (v bool) {
	return p.IsTop
}

func (p *NotificationMessage) GetCreatedAt() (v int64) {
	return p.CreatedAt
}

func (p *NotificationMessage) GetCreator() (v string) {
	return p.Creator
}

func (p *NotificationMessage) GetType() (v MessageType) {
	return p.Type
}

func (p *NotificationMessage) IsSetLinkInfo() bool {
	return p.LinkInfo != nil
}

func (p *NotificationMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NotificationMessage(%+v)", *p)
}

type LinkInfo struct {
	Link string `thrift:"Link,1,required" json:"link"`
	Name string `thrift:"Name,2,required" json:"name"`
}

func NewLinkInfo() *LinkInfo {
	return &LinkInfo{}
}

func (p *LinkInfo) InitDefault() {
}

func (p *LinkInfo) GetLink() (v string) {
	return p.Link
}

func (p *LinkInfo) GetName() (v string) {
	return p.Name
}

func (p *LinkInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LinkInfo(%+v)", *p)
}

type ListNotificationMessagesResponse struct {
	Messages []*NotificationMessage `thrift:"Messages,1,optional" json:"message"`
}

func NewListNotificationMessagesResponse() *ListNotificationMessagesResponse {
	return &ListNotificationMessagesResponse{}
}

func (p *ListNotificationMessagesResponse) InitDefault() {
}

var ListNotificationMessagesResponse_Messages_DEFAULT []*NotificationMessage

func (p *ListNotificationMessagesResponse) GetMessages() (v []*NotificationMessage) {
	if !p.IsSetMessages() {
		return ListNotificationMessagesResponse_Messages_DEFAULT
	}
	return p.Messages
}

func (p *ListNotificationMessagesResponse) IsSetMessages() bool {
	return p.Messages != nil
}

func (p *ListNotificationMessagesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListNotificationMessagesResponse(%+v)", *p)
}

type UpdateNotificationMessageStatusRequest struct {
	MessageID string        `thrift:"MessageID,1,required" json:"messageid"`
	Status    MessageStatus `thrift:"Status,2,required" json:"status"`
}

func NewUpdateNotificationMessageStatusRequest() *UpdateNotificationMessageStatusRequest {
	return &UpdateNotificationMessageStatusRequest{}
}

func (p *UpdateNotificationMessageStatusRequest) InitDefault() {
}

func (p *UpdateNotificationMessageStatusRequest) GetMessageID() (v string) {
	return p.MessageID
}

func (p *UpdateNotificationMessageStatusRequest) GetStatus() (v MessageStatus) {
	return p.Status
}

func (p *UpdateNotificationMessageStatusRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateNotificationMessageStatusRequest(%+v)", *p)
}

type UpdateNotificationMessageStatusResponse struct {
}

func NewUpdateNotificationMessageStatusResponse() *UpdateNotificationMessageStatusResponse {
	return &UpdateNotificationMessageStatusResponse{}
}

func (p *UpdateNotificationMessageStatusResponse) InitDefault() {
}

func (p *UpdateNotificationMessageStatusResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateNotificationMessageStatusResponse(%+v)", *p)
}
