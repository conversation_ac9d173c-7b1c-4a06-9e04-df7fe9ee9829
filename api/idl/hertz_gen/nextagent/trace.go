// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package nextagent

import (
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"fmt"
)

const (
	Running = "running"

	Recoverable = "recoverable"

	UnRecoverable = "unrecoverable"

	Local = "local"

	Docker = "docker"

	StratoCube = "stratocube"

	Bytesuite = "bytesuite"

	Success = "success"

	Fail = "fail"

	AgentStepCreated = "created"

	AgentStepRunning = "running"

	AgentStepSuccess = "success"

	AgentStepFailed = "failed"

	Text = "text"

	ImageURL = "image_url"
)

type RunDebugStatus = string

type RuntimeProvider = string

type ChatCompletionStatus = string

type AgentStepStatus = string

type ChatMessagePartType = string

type GetTraceSessionRequest struct {
	SessionID *string `thrift:"SessionID,1,optional" json:"session_id,omitempty" query:"session_id"`
	ReplayID  *string `thrift:"ReplayID,2,optional" json:"replay_id,omitempty" query:"replay_id"`
}

func NewGetTraceSessionRequest() *GetTraceSessionRequest {
	return &GetTraceSessionRequest{}
}

func (p *GetTraceSessionRequest) InitDefault() {
}

var GetTraceSessionRequest_SessionID_DEFAULT string

func (p *GetTraceSessionRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return GetTraceSessionRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var GetTraceSessionRequest_ReplayID_DEFAULT string

func (p *GetTraceSessionRequest) GetReplayID() (v string) {
	if !p.IsSetReplayID() {
		return GetTraceSessionRequest_ReplayID_DEFAULT
	}
	return *p.ReplayID
}

func (p *GetTraceSessionRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *GetTraceSessionRequest) IsSetReplayID() bool {
	return p.ReplayID != nil
}

func (p *GetTraceSessionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTraceSessionRequest(%+v)", *p)
}

type GetTraceSessionResponse struct {
	Session *TraceSession `thrift:"Session,1,required" json:"session"`
}

func NewGetTraceSessionResponse() *GetTraceSessionResponse {
	return &GetTraceSessionResponse{}
}

func (p *GetTraceSessionResponse) InitDefault() {
}

var GetTraceSessionResponse_Session_DEFAULT *TraceSession

func (p *GetTraceSessionResponse) GetSession() (v *TraceSession) {
	if !p.IsSetSession() {
		return GetTraceSessionResponse_Session_DEFAULT
	}
	return p.Session
}

func (p *GetTraceSessionResponse) IsSetSession() bool {
	return p.Session != nil
}

func (p *GetTraceSessionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTraceSessionResponse(%+v)", *p)
}

type TraceSession struct {
	ID             string          `thrift:"ID,1" json:"id"`
	Creator        string          `thrift:"Creator,2" json:"creator"`
	CreatedAt      string          `thrift:"CreatedAt,3" json:"created_at"`
	UpdatedAt      string          `thrift:"UpdatedAt,4" json:"updated_at"`
	Title          string          `thrift:"Title,5" json:"title"`
	Status         SessionStatus   `thrift:"Status,6" json:"status"`
	RunID          string          `thrift:"RunID,7" json:"run_id"`
	RunProvider    RuntimeProvider `thrift:"RunProvider,8" json:"run_provider"`
	RunStatus      string          `thrift:"RunStatus,9" json:"run_status"`
	RunDebugStatus RunDebugStatus  `thrift:"RunDebugStatus,10" json:"run_debug_status"`
	WebshellURL    string          `thrift:"WebshellURL,11" json:"webshell_url"`
	FileServerURL  string          `thrift:"FileServerURL,12" json:"file_server_url"`
	EventsURL      string          `thrift:"EventsURL,13" json:"events_url"`
	AgentMetadata  *AgentMetadata  `thrift:"AgentMetadata,14" json:"agent_metadata"`
	LogID          string          `thrift:"LogID,15" json:"log_id"`
	BrowserURL     string          `thrift:"BrowserURL,16" json:"browser_url"`
}

func NewTraceSession() *TraceSession {
	return &TraceSession{}
}

func (p *TraceSession) InitDefault() {
}

func (p *TraceSession) GetID() (v string) {
	return p.ID
}

func (p *TraceSession) GetCreator() (v string) {
	return p.Creator
}

func (p *TraceSession) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *TraceSession) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *TraceSession) GetTitle() (v string) {
	return p.Title
}

func (p *TraceSession) GetStatus() (v SessionStatus) {
	return p.Status
}

func (p *TraceSession) GetRunID() (v string) {
	return p.RunID
}

func (p *TraceSession) GetRunProvider() (v RuntimeProvider) {
	return p.RunProvider
}

func (p *TraceSession) GetRunStatus() (v string) {
	return p.RunStatus
}

func (p *TraceSession) GetRunDebugStatus() (v RunDebugStatus) {
	return p.RunDebugStatus
}

func (p *TraceSession) GetWebshellURL() (v string) {
	return p.WebshellURL
}

func (p *TraceSession) GetFileServerURL() (v string) {
	return p.FileServerURL
}

func (p *TraceSession) GetEventsURL() (v string) {
	return p.EventsURL
}

var TraceSession_AgentMetadata_DEFAULT *AgentMetadata

func (p *TraceSession) GetAgentMetadata() (v *AgentMetadata) {
	if !p.IsSetAgentMetadata() {
		return TraceSession_AgentMetadata_DEFAULT
	}
	return p.AgentMetadata
}

func (p *TraceSession) GetLogID() (v string) {
	return p.LogID
}

func (p *TraceSession) GetBrowserURL() (v string) {
	return p.BrowserURL
}

func (p *TraceSession) IsSetAgentMetadata() bool {
	return p.AgentMetadata != nil
}

func (p *TraceSession) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceSession(%+v)", *p)
}

type AgentMetadata struct {
	AgentName            string `thrift:"AgentName,1" json:"agent_name"`
	AgentConfigID        string `thrift:"AgentConfigID,2" json:"agent_config_id"`
	AgentConfigVersion   int32  `thrift:"AgentConfigVersion,3" json:"agent_config_version"`
	AgentConfigVersionID string `thrift:"AgentConfigVersionID,4" json:"agent_config_version_id"`
}

func NewAgentMetadata() *AgentMetadata {
	return &AgentMetadata{}
}

func (p *AgentMetadata) InitDefault() {
}

func (p *AgentMetadata) GetAgentName() (v string) {
	return p.AgentName
}

func (p *AgentMetadata) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *AgentMetadata) GetAgentConfigVersion() (v int32) {
	return p.AgentConfigVersion
}

func (p *AgentMetadata) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *AgentMetadata) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentMetadata(%+v)", *p)
}

type GetTraceEventsRequest struct {
	RunID       *string `thrift:"RunID,1,optional" json:"run_id,omitempty" query:"run_id"`
	SessionID   *string `thrift:"SessionID,2,optional" json:"session_id,omitempty" query:"session_id"`
	ContainerID *string `thrift:"ContainerID,3,optional" json:"container_id,omitempty" query:"container_id"`
	URI         *string `thrift:"URI,4,optional" json:"uri,omitempty" query:"uri"`
	Provider    *string `thrift:"Provider,5,optional" json:"provider,omitempty" query:"provider"`
}

func NewGetTraceEventsRequest() *GetTraceEventsRequest {
	return &GetTraceEventsRequest{}
}

func (p *GetTraceEventsRequest) InitDefault() {
}

var GetTraceEventsRequest_RunID_DEFAULT string

func (p *GetTraceEventsRequest) GetRunID() (v string) {
	if !p.IsSetRunID() {
		return GetTraceEventsRequest_RunID_DEFAULT
	}
	return *p.RunID
}

var GetTraceEventsRequest_SessionID_DEFAULT string

func (p *GetTraceEventsRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return GetTraceEventsRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var GetTraceEventsRequest_ContainerID_DEFAULT string

func (p *GetTraceEventsRequest) GetContainerID() (v string) {
	if !p.IsSetContainerID() {
		return GetTraceEventsRequest_ContainerID_DEFAULT
	}
	return *p.ContainerID
}

var GetTraceEventsRequest_URI_DEFAULT string

func (p *GetTraceEventsRequest) GetURI() (v string) {
	if !p.IsSetURI() {
		return GetTraceEventsRequest_URI_DEFAULT
	}
	return *p.URI
}

var GetTraceEventsRequest_Provider_DEFAULT string

func (p *GetTraceEventsRequest) GetProvider() (v string) {
	if !p.IsSetProvider() {
		return GetTraceEventsRequest_Provider_DEFAULT
	}
	return *p.Provider
}

func (p *GetTraceEventsRequest) IsSetRunID() bool {
	return p.RunID != nil
}

func (p *GetTraceEventsRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *GetTraceEventsRequest) IsSetContainerID() bool {
	return p.ContainerID != nil
}

func (p *GetTraceEventsRequest) IsSetURI() bool {
	return p.URI != nil
}

func (p *GetTraceEventsRequest) IsSetProvider() bool {
	return p.Provider != nil
}

func (p *GetTraceEventsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTraceEventsRequest(%+v)", *p)
}

type ResumeRuntimeRequest struct {
	RunID string `thrift:"RunID,1,required" json:"run_id,required" query:"run_id,required"`
}

func NewResumeRuntimeRequest() *ResumeRuntimeRequest {
	return &ResumeRuntimeRequest{}
}

func (p *ResumeRuntimeRequest) InitDefault() {
}

func (p *ResumeRuntimeRequest) GetRunID() (v string) {
	return p.RunID
}

func (p *ResumeRuntimeRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResumeRuntimeRequest(%+v)", *p)
}

type ResumeRuntimeResponse struct {
	Message string `thrift:"Message,1,required" json:"message"`
}

func NewResumeRuntimeResponse() *ResumeRuntimeResponse {
	return &ResumeRuntimeResponse{}
}

func (p *ResumeRuntimeResponse) InitDefault() {
}

func (p *ResumeRuntimeResponse) GetMessage() (v string) {
	return p.Message
}

func (p *ResumeRuntimeResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResumeRuntimeResponse(%+v)", *p)
}

type SuspendRuntimeRequest struct {
	RunID string `thrift:"RunID,1,required" json:"run_id,required" query:"run_id,required"`
}

func NewSuspendRuntimeRequest() *SuspendRuntimeRequest {
	return &SuspendRuntimeRequest{}
}

func (p *SuspendRuntimeRequest) InitDefault() {
}

func (p *SuspendRuntimeRequest) GetRunID() (v string) {
	return p.RunID
}

func (p *SuspendRuntimeRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SuspendRuntimeRequest(%+v)", *p)
}

type SuspendRuntimeResponse struct {
	Message string `thrift:"Message,1,required" json:"message"`
}

func NewSuspendRuntimeResponse() *SuspendRuntimeResponse {
	return &SuspendRuntimeResponse{}
}

func (p *SuspendRuntimeResponse) InitDefault() {
}

func (p *SuspendRuntimeResponse) GetMessage() (v string) {
	return p.Message
}

func (p *SuspendRuntimeResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SuspendRuntimeResponse(%+v)", *p)
}

type DeleteRuntimeRequest struct {
	RunID string `thrift:"RunID,1,required" json:"run_id,required" query:"run_id,required"`
}

func NewDeleteRuntimeRequest() *DeleteRuntimeRequest {
	return &DeleteRuntimeRequest{}
}

func (p *DeleteRuntimeRequest) InitDefault() {
}

func (p *DeleteRuntimeRequest) GetRunID() (v string) {
	return p.RunID
}

func (p *DeleteRuntimeRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteRuntimeRequest(%+v)", *p)
}

type DeleteRuntimeResponse struct {
	Message string `thrift:"Message,1,required" json:"message"`
}

func NewDeleteRuntimeResponse() *DeleteRuntimeResponse {
	return &DeleteRuntimeResponse{}
}

func (p *DeleteRuntimeResponse) InitDefault() {
}

func (p *DeleteRuntimeResponse) GetMessage() (v string) {
	return p.Message
}

func (p *DeleteRuntimeResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteRuntimeResponse(%+v)", *p)
}

type GetTraceSessionChatRequest struct {
	SessionID string                `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
	Status    *ChatCompletionStatus `thrift:"Status,2,optional" json:"status,omitempty" query:"status"`
	Type      *string               `thrift:"Type,3,optional" json:"type,omitempty" query:"type"`
	PageNum   int64                 `thrift:"PageNum,4,required" json:"page_num,required" query:"page_num,required"`
	PageSize  int64                 `thrift:"PageSize,5,required" json:"page_size,required" query:"page_size,required"`
	TraceID   *string               `thrift:"TraceID,6,optional" json:"trace_id,omitempty" query:"trace_id"`
}

func NewGetTraceSessionChatRequest() *GetTraceSessionChatRequest {
	return &GetTraceSessionChatRequest{}
}

func (p *GetTraceSessionChatRequest) InitDefault() {
}

func (p *GetTraceSessionChatRequest) GetSessionID() (v string) {
	return p.SessionID
}

var GetTraceSessionChatRequest_Status_DEFAULT ChatCompletionStatus

func (p *GetTraceSessionChatRequest) GetStatus() (v ChatCompletionStatus) {
	if !p.IsSetStatus() {
		return GetTraceSessionChatRequest_Status_DEFAULT
	}
	return *p.Status
}

var GetTraceSessionChatRequest_Type_DEFAULT string

func (p *GetTraceSessionChatRequest) GetType() (v string) {
	if !p.IsSetType() {
		return GetTraceSessionChatRequest_Type_DEFAULT
	}
	return *p.Type
}

func (p *GetTraceSessionChatRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *GetTraceSessionChatRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var GetTraceSessionChatRequest_TraceID_DEFAULT string

func (p *GetTraceSessionChatRequest) GetTraceID() (v string) {
	if !p.IsSetTraceID() {
		return GetTraceSessionChatRequest_TraceID_DEFAULT
	}
	return *p.TraceID
}

func (p *GetTraceSessionChatRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *GetTraceSessionChatRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *GetTraceSessionChatRequest) IsSetTraceID() bool {
	return p.TraceID != nil
}

func (p *GetTraceSessionChatRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTraceSessionChatRequest(%+v)", *p)
}

type GetTraceSessionChatResponse struct {
	ChatCompletions []*ChatCompletion `thrift:"ChatCompletions,1,required" json:"chat_completions"`
	Total           int64             `thrift:"Total,2,required" json:"total"`
}

func NewGetTraceSessionChatResponse() *GetTraceSessionChatResponse {
	return &GetTraceSessionChatResponse{}
}

func (p *GetTraceSessionChatResponse) InitDefault() {
}

func (p *GetTraceSessionChatResponse) GetChatCompletions() (v []*ChatCompletion) {
	return p.ChatCompletions
}

func (p *GetTraceSessionChatResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *GetTraceSessionChatResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTraceSessionChatResponse(%+v)", *p)
}

type ChatCompletion struct {
	ID        string               `thrift:"ID,1,required" json:"id"`
	CreatedAt string               `thrift:"CreatedAt,2,required" json:"created_at"`
	UpdatedAt string               `thrift:"UpdatedAt,3,required" json:"updated_at"`
	Prompt    string               `thrift:"Prompt,4,required" json:"prompt"`
	Response  string               `thrift:"Response,5,required" json:"response"`
	Type      string               `thrift:"Type,6,required" json:"type"`
	Metadata  common.JsonVariables `thrift:"Metadata,7,required" json:"metadata"`
	Status    ChatCompletionStatus `thrift:"Status,8,required" json:"status"`
	ModelName string               `thrift:"ModelName,9,required" json:"model_name"`
}

func NewChatCompletion() *ChatCompletion {
	return &ChatCompletion{}
}

func (p *ChatCompletion) InitDefault() {
}

func (p *ChatCompletion) GetID() (v string) {
	return p.ID
}

func (p *ChatCompletion) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *ChatCompletion) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *ChatCompletion) GetPrompt() (v string) {
	return p.Prompt
}

func (p *ChatCompletion) GetResponse() (v string) {
	return p.Response
}

func (p *ChatCompletion) GetType() (v string) {
	return p.Type
}

func (p *ChatCompletion) GetMetadata() (v common.JsonVariables) {
	return p.Metadata
}

func (p *ChatCompletion) GetStatus() (v ChatCompletionStatus) {
	return p.Status
}

func (p *ChatCompletion) GetModelName() (v string) {
	return p.ModelName
}

func (p *ChatCompletion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatCompletion(%+v)", *p)
}

type ListSessionAgentStepRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
}

func NewListSessionAgentStepRequest() *ListSessionAgentStepRequest {
	return &ListSessionAgentStepRequest{}
}

func (p *ListSessionAgentStepRequest) InitDefault() {
}

func (p *ListSessionAgentStepRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ListSessionAgentStepRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSessionAgentStepRequest(%+v)", *p)
}

type ListSessionAgentStepResponse struct {
	Steps []*AgentStep `thrift:"Steps,1,required" json:"steps"`
	Total int64        `thrift:"Total,2,required" json:"total"`
}

func NewListSessionAgentStepResponse() *ListSessionAgentStepResponse {
	return &ListSessionAgentStepResponse{}
}

func (p *ListSessionAgentStepResponse) InitDefault() {
}

func (p *ListSessionAgentStepResponse) GetSteps() (v []*AgentStep) {
	return p.Steps
}

func (p *ListSessionAgentStepResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListSessionAgentStepResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSessionAgentStepResponse(%+v)", *p)
}

type AgentStep struct {
	Action    string          `thrift:"Action,1,required" json:"action"`
	Executor  string          `thrift:"Executor,2,required" json:"executor"`
	MCPTool   string          `thrift:"MCPTool,3,required" json:"mcp_tool"`
	StartTime int64           `thrift:"StartTime,4,required" json:"start_time"`
	EndTime   int64           `thrift:"EndTime,5,required" json:"end_time"`
	Status    AgentStepStatus `thrift:"Status,6,required" json:"status"`
	StepID    string          `thrift:"StepID,7,required" json:"step_id"`
}

func NewAgentStep() *AgentStep {
	return &AgentStep{}
}

func (p *AgentStep) InitDefault() {
}

func (p *AgentStep) GetAction() (v string) {
	return p.Action
}

func (p *AgentStep) GetExecutor() (v string) {
	return p.Executor
}

func (p *AgentStep) GetMCPTool() (v string) {
	return p.MCPTool
}

func (p *AgentStep) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *AgentStep) GetEndTime() (v int64) {
	return p.EndTime
}

func (p *AgentStep) GetStatus() (v AgentStepStatus) {
	return p.Status
}

func (p *AgentStep) GetStepID() (v string) {
	return p.StepID
}

func (p *AgentStep) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentStep(%+v)", *p)
}

type DownloadSessionLogRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
}

func NewDownloadSessionLogRequest() *DownloadSessionLogRequest {
	return &DownloadSessionLogRequest{}
}

func (p *DownloadSessionLogRequest) InitDefault() {
}

func (p *DownloadSessionLogRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *DownloadSessionLogRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadSessionLogRequest(%+v)", *p)
}

type ListModelsRequest struct {
}

func NewListModelsRequest() *ListModelsRequest {
	return &ListModelsRequest{}
}

func (p *ListModelsRequest) InitDefault() {
}

func (p *ListModelsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListModelsRequest(%+v)", *p)
}

type ListModelsResponse struct {
	Models []*Model `thrift:"Models,1,required" json:"models"`
}

func NewListModelsResponse() *ListModelsResponse {
	return &ListModelsResponse{}
}

func (p *ListModelsResponse) InitDefault() {
}

func (p *ListModelsResponse) GetModels() (v []*Model) {
	return p.Models
}

func (p *ListModelsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListModelsResponse(%+v)", *p)
}

type Model struct {
	Type   string   `thrift:"Type,1" json:"type"`
	Models []string `thrift:"Models,2" json:"models"`
}

func NewModel() *Model {
	return &Model{}
}

func (p *Model) InitDefault() {
}

func (p *Model) GetType() (v string) {
	return p.Type
}

func (p *Model) GetModels() (v []string) {
	return p.Models
}

func (p *Model) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Model(%+v)", *p)
}

type ChatStreamRequest struct {
	Model               string                   `thrift:"Model,1,required" json:"model"`
	Messages            []*ChatCompletionMessage `thrift:"Messages,2,required" json:"messages"`
	MaxTokens           *int32                   `thrift:"MaxTokens,3,optional" json:"max_tokens"`
	MaxCompletionTokens *int32                   `thrift:"MaxCompletionTokens,4,optional" json:"max_completion_tokens"`
	Temperature         *float64                 `thrift:"Temperature,5,optional" json:"temperature"`
	TopP                *float64                 `thrift:"TopP,6,optional" json:"top_p"`
	N                   *int32                   `thrift:"N,7,optional" json:"n"`
	Stream              *bool                    `thrift:"Stream,8,optional" json:"stream"`
	Stop                []string                 `thrift:"Stop,9,optional" json:"stop"`
	PresencePenalty     *float64                 `thrift:"PresencePenalty,10,optional" json:"presence_penalty"`
	FrequencyPenalty    *float64                 `thrift:"FrequencyPenalty,11,optional" json:"frequency_penalty"`
	LogitBias           map[string]int32         `thrift:"LogitBias,12,optional" json:"logit_bias"`
	LogProbs            *bool                    `thrift:"LogProbs,13,optional" json:"logprobs"`
	TopLogProbs         *int32                   `thrift:"TopLogProbs,14,optional" json:"top_logprobs"`
	User                *string                  `thrift:"User,15,optional" json:"user"`
	Tools               []*Tool                  `thrift:"Tools,16,optional" json:"tools"`
	ToolChoice          *ToolChoiceType          `thrift:"ToolChoice,17,optional" json:"tool_choice"`
	Store               *bool                    `thrift:"Store,18,optional" json:"store"`
	ReasoningEffort     *string                  `thrift:"ReasoningEffort,19,optional" json:"reasoning_effort"`
	Metadata            map[string]string        `thrift:"Metadata,20,optional" json:"metadata"`
	Thinking            *ThinkingConfig          `thrift:"Thinking,21,optional" json:"thinking"`
}

func NewChatStreamRequest() *ChatStreamRequest {
	return &ChatStreamRequest{}
}

func (p *ChatStreamRequest) InitDefault() {
}

func (p *ChatStreamRequest) GetModel() (v string) {
	return p.Model
}

func (p *ChatStreamRequest) GetMessages() (v []*ChatCompletionMessage) {
	return p.Messages
}

var ChatStreamRequest_MaxTokens_DEFAULT int32

func (p *ChatStreamRequest) GetMaxTokens() (v int32) {
	if !p.IsSetMaxTokens() {
		return ChatStreamRequest_MaxTokens_DEFAULT
	}
	return *p.MaxTokens
}

var ChatStreamRequest_MaxCompletionTokens_DEFAULT int32

func (p *ChatStreamRequest) GetMaxCompletionTokens() (v int32) {
	if !p.IsSetMaxCompletionTokens() {
		return ChatStreamRequest_MaxCompletionTokens_DEFAULT
	}
	return *p.MaxCompletionTokens
}

var ChatStreamRequest_Temperature_DEFAULT float64

func (p *ChatStreamRequest) GetTemperature() (v float64) {
	if !p.IsSetTemperature() {
		return ChatStreamRequest_Temperature_DEFAULT
	}
	return *p.Temperature
}

var ChatStreamRequest_TopP_DEFAULT float64

func (p *ChatStreamRequest) GetTopP() (v float64) {
	if !p.IsSetTopP() {
		return ChatStreamRequest_TopP_DEFAULT
	}
	return *p.TopP
}

var ChatStreamRequest_N_DEFAULT int32

func (p *ChatStreamRequest) GetN() (v int32) {
	if !p.IsSetN() {
		return ChatStreamRequest_N_DEFAULT
	}
	return *p.N
}

var ChatStreamRequest_Stream_DEFAULT bool

func (p *ChatStreamRequest) GetStream() (v bool) {
	if !p.IsSetStream() {
		return ChatStreamRequest_Stream_DEFAULT
	}
	return *p.Stream
}

var ChatStreamRequest_Stop_DEFAULT []string

func (p *ChatStreamRequest) GetStop() (v []string) {
	if !p.IsSetStop() {
		return ChatStreamRequest_Stop_DEFAULT
	}
	return p.Stop
}

var ChatStreamRequest_PresencePenalty_DEFAULT float64

func (p *ChatStreamRequest) GetPresencePenalty() (v float64) {
	if !p.IsSetPresencePenalty() {
		return ChatStreamRequest_PresencePenalty_DEFAULT
	}
	return *p.PresencePenalty
}

var ChatStreamRequest_FrequencyPenalty_DEFAULT float64

func (p *ChatStreamRequest) GetFrequencyPenalty() (v float64) {
	if !p.IsSetFrequencyPenalty() {
		return ChatStreamRequest_FrequencyPenalty_DEFAULT
	}
	return *p.FrequencyPenalty
}

var ChatStreamRequest_LogitBias_DEFAULT map[string]int32

func (p *ChatStreamRequest) GetLogitBias() (v map[string]int32) {
	if !p.IsSetLogitBias() {
		return ChatStreamRequest_LogitBias_DEFAULT
	}
	return p.LogitBias
}

var ChatStreamRequest_LogProbs_DEFAULT bool

func (p *ChatStreamRequest) GetLogProbs() (v bool) {
	if !p.IsSetLogProbs() {
		return ChatStreamRequest_LogProbs_DEFAULT
	}
	return *p.LogProbs
}

var ChatStreamRequest_TopLogProbs_DEFAULT int32

func (p *ChatStreamRequest) GetTopLogProbs() (v int32) {
	if !p.IsSetTopLogProbs() {
		return ChatStreamRequest_TopLogProbs_DEFAULT
	}
	return *p.TopLogProbs
}

var ChatStreamRequest_User_DEFAULT string

func (p *ChatStreamRequest) GetUser() (v string) {
	if !p.IsSetUser() {
		return ChatStreamRequest_User_DEFAULT
	}
	return *p.User
}

var ChatStreamRequest_Tools_DEFAULT []*Tool

func (p *ChatStreamRequest) GetTools() (v []*Tool) {
	if !p.IsSetTools() {
		return ChatStreamRequest_Tools_DEFAULT
	}
	return p.Tools
}

var ChatStreamRequest_ToolChoice_DEFAULT *ToolChoiceType

func (p *ChatStreamRequest) GetToolChoice() (v *ToolChoiceType) {
	if !p.IsSetToolChoice() {
		return ChatStreamRequest_ToolChoice_DEFAULT
	}
	return p.ToolChoice
}

var ChatStreamRequest_Store_DEFAULT bool

func (p *ChatStreamRequest) GetStore() (v bool) {
	if !p.IsSetStore() {
		return ChatStreamRequest_Store_DEFAULT
	}
	return *p.Store
}

var ChatStreamRequest_ReasoningEffort_DEFAULT string

func (p *ChatStreamRequest) GetReasoningEffort() (v string) {
	if !p.IsSetReasoningEffort() {
		return ChatStreamRequest_ReasoningEffort_DEFAULT
	}
	return *p.ReasoningEffort
}

var ChatStreamRequest_Metadata_DEFAULT map[string]string

func (p *ChatStreamRequest) GetMetadata() (v map[string]string) {
	if !p.IsSetMetadata() {
		return ChatStreamRequest_Metadata_DEFAULT
	}
	return p.Metadata
}

var ChatStreamRequest_Thinking_DEFAULT *ThinkingConfig

func (p *ChatStreamRequest) GetThinking() (v *ThinkingConfig) {
	if !p.IsSetThinking() {
		return ChatStreamRequest_Thinking_DEFAULT
	}
	return p.Thinking
}

func (p *ChatStreamRequest) IsSetMaxTokens() bool {
	return p.MaxTokens != nil
}

func (p *ChatStreamRequest) IsSetMaxCompletionTokens() bool {
	return p.MaxCompletionTokens != nil
}

func (p *ChatStreamRequest) IsSetTemperature() bool {
	return p.Temperature != nil
}

func (p *ChatStreamRequest) IsSetTopP() bool {
	return p.TopP != nil
}

func (p *ChatStreamRequest) IsSetN() bool {
	return p.N != nil
}

func (p *ChatStreamRequest) IsSetStream() bool {
	return p.Stream != nil
}

func (p *ChatStreamRequest) IsSetStop() bool {
	return p.Stop != nil
}

func (p *ChatStreamRequest) IsSetPresencePenalty() bool {
	return p.PresencePenalty != nil
}

func (p *ChatStreamRequest) IsSetFrequencyPenalty() bool {
	return p.FrequencyPenalty != nil
}

func (p *ChatStreamRequest) IsSetLogitBias() bool {
	return p.LogitBias != nil
}

func (p *ChatStreamRequest) IsSetLogProbs() bool {
	return p.LogProbs != nil
}

func (p *ChatStreamRequest) IsSetTopLogProbs() bool {
	return p.TopLogProbs != nil
}

func (p *ChatStreamRequest) IsSetUser() bool {
	return p.User != nil
}

func (p *ChatStreamRequest) IsSetTools() bool {
	return p.Tools != nil
}

func (p *ChatStreamRequest) IsSetToolChoice() bool {
	return p.ToolChoice != nil
}

func (p *ChatStreamRequest) IsSetStore() bool {
	return p.Store != nil
}

func (p *ChatStreamRequest) IsSetReasoningEffort() bool {
	return p.ReasoningEffort != nil
}

func (p *ChatStreamRequest) IsSetMetadata() bool {
	return p.Metadata != nil
}

func (p *ChatStreamRequest) IsSetThinking() bool {
	return p.Thinking != nil
}

func (p *ChatStreamRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatStreamRequest(%+v)", *p)
}

type ChatCompletionMessage struct {
	Role         string             `thrift:"Role,1,required" json:"role"`
	Content      *string            `thrift:"Content,2,optional" json:"content"`
	Refusal      *string            `thrift:"Refusal,3,optional" json:"refusal"`
	MultiContent []*ChatMessagePart `thrift:"MultiContent,4,optional" json:"multi_content"`
	Name         *string            `thrift:"Name,5,optional" json:"name"`
	FunctionCall *FunctionCall      `thrift:"FunctionCall,6,optional" json:"function_call"`
	ToolCalls    []*ChatToolCall    `thrift:"ToolCalls,7,optional" json:"tool_calls"`
	ToolCallID   *string            `thrift:"ToolCallID,8,optional" json:"tool_call_id"`
}

func NewChatCompletionMessage() *ChatCompletionMessage {
	return &ChatCompletionMessage{}
}

func (p *ChatCompletionMessage) InitDefault() {
}

func (p *ChatCompletionMessage) GetRole() (v string) {
	return p.Role
}

var ChatCompletionMessage_Content_DEFAULT string

func (p *ChatCompletionMessage) GetContent() (v string) {
	if !p.IsSetContent() {
		return ChatCompletionMessage_Content_DEFAULT
	}
	return *p.Content
}

var ChatCompletionMessage_Refusal_DEFAULT string

func (p *ChatCompletionMessage) GetRefusal() (v string) {
	if !p.IsSetRefusal() {
		return ChatCompletionMessage_Refusal_DEFAULT
	}
	return *p.Refusal
}

var ChatCompletionMessage_MultiContent_DEFAULT []*ChatMessagePart

func (p *ChatCompletionMessage) GetMultiContent() (v []*ChatMessagePart) {
	if !p.IsSetMultiContent() {
		return ChatCompletionMessage_MultiContent_DEFAULT
	}
	return p.MultiContent
}

var ChatCompletionMessage_Name_DEFAULT string

func (p *ChatCompletionMessage) GetName() (v string) {
	if !p.IsSetName() {
		return ChatCompletionMessage_Name_DEFAULT
	}
	return *p.Name
}

var ChatCompletionMessage_FunctionCall_DEFAULT *FunctionCall

func (p *ChatCompletionMessage) GetFunctionCall() (v *FunctionCall) {
	if !p.IsSetFunctionCall() {
		return ChatCompletionMessage_FunctionCall_DEFAULT
	}
	return p.FunctionCall
}

var ChatCompletionMessage_ToolCalls_DEFAULT []*ChatToolCall

func (p *ChatCompletionMessage) GetToolCalls() (v []*ChatToolCall) {
	if !p.IsSetToolCalls() {
		return ChatCompletionMessage_ToolCalls_DEFAULT
	}
	return p.ToolCalls
}

var ChatCompletionMessage_ToolCallID_DEFAULT string

func (p *ChatCompletionMessage) GetToolCallID() (v string) {
	if !p.IsSetToolCallID() {
		return ChatCompletionMessage_ToolCallID_DEFAULT
	}
	return *p.ToolCallID
}

func (p *ChatCompletionMessage) IsSetContent() bool {
	return p.Content != nil
}

func (p *ChatCompletionMessage) IsSetRefusal() bool {
	return p.Refusal != nil
}

func (p *ChatCompletionMessage) IsSetMultiContent() bool {
	return p.MultiContent != nil
}

func (p *ChatCompletionMessage) IsSetName() bool {
	return p.Name != nil
}

func (p *ChatCompletionMessage) IsSetFunctionCall() bool {
	return p.FunctionCall != nil
}

func (p *ChatCompletionMessage) IsSetToolCalls() bool {
	return p.ToolCalls != nil
}

func (p *ChatCompletionMessage) IsSetToolCallID() bool {
	return p.ToolCallID != nil
}

func (p *ChatCompletionMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatCompletionMessage(%+v)", *p)
}

type ChatMessagePart struct {
	Type     *ChatMessagePartType `thrift:"Type,1,optional" json:"type"`
	Text     *string              `thrift:"Text,2,optional" json:"text"`
	ImageURL *ChatMessageImageURL `thrift:"ImageURL,3,optional" json:"image_url"`
}

func NewChatMessagePart() *ChatMessagePart {
	return &ChatMessagePart{}
}

func (p *ChatMessagePart) InitDefault() {
}

var ChatMessagePart_Type_DEFAULT ChatMessagePartType

func (p *ChatMessagePart) GetType() (v ChatMessagePartType) {
	if !p.IsSetType() {
		return ChatMessagePart_Type_DEFAULT
	}
	return *p.Type
}

var ChatMessagePart_Text_DEFAULT string

func (p *ChatMessagePart) GetText() (v string) {
	if !p.IsSetText() {
		return ChatMessagePart_Text_DEFAULT
	}
	return *p.Text
}

var ChatMessagePart_ImageURL_DEFAULT *ChatMessageImageURL

func (p *ChatMessagePart) GetImageURL() (v *ChatMessageImageURL) {
	if !p.IsSetImageURL() {
		return ChatMessagePart_ImageURL_DEFAULT
	}
	return p.ImageURL
}

func (p *ChatMessagePart) IsSetType() bool {
	return p.Type != nil
}

func (p *ChatMessagePart) IsSetText() bool {
	return p.Text != nil
}

func (p *ChatMessagePart) IsSetImageURL() bool {
	return p.ImageURL != nil
}

func (p *ChatMessagePart) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatMessagePart(%+v)", *p)
}

type ChatMessageImageURL struct {
	URL    *string `thrift:"URL,1,optional" json:"url"`
	Detail *string `thrift:"Detail,2,optional" json:"detail"`
}

func NewChatMessageImageURL() *ChatMessageImageURL {
	return &ChatMessageImageURL{}
}

func (p *ChatMessageImageURL) InitDefault() {
}

var ChatMessageImageURL_URL_DEFAULT string

func (p *ChatMessageImageURL) GetURL() (v string) {
	if !p.IsSetURL() {
		return ChatMessageImageURL_URL_DEFAULT
	}
	return *p.URL
}

var ChatMessageImageURL_Detail_DEFAULT string

func (p *ChatMessageImageURL) GetDetail() (v string) {
	if !p.IsSetDetail() {
		return ChatMessageImageURL_Detail_DEFAULT
	}
	return *p.Detail
}

func (p *ChatMessageImageURL) IsSetURL() bool {
	return p.URL != nil
}

func (p *ChatMessageImageURL) IsSetDetail() bool {
	return p.Detail != nil
}

func (p *ChatMessageImageURL) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatMessageImageURL(%+v)", *p)
}

type ChatToolCall struct {
	Index    *int32        `thrift:"Index,1,optional" json:"index"`
	ID       *string       `thrift:"ID,2,optional" json:"id"`
	Type     string        `thrift:"Type,3,required" json:"type"`
	Function *FunctionCall `thrift:"Function,4,required" json:"function"`
}

func NewChatToolCall() *ChatToolCall {
	return &ChatToolCall{}
}

func (p *ChatToolCall) InitDefault() {
}

var ChatToolCall_Index_DEFAULT int32

func (p *ChatToolCall) GetIndex() (v int32) {
	if !p.IsSetIndex() {
		return ChatToolCall_Index_DEFAULT
	}
	return *p.Index
}

var ChatToolCall_ID_DEFAULT string

func (p *ChatToolCall) GetID() (v string) {
	if !p.IsSetID() {
		return ChatToolCall_ID_DEFAULT
	}
	return *p.ID
}

func (p *ChatToolCall) GetType() (v string) {
	return p.Type
}

var ChatToolCall_Function_DEFAULT *FunctionCall

func (p *ChatToolCall) GetFunction() (v *FunctionCall) {
	if !p.IsSetFunction() {
		return ChatToolCall_Function_DEFAULT
	}
	return p.Function
}

func (p *ChatToolCall) IsSetIndex() bool {
	return p.Index != nil
}

func (p *ChatToolCall) IsSetID() bool {
	return p.ID != nil
}

func (p *ChatToolCall) IsSetFunction() bool {
	return p.Function != nil
}

func (p *ChatToolCall) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChatToolCall(%+v)", *p)
}

type FunctionCall struct {
	Name      *string `thrift:"Name,1,optional" json:"name"`
	Arguments *string `thrift:"Arguments,2,optional" json:"arguments"`
}

func NewFunctionCall() *FunctionCall {
	return &FunctionCall{}
}

func (p *FunctionCall) InitDefault() {
}

var FunctionCall_Name_DEFAULT string

func (p *FunctionCall) GetName() (v string) {
	if !p.IsSetName() {
		return FunctionCall_Name_DEFAULT
	}
	return *p.Name
}

var FunctionCall_Arguments_DEFAULT string

func (p *FunctionCall) GetArguments() (v string) {
	if !p.IsSetArguments() {
		return FunctionCall_Arguments_DEFAULT
	}
	return *p.Arguments
}

func (p *FunctionCall) IsSetName() bool {
	return p.Name != nil
}

func (p *FunctionCall) IsSetArguments() bool {
	return p.Arguments != nil
}

func (p *FunctionCall) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FunctionCall(%+v)", *p)
}

type Tool struct {
	Type     string              `thrift:"Type,1,required" json:"type"`
	Function *FunctionDefinition `thrift:"Function,2,optional" json:"function"`
}

func NewTool() *Tool {
	return &Tool{}
}

func (p *Tool) InitDefault() {
}

func (p *Tool) GetType() (v string) {
	return p.Type
}

var Tool_Function_DEFAULT *FunctionDefinition

func (p *Tool) GetFunction() (v *FunctionDefinition) {
	if !p.IsSetFunction() {
		return Tool_Function_DEFAULT
	}
	return p.Function
}

func (p *Tool) IsSetFunction() bool {
	return p.Function != nil
}

func (p *Tool) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Tool(%+v)", *p)
}

type ToolChoiceObject struct {
	Type     string        `thrift:"Type,1,required" json:"type"`
	Function *ToolFunction `thrift:"Function,2,required" json:"function"`
}

func NewToolChoiceObject() *ToolChoiceObject {
	return &ToolChoiceObject{}
}

func (p *ToolChoiceObject) InitDefault() {
}

func (p *ToolChoiceObject) GetType() (v string) {
	return p.Type
}

var ToolChoiceObject_Function_DEFAULT *ToolFunction

func (p *ToolChoiceObject) GetFunction() (v *ToolFunction) {
	if !p.IsSetFunction() {
		return ToolChoiceObject_Function_DEFAULT
	}
	return p.Function
}

func (p *ToolChoiceObject) IsSetFunction() bool {
	return p.Function != nil
}

func (p *ToolChoiceObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolChoiceObject(%+v)", *p)
}

type ToolFunction struct {
	Name string `thrift:"Name,1,required" json:"name"`
}

func NewToolFunction() *ToolFunction {
	return &ToolFunction{}
}

func (p *ToolFunction) InitDefault() {
}

func (p *ToolFunction) GetName() (v string) {
	return p.Name
}

func (p *ToolFunction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolFunction(%+v)", *p)
}

type FunctionDefinition struct {
	Name        string               `thrift:"Name,1,required" json:"name"`
	Description *string              `thrift:"Description,2,optional" json:"description"`
	Strict      *bool                `thrift:"Strict,3,optional" json:"strict"`
	Parameters  common.JsonVariables `thrift:"Parameters,4,required" json:"parameters"`
}

func NewFunctionDefinition() *FunctionDefinition {
	return &FunctionDefinition{}
}

func (p *FunctionDefinition) InitDefault() {
}

func (p *FunctionDefinition) GetName() (v string) {
	return p.Name
}

var FunctionDefinition_Description_DEFAULT string

func (p *FunctionDefinition) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return FunctionDefinition_Description_DEFAULT
	}
	return *p.Description
}

var FunctionDefinition_Strict_DEFAULT bool

func (p *FunctionDefinition) GetStrict() (v bool) {
	if !p.IsSetStrict() {
		return FunctionDefinition_Strict_DEFAULT
	}
	return *p.Strict
}

func (p *FunctionDefinition) GetParameters() (v common.JsonVariables) {
	return p.Parameters
}

func (p *FunctionDefinition) IsSetDescription() bool {
	return p.Description != nil
}

func (p *FunctionDefinition) IsSetStrict() bool {
	return p.Strict != nil
}

func (p *FunctionDefinition) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FunctionDefinition(%+v)", *p)
}

type ThinkingConfig struct {
	Type         *string `thrift:"Type,1,optional" json:"type"`
	BudgetTokens *int32  `thrift:"BudgetTokens,2,optional" json:"budget_tokens"`
}

func NewThinkingConfig() *ThinkingConfig {
	return &ThinkingConfig{}
}

func (p *ThinkingConfig) InitDefault() {
}

var ThinkingConfig_Type_DEFAULT string

func (p *ThinkingConfig) GetType() (v string) {
	if !p.IsSetType() {
		return ThinkingConfig_Type_DEFAULT
	}
	return *p.Type
}

var ThinkingConfig_BudgetTokens_DEFAULT int32

func (p *ThinkingConfig) GetBudgetTokens() (v int32) {
	if !p.IsSetBudgetTokens() {
		return ThinkingConfig_BudgetTokens_DEFAULT
	}
	return *p.BudgetTokens
}

func (p *ThinkingConfig) IsSetType() bool {
	return p.Type != nil
}

func (p *ThinkingConfig) IsSetBudgetTokens() bool {
	return p.BudgetTokens != nil
}

func (p *ThinkingConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ThinkingConfig(%+v)", *p)
}

type ListSessionDocumentsRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
}

func NewListSessionDocumentsRequest() *ListSessionDocumentsRequest {
	return &ListSessionDocumentsRequest{}
}

func (p *ListSessionDocumentsRequest) InitDefault() {
}

func (p *ListSessionDocumentsRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ListSessionDocumentsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSessionDocumentsRequest(%+v)", *p)
}

type ListSessionDocumentsResponse struct {
	Documents []*Document `thrift:"Documents,1,required" json:"documents"`
}

func NewListSessionDocumentsResponse() *ListSessionDocumentsResponse {
	return &ListSessionDocumentsResponse{}
}

func (p *ListSessionDocumentsResponse) InitDefault() {
}

func (p *ListSessionDocumentsResponse) GetDocuments() (v []*Document) {
	return p.Documents
}

func (p *ListSessionDocumentsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSessionDocumentsResponse(%+v)", *p)
}

type Document struct {
	Name         string   `thrift:"Name,1,required" json:"name"`
	FilePath     string   `thrift:"FilePath,2,required" json:"file_path"`
	Type         string   `thrift:"Type,3,required" json:"type"`
	Content      string   `thrift:"Content,4,required" json:"content"`
	Size         int64    `thrift:"Size,5,required" json:"size"`
	ArtifactID   *string  `thrift:"ArtifactID,6,optional" json:"artifact_id"`
	RelatedFiles []string `thrift:"RelatedFiles,7,required" json:"related_files"`
	CreatedAt    string   `thrift:"CreatedAt,8,required" json:"created_at"`
}

func NewDocument() *Document {
	return &Document{}
}

func (p *Document) InitDefault() {
}

func (p *Document) GetName() (v string) {
	return p.Name
}

func (p *Document) GetFilePath() (v string) {
	return p.FilePath
}

func (p *Document) GetType() (v string) {
	return p.Type
}

func (p *Document) GetContent() (v string) {
	return p.Content
}

func (p *Document) GetSize() (v int64) {
	return p.Size
}

var Document_ArtifactID_DEFAULT string

func (p *Document) GetArtifactID() (v string) {
	if !p.IsSetArtifactID() {
		return Document_ArtifactID_DEFAULT
	}
	return *p.ArtifactID
}

func (p *Document) GetRelatedFiles() (v []string) {
	return p.RelatedFiles
}

func (p *Document) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Document) IsSetArtifactID() bool {
	return p.ArtifactID != nil
}

func (p *Document) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Document(%+v)", *p)
}

type ConvertSessionDocumentToLarkRequest struct {
	SessionID       string  `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
	FilePath        string  `thrift:"FilePath,2,required" json:"file_path,required" query:"file_path,required"`
	ArtifactID      *string `thrift:"ArtifactID,3,optional" json:"artifact_id,omitempty" query:"artifact_id"`
	ForceRegenerate *bool   `thrift:"ForceRegenerate,4,optional" json:"force_regenerate,omitempty" query:"force_regenerate"`
}

func NewConvertSessionDocumentToLarkRequest() *ConvertSessionDocumentToLarkRequest {
	return &ConvertSessionDocumentToLarkRequest{}
}

func (p *ConvertSessionDocumentToLarkRequest) InitDefault() {
}

func (p *ConvertSessionDocumentToLarkRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ConvertSessionDocumentToLarkRequest) GetFilePath() (v string) {
	return p.FilePath
}

var ConvertSessionDocumentToLarkRequest_ArtifactID_DEFAULT string

func (p *ConvertSessionDocumentToLarkRequest) GetArtifactID() (v string) {
	if !p.IsSetArtifactID() {
		return ConvertSessionDocumentToLarkRequest_ArtifactID_DEFAULT
	}
	return *p.ArtifactID
}

var ConvertSessionDocumentToLarkRequest_ForceRegenerate_DEFAULT bool

func (p *ConvertSessionDocumentToLarkRequest) GetForceRegenerate() (v bool) {
	if !p.IsSetForceRegenerate() {
		return ConvertSessionDocumentToLarkRequest_ForceRegenerate_DEFAULT
	}
	return *p.ForceRegenerate
}

func (p *ConvertSessionDocumentToLarkRequest) IsSetArtifactID() bool {
	return p.ArtifactID != nil
}

func (p *ConvertSessionDocumentToLarkRequest) IsSetForceRegenerate() bool {
	return p.ForceRegenerate != nil
}

func (p *ConvertSessionDocumentToLarkRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertSessionDocumentToLarkRequest(%+v)", *p)
}

type ConvertSessionDocumentToLarkResponse struct {
	LarkURL string `thrift:"LarkURL,1,required" json:"lark_url"`
}

func NewConvertSessionDocumentToLarkResponse() *ConvertSessionDocumentToLarkResponse {
	return &ConvertSessionDocumentToLarkResponse{}
}

func (p *ConvertSessionDocumentToLarkResponse) InitDefault() {
}

func (p *ConvertSessionDocumentToLarkResponse) GetLarkURL() (v string) {
	return p.LarkURL
}

func (p *ConvertSessionDocumentToLarkResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertSessionDocumentToLarkResponse(%+v)", *p)
}

type TraceMCPEventsRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
}

func NewTraceMCPEventsRequest() *TraceMCPEventsRequest {
	return &TraceMCPEventsRequest{}
}

func (p *TraceMCPEventsRequest) InitDefault() {
}

func (p *TraceMCPEventsRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *TraceMCPEventsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TraceMCPEventsRequest(%+v)", *p)
}

type ConvertMarkdownToLarkRequest struct {
	Content   string  `thrift:"Content,1,required" json:"content,required" `
	SessionID *string `thrift:"SessionID,2,optional" json:"session_id,omitempty" `
}

func NewConvertMarkdownToLarkRequest() *ConvertMarkdownToLarkRequest {
	return &ConvertMarkdownToLarkRequest{}
}

func (p *ConvertMarkdownToLarkRequest) InitDefault() {
}

func (p *ConvertMarkdownToLarkRequest) GetContent() (v string) {
	return p.Content
}

var ConvertMarkdownToLarkRequest_SessionID_DEFAULT string

func (p *ConvertMarkdownToLarkRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return ConvertMarkdownToLarkRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

func (p *ConvertMarkdownToLarkRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *ConvertMarkdownToLarkRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertMarkdownToLarkRequest(%+v)", *p)
}

type ConvertMarkdownToLarkResponse struct {
	LarkURL string `thrift:"LarkURL,1,required" json:"lark_url"`
}

func NewConvertMarkdownToLarkResponse() *ConvertMarkdownToLarkResponse {
	return &ConvertMarkdownToLarkResponse{}
}

func (p *ConvertMarkdownToLarkResponse) InitDefault() {
}

func (p *ConvertMarkdownToLarkResponse) GetLarkURL() (v string) {
	return p.LarkURL
}

func (p *ConvertMarkdownToLarkResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertMarkdownToLarkResponse(%+v)", *p)
}

type ToolChoiceType struct {
	StringChoice *string           `thrift:"StringChoice,1,optional"`
	ObjectChoice *ToolChoiceObject `thrift:"ObjectChoice,2,optional"`
}

func NewToolChoiceType() *ToolChoiceType {
	return &ToolChoiceType{}
}

func (p *ToolChoiceType) InitDefault() {
}

var ToolChoiceType_StringChoice_DEFAULT string

func (p *ToolChoiceType) GetStringChoice() (v string) {
	if !p.IsSetStringChoice() {
		return ToolChoiceType_StringChoice_DEFAULT
	}
	return *p.StringChoice
}

var ToolChoiceType_ObjectChoice_DEFAULT *ToolChoiceObject

func (p *ToolChoiceType) GetObjectChoice() (v *ToolChoiceObject) {
	if !p.IsSetObjectChoice() {
		return ToolChoiceType_ObjectChoice_DEFAULT
	}
	return p.ObjectChoice
}

func (p *ToolChoiceType) CountSetFieldsToolChoiceType() int {
	count := 0
	if p.IsSetStringChoice() {
		count++
	}
	if p.IsSetObjectChoice() {
		count++
	}
	return count
}

func (p *ToolChoiceType) IsSetStringChoice() bool {
	return p.StringChoice != nil
}

func (p *ToolChoiceType) IsSetObjectChoice() bool {
	return p.ObjectChoice != nil
}

func (p *ToolChoiceType) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolChoiceType(%+v)", *p)
}
