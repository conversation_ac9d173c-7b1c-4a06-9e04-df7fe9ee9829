// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

const (
	PermissionActionUnknown = "unknown"
	// Session 模块
	PermissionActionSessionCreate = "session.create"

	PermissionActionSessionDelete = "session.delete"

	PermissionActionSessionRead = "session.read"

	PermissionActionSessionUpdate = "session.update"

	PermissionActionSessionAllFile = "session.all_file"

	PermissionActionSessionChat = "session.chat"

	PermissionActionSessionVisualization = "session.visualization"

	PermissionActionSessionTemplateCreate = "session.template.create"

	PermissionActionSessionFeedback = "session.feedback"

	PermissionActionSessionDownload = "session.download"

	PermissionActionSessionShareCreate = "session.share.create"

	PermissionActionSessionVSCodeOpen = "session.vscode.open"

	PermissionActionSessionTraceRead = "session.trace.read"

	PermissionActionSessionMCPTraceRead = "session.mcp_trace.read"
	// MCP 模块
	PermissionActionMCPCreate = "mcp.create"

	PermissionActionMCPRead = "mcp.read"

	PermissionActionMCPUpdate = "mcp.update"

	PermissionActionMCPDelete = "mcp.delete"
	// Template 模块
	PermissionActionTemplateRead = "template.read"

	PermissionActionTemplateUpdate = "template.update"

	PermissionActionTemplateDelete = "template.delete"

	PermissionActionTemplateCreate = "template.create"
	// Knowledgebase 模块
	PermissionActionKnowledgebaseRead = "knowledgebase.read"

	PermissionActionKnowledgebaseUpdate = "knowledgebase.update"

	PermissionActionKnowledgebaseDelete = "knowledgebase.delete"

	PermissionActionKnowledgebaseCreate = "knowledgebase.create"
	// Space 模块
	PermissionActionSpaceCreate = "space.create"

	PermissionActionSpaceUpdate = "space.update"

	PermissionActionSpaceDelete = "space.delete"

	PermissionActionSpaceRead = "space.read"

	PermissionActionSpaceDevResourceUpdate = "space.dev_resource_update"
	// Agent模块
	PermissionActionAgentCreate = "agent.create"

	PermissionActionAgentUpdate = "agent.update"

	PermissionActionAgentDelete = "agent.delete"

	PermissionActionAgentRead = "agent.read"

	PermissionTypeUnknown = "unknown"

	PermissionTypeUser = "user"

	PermissionTypeDepartment = "department"

	PermissionTypeSpace = "space"

	PermissionTypeServiceAccount = "service_accout"

	ResourceTypeUnknown = "unknown"

	ResourceTypeSession = "session"

	ResourceTypeArtifact = "artifact"

	ResourceTypeMCP = "mcp"

	ResourceTypeTemplate = "template"

	ResourceTypeSpace = "space"

	ResourceTypeKnowledgebase = "knowledgebase"

	ResourceStatusUnknown = "unknown"

	ResourceStatusPublic = "public"

	ResourceStatusPrivate = "private"
)

type PermissionRole int64

const (
	PermissionRole_PermissionRoleUnknown  PermissionRole = 0
	PermissionRole_PermissionRoleVisitor  PermissionRole = 1000
	PermissionRole_PermissionRoleMember   PermissionRole = 2000
	PermissionRole_PermissionRoleMananger PermissionRole = 3000
)

func (p PermissionRole) String() string {
	switch p {
	case PermissionRole_PermissionRoleUnknown:
		return "PermissionRoleUnknown"
	case PermissionRole_PermissionRoleVisitor:
		return "PermissionRoleVisitor"
	case PermissionRole_PermissionRoleMember:
		return "PermissionRoleMember"
	case PermissionRole_PermissionRoleMananger:
		return "PermissionRoleMananger"
	}
	return "<UNSET>"
}

func PermissionRoleFromString(s string) (PermissionRole, error) {
	switch s {
	case "PermissionRoleUnknown":
		return PermissionRole_PermissionRoleUnknown, nil
	case "PermissionRoleVisitor":
		return PermissionRole_PermissionRoleVisitor, nil
	case "PermissionRoleMember":
		return PermissionRole_PermissionRoleMember, nil
	case "PermissionRoleMananger":
		return PermissionRole_PermissionRoleMananger, nil
	}
	return PermissionRole(0), fmt.Errorf("not a valid PermissionRole string")
}

func PermissionRolePtr(v PermissionRole) *PermissionRole { return &v }
func (p *PermissionRole) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PermissionRole(result.Int64)
	return
}

func (p *PermissionRole) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PermissionAction = string

type PermissionType = string

type ResourceType = string

type ResourceStatus = string

type ResourcePermission struct {
	ID           string         `thrift:"ID,1,required" json:"id"`
	ResourceID   string         `thrift:"ResourceID,2,required" json:"resource_id"`
	ResourceType ResourceType   `thrift:"ResourceType,3,required" json:"resource_type"`
	Type         PermissionType `thrift:"Type,4,required" json:"type"`
	// Usernme / DepartmentName / SpaceID
	ExternalID string             `thrift:"ExternalID,5,required" json:"external_id"`
	Role       PermissionRole     `thrift:"Role,6,required" json:"role"`
	CreatedAt  string             `thrift:"CreatedAt,7,required" json:"created_at"`
	UpdatedAt  string             `thrift:"UpdatedAt,8,required" json:"updated_at"`
	Actions    []PermissionAction `thrift:"Actions,9,optional" json:"actions"`
}

func NewResourcePermission() *ResourcePermission {
	return &ResourcePermission{}
}

func (p *ResourcePermission) InitDefault() {
}

func (p *ResourcePermission) GetID() (v string) {
	return p.ID
}

func (p *ResourcePermission) GetResourceID() (v string) {
	return p.ResourceID
}

func (p *ResourcePermission) GetResourceType() (v ResourceType) {
	return p.ResourceType
}

func (p *ResourcePermission) GetType() (v PermissionType) {
	return p.Type
}

func (p *ResourcePermission) GetExternalID() (v string) {
	return p.ExternalID
}

func (p *ResourcePermission) GetRole() (v PermissionRole) {
	return p.Role
}

func (p *ResourcePermission) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *ResourcePermission) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

var ResourcePermission_Actions_DEFAULT []PermissionAction

func (p *ResourcePermission) GetActions() (v []PermissionAction) {
	if !p.IsSetActions() {
		return ResourcePermission_Actions_DEFAULT
	}
	return p.Actions
}

func (p *ResourcePermission) IsSetActions() bool {
	return p.Actions != nil
}

func (p *ResourcePermission) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourcePermission(%+v)", *p)
}

type Group struct {
	ResourceID string `thrift:"ResourceID,1,required" json:"resource_id"`
	GroupID    string `thrift:"GroupID,2,required" json:"group_id"`
}

func NewGroup() *Group {
	return &Group{}
}

func (p *Group) InitDefault() {
}

func (p *Group) GetResourceID() (v string) {
	return p.ResourceID
}

func (p *Group) GetGroupID() (v string) {
	return p.GroupID
}

func (p *Group) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Group(%+v)", *p)
}

type Resource struct {
	ID         string         `thrift:"ID,1,required" json:"id"`
	Type       ResourceType   `thrift:"Type,2,required" json:"type"`
	ExternalID string         `thrift:"ExternalID,3,required" json:"external_id"`
	Owner      string         `thrift:"Owner,4,required" json:"owner"`
	Status     ResourceStatus `thrift:"Status,5,required" json:"status"`
	CreatedAt  string         `thrift:"CreatedAt,6,required" json:"created_at"`
	UpdatedAt  string         `thrift:"UpdatedAt,7,required" json:"updated_at"`
	// 关联数据，按需返回
	Groups []*Group `thrift:"Groups,8,optional" json:"groups"`
	// 一个用户可能存在多个不同的角色，不同的角色有不同的权限点取并集
	Permissions []*ResourcePermission `thrift:"Permissions,9,optional" json:"permissions"`
}

func NewResource() *Resource {
	return &Resource{}
}

func (p *Resource) InitDefault() {
}

func (p *Resource) GetID() (v string) {
	return p.ID
}

func (p *Resource) GetType() (v ResourceType) {
	return p.Type
}

func (p *Resource) GetExternalID() (v string) {
	return p.ExternalID
}

func (p *Resource) GetOwner() (v string) {
	return p.Owner
}

func (p *Resource) GetStatus() (v ResourceStatus) {
	return p.Status
}

func (p *Resource) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Resource) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

var Resource_Groups_DEFAULT []*Group

func (p *Resource) GetGroups() (v []*Group) {
	if !p.IsSetGroups() {
		return Resource_Groups_DEFAULT
	}
	return p.Groups
}

var Resource_Permissions_DEFAULT []*ResourcePermission

func (p *Resource) GetPermissions() (v []*ResourcePermission) {
	if !p.IsSetPermissions() {
		return Resource_Permissions_DEFAULT
	}
	return p.Permissions
}

func (p *Resource) IsSetGroups() bool {
	return p.Groups != nil
}

func (p *Resource) IsSetPermissions() bool {
	return p.Permissions != nil
}

func (p *Resource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Resource(%+v)", *p)
}

type GetUserResourcePermissionRequest struct {
	// User 取当前登陆的用户
	// ID 不存在时，使用 ExternalID + Type 的组合查询资源, 比如查当前用户所在空间的权限，external_id 为 space_id,type 为 space, 也可以直接使用 resource id
	ResourceID *string       `thrift:"ResourceID,1,optional" json:"resource_id,omitempty" query:"resource_id"`
	ExternalID *string       `thrift:"ExternalID,2,optional" json:"external_id,omitempty" query:"external_id"`
	Type       *ResourceType `thrift:"Type,3,optional" json:"type,omitempty" query:"type"`
}

func NewGetUserResourcePermissionRequest() *GetUserResourcePermissionRequest {
	return &GetUserResourcePermissionRequest{}
}

func (p *GetUserResourcePermissionRequest) InitDefault() {
}

var GetUserResourcePermissionRequest_ResourceID_DEFAULT string

func (p *GetUserResourcePermissionRequest) GetResourceID() (v string) {
	if !p.IsSetResourceID() {
		return GetUserResourcePermissionRequest_ResourceID_DEFAULT
	}
	return *p.ResourceID
}

var GetUserResourcePermissionRequest_ExternalID_DEFAULT string

func (p *GetUserResourcePermissionRequest) GetExternalID() (v string) {
	if !p.IsSetExternalID() {
		return GetUserResourcePermissionRequest_ExternalID_DEFAULT
	}
	return *p.ExternalID
}

var GetUserResourcePermissionRequest_Type_DEFAULT ResourceType

func (p *GetUserResourcePermissionRequest) GetType() (v ResourceType) {
	if !p.IsSetType() {
		return GetUserResourcePermissionRequest_Type_DEFAULT
	}
	return *p.Type
}

func (p *GetUserResourcePermissionRequest) IsSetResourceID() bool {
	return p.ResourceID != nil
}

func (p *GetUserResourcePermissionRequest) IsSetExternalID() bool {
	return p.ExternalID != nil
}

func (p *GetUserResourcePermissionRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *GetUserResourcePermissionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserResourcePermissionRequest(%+v)", *p)
}

type GetUserResourcePermissionResponse struct {
	Resource *Resource `thrift:"Resource,1,required" json:"resource"`
}

func NewGetUserResourcePermissionResponse() *GetUserResourcePermissionResponse {
	return &GetUserResourcePermissionResponse{}
}

func (p *GetUserResourcePermissionResponse) InitDefault() {
}

var GetUserResourcePermissionResponse_Resource_DEFAULT *Resource

func (p *GetUserResourcePermissionResponse) GetResource() (v *Resource) {
	if !p.IsSetResource() {
		return GetUserResourcePermissionResponse_Resource_DEFAULT
	}
	return p.Resource
}

func (p *GetUserResourcePermissionResponse) IsSetResource() bool {
	return p.Resource != nil
}

func (p *GetUserResourcePermissionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserResourcePermissionResponse(%+v)", *p)
}
