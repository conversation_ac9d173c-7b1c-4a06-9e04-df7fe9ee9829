// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"fmt"
)

const (
	DocumentContentTypeDoc = "doc"

	DocumentContentTypeSheet = "sheet"

	DocumentContentTypeBitable = "bitable"

	KnowledgeTaskStatusFinished = "finished"

	KnowledgeTaskStatusProcessing = "processing"

	ListDocumentsDescOrderByLastUpdatedAt = "last_updated_at"

	ListDocumentsDescOrderByHeat = "heat"

	ListDocumentsDescOrderByUpdatedAt = "updated_at"

	ImportTypeSingle = "single"

	ImportTypeWikiTree = "wiki_tree"
)

type DocumentContentType = string

type KnowledgeTaskStatus = string

type ListDocumentsDescOrderBy = string

type ImportType = string

type UploadDocumentsRequest struct {
	DatasetID    string   `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	DocumentURLs []string `thrift:"DocumentURLs,2,required" json:"document_urls"`
	// 导入的知识库列表
	WikiList []*WikiDocConfig `thrift:"WikiList,3,optional" json:"wiki_list"`
}

func NewUploadDocumentsRequest() *UploadDocumentsRequest {
	return &UploadDocumentsRequest{}
}

func (p *UploadDocumentsRequest) InitDefault() {
}

func (p *UploadDocumentsRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *UploadDocumentsRequest) GetDocumentURLs() (v []string) {
	return p.DocumentURLs
}

var UploadDocumentsRequest_WikiList_DEFAULT []*WikiDocConfig

func (p *UploadDocumentsRequest) GetWikiList() (v []*WikiDocConfig) {
	if !p.IsSetWikiList() {
		return UploadDocumentsRequest_WikiList_DEFAULT
	}
	return p.WikiList
}

func (p *UploadDocumentsRequest) IsSetWikiList() bool {
	return p.WikiList != nil
}

func (p *UploadDocumentsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadDocumentsRequest(%+v)", *p)
}

type UploadDocumentsResponse struct {
	Documents []*KnowledgeBaseDocument `thrift:"Documents,1" json:"documents" `
}

func NewUploadDocumentsResponse() *UploadDocumentsResponse {
	return &UploadDocumentsResponse{}
}

func (p *UploadDocumentsResponse) InitDefault() {
}

func (p *UploadDocumentsResponse) GetDocuments() (v []*KnowledgeBaseDocument) {
	return p.Documents
}

func (p *UploadDocumentsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadDocumentsResponse(%+v)", *p)
}

type KnowledgeBaseDocument struct {
	ID            string              `thrift:"ID,1" json:"id"`
	Title         string              `thrift:"Title,2" json:"title"`
	Creator       string              `thrift:"Creator,3" json:"creator"`
	URL           string              `thrift:"URL,4" json:"url"`
	UpdatedAt     string              `thrift:"UpdatedAt,5" json:"updated_at"`
	Owner         string              `thrift:"Owner,6" json:"owner"`
	CreatedAt     string              `thrift:"CreatedAt,7" json:"created_at"`
	Content       *string             `thrift:"Content,8,optional" json:"content"`
	Heat          *int64              `thrift:"Heat,9,optional" json:"heat"`
	DatasetID     string              `thrift:"DatasetID,10" json:"dataset_id"`
	ContentType   DocumentContentType `thrift:"ContentType,11" json:"content_type"`
	ImportType    ImportType          `thrift:"ImportType,12" json:"import_type"`
	ProcessStatus ProcessStatus       `thrift:"ProcessStatus,13" json:"process_status"`
	WikiSpaceName *string             `thrift:"WikiSpaceName,14,optional" json:"wiki_space_name"`
	FailedReason  *string             `thrift:"FailedReason,15,optional" json:"failed_reason"`
}

func NewKnowledgeBaseDocument() *KnowledgeBaseDocument {
	return &KnowledgeBaseDocument{}
}

func (p *KnowledgeBaseDocument) InitDefault() {
}

func (p *KnowledgeBaseDocument) GetID() (v string) {
	return p.ID
}

func (p *KnowledgeBaseDocument) GetTitle() (v string) {
	return p.Title
}

func (p *KnowledgeBaseDocument) GetCreator() (v string) {
	return p.Creator
}

func (p *KnowledgeBaseDocument) GetURL() (v string) {
	return p.URL
}

func (p *KnowledgeBaseDocument) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *KnowledgeBaseDocument) GetOwner() (v string) {
	return p.Owner
}

func (p *KnowledgeBaseDocument) GetCreatedAt() (v string) {
	return p.CreatedAt
}

var KnowledgeBaseDocument_Content_DEFAULT string

func (p *KnowledgeBaseDocument) GetContent() (v string) {
	if !p.IsSetContent() {
		return KnowledgeBaseDocument_Content_DEFAULT
	}
	return *p.Content
}

var KnowledgeBaseDocument_Heat_DEFAULT int64

func (p *KnowledgeBaseDocument) GetHeat() (v int64) {
	if !p.IsSetHeat() {
		return KnowledgeBaseDocument_Heat_DEFAULT
	}
	return *p.Heat
}

func (p *KnowledgeBaseDocument) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *KnowledgeBaseDocument) GetContentType() (v DocumentContentType) {
	return p.ContentType
}

func (p *KnowledgeBaseDocument) GetImportType() (v ImportType) {
	return p.ImportType
}

func (p *KnowledgeBaseDocument) GetProcessStatus() (v ProcessStatus) {
	return p.ProcessStatus
}

var KnowledgeBaseDocument_WikiSpaceName_DEFAULT string

func (p *KnowledgeBaseDocument) GetWikiSpaceName() (v string) {
	if !p.IsSetWikiSpaceName() {
		return KnowledgeBaseDocument_WikiSpaceName_DEFAULT
	}
	return *p.WikiSpaceName
}

var KnowledgeBaseDocument_FailedReason_DEFAULT string

func (p *KnowledgeBaseDocument) GetFailedReason() (v string) {
	if !p.IsSetFailedReason() {
		return KnowledgeBaseDocument_FailedReason_DEFAULT
	}
	return *p.FailedReason
}

func (p *KnowledgeBaseDocument) IsSetContent() bool {
	return p.Content != nil
}

func (p *KnowledgeBaseDocument) IsSetHeat() bool {
	return p.Heat != nil
}

func (p *KnowledgeBaseDocument) IsSetWikiSpaceName() bool {
	return p.WikiSpaceName != nil
}

func (p *KnowledgeBaseDocument) IsSetFailedReason() bool {
	return p.FailedReason != nil
}

func (p *KnowledgeBaseDocument) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KnowledgeBaseDocument(%+v)", *p)
}

type DeleteDocumentRequest struct {
	DatasetID  string `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	DocumentID string `thrift:"DocumentID,2,required" json:"document_id" path:"document_id,required" `
}

func NewDeleteDocumentRequest() *DeleteDocumentRequest {
	return &DeleteDocumentRequest{}
}

func (p *DeleteDocumentRequest) InitDefault() {
}

func (p *DeleteDocumentRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *DeleteDocumentRequest) GetDocumentID() (v string) {
	return p.DocumentID
}

func (p *DeleteDocumentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDocumentRequest(%+v)", *p)
}

type DeleteDocumentResponse struct {
}

func NewDeleteDocumentResponse() *DeleteDocumentResponse {
	return &DeleteDocumentResponse{}
}

func (p *DeleteDocumentResponse) InitDefault() {
}

func (p *DeleteDocumentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDocumentResponse(%+v)", *p)
}

type BatchDeleteDocumentRequest struct {
	DatasetID   string   `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	DocumentIDs []string `thrift:"DocumentIDs,2,required" json:"document_ids" form:"document_ids,required" `
}

func NewBatchDeleteDocumentRequest() *BatchDeleteDocumentRequest {
	return &BatchDeleteDocumentRequest{}
}

func (p *BatchDeleteDocumentRequest) InitDefault() {
}

func (p *BatchDeleteDocumentRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *BatchDeleteDocumentRequest) GetDocumentIDs() (v []string) {
	return p.DocumentIDs
}

func (p *BatchDeleteDocumentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchDeleteDocumentRequest(%+v)", *p)
}

type BatchDeleteDocumentResponse struct {
}

func NewBatchDeleteDocumentResponse() *BatchDeleteDocumentResponse {
	return &BatchDeleteDocumentResponse{}
}

func (p *BatchDeleteDocumentResponse) InitDefault() {
}

func (p *BatchDeleteDocumentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchDeleteDocumentResponse(%+v)", *p)
}

type UpdateDocumentRequest struct {
	DatasetID  string `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	DocumentID string `thrift:"DocumentID,2,required" json:"document_id" path:"document_id,required" `
}

func NewUpdateDocumentRequest() *UpdateDocumentRequest {
	return &UpdateDocumentRequest{}
}

func (p *UpdateDocumentRequest) InitDefault() {
}

func (p *UpdateDocumentRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *UpdateDocumentRequest) GetDocumentID() (v string) {
	return p.DocumentID
}

func (p *UpdateDocumentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateDocumentRequest(%+v)", *p)
}

type UpdateDocumentResponse struct {
}

func NewUpdateDocumentResponse() *UpdateDocumentResponse {
	return &UpdateDocumentResponse{}
}

func (p *UpdateDocumentResponse) InitDefault() {
}

func (p *UpdateDocumentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateDocumentResponse(%+v)", *p)
}

type BatchUpdateDocumentRequest struct {
	DatasetID   string   `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	DocumentIDs []string `thrift:"DocumentIDs,2,required" json:"document_ids" form:"document_ids,required" `
}

func NewBatchUpdateDocumentRequest() *BatchUpdateDocumentRequest {
	return &BatchUpdateDocumentRequest{}
}

func (p *BatchUpdateDocumentRequest) InitDefault() {
}

func (p *BatchUpdateDocumentRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *BatchUpdateDocumentRequest) GetDocumentIDs() (v []string) {
	return p.DocumentIDs
}

func (p *BatchUpdateDocumentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchUpdateDocumentRequest(%+v)", *p)
}

type BatchUpdateDocumentResponse struct {
}

func NewBatchUpdateDocumentResponse() *BatchUpdateDocumentResponse {
	return &BatchUpdateDocumentResponse{}
}

func (p *BatchUpdateDocumentResponse) InitDefault() {
}

func (p *BatchUpdateDocumentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchUpdateDocumentResponse(%+v)", *p)
}

type GetKnowledgeTaskStatusRequest struct {
	DatasetID string `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
}

func NewGetKnowledgeTaskStatusRequest() *GetKnowledgeTaskStatusRequest {
	return &GetKnowledgeTaskStatusRequest{}
}

func (p *GetKnowledgeTaskStatusRequest) InitDefault() {
}

func (p *GetKnowledgeTaskStatusRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *GetKnowledgeTaskStatusRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetKnowledgeTaskStatusRequest(%+v)", *p)
}

type GetKnowledgeTaskStatusResponse struct {
	Status KnowledgeTaskStatus `thrift:"Status,1" json:"status"`
}

func NewGetKnowledgeTaskStatusResponse() *GetKnowledgeTaskStatusResponse {
	return &GetKnowledgeTaskStatusResponse{}
}

func (p *GetKnowledgeTaskStatusResponse) InitDefault() {
}

func (p *GetKnowledgeTaskStatusResponse) GetStatus() (v KnowledgeTaskStatus) {
	return p.Status
}

func (p *GetKnowledgeTaskStatusResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetKnowledgeTaskStatusResponse(%+v)", *p)
}

type ListDocumentsRequest struct {
	DatasetID     string                    `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	Query         *string                   `thrift:"Query,2,optional" json:"query"`
	Creators      []string                  `thrift:"Creators,3,optional" json:"creators"`
	PageNum       int64                     `thrift:"PageNum,4,required" json:"page_num"`
	PageSize      int64                     `thrift:"PageSize,5,required" json:"page_size"`
	DescOrderBy   *ListDocumentsDescOrderBy `thrift:"DescOrderBy,6,optional" json:"desc_order_by"`
	ProcessStatus []ProcessStatus           `thrift:"ProcessStatus,7,optional" json:"process_status"`
}

func NewListDocumentsRequest() *ListDocumentsRequest {
	return &ListDocumentsRequest{}
}

func (p *ListDocumentsRequest) InitDefault() {
}

func (p *ListDocumentsRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

var ListDocumentsRequest_Query_DEFAULT string

func (p *ListDocumentsRequest) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return ListDocumentsRequest_Query_DEFAULT
	}
	return *p.Query
}

var ListDocumentsRequest_Creators_DEFAULT []string

func (p *ListDocumentsRequest) GetCreators() (v []string) {
	if !p.IsSetCreators() {
		return ListDocumentsRequest_Creators_DEFAULT
	}
	return p.Creators
}

func (p *ListDocumentsRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListDocumentsRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var ListDocumentsRequest_DescOrderBy_DEFAULT ListDocumentsDescOrderBy

func (p *ListDocumentsRequest) GetDescOrderBy() (v ListDocumentsDescOrderBy) {
	if !p.IsSetDescOrderBy() {
		return ListDocumentsRequest_DescOrderBy_DEFAULT
	}
	return *p.DescOrderBy
}

var ListDocumentsRequest_ProcessStatus_DEFAULT []ProcessStatus

func (p *ListDocumentsRequest) GetProcessStatus() (v []ProcessStatus) {
	if !p.IsSetProcessStatus() {
		return ListDocumentsRequest_ProcessStatus_DEFAULT
	}
	return p.ProcessStatus
}

func (p *ListDocumentsRequest) IsSetQuery() bool {
	return p.Query != nil
}

func (p *ListDocumentsRequest) IsSetCreators() bool {
	return p.Creators != nil
}

func (p *ListDocumentsRequest) IsSetDescOrderBy() bool {
	return p.DescOrderBy != nil
}

func (p *ListDocumentsRequest) IsSetProcessStatus() bool {
	return p.ProcessStatus != nil
}

func (p *ListDocumentsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDocumentsRequest(%+v)", *p)
}

type ListDocumentsResponse struct {
	Total     int64                    `thrift:"Total,1" json:"total"`
	Documents []*KnowledgeBaseDocument `thrift:"Documents,2" json:"documents"`
}

func NewListDocumentsResponse() *ListDocumentsResponse {
	return &ListDocumentsResponse{}
}

func (p *ListDocumentsResponse) InitDefault() {
}

func (p *ListDocumentsResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListDocumentsResponse) GetDocuments() (v []*KnowledgeBaseDocument) {
	return p.Documents
}

func (p *ListDocumentsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDocumentsResponse(%+v)", *p)
}

type GetDocumentRequest struct {
	DatasetID  string `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	DocumentID string `thrift:"DocumentID,2,required" json:"document_id" path:"document_id,required" `
}

func NewGetDocumentRequest() *GetDocumentRequest {
	return &GetDocumentRequest{}
}

func (p *GetDocumentRequest) InitDefault() {
}

func (p *GetDocumentRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *GetDocumentRequest) GetDocumentID() (v string) {
	return p.DocumentID
}

func (p *GetDocumentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDocumentRequest(%+v)", *p)
}

type GetDocumentResponse struct {
	Document *KnowledgeBaseDocument `thrift:"Document,1" json:"document"`
}

func NewGetDocumentResponse() *GetDocumentResponse {
	return &GetDocumentResponse{}
}

func (p *GetDocumentResponse) InitDefault() {
}

var GetDocumentResponse_Document_DEFAULT *KnowledgeBaseDocument

func (p *GetDocumentResponse) GetDocument() (v *KnowledgeBaseDocument) {
	if !p.IsSetDocument() {
		return GetDocumentResponse_Document_DEFAULT
	}
	return p.Document
}

func (p *GetDocumentResponse) IsSetDocument() bool {
	return p.Document != nil
}

func (p *GetDocumentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDocumentResponse(%+v)", *p)
}

type LarkDocument struct {
	Title       string              `thrift:"Title,1" json:"title"`
	Content     string              `thrift:"Content,2" json:"content"`
	URL         string              `thrift:"URL,3" json:"url"`
	IsUploaded  bool                `thrift:"IsUploaded,4" json:"is_uploaded"`
	ContentType DocumentContentType `thrift:"ContentType,5" json:"content_type"`
	OwnerName   string              `thrift:"OwnerName,6" json:"owner_name"`
	HasChild    bool                `thrift:"HasChild,7" json:"has_child"`
	IsRoot      bool                `thrift:"IsRoot,8" json:"is_root"`
}

func NewLarkDocument() *LarkDocument {
	return &LarkDocument{}
}

func (p *LarkDocument) InitDefault() {
}

func (p *LarkDocument) GetTitle() (v string) {
	return p.Title
}

func (p *LarkDocument) GetContent() (v string) {
	return p.Content
}

func (p *LarkDocument) GetURL() (v string) {
	return p.URL
}

func (p *LarkDocument) GetIsUploaded() (v bool) {
	return p.IsUploaded
}

func (p *LarkDocument) GetContentType() (v DocumentContentType) {
	return p.ContentType
}

func (p *LarkDocument) GetOwnerName() (v string) {
	return p.OwnerName
}

func (p *LarkDocument) GetHasChild() (v bool) {
	return p.HasChild
}

func (p *LarkDocument) GetIsRoot() (v bool) {
	return p.IsRoot
}

func (p *LarkDocument) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkDocument(%+v)", *p)
}

type SearchLarkDocumentsRequest struct {
	Query      string      `thrift:"Query,1,required" json:"query" query:"query,required" `
	DatasetID  string      `thrift:"DatasetID,2,required" json:"dataset_id" path:"dataset_id,required" `
	ImportType *ImportType `thrift:"ImportType,3,optional" json:"import_type" query:"import_type" `
}

func NewSearchLarkDocumentsRequest() *SearchLarkDocumentsRequest {
	return &SearchLarkDocumentsRequest{}
}

func (p *SearchLarkDocumentsRequest) InitDefault() {
}

func (p *SearchLarkDocumentsRequest) GetQuery() (v string) {
	return p.Query
}

func (p *SearchLarkDocumentsRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

var SearchLarkDocumentsRequest_ImportType_DEFAULT ImportType

func (p *SearchLarkDocumentsRequest) GetImportType() (v ImportType) {
	if !p.IsSetImportType() {
		return SearchLarkDocumentsRequest_ImportType_DEFAULT
	}
	return *p.ImportType
}

func (p *SearchLarkDocumentsRequest) IsSetImportType() bool {
	return p.ImportType != nil
}

func (p *SearchLarkDocumentsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchLarkDocumentsRequest(%+v)", *p)
}

type SearchLarkDocumentsResponse struct {
	Documents []*LarkDocument `thrift:"Documents,1" json:"documents"`
	// 从知识库导入时返回
	WikiSpace *WikiSpace `thrift:"WikiSpace,2" json:"wiki_space"`
}

func NewSearchLarkDocumentsResponse() *SearchLarkDocumentsResponse {
	return &SearchLarkDocumentsResponse{}
}

func (p *SearchLarkDocumentsResponse) InitDefault() {
}

func (p *SearchLarkDocumentsResponse) GetDocuments() (v []*LarkDocument) {
	return p.Documents
}

var SearchLarkDocumentsResponse_WikiSpace_DEFAULT *WikiSpace

func (p *SearchLarkDocumentsResponse) GetWikiSpace() (v *WikiSpace) {
	if !p.IsSetWikiSpace() {
		return SearchLarkDocumentsResponse_WikiSpace_DEFAULT
	}
	return p.WikiSpace
}

func (p *SearchLarkDocumentsResponse) IsSetWikiSpace() bool {
	return p.WikiSpace != nil
}

func (p *SearchLarkDocumentsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchLarkDocumentsResponse(%+v)", *p)
}

type RecallDatasetRequest struct {
	DatasetID string `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	Query     string `thrift:"Query,2,required" json:"query" query:"query,required" `
	TopK      int64  `thrift:"TopK,3,required" json:"top_k"`
}

func NewRecallDatasetRequest() *RecallDatasetRequest {
	return &RecallDatasetRequest{}
}

func (p *RecallDatasetRequest) InitDefault() {
}

func (p *RecallDatasetRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *RecallDatasetRequest) GetQuery() (v string) {
	return p.Query
}

func (p *RecallDatasetRequest) GetTopK() (v int64) {
	return p.TopK
}

func (p *RecallDatasetRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecallDatasetRequest(%+v)", *p)
}

type RecallDatasetResponse struct {
	Segments []*RecallSegment `thrift:"Segments,1" json:"segments"`
}

func NewRecallDatasetResponse() *RecallDatasetResponse {
	return &RecallDatasetResponse{}
}

func (p *RecallDatasetResponse) InitDefault() {
}

func (p *RecallDatasetResponse) GetSegments() (v []*RecallSegment) {
	return p.Segments
}

func (p *RecallDatasetResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecallDatasetResponse(%+v)", *p)
}

type RecallSegment struct {
	DatasetID         string `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
	DocumentID        string `thrift:"DocumentID,2,required" json:"document_id" path:"document_id,required" `
	Content           string `thrift:"Content,3,required" json:"content"`
	Title             string `thrift:"Title,4,required" json:"title"`
	URL               string `thrift:"URL,5,required" json:"url"`
	LastUpdatedAt     string `thrift:"LastUpdatedAt,6,required" json:"last_updated_at"`
	Owner             string `thrift:"Owner,7,required" json:"Owner"`
	DocumentCreatedAt string `thrift:"DocumentCreatedAt,8,required" json:"document_created_at"`
}

func NewRecallSegment() *RecallSegment {
	return &RecallSegment{}
}

func (p *RecallSegment) InitDefault() {
}

func (p *RecallSegment) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *RecallSegment) GetDocumentID() (v string) {
	return p.DocumentID
}

func (p *RecallSegment) GetContent() (v string) {
	return p.Content
}

func (p *RecallSegment) GetTitle() (v string) {
	return p.Title
}

func (p *RecallSegment) GetURL() (v string) {
	return p.URL
}

func (p *RecallSegment) GetLastUpdatedAt() (v string) {
	return p.LastUpdatedAt
}

func (p *RecallSegment) GetOwner() (v string) {
	return p.Owner
}

func (p *RecallSegment) GetDocumentCreatedAt() (v string) {
	return p.DocumentCreatedAt
}

func (p *RecallSegment) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecallSegment(%+v)", *p)
}

type RecommendDocumentsRequest struct {
	ReferenceDocuments []*ReferenceDocument `thrift:"ReferenceDocuments,1" json:"reference_documents"`
	DatasetID          string               `thrift:"DatasetID,2,required" json:"dataset_id" path:"dataset_id,required" `
}

func NewRecommendDocumentsRequest() *RecommendDocumentsRequest {
	return &RecommendDocumentsRequest{}
}

func (p *RecommendDocumentsRequest) InitDefault() {
}

func (p *RecommendDocumentsRequest) GetReferenceDocuments() (v []*ReferenceDocument) {
	return p.ReferenceDocuments
}

func (p *RecommendDocumentsRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *RecommendDocumentsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecommendDocumentsRequest(%+v)", *p)
}

type RecommendDocumentsResponse struct {
	Documents []*LarkDocument `thrift:"Documents,1" json:"documents"`
}

func NewRecommendDocumentsResponse() *RecommendDocumentsResponse {
	return &RecommendDocumentsResponse{}
}

func (p *RecommendDocumentsResponse) InitDefault() {
}

func (p *RecommendDocumentsResponse) GetDocuments() (v []*LarkDocument) {
	return p.Documents
}

func (p *RecommendDocumentsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecommendDocumentsResponse(%+v)", *p)
}

type ReferenceDocument struct {
	Title string `thrift:"Title,1" json:"title"`
	URL   string `thrift:"URL,3" json:"url"`
}

func NewReferenceDocument() *ReferenceDocument {
	return &ReferenceDocument{}
}

func (p *ReferenceDocument) InitDefault() {
}

func (p *ReferenceDocument) GetTitle() (v string) {
	return p.Title
}

func (p *ReferenceDocument) GetURL() (v string) {
	return p.URL
}

func (p *ReferenceDocument) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReferenceDocument(%+v)", *p)
}

type CountDocumentsRequest struct {
	DatasetID string `thrift:"DatasetID,1,required" json:"dataset_id" path:"dataset_id,required" `
}

func NewCountDocumentsRequest() *CountDocumentsRequest {
	return &CountDocumentsRequest{}
}

func (p *CountDocumentsRequest) InitDefault() {
}

func (p *CountDocumentsRequest) GetDatasetID() (v string) {
	return p.DatasetID
}

func (p *CountDocumentsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CountDocumentsRequest(%+v)", *p)
}

type CountDocumentsResponse struct {
	AllTotal int64 `thrift:"AllTotal,1" json:"all_total"`
	MyTotal  int64 `thrift:"MyTotal,2" json:"my_total"`
}

func NewCountDocumentsResponse() *CountDocumentsResponse {
	return &CountDocumentsResponse{}
}

func (p *CountDocumentsResponse) InitDefault() {
}

func (p *CountDocumentsResponse) GetAllTotal() (v int64) {
	return p.AllTotal
}

func (p *CountDocumentsResponse) GetMyTotal() (v int64) {
	return p.MyTotal
}

func (p *CountDocumentsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CountDocumentsResponse(%+v)", *p)
}
