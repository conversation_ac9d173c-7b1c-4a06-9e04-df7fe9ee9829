namespace go nextagent

include "../common.thrift"
include "event.thrift"
include "showcase.thrift"
include "mcp.thrift"
include "types.thrift"
include "knowledgebase.thrift"
include "permission.thrift"
include "mention.thrift"
include "mcp.thrift"

struct CheckCreateSessionRequest {
    // query 通过逗号切分，不支持多个 roles, example 1,2,3
    1: string Roles (api.query = "roles"),
}

struct SessionRoleAllowed {
    1: required types.SessionRole Role (go.tag = "json:\"role\""),
    2: required bool Allowed (go.tag = "json:\"allowed\""),
    3: optional i32 RemainingTimes (go.tag = "json:\"remaining_times,omitempty\"")
}

struct CheckCreateSessionResponse {
    1: required bool Allowed (go.tag = "json:\"allowed\""),
    2: optional list<SessionRoleAllowed> Roles (go.tag = "json:\"roles\"")
}

struct SessionRoleConfig {
    1: required types.SessionRole Role (go.tag = "json:\"role\""),
    2: required bool UseInternalTool (go.tag = "json:\"use_internal_tool\""),
    3: optional i64 SessionLimit (go.tag = "json:\"session_limit,omitempty\""),
    4: optional i64 MessageLimit (go.tag = "json:\"message_limit,omitempty\""),
    5: optional i64 MessageWarning (go.tag = "json:\"message_warning,omitempty\"")
}

struct GetUserRolesRequest {
}

struct GetUserRolesResponse {
    1: required list<UserRole> Roles (go.tag = "json:\"roles\""),
}

typedef string UserRole
const UserRole UserRoleAimoAdmin = "AimoAdmin"
const UserRole UserRoleAimoInternalDev = "AimoInternalDev"
const UserRole UserRoleAimoAgentDeveloper = "AimoAgentDeveloper"
const UserRole UserRoleAimoMCPDeveloper = "AimoMCPDeveloper"
const UserRole UserRoleAimoMCPPlayground = "AimoMCPPlayground"
const UserRole UserRoleAimoTraceAll = "AimoTraceAll"
const UserRole UserRoleAimoTraceSelf = "AimoTraceSelf"
const UserRole UserRoleAimoTraceDiagnose = "AimoTraceDiagnose"
const UserRole UserRoleViewOthers = "ViewOthers"
const UserRole UserRoleCollectionOperator = "CollectionOperator"


const SessionStatus SessionStatusCreated = "created"   // 会话已创建
const SessionStatus SessionStatusRunning = "running"   // 会话运行中
const SessionStatus SessionStatusCanceled = "canceled" // 会话已取消，用户主动取消
const SessionStatus SessionStatusStopped = "stopped"   // 会话已休眠
const SessionStatus SessionStatusWaiting = "waiting"   // 会话等待中，需要用户交互
const SessionStatus SessionStatusIdle = "idle"         // 会话空闲，当前轮任务完成
const SessionStatus SessionStatusAbnormal = "abnormal" // 会话异常，可重试
const SessionStatus SessionStatusError = "error"       // 会话异常， 终态
const SessionStatus SessionStatusClosed = "closed"     // 会话已关闭，终态

typedef string SessionStatus

const CanNotResumeReason CanNotResumeReasonUnknown = "unknown"
const CanNotResumeReason CanNotResumeReasonDeleted = "deleted"
const CanNotResumeReason CanNotResumeReasonNotAllowed = "not_allowed" // 不支持，老数据都不支持
const CanNotResumeReason CanNotResumeReasonExpired = "expired"

typedef string CanNotResumeReason

enum SessionScope {
    Unknown = 0
    Private = 1           // 个人
    Public = 2            // 公司内公开
    ProjectPublic = 3     // 项目内公开
}

struct Session {
    1: required string ID (go.tag = "json:\"id\""),
    2: required SessionStatus Status (go.tag = "json:\"status\""),
    3: required string Title (go.tag = "json:\"title\""),
    4: required SessionContext Context (go.tag = "json:\"context\""),
    5: required string CreatedAt (go.tag = "json:\"created_at\""),
    6: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    7: optional types.SessionRole Role (go.tag = "json:\"role\""),
    8: optional string Creator (go.tag = "json:\"creator\""),
    9: optional SessionRuntimeMetadata Metadata (go.tag = "json:\"metadata\""),
    10: string LastMessageAt (go.tag = "json:\"last_message_at\""),
    11: optional bool CanResume (go.tag = "json:\"can_resume\""),
    12: optional CanNotResumeReason CanNotResumeReason (go.tag = "json:\"can_not_resume_reason\""),
    13: optional string TemplateID (go.tag = "json:\"template_id\""),
    14: optional string SourceSpaceID (go.tag = "json:\"source_space_id\""),
    15: optional SessionScope Scope (go.tag = "json:\"scope,omitempty\""),
    16: optional list<permission.PermissionAction> PermissionActions (go.tag = "json:\"permission_actions,omitempty\""), // 权限列表
    17: optional bool Starred (go.tag = "json:\"starred,omitempty\"")  // 会话是否被收藏
    18: optional string FirstUserQuery (go.tag = "json:\"first_user_query,omitempty\""), // 会话的第一个用户提问
}

struct RuntimeMeta {
    1: required string Provider (go.tag = "json:\"provider\""),
    2: required string ContainerID (go.tag = "json:\"container_id\""),
    3: required string ContainerHost (go.tag = "json:\"container_host\""),
    4: required string WildcardDomain (go.tag = "json:\"wildcard_domain\""),
}

struct SessionContext {
    1: optional bool UseInternalTool (go.tag = "json:\"use_internal_tool\""),
    2: optional list<mcp.MCP> MCPs (go.tag = "json:\"mcps\""), // MCP集
}

struct SessionRuntimeMetadata {
    1: optional string LogID (go.tag = "json:\"log_id,omitempty\""),
    2: optional string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id,omitempty\""),
    3: optional string AgentConfigID (go.tag = "json:\"agent_config_id,omitempty\""),
}

struct CreateSessionRequest {
    1: optional types.SessionRole Role (go.tag = "json:\"role\""),
    2: optional bool UseInternalTool (go.tag = "json:\"use_internal_tool\""),
    3: optional string SpaceID (go.tag = "json:\"space_id\""),
    4: optional list<mcp.MCPKey> ExcludedMCPs (go.tag = "json:\"excluded_mcps\"")
}

struct CreateSessionResponse {
    1: required Session session (go.tag = "json:\"session\""),
}

struct ListSessionsRequest {
    1: required i64 PageNum (api.query = "page_num"),
    2: required i64 PageSize (api.query = "page_size"),
    3: optional string SpaceID (api.query = "space_id"),
}

struct ListSessionsResponse {
    1: required list<Session> Sessions (go.tag = "json:\"sessions\""),
    2: required i64 Total (go.tag = "json:\"total\""),
}

struct ListUserSessionsRequest {
    1: required i64 PageNum (api.query = "page_num"),
    2: required i64 PageSize (api.query = "page_size"),
    3: optional string SessionID (api.query = "session_id"),
    4: optional string AgentConfigVersionID (api.query="agent_config_version_id"),
    5: optional string StartTime (api.query="start_time"),
    6: optional string Endtime (api.query="end_time"),
    7: optional SessionStatus Status (api.query="status"),
}

struct UserSession{
    1: required string ID (go.tag = "json:\"id\""),
    2: required SessionStatus Status (go.tag = "json:\"status\""),
    3: required string CreatedAt (go.tag = "json:\"created_at\""),
    4: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    5: optional types.SessionRole Role (go.tag = "json:\"role\""),
    6: optional SessionRuntimeMetadata Metadata (go.tag = "json:\"metadata\""),
    7: string LastMessageAt (go.tag = "json:\"last_message_at\""),
    8: optional bool CanResume (go.tag = "json:\"can_resume\""),
    9: optional CanNotResumeReason CanNotResumeReason (go.tag = "json:\"can_not_resume_reason\""),
    10: optional string TemplateID (go.tag = "json:\"template_id\""),
    11: optional string SourceSpaceID (go.tag = "json:\"source_space_id\""),
    12: optional SessionScope Scope (go.tag = "json:\"scope,omitempty\""),
    13: optional list<permission.PermissionAction> PermissionActions (go.tag = "json:\"permission_actions,omitempty\""), // 权限列表
    14: optional bool Starred (go.tag = "json:\"starred,omitempty\"")  // 会话是否被收藏
}

struct ListUserSessionsResponse {
    1: required list<UserSession> Sessions (go.tag = "json:\"sessions\""),
    2: required i64 Total (go.tag = "json:\"total\""),
}

enum ListFilterType {
    SelfCreated = 0 // 空间内自己创建的
    SpacePublic = 1 // 空间内公开的
}

enum SessionTab {
    All = 0     // 全部会话
    Starred = 1 // 收藏会话
    UnStarred = 2 // 未收藏会话
}

struct ListSpaceSessionsRequest {
    1: optional string SpaceID (api.query = "space_id"),
    2: required i64 Limit (api.query = "limit"),
    3: optional string NextID (api.query = "next_id"), // 时间戳起始位置
    4: optional ListFilterType Type (api.query = "type"),
    5: optional string Search (api.query = "search"), // 搜索关键词
    6: optional SessionTab Tab (api.query = "tab"), // 会话列表页签
    7: optional list<string> Creators (api.query = "creators"), // 创建者
    8: optional TimeDuration CreatedTime (api.query = "created_time"), // 创建时间
    9: optional TimeDuration UpdatedTime (api.query = "updated_time"), // 更新时间
    10: optional list<SessionStatus> Statuses (api.query = "statuses"), // 状态
    11: optional list<SessionScope> Scopes (api.query = "scopes"), // 作用域
}

struct TimeDuration {
    1: optional i64 StartTime (api.query = "start_time"), // 开始时间，毫秒级时间戳
    2: optional i64 EndTime(api.query = "end_time"),      // 结束时间，毫秒级时间戳
}

struct ListSpaceSessionsCount {
    1: required i64 SpacePublic (go.tag = "json:\"space_public\""),
    2: required i64 SelfCreated (go.tag = "json:\"self_created\""),
}
struct ListSpaceSessionsResponse {
    1: required list<Session> Sessions (go.tag = "json:\"sessions\""),
    2: required bool HasMore (go.tag = "json:\"has_more\""), // 是否还有更多
    3: optional string NextID (go.tag = "json:\"next_id,omitempty\""),
    4: required ListSpaceSessionsCount Count (go.tag = "json:\"count\""),
}


struct ListSessionPartialRequest {
}

struct ListSessionPartialResponse {
    1: required list<SessionPartial> Sessions (go.tag = "json:\"sessions\""),
}

struct SessionPartial {
    1: required string ID (go.tag = "json:\"id\""),
    2: required SessionStatus Status (go.tag = "json:\"status\""),
    3: required string Title (go.tag = "json:\"title\""),
}

struct GetSessionRequest {
    1: required string SessionID (api.path = "session_id"),
    2: optional string SpaceID (api.query = "space_id"),
}

struct GetSessionResponse {
    1: required Session Session (go.tag = "json:\"session\""),
    2: optional list<event.Message> Messages (go.tag = "json:\"messages\""),
}

struct UpdateSessionRequest {
    1: required string SessionID (api.path = "session_id"),
    2: optional string Title (go.tag = "json:\"title\""),
    3: optional SessionStatus Status (go.tag = "json:\"status\""),
    4: optional SessionScope Scope (go.tag = "json:\"scope\""),
    5: optional string SpaceID (go.tag = "json:\"space_id\""),
}

struct UpdateSessionResponse {
    1: required Session Session (go.tag = "json:\"session\""),
}

struct DeleteSessionRequest {
    1: required string SessionID (api.path = "session_id"),
    2: optional string SpaceID (go.tag = "json:\"space_id\""),
}

struct DeleteSessionResponse {
    1: required string Message (go.tag = "json:\"message\""),
}

struct BatchDeleteSessionRequest {
    1: required list<string> SessionIDs (api.body = "session_ids", go.tag = "json:\"session_ids\""),
}

struct BatchDeleteSessionResponse {
}

struct GetSessionStreamEventsRequest {
    1: required string SessionID (api.path = "session_id"),
    2: optional i64 EventOffset (api.query = "event_offset"),
    3: optional string SpaceID (api.query = "space_id"),
}

struct GetOldSessionEventsRequest {
    1: required string SessionID (api.path = "session_id"),
    2: optional string SpaceID (api.query = "space_id"),
}

struct GetOldSessionEventsResponse {
    1: required list<event.Event> Events (go.tag = "json:\"events\""),
    2: optional types.SessionRole Role (go.tag = "json:\"role\""),
    3: optional Session Session (go.tag = "json:\"session\""),
}

struct CreateMessageRequest {
    1: required string SessionID (api.path = "session_id"),
    // Content 用户发送的消息内容
    2: required string Content (go.tag = "json:\"content\""),
    3: optional list<AttachmentRequired> attachments (go.tag = "json:\"attachments\""),
    // ToolCalls Agent 发送的工具调用，用户需要回复Agent时填写，例如二次确认、授权等
    // Content和ToolCalls互斥，ToolCalls仅在有 tool_call_required event 时使用
    4: optional list<ToolCall> ToolCalls (go.tag = "json:\"tool_calls\""),
    5: i64 EventOffset (go.tag = "json:\"event_offset\""), // 指定创建消息的事件的 offset，通常用于打断或者澄清的场景
    6: optional string Options (go.tag = "json:\"options\""),
    7: optional list<mention.Mention> Mentions (go.tag = "json:\"mentions\""),
    8: optional string SpaceID (go.tag = "json:\"space_id\""),
}

struct AttachmentRequired {
    1: required string ID (go.tag = "json:\"id\""),
    2: required string FileName (go.tag = "json:\"file_name\""),
}

// ToolCall 是用户对工具调用的回复，例如二次确认、授权等
struct ToolCall {
    1: required string ToolCallID (go.tag = "json:\"id\""),
    2: required string Name (go.tag = "json:\"name\""),
    3: required string Content (go.tag = "json:\"content\""), // 用户回复的内容
    4: required event.ToolCallAction Action (go.tag = "json:\"action\""), // 用户的操作
    5: optional bool NeedKeepLogin (go.tag = "json:\"need_keep_login\""), // 是否需要保持登录状态
}

struct CreateMessageResponse {
    1: required event.Message Message (go.tag = "json:\"message\""),
}

struct CreateMessageWithTemplateRequest {
    1: optional types.SessionRole Role (go.tag = "json:\"role\""),
    2: optional bool UseInternalTool (go.tag = "json:\"use_internal_tool\""),
    3: required string TemplateID (go.tag = "json:\"template_id\""),
    4: required string Content (go.tag = "json:\"content\""),
    5: optional TemplateFormValue FormValue (go.tag = "json:\"form_value\""),
    6: optional string Options (go.tag = "json:\"options\""),
    7: optional list<mcp.MCPKey> mcps (go.tag = "json:\"mcps\""),
    8: optional string SpaceID (go.tag = "json:\"space_id\""),
    9: optional list<mcp.MCPKey> ExcludedMCPs (go.tag = "json:\"excluded_mcps\"")
    10: optional string FromApp (go.tag = "json:\"from_app\""),
}

struct CreateMessageWithTemplateResponse {
    1: required event.Message Message (go.tag = "json:\"message\""),
    2: required Session Session (go.tag = "json:\"session\""),
}

enum TemplateSource {
    All = 0            // 全部模板
    My = 1             // 我的模板
    Star = 2           // 收藏模板
    Project = 3        // 项目内模板
}

struct ListTemplatesRequest {
    1: optional i64 PageNum (api.query = "page_num"),
    2: optional i64 PageSize (api.query = "page_size"),
    3: optional TemplateCategory Category (api.query = "category"),
    4: optional string Search (api.query = "search"),
    5: optional TemplateSource Source (api.query = "source"),
    6: optional TemplateLabel Label (api.query = "label"),
}

struct ListTemplatesResponse {
    1: required list<Template> Templates (go.tag = "json:\"templates\""),
    2: required i64 Total (go.tag = "json:\"total\""),
}

struct Template {
    1: required string ID (go.tag = "json:\"id\""),
    2: required TemplateCategory Category (go.tag = "json:\"category\""),
    3: required string Name (go.tag = "json:\"name\""),
    4: required string Prompt (go.tag = "json:\"prompt\""),
    5: required list<TemplateVariable> Variables (go.tag = "json:\"variables\""),
    6: optional showcase.Showcase Showcase (go.tag = "json:\"showcase,omitempty\""),
    8: optional EstimatedMinutes EstimatedMinutes (go.tag = "json:\"estimated_minutes,omitempty\""),
    9: optional list<types.SessionRole> SupportRoles (go.tag = "json:\"support_roles,omitempty\"")  // 快捷模板支持的角色列表
    10: required TemplateScope Scope (go.tag = "json:\"scope\"")  // 快捷模板的作用域，public、private、shared
    11: required string Creator (go.tag = "json:\"creator\"")  // 快捷模板的创建者
    12: optional i64 StarCount (go.tag = "json:\"star_count,omitempty\"")  // 快捷模板的收藏数
    13: optional list<mcp.MCP> SupportMCPs (go.tag = "json:\"support_mcps,omitempty\"")  // 快捷模板支持的MCP列表
    14: required TemplateStatus Status (go.tag = "json:\"status\"")  // 快捷模板的状态
    15: required string Version (go.tag = "json:\"version\"")  // 快捷模板的版本号
    16: optional list<string> Steps (go.tag = "json:\"steps,omitempty\"")  // 快捷模板的步骤
    17: required TemplatePlanStepStatus PlanStepStatus (go.tag = "json:\"plan_step_status\"")  // 快捷模板的执行规划状态
    18: required TemplateLabel Label (go.tag = "json:\"label\"")  // 快捷模板的标签
    19: optional bool Starred (go.tag = "json:\"starred,omitempty\"")  // 快捷模板是否已收藏
    20: optional string ShareID (go.tag = "json:\"share_id,omitempty\"")  // 快捷模板的分享ID
}

// ModifyTemplate 中可选字段根据用户的编辑操作来传，如果用户编辑了则传该字段，否则不传
struct ModifyTemplate {
    1: required TemplateCategory Category (go.tag = "json:\"category\""),
    2: optional string Name (go.tag = "json:\"name\""),
    3: optional string Prompt (go.tag = "json:\"prompt\""),
    4: optional list<TemplateVariable> Variables (go.tag = "json:\"variables\""),
    5: required TemplateScope Scope (go.tag = "json:\"scope\"")  // 快捷模板的作用域，public、private
    6: required TemplateLabel Label (go.tag = "json:\"label\"")  // 快捷模板的标签
}

enum TemplateScope {
    Private = 0           // 私有模板
    Public = 1            // 公司内公开模板
    Shared = 2            // 共享模板
    Official = 3          // 官方模板
    ProjectPublic = 4     // 项目内公开模板
}

enum TemplateStatus {
    Draft = 0            // 草稿状态，此时对用户不可见
    Generating = 1       // 生成经验中，此时用户可见，但是不可执行
    Available = 2        // 可用状态，此时用户可见，且可执行
}

enum TemplatePlanStepStatus {
    Default = 0                 // 未生成执行规划
    None = 1                    // 当前模板无需生成执行规划（比如因为用户编辑过模板）
    Generating = 2              // 当前模板正在生成执行规划
    Generated = 3               // 生成结束
}

typedef string TemplateLabel // 职能标签
const TemplateLabel TemplateLabelGeneral = "General"        // 通用
const TemplateLabel TemplateLabelProduct = "Product"        // 产品
const TemplateLabel TemplateLabelFrontend = "Frontend"      // 前端
const TemplateLabel TemplateLabelClient = "Client"          // 客户端
const TemplateLabel TemplateLabelServer = "Server"          // 服务端
const TemplateLabel TemplateLabelAlgorithm = "Algorithm"    // 算法
const TemplateLabel TemplateLabelData = "Data"              // 数据
const TemplateLabel TemplateLabelDesign = "Design"          // 设计
const TemplateLabel TemplateLabelOperations = "Operations"  // 运营
const TemplateLabel TemplateLabelPMO = "PMO"                // PMO
const TemplateLabel TemplateLabelQuality = "Quality"        // 质量
const TemplateLabel TemplateLabelOther = "Other"            // 其他

typedef string TemplateCategory
const TemplateCategory TemplateCategoryAnalysis = "Analysis"        // 数据分析
const TemplateCategory TemplateCategoryStudy = "Study"              // 产品调研
const TemplateCategory TemplateCategoryCode = "Code"                // 代码生成
const TemplateCategory TemplateCategoryProjectAsk = "ProjectAsk"    // 项目问答
const TemplateCategory TemplateCategoryWrite = "Write"              // 方案撰写
const TemplateCategory TemplateCategoryTroubleshooting = "Troubleshooting"    // 故障排查
const TemplateCategory TemplateCategoryQuality = "Quality"          // 质量检测

struct TemplateVariable {
    1: required string Name (go.tag = "json:\"name\""),
    2: optional bool Required (go.tag = "json:\"required\""),
    3: optional string Description (go.tag = "json:\"description\""),
    4: required TemplateVariableType Type (go.tag = "json:\"type\""),
    5: optional string Placeholder (go.tag = "json:\"placeholder\""),
    6: optional string DefaultValue (go.tag = "json:\"default_value\""),
    7: optional string SelectContent (go.tag = "json:\"select_content\""),
    8: required string ID (go.tag = "json:\"id\""),
}

struct TemplateFormValue {
    1: required map<string, TemplateVariableValue> Variables (go.tag = "json:\"variables\""),
}

struct TemplateVariableValue {
    1: optional string Content (go.tag = "json:\"content,omitempty\""),
    2: optional list<AttachmentRequired> attachments (go.tag = "json:\"attachments,omitempty\""),
}

typedef string TemplateVariableType
const TemplateVariableType TemplateVariableTypeAutoComplete = "AutoComplete" // 文本输入
const TemplateVariableType TemplateVariableTypeUpload = "Upload" // 文件上传
const TemplateVariableType TemplateVariableTypeUploadWithText = "UploadWithText" // 文件上传+文本输入

struct EstimatedMinutes {
    1: required i64 Minimum (go.tag = "json:\"min\""),
    2: required i64 Maximum (go.tag = "json:\"max\""),
}

struct GetHistoryTemplateVariablesRequest {
    1: required string TemplateID (api.path = "template_id"),
    2: optional string SpaceID (api.query = "space_id"),
}

struct GetHistoryTemplateVariablesResponse {
    1: required list<TemplateFormValueDetail> FormValue (go.tag = "json:\"form_value\""),
}

struct TemplateFormValueDetail {
    1: required map<string, TemplateVariableValueDetail> Variables (go.tag = "json:\"variables\""),
}

struct TemplateVariableValueDetail {
    1: optional string Content (go.tag = "json:\"content,omitempty\""),
    2: optional list<event.Attachment> attachments (go.tag = "json:\"attachments,omitempty\""),
}


struct GetSessionAgentRunRequest {
    1: required string SessionID (api.path = "session_id"),
}

struct GetSessionAgentRunResponse {
    1: required bool IsAvailable (go.tag = "json:\"is_available\""),
    2: required RuntimeMeta RuntimeMeta (go.tag = "json:\"runtime_meta\""),
    3: required string CreatedAt (go.tag = "json:\"created_at\""),
    4: required string UpdatedAt (go.tag = "json:\"updated_at\""),
}

struct SubmitToolCallRequest {
    1: required string SessionID (api.path = "session_id"),
    // ToolCalls Agent 发送工具调用，用户需要回复 Agent 时填写，例如二次确认、授权等
    2: optional list<ToolCall> ToolCalls (go.tag = "json:\"tool_calls\""),
}

struct SubmitToolCallResponse {
}

// 会话MCP详细信息
struct SessionMCPDetail {
    1: required string ID (go.tag = "json:\"id\""),              // MCP ID
    2: required string Name (go.tag = "json:\"name\""),          // MCP 名称
    3: required string Description (go.tag = "json:\"description\""), // MCP 描述
    4: required mcp.MCPSource Source (go.tag = "json:\"source\""), // MCP 来源
    5: optional list<mcp.MCPTool> Tools (go.tag = "json:\"tools\""), // 工具列表
    6: required string NameForAgent (go.tag = "\"name_for_agent\"") // Agent看到的MCP名称
    255: optional string ErrMessage (go.tag = "json:\"err_message,omitempty\"") // 获取不到工具情况下的错误信息
}

// 获取会话MCP详细信息的请求
struct GetSessionMCPDetailsRequest {
    1: required string SessionID (api.path = "session_id"),      // 会话ID
}

// 获取会话MCP详细信息的响应
struct GetSessionMCPDetailsResponse {
    1: optional list<SessionMCPDetail> MCPs (go.tag = "json:\"mcps\""), // MCP详细信息列表
}

struct CreateSessionStarRequest {
    1: required string SessionID (api.path="session_id"),
}

struct CreateSessionStarResponse {}

struct DeleteSessionStarRequest {
    1: required string SessionID (api.path="session_id"),
}

struct DeleteSessionStarResponse {}