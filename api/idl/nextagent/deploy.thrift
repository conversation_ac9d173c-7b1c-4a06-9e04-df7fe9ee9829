namespace go nextagent

struct BPMAuthCallbackRequest {
    1: required string ID (go.tag = "json:\"id\"")
    2: required string WorkflowConfigID (go.tag = "json:\"workflow_config_id\"")
    3: required string AgentConfigID (go.tag = "json:\"agent_config_id\"")
    4: required string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id\"")
    5: required string Creator (go.tag = "json:\"creator\"") 
}

struct BPMAuthCallbackResponseData{
    1: required bool AuthResult (go.tag = "json:\"auth_result\"")
}

struct BPMAuthCallbackResponse {
    1: required i32 Code (go.tag = "json:\"id\"")
    2: required BPMAuthCallbackResponseData Data (go.tag = "json:\"data\"")
}

struct BPMCloseCallbackRequest {
    1: required i64 ID (go.tag = "json:\"id\"")
    2: required string DeployID (go.tag = "json:\"deploy_id\"")
    3: required string AgentConfigID (go.tag = "json:\"agent_config_id\"")
    4: required string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id\"")
    5: optional string Comment (go.tag = "json:\"comment\"")
    6: optional string AuditRejectComment (go.tag = "json:\"audit_reject_comment\"")
}

struct BPMCloseCallbackResponse {
    1: required i32 Code (go.tag = "json:\"code\"")
}

struct BPMCancelCanaryCallbackRequest {
    1: required i64 ID (go.tag = "json:\"id\"")
    2: required string DeployID (go.tag = "json:\"deploy_id\"")
    3: required string AgentConfigID (go.tag = "json:\"agent_config_id\"")
    4: required string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id\"")
}

struct BPMCancelCanaryCallbackResponse {
    1: required i32 Code (go.tag = "json:\"code\"")
}

struct BPMCanaryCallbackRequest {
    1: required i64 ID (go.tag = "json:\"id\"")
    2: required string DeployID (go.tag = "json:\"deploy_id\"")
    3: required string AgentConfigID (go.tag = "json:\"agent_config_id\"")
    4: required string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id\"")
    5: required string Status (go.tag = "json:\"status\"")
}

struct BPMCanaryCallbackResponse {
    1: required i32 Code (go.tag = "json:\"code\"")
}
struct BPMOnlineCallbackRequest {
    1: required i64 ID (go.tag = "json:\"id\"")
    2: required string DeployID (go.tag = "json:\"deploy_id\"")
    3: required string AgentConfigID (go.tag = "json:\"agent_config_id\"")
    4: required string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id\"")
    5: optional string SkipCanaryComment (go.tag = "json:\"skip_canary_comment\"")
}

struct BPMOnlineCallbackResponseData {
    1: required bool OnlineResult (go.tag = "json:\"online_result\"")
}

struct BPMOnlineCallbackResponse {
    1: required i32 Code (go.tag = "json:\"code\"")
    2: required BPMOnlineCallbackResponseData Data (go.tag = "json:\"data\"")
}

struct CreateDeployRequest {
    1: required string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id\"")
    2: required string Reviewer (go.tag = "json:\"reviewer\"")
    3: required bool EnableAB (go.tag = "json:\"is_enable_ab\"")
    4: required string ABComment (go.tag = "json:\"ab_comment\"")
}

struct CreateDeployResponse {
    1: required string DeployID (go.tag = "json:\"deploy_id\"")
    2: required i64 WorkflowID (go.tag = "json:\"workflow_id\"")
}


struct GetDeployProcessInfoRequest {
    1: required string DeployID (api.query = "deploy_id")
}

struct GetDeployProcessInfoResponse {
   1: required double CanaryRatio (go.tag = "json:\"canary_ratio\"")
   2: required i64 SuccessCount (go.tag = "json:\"success_count\"")
   3: required i64 FailCount (go.tag = "json:\"fail_count\"")
   4: required i64 RunningCount (go.tag = "json:\"running_count\"")
   5: required string DeployStatus (go.tag = "json:\"deploy_status\"")
}


struct GetDeployRequest {
    1: required string DeployID (api.path = "deploy_id")
}

struct AgentConfigVersionInfo {
    1: required string ID (go.tag = "json:\"id\"")
    2: required i32 Version (go.tag = "json:\"version\"")
    3: required string Description (go.tag = "json:\"description\"")
}

struct AgentDeploy {
    1: required string ID (go.tag = "json:\"id\"")
    2: required string AgentConfigID (go.tag = "json:\"agent_config_id\"")
    3: required AgentConfigVersionInfo AgentConfigVersionInfo (go.tag = "json:\"agent_config_version_info\"")
    4: required i64 WorkflowID (go.tag = "json:\"workflow_id\"")
    5: optional DeployExtraInfo ExtraInfo (go.tag = "json:\"extra_info\"")
    6: required string CreatedAt (go.tag = "json:\"created_at\"")
    7: required string Status (go.tag = "json:\"status\"")
    8: optional AgentConfigVersionInfo AgentConfigVersionOnlineInfo (go.tag = "json:\"agent_config_version_online_info\"")
    9: required string Actor (go.tag = "json:\"actor\"")
}

struct DeployExtraInfo {
  1: optional string Reviewer (go.tag = "json:\"reviewer\"")
  2: optional bool EnableAB (go.tag = "json:\"enable_ab\"")
  3: optional string ABComment (go.tag = "json:\"ab_comment\"")
  4: optional bool SkipCanary (go.tag = "json:\"skip_canary\"")
  5: optional string SkipCanaryComment (go.tag = "json:\"skip_canary_comment\"")
  6: optional string CloseReason (go.tag = "json:\"close_reason\"")
  7: optional string AuditRejectComment (go.tag = "json:\"audit_reject_comment\"")
}

struct GetDeployResponse {
   1: required AgentDeploy Deploy (go.tag = "json:\"deploy\"")
}

struct GetScmVersionRequest {
    1: optional string Branch (api.query="branch")
    // 逗号分隔, online/offline/test
    2: optional string TypeList (api.query="type_list")
    3: optional string Version (api.query="version")
    4: optional string Commit (api.query="commit")
}

struct ScmVersion {
    1: required string Version (go.tag = "json:\"version\"")
    2: required list<string> Arch (go.tag = "json:\"arch\"")
    3: required string Type (go.tag = "json:\"type\"")
    4: required string CreateUser (go.tag = "json:\"create_user\"")
    5: required string GitURL (go.tag = "json:\"git_url\"")
    6: required string Desc (go.tag = "json:\"desc\"")
    7: required string BranchName (go.tag = "json:\"branch_name\"")
    8: required string BaseCommitHash (go.tag = "json:\"base_commit_hash\"")
    9: required string LocalDate (go.tag = "json:\"local_date\"")
    10: required string CommitURL (go.tag = "json:\"commit_url\"")
    11: required string Status (go.tag = "json:\"status\"")
    12: required string StatusAarch64 (go.tag = "json:\"status_aarch64\"")
    13: required string CreateDate (go.tag = "json:\"create_date\"")
    14: required string StatusDisplay (go.tag = "json:\"status_display\"")
    15: required string StatusDisplayAarch64 (go.tag = "json:\"status_display_aarch64\"")
    16: required i32 ID (go.tag = "json:\"id\"")
    17: optional string GitTag (go.tag = "json:\"git_tag\"")
    18: required list<string> Tags (go.tag = "json:\"tags\"")
}
struct GetScmVersionResponse {
    1: required list<ScmVersion> ScmVersions (go.tag = "json:\"scm_versions\"")
}

struct GetIcmVersionRequest{
    1: optional string Version (api.query="version")
    2: optional string Region (api.query="region")
    3: optional string SpecificTag (api.query="specific_tag")
    // agent config type, base/abtest/feature
    4: optional string ConfigType (api.query="config_type")
}

struct IcmVersion{
    1: required string Describe (go.tag = "json:\"describe\"")
    2: required string ImageSize (go.tag = "json:\"image_size\"")
    3: required string Builder (go.tag = "json:\"builder\"")
    4: required string ImageName (go.tag = "json:\"image_name\"")
    5: required string ImageVersion (go.tag = "json:\"image_version\"")
    6: required string SpecificTag (go.tag = "json:\"specific_tag\"")
    7: required string SpecificImageName (go.tag = "json:\"specific_image_name\"")
    8: required string CreateTime (go.tag = "json:\"create_time\"")
    // 9: required string RepoName (go.tag = "json:\"repo_name\"")
    // 10: required string Lazyload (go.tag = "json:\"lazyload\"")
    // 11: required string CommitID (go.tag = "json:\"commit_id\"")
    // 12: required list<string> UserTags (go.tag = "json:\"user_tags\"")
}

struct GetIcmVersionResponse{
    1: required list<IcmVersion> IcmVersions (go.tag = "json:\"icm_versions\"")
}

struct GetDeployReviewUserRequest {
}

struct GetDeployReviewUserResponse {
  1: required list<string> Users (go.tag = "json:\"users\"")
}

struct GetAgentDeployListRequest {
    1: required string AgentConfigID (api.query = "agent_config_id")
    2: required i64 PageNum (api.query = "page_num")
    3: required i64 PageSize (api.query = "page_size")
    4: optional string Status (api.query = "status")
}

struct GetAgentDeployListResponse {
    1: required list<AgentDeploy> AgentDeploys (go.tag = "json:\"agent_deploys\"")
    2: required i64 Total (go.tag = "json:\"total\"")
}

struct BitsUpsertAgentVersionRequest{
  1: required string AgentConfigID (go.tag = "json:\"agent_config_id\"")
  2: required string BitsBuildID (go.tag = "json:\"bits_build_id\"")
  3: required string ScmVersion (go.tag = "json:\"scm_version\"")
  4: required string User (go.tag = "json:\"user\"")
  5: required string Description (go.tag = "json:\"description\"")
  6: required string IcmVersion (go.tag = "json:\"icm_version\"")
}

struct BitsUpsertAgentVersionResponse{
  1: required string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id\"")
}
