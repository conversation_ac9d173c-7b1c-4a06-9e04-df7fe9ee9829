namespace go nextagent

// 飞书回调请求体结构：https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/development-link-preview/pull-link-preview-data-callback-structure
struct SharePreviewCallbackRequest{
    // 绑定回调时候需验证：https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/event-subscription-guide/callback-subscription/configure-callback-request-address
    1: optional string Challenge (go.tag="json:\"challenge\"", api.body="challenge"),
    2: optional string Type (go.tag="json:\"type\"", api.body="type"),
    3: optional ShareEvent Event (go.tag="json:\"event\"", api.body="event")
    4: CallbackHeader Header (go.tag="json:\"header\"", api.body="header")
}

struct SharePreviewCallbackResponse{
    1: optional string Challenge (go.tag="json:\"challenge\"", api.body="challenge"),
    2: required PreviewInline Inline (go.tag="json:\"inline\"", api.body="inline")
    3: required PreviewCard Card (go.tag="json:\"card\"", api.body="card")
}

struct ShareEvent{
    1: optional ShareOperator Operator  (go.tag="json:\"operator\"", agw.key="operator"),
    2: required ShareContext Context (go.tag="json:\"context\"", agw.key="context")
    3: optional CallbackAction Action (go.tag="json:\"action\"", agw.key="action")
}


struct CallbackHeader  {
	1: string EventID  (go.tag="json:\"event_id\"", agw.key="event_id")
	2: string Token     (go.tag="json:\"token\"", agw.key="token")
	3: string CreateTime (go.tag="json:\"create_time\"", agw.key="create_time")
	4: string EventType  (go.tag="json:\"event_type\"", agw.key="event_type")
	5: string TenantKey  (go.tag="json:\"tenant_key\"", agw.key="tenant_key")
	6: string AppID    (go.tag="json:\"app_id\"", agw.key="app_id")
}

struct ShareOperator{
    1: required string UserID (go.tag="json:\"user_id\"", agw.key="user_id")
    2: required string OpenID (go.tag="json:\"open_id\"", agw.key="open_id")
}

struct ShareContext{
    1: required string URL  (go.tag="json:\"url\"", agw.key="url")
    2: required string PreviewToken (go.tag="json:\"preview_token\"", agw.key="preview_token")
    3: string OpenMessageID (go.tag="json:\"open_message_id\"", agw.key="open_message_id")
}

struct PreviewInline{
    1: required PreviewTitle I18nTitle (go.tag="json:\"i18n_title\"", agw.key="i18n_title")
    2: optional string ImageKey (go.tag="json:\"image_key\"", agw.key="image_key")
}

struct PreviewTitle{
    1: required string ZhCN (go.tag="json:\"zh_cn\"", agw.key="zh_cn")
    2: required string EnUS (go.tag="json:\"en_us\"", agw.key="en_us")
}

struct PreviewCard{
    1: required string Type (go.tag="json:\"type\"", agw.key="type")
    2: required PreviewCardData Data (go.tag="json:\"data\"", agw.key="data")
}

struct PreviewCardData{
    1: required string  TemplateID (go.tag="json:\"template_id\"", agw.key="template_id")
    2: required PreviewCardTemplateVariable TemplateVariable (go.tag="json:\"template_variable\"", agw.key="template_variable")
}
struct PreviewCardTemplateVariable{
    1: required string Title (go.tag="json:\"title\"", agw.key="title")
    2: required string Content (go.tag="json:\"content\"", agw.key="content")
    3: required string URL (go.tag="json:\"url\"", agw.key="url")
    4: required string Creator (go.tag="json:\"creator\"", agw.key="creator")
}

struct CallbackAction {
    1: string Value (go.tag="json:\"value\"", agw.key="value")
    2: string Tag (go.tag="json:\"tag\"", agw.key="tag")
    3: string Timezone (go.tag="json:\"timezone\"", agw.key="timezone")
    4: map<string, string> FormValue (go.tag="json:\"form_value\"", agw.key="form_value")
    5: string Name (go.tag="json:\"name\"", agw.key="name")
    6: optional string InputValue (go.tag="json:\"input_value\"", agw.key="input_value")
}