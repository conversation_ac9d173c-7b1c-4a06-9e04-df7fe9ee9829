namespace go nextagent

include "../common.thrift"
include "mention.thrift"

// DS = 'DataStream'

const string EventNameMessageCreate = "session.message.create"
const string EventNameProgressNotice = "session.progress_notice"
const string EventNamePlanUpdate = "session.plan.update"
const string EventNameStepUpdate = "session.step.update"
const string EventNameUseTool = "session.action.use_tool"
const string EventNameToolCallRequired = "session.action.tool_call_required"
const string EventNameToolCallConfirmed = "session.action.tool_call_confirmed"
const string EventNameReference = "session.reference"
const string EventNameSessionCompleted = "session.completed"
const string EventNameDone = "done"
const string EventNameError = "error"

struct Event {
    1: required string Event (go.tag = "json:\"event\""),
    2: required string Data (go.tag = "json:\"data\"" api.type='json'),
}

struct ErrorEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string SessionID (go.tag = "json:\"session_id\""),
    3: required common.ErrorCode ErrorCode (go.tag = "json:\"error_code\""),
    4: required string Message (go.tag = "json:\"message\""),
    5: i64 Timestamp (go.tag = "json:\"timestamp\""),
    6: i64 EventOffset (go.tag = "json:\"event_offset\""),
    7: string EventKey (go.tag = "json:\"event_key\""),
}

enum DoneStatus {
    Success = 0
    Failed = 1
}

struct DoneEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required DoneStatus Status (go.tag = "json:\"status\""),
    3: required string Message (go.tag = "json:\"message\""),
    4: required i64 Timestamp (go.tag = "json:\"timestamp\""),
    5: optional common.ErrorCode ErrorCode (go.tag = "json:\"error_code\""),
}

/* 不要使用这个结构体，只用于调试 */
struct EventStruct {
    1: optional MessageCreateEvent MessageCreateEvent (go.tag = "json:\"message_create_event\""),
    2: optional PlanUpdateEvent PlanUpdateEvent (go.tag = "json:\"plan_update_event\""),
    3: optional StepUpdateEvent StepUpdateEvent (go.tag = "json:\"step_update_event\""),
    4: optional UseToolEvent UseToolEvent (go.tag = "json:\"use_tool_event\""),
    5: optional ProgressNoticeEvent ProgressNoticeEvent (go.tag = "json:\"progress_notice_event\""),
    6: optional ToolCallRequiredEvent ToolCallRequiredEvent (go.tag = "json:\"tool_call_required_event\""),
    7: optional SessionCompletedEvent SessionCompletedEvent (go.tag = "json:\"session_completed\""),
    8: optional DoneEvent DoneEvent (go.tag = "json:\"done_event\""),
    9: optional ErrorEvent ErrorEvent (go.tag = "json:\"error_event\""),
    10: optional common.ErrorCode ErrorCode (go.tag = "json:\"error_code\""),
    11: optional ReferenceEvent ReferenceEvent (go.tag = "json:\"reference_event\""),
    12: optional ToolCallConfirmedEvent ToolCallConfirmedEvent (go.tag = "json:\"tool_call_confirmed_event\""),
}

struct ReferenceEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string SessionID (go.tag = "json:\"session_id\""),
    3: required string TaskID (go.tag = "json:\"task_id\""),
    4: required list<ReferenceItem> References (go.tag = "json:\"references\""),
    5: i64 Timestamp (go.tag = "json:\"timestamp\""),
    6: i64 EventOffset (go.tag = "json:\"event_offset\""),
    7: string EventKey (go.tag = "json:\"event_key\""),
}

struct ReferenceItem {
    1: required i32 ID (go.tag = "json:\"id\""),
    2: required string Title (go.tag = "json:\"title\""),
    3: required string URI (go.tag = "json:\"uri\""),
}

struct MessageCreateEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required Message Message (go.tag = "json:\"message\""),
    3: i64 Timestamp (go.tag = "json:\"timestamp\""),
    4: i64 EventOffset (go.tag = "json:\"event_offset\""),
    5: string ReplyMessageID (go.tag = "json:\"reply_message_id\""),
    6: string EventKey (go.tag = "json:\"event_key\""),
    7: optional GenerateVisualPage GenerateVisualPage (go.tag = "json:\"generate_visual_page\""),
}

struct Message {
    1: required string MessageID (go.tag = "json:\"message_id\""),
    2: required string SessionID (go.tag = "json:\"session_id\""),
    3: required string TaskID (go.tag = "json:\"task_id\""),
    4: required string Role (go.tag = "json:\"role\""),
    5: required string Content (go.tag = "json:\"content\""),
    7: optional list<Attachment> Attachments (go.tag = "json:\"attachments\""),
    6: required string Creator (go.tag = "json:\"creator\""),
    8: required string CreatedAt (go.tag = "json:\"created_at\""),
    9: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    10: optional list<mention.Mention> Mentions (go.tag = "json:\"mentions\""),
}

struct GenerateVisualPage {
    1: required bool DisplayGenerate (go.tag = "json:\"display_generate\""), // 显示生成可视化页面按钮
    2: required string GeneratePageQuery (go.tag = "json:\"generate_page_query\""), // 点击生成可视化页面的默认 query
}

struct Attachment {
    1: required string ID (go.tag = "json:\"id\""),
    2: required string FileName (go.tag = "json:\"file_name\""),
    3: required string Path (go.tag = "json:\"path\""),
    4: required string Type (go.tag = "json:\"type\""),
    5: required string URL (go.tag = "json:\"url\""),
    6: required string ContentType (go.tag = "json:\"content_type\""),
    7: required i64 ContentLength (go.tag = "json:\"content_length\""),
    8: optional list<string> ParentStepIDs (go.tag = "json:\"parent_step_ids,omitempty\""),
    9: required string LarkToken (go.tag = "json:\"lark_token\""),
    10: bool NeedPreview (go.tag = "json:\"need_preview\""),
    // 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
    11: optional string SubType (go.tag = "json:\"sub_type\""),
    12: bool NeedJump (go.tag = "json:\"need_jump\""), // 是否是直接跳转的链接
    13: i32 Version (go.tag = "json:\"version\""),
}

// 产物文件中 link 类型的枚举范围
typedef string ArtifactLinkSubType
const string ArtifactLinkSubTypeLarkDoc = "lark_doc"
const string ArtifactLinkSubTypeLarkSheet = "lark_sheet"
const string ArtifactLinkSubTypeDeployment = "deployment"

struct PlanUpdateEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string TaskID (go.tag = "json:\"task_id\""),
    3: required string PlanID (go.tag = "json:\"plan_id\""),
    4: required list<Step> Steps (go.tag = "json:\"steps\""),
    5: i64 Timestamp (go.tag = "json:\"timestamp\""),
    6: i64 EventOffset (go.tag = "json:\"event_offset\""),
    7: required string Status (go.tag = "json:\"status\""),
    8: required string SessionID (go.tag = "json:\"session_id\""),
    9: string EventKey (go.tag = "json:\"event_key\""),
}

struct Step {
    1: required string StepID (go.tag = "json:\"step_id\""),
    2: required string TaskID (go.tag = "json:\"task_id\""),
    3: required string Title (go.tag = "json:\"title\""),
    4: required string Status (go.tag = "json:\"status\""),
    5: required i64 StartTime (go.tag = "json:\"start_time\""),
    6: required i64 EndTime (go.tag = "json:\"end_time\""),
    7: required list<string> ParentMessageIDs (go.tag = "json:\"parent_message_ids,omitempty\""),
}

struct StepUpdateEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string StepID (go.tag = "json:\"step_id\""),
    3: required string PlanID (go.tag = "json:\"plan_id\""),
    4: required string Status (go.tag = "json:\"status\""),
    5: required string Summary (go.tag = "json:\"summary\""),
    6: required string Description (go.tag = "json:\"description\""),
    7: i64 Timestamp (go.tag = "json:\"timestamp\""),
    8: i64 EventOffset (go.tag = "json:\"event_offset\""),
    9: required string SessionID (go.tag = "json:\"session_id\""),
    10: string EventKey (go.tag = "json:\"event_key\""),
}

struct UseToolEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string StepID (go.tag = "json:\"step_id\""),
    3: required string PlanID (go.tag = "json:\"plan_id\""),
    4: required string ToolName (go.tag = "json:\"tool_name\""),
    5: required string Summary (go.tag = "json:\"summary\""),
    6: required string Description (go.tag = "json:\"description\""),
    7: required string Status (go.tag = "json:\"status\""),
    8: optional Terminal Terminal (go.tag = "json:\"terminal,omitempty\""),
    9: optional TextEditor TextEditor (go.tag = "json:\"text_editor,omitempty\""),
    10: optional CodeEditor CodeEditor (go.tag = "json:\"code_editor,omitempty\""),
    11: optional Browser Browser (go.tag = "json:\"browser,omitempty\""),
    12: optional SearchList SearchList (go.tag = "json:\"search_list,omitempty\""),
    13: i64 Timestamp (go.tag = "json:\"timestamp\""),
    14: i64 EventOffset (go.tag = "json:\"event_offset\""),
    15: required string SessionID (go.tag = "json:\"session_id\""),
    16: required string AgentStepID (go.tag = "json:\"agent_step_id\""),
    17: string EventKey (go.tag = "json:\"event_key\""),
}

struct Terminal {
    1: required string Command (go.tag = "json:\"command\""),
    2: required list<string> Output (go.tag = "json:\"output\""),
}

struct TextEditor {
    1: CreateFile CreateFile (go.tag = "json:\"create_file\""),
    2: PatchFile PatchFile (go.tag = "json:\"patch_file\""),
    3: AppendFile AppendFile (go.tag = "json:\"append_file\""),
}

struct CodeEditor {
    1: CreateFile CreateFile (go.tag = "json:\"create_file\""),
    2: PatchFile PatchFile (go.tag = "json:\"patch_file\""),
    3: AppendFile AppendFile (go.tag = "json:\"append_file\""),
}

struct FileLines {
    1: i64 StartLine (go.tag = "json:\"start_line\""),
    2: i64 EndLine (go.tag = "json:\"end_line\""),
    3: list<Line> Lines (go.tag = "json:\"lines\""),
}

struct FilePatch {
    1: string FilePath (go.tag = "json:\"file_path\""),
    2: string Patch (go.tag = "json:\"patch\""),
}

struct Line {
    1: i64 Line (go.tag = "json:\"line\""),
    2: string Content (go.tag = "json:\"content\""),
}

struct CreateFile {
    1: string FilePath (go.tag = "json:\"file_path\""), // 文档名称/ Title
    2: string Content (go.tag = "json:\"content\""), // 文档内容
    3: string SubType (go.tag = "json:\"sub_type\""), // 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
    4: string URL (go.tag = "json:\"url\""), // 文档链接，与 Content 二选一
}

struct PatchFile {
    1: string FilePath (go.tag = "json:\"file_path\""), // 文档名称/ Title
    2: string Diff (go.tag = "json:\"diff\""),
    3: list<FilePatch> PatchFileResult  (go.tag = "json:\"patch_file_result\""),
    4: string SubType (go.tag = "json:\"sub_type\""), // 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
    5: string URL (go.tag = "json:\"url\""), // 文档链接
}

struct AppendFile {
    1: string FilePath (go.tag = "json:\"file_path\""), // 文档名称/ Title
    2: string Content (go.tag = "json:\"content\""), // 文档内容
    3: string SubType (go.tag = "json:\"sub_type\""), // 如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
    4: string URL (go.tag = "json:\"url\""), // 文档链接，与 Content 二选一
}

struct ReadFile {
    1: string FilePath (go.tag = "json:\"file_path\""),
    2: i64 StartLine (go.tag = "json:\"start_line\""),
    3: i64 EndLine (go.tag = "json:\"end_line\""),
}

struct Screenshot {
    1: string ArtifactID (go.tag = "json:\"id\" mapstructure:\"id\""),
    2: string Path (go.tag = "json:\"path\" mapstructure:\"path\""),
    3: string MimeType (go.tag = "json:\"mime_type\" mapstructure:\"mime_type\""),
}

enum BrowserContentType {
    BrowserTypeScreenshot = 1
    BrowserTypeDeploy = 2
    BrowserTypeLogin = 3
}

struct Browser {
    1: string ScreenshotURL (go.tag = "json:\"screenshot_url\""), // 截图url
    2: string URL (go.tag = "json:\"url\""), // 访问页面url 或者部署的页面url
    3: BrowserContentType ContentType (go.tag = "json:\"content_type\"")
    4: string DeployID (go.tag = "json:\"deploy_id\"") // 部署id
    5: optional LoginInfo LoginInfo (go.tag = "json:\"login_info\""),
}

struct LoginInfo {
    1: required LoginStatus LoginStatus (go.tag = "json:\"login_status\""),
    2: optional i64 LoginTimeout (go.tag = "json:\"login_timeout\""),
}

enum LoginStatus {
    LoginStatusUnknown = 0
    LoginStatusWaiting = 1
    LoginStatusSuccess = 4
    LoginStatusFailed = 5
}

struct SearchList {
    1: required string Query (go.tag = "json:\"query\""),
    2: required list<SearchListItem> Results (go.tag = "json:\"results\""),
}

struct SearchListItem {
    1: required string Title (go.tag = "json:\"title\" mapstructure:\"title\""),
    2: required string URL (go.tag = "json:\"url\" mapstructure:\"url\""),
    3: required string Description (go.tag = "json:\"description\" mapstructure:\"description\""),
    4: required string Favicon (go.tag = "json:\"favicon\" mapstructure:\"favicon\""),
}

const string Preparing = "preparing"
const string Recognizing = "recognizing"
const string Thinking = "thinking"
const string Executing = "executing"
const string ReThinking = "rethinking"
const string WaitForReplay = "waiting_for_replay"
const string WaitForNext = "waiting_for_next"
const string Sleeping = "sleeping"
const string Waking = "waking"

struct ProgressNoticeEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string SessionID (go.tag = "json:\"session_id\""),
    3: string Status (go.tag = "json:\"status\""),
    4: i64 Timestamp (go.tag = "json:\"timestamp\""),
    5: i64 EventOffset (go.tag = "json:\"event_offset\""),
    6: string EventKey (go.tag = "json:\"event_key\""),
}

struct ToolCallRequiredEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string SessionID (go.tag = "json:\"session_id\""),
    3: string Tool (go.tag = "json:\"tool\""),
    4: string Question (go.tag = "json:\"question\""),
    5: ToolCallRequiredType Type (go.tag = "json:\"type\""),
    6: i64 Timestamp (go.tag = "json:\"timestamp\""),
    7: i64 EventOffset (go.tag = "json:\"event_offset\""),
    8: required string ToolCallID (go.tag = "json:\"tool_call_id\""),
    9: string EventKey (go.tag = "json:\"event_key\""),
    10: optional TakeBrowserParams TakeBrowserParams (go.tag = "json:\"take_browser_params\""),
    11: string StepID (go.tag = "json:\"step_id\""),
    12: optional FormParams FormParams (go.tag = "json:\"form_params\""),
}

typedef string ToolCallRequiredType

const ToolCallRequiredType ToolCallRequiredTypeAsk = "ask" // 需要用户做自然语言澄清
const ToolCallRequiredType ToolCallRequiredTypeTakeControl = "take_control" // 需要用户接管浏览器

struct TakeBrowserParams { // 接管浏览器参数
    1: required string StreamURL (go.tag = "json:\"stream_url\""),
    2: required string Reason (go.tag = "json:\"reason\""),
    3: required i64 Timeout (go.tag = "json:\"timeout\""), // 超时时间，单位秒
    4: optional bool AskKeepLogin (go.tag = "json:\"ask_keep_login\""), // 是否需要用户确认是否保持登录状态
}

struct FormParams {
    1: required string Title (go.tag = "json:\"title\""),   // 表单标题或解释
    2: required string Body (go.tag = "json:\"body\"")      // 待填写的表单内容，序列化后的 json 字符串
    3: required string Message (go.tag = "json:\"message\""), // 原始 query
    4: optional i64 Timeout (go.tag = "json:\"timeout\""),  // 超时时间，单位秒
}

struct ToolCallConfirmedEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string SessionID (go.tag = "json:\"session_id\""),
    3: required string ToolCallID (go.tag = "json:\"tool_call_id\""),
    4: required string StepID (go.tag = "json:\"step_id\""),
    5: required ToolCallAction Action (go.tag = "json:\"action\""),
    6: string Reason (go.tag = "json:\"reason\""),
    7: i64 Timestamp (go.tag = "json:\"timestamp\""),
    8: i64 EventOffset (go.tag = "json:\"event_offset\""),
    9: string EventKey (go.tag = "json:\"event_key\""),
    10: optional Browser Browser (go.tag = "json:\"browser,omitempty\""),
    11: optional string FormData (go.tag = "json:\"form_data\""), // 填写后的表单数据
}

enum ToolCallAction {
    Reject = 0
    Confirm = 1
    Timeout = 2
}

struct SessionCompletedEvent {
    1: required string EventID (go.tag = "json:\"event_id\""),
    2: required string SessionID (go.tag = "json:\"session_id\""),
    3: string Status (go.tag = "json:\"status\""),
    4: i64 Timestamp (go.tag = "json:\"timestamp\""),
    5: i64 EventOffset (go.tag = "json:\"event_offset\""),
    6: string EventKey (go.tag = "json:\"event_key\""),
}

struct SaveEventKeyRequest  {
    1: required i64 Offset (go.tag = "json:\"offset\""),
    2: required i64 Limit (go.tag = "json:\"limit\""),
    3: optional list<string> EventIDs (go.tag = "json:\"event_ids\""),
}

struct SaveEventKeyResponse  {
    1: required list<string> SuccessEventIDs (go.tag = "json:\"success_event_ids\""),
    2: required list<string> FailedEventIDs (go.tag = "json:\"failed_event_ids\""),
}