namespace go nextagent

include "session.thrift"
include "../common.thrift"

struct GetTraceSessionRequest {
    1: optional string SessionID (api.query = "session_id",),
    2: optional string ReplayID (api.query = "replay_id"),
}

struct GetTraceSessionResponse {
    1: required TraceSession Session (go.tag = "json:\"session\""),
}

struct TraceSession {
    1: string ID (go.tag = "json:\"id\""),
    2: string Creator (go.tag = "json:\"creator\""),
    3: string CreatedAt (go.tag = "json:\"created_at\""),
    4: string UpdatedAt (go.tag = "json:\"updated_at\""),
    5: string Title (go.tag = "json:\"title\""),
    6: session.SessionStatus Status (go.tag = "json:\"status\""),
    7: string RunID (go.tag = "json:\"run_id\""),
    8: RuntimeProvider RunProvider (go.tag = "json:\"run_provider\""),
    9: string RunStatus (go.tag = "json:\"run_status\""),
    10: RunDebugStatus RunDebugStatus (go.tag = "json:\"run_debug_status\""),
    11: string WebshellURL (go.tag = "json:\"webshell_url\""),
    12: string FileServerURL (go.tag = "json:\"file_server_url\""),
    13: string EventsURL (go.tag = "json:\"events_url\""),
    14: AgentMetadata AgentMetadata (go.tag = "json:\"agent_metadata\""),
    15: string LogID (go.tag = "json:\"log_id\""),
    16: string BrowserURL (go.tag = "json:\"browser_url\""),
}

struct AgentMetadata {
    1: string AgentName (go.tag = "json:\"agent_name\""),
    2: string AgentConfigID (go.tag = "json:\"agent_config_id\""),
    3: i32 AgentConfigVersion (go.tag = "json:\"agent_config_version\""),
    4: string AgentConfigVersionID (go.tag = "json:\"agent_config_version_id\""),
}

typedef string RunDebugStatus
const RunDebugStatus Running = "running"
const RunDebugStatus Recoverable = "recoverable"
const RunDebugStatus UnRecoverable = "unrecoverable"

typedef string RuntimeProvider

const RuntimeProvider Local = "local"
const RuntimeProvider Docker = "docker"
const RuntimeProvider StratoCube = "stratocube"
const RuntimeProvider Bytesuite = "bytesuite"

struct GetTraceEventsRequest {
    1: optional string RunID (api.query = "run_id"),
    2: optional string SessionID (api.query = "session_id"),
    3: optional string ContainerID (api.query = "container_id"),
    4: optional string URI (api.query = "uri"),
    5: optional string Provider (api.query = "provider"),
}

struct ResumeRuntimeRequest {
    1: required string RunID (api.query = "run_id"),
}

struct ResumeRuntimeResponse {
    1: required string Message (go.tag = "json:\"message\""),
}

struct SuspendRuntimeRequest {
    1: required string RunID (api.query = "run_id"),
}

struct SuspendRuntimeResponse {
    1: required string Message (go.tag = "json:\"message\""),
}

struct DeleteRuntimeRequest {
    1: required string RunID (api.query = "run_id")
}

struct DeleteRuntimeResponse {
    1: required string Message (go.tag = "json:\"message\""),
}

struct GetTraceSessionChatRequest {
    1: required string SessionID (api.query = "session_id"),
    2: optional ChatCompletionStatus Status (api.query = "status"),
    3: optional string Type (api.query = "type"),
    4: required i64 PageNum (api.query = "page_num"),
    5: required i64 PageSize (api.query = "page_size"),
    6: optional string TraceID (api.query = "trace_id"),
}

struct GetTraceSessionChatResponse {
    1: required list<ChatCompletion> ChatCompletions (go.tag = "json:\"chat_completions\""),
    2: required i64 Total (go.tag = "json:\"total\""),
}

struct ChatCompletion {
    1: required string ID (go.tag = "json:\"id\""),
    2: required string CreatedAt (go.tag = "json:\"created_at\""),
    3: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    4: required string Prompt (go.tag = "json:\"prompt\""),
    5: required string Response (go.tag = "json:\"response\""),
    6: required string Type (go.tag = "json:\"type\""),
    7: required common.JsonVariables Metadata (go.tag = "json:\"metadata\""),
    8: required ChatCompletionStatus Status (go.tag = "json:\"status\""),
    9: required string ModelName (go.tag = "json:\"model_name\""),
}

typedef string ChatCompletionStatus
const ChatCompletionStatus Success = "success"
const ChatCompletionStatus Fail = "fail"

struct ListSessionAgentStepRequest {
    1: required string SessionID (api.query = "session_id"),
}

struct ListSessionAgentStepResponse {
    1: required list<AgentStep> Steps (go.tag = "json:\"steps\""),
    2: required i64 Total (go.tag = "json:\"total\""),
}

struct AgentStep {
    1: required string Action (go.tag = "json:\"action\""),
    2: required string Executor (go.tag = "json:\"executor\""),
    3: required string MCPTool (go.tag = "json:\"mcp_tool\""),
    4: required i64 StartTime (go.tag = "json:\"start_time\""),
    5: required i64 EndTime (go.tag = "json:\"end_time\""),
    6: required AgentStepStatus Status (go.tag = "json:\"status\""),
    7: required string StepID (go.tag = "json:\"step_id\""),
}

typedef string AgentStepStatus
const AgentStepStatus AgentStepCreated = "created"
const AgentStepStatus AgentStepRunning = "running"
const AgentStepStatus AgentStepSuccess = "success"
const AgentStepStatus AgentStepFailed =  "failed"

struct DownloadSessionLogRequest {
    1: required string SessionID (api.query = "session_id"),
}

struct ListModelsRequest {
}

struct ListModelsResponse {
    1: required list<Model> Models (go.tag = "json:\"models\""),
}

struct Model {
    1: string Type (go.tag = "json:\"type\""),
    2: list<string> Models (go.tag = "json:\"models\""),
}

struct ChatStreamRequest {
    1: required string Model (go.tag = "json:\"model\""),
    2: required list<ChatCompletionMessage> Messages (go.tag = "json:\"messages\""),
    3: optional i32 MaxTokens (go.tag = "json:\"max_tokens\""),
    4: optional i32 MaxCompletionTokens (go.tag = "json:\"max_completion_tokens\""),
    5: optional double Temperature (go.tag = "json:\"temperature\""),
    6: optional double TopP (go.tag = "json:\"top_p\""),
    7: optional i32 N (go.tag = "json:\"n\""),
    8: optional bool Stream (go.tag = "json:\"stream\""),
    9: optional list<string> Stop (go.tag = "json:\"stop\""),
    10: optional double PresencePenalty (go.tag = "json:\"presence_penalty\""),
    11: optional double FrequencyPenalty (go.tag = "json:\"frequency_penalty\""),
    12: optional map<string, i32> LogitBias (go.tag = "json:\"logit_bias\""),
    13: optional bool LogProbs (go.tag = "json:\"logprobs\""),
    14: optional i32 TopLogProbs (go.tag = "json:\"top_logprobs\""),
    15: optional string User (go.tag = "json:\"user\""),
    16: optional list<Tool> Tools (go.tag = "json:\"tools\""),
    17: optional ToolChoiceType ToolChoice (go.tag = "json:\"tool_choice\""),
    18: optional bool Store (go.tag = "json:\"store\""),
    19: optional string ReasoningEffort (go.tag = "json:\"reasoning_effort\""),
    20: optional map<string, string> Metadata (go.tag = "json:\"metadata\""),
    21: optional ThinkingConfig Thinking (go.tag = "json:\"thinking\""),
}

struct ChatCompletionMessage {
    1: required string Role (go.tag = "json:\"role\""),
    2: optional string Content (go.tag = "json:\"content\""),
    3: optional string Refusal (go.tag = "json:\"refusal\""),
    4: optional list<ChatMessagePart> MultiContent (go.tag = "json:\"multi_content\""),
    5: optional string Name (go.tag = "json:\"name\""),
    6: optional FunctionCall FunctionCall (go.tag = "json:\"function_call\""),
    7: optional list<ChatToolCall> ToolCalls (go.tag = "json:\"tool_calls\""),
    8: optional string ToolCallID (go.tag = "json:\"tool_call_id\""),
}

typedef string ChatMessagePartType
const ChatMessagePartType Text = "text"
const ChatMessagePartType ImageURL = "image_url"

struct ChatMessagePart {
    1: optional ChatMessagePartType Type (go.tag = "json:\"type\""),
    2: optional string Text (go.tag = "json:\"text\""),
    3: optional ChatMessageImageURL ImageURL (go.tag = "json:\"image_url\""),
}

struct ChatMessageImageURL {
    1: optional string URL (go.tag = "json:\"url\""),
    2: optional string Detail (go.tag = "json:\"detail\""),
}

struct ChatToolCall {
    1: optional i32 Index (go.tag = "json:\"index\""),
    2: optional string ID (go.tag = "json:\"id\""),
    3: required string Type (go.tag = "json:\"type\""),
    4: required FunctionCall Function (go.tag = "json:\"function\""),
}

struct FunctionCall {
    1: optional string Name (go.tag = "json:\"name\""),
    2: optional string Arguments (go.tag = "json:\"arguments\""),
}

struct Tool {
    1: required string Type (go.tag = "json:\"type\""),
    2: optional FunctionDefinition Function (go.tag = "json:\"function\""),
}

union ToolChoiceType {
    1: string StringChoice,
    2: ToolChoiceObject ObjectChoice,
}

struct ToolChoiceObject {
    1: required string Type (go.tag = "json:\"type\""),
    2: required ToolFunction Function (go.tag = "json:\"function\""),
}

struct ToolFunction {
    1: required string Name (go.tag = "json:\"name\""),
}

struct FunctionDefinition {
    1: required string Name (go.tag = "json:\"name\""),
    2: optional string Description (go.tag = "json:\"description\""),
    3: optional bool Strict (go.tag = "json:\"strict\""),
    4: required common.JsonVariables Parameters (go.tag = "json:\"parameters\""),
}

struct ThinkingConfig {
    1: optional string Type (go.tag = "json:\"type\""),
    2: optional i32 BudgetTokens (go.tag = "json:\"budget_tokens\""),
}

struct ListSessionDocumentsRequest {
    1: required string SessionID (api.query = "session_id"),
}

struct ListSessionDocumentsResponse {
    1: required list<Document> Documents (go.tag = "json:\"documents\""),
}

struct Document {
    1: required string Name (go.tag = "json:\"name\""),
    2: required string FilePath (go.tag = "json:\"file_path\""),
    3: required string Type (go.tag = "json:\"type\""),
    4: required string Content (go.tag = "json:\"content\""),
    5: required i64 Size (go.tag = "json:\"size\""),
    6: optional string ArtifactID (go.tag = "json:\"artifact_id\""),
    7: required list<string> RelatedFiles (go.tag = "json:\"related_files\""),
    8: required string CreatedAt (go.tag = "json:\"created_at\""),
}

struct ConvertSessionDocumentToLarkRequest {
    1: required string SessionID (api.query = "session_id"),
    2: required string FilePath (api.query = "file_path"),
    3: optional string ArtifactID (api.query = "artifact_id"),
    4: optional bool ForceRegenerate (api.query = "force_regenerate"),
}

struct ConvertSessionDocumentToLarkResponse {
    1: required string LarkURL (go.tag = "json:\"lark_url\""),
}

struct TraceMCPEventsRequest {
    1: required string SessionID (api.query = "session_id"),
}

struct ConvertMarkdownToLarkRequest {
    1: required string Content (api.tag = "content"),
    2: optional string SessionID (api.tag = "session_id"),
}

struct ConvertMarkdownToLarkResponse {
    1: required string LarkURL (go.tag = "json:\"lark_url\""),
}