namespace go nextagent

include "../common.thrift"

typedef string PermissionAction
const PermissionAction PermissionActionUnknown = "unknown"
// Session 模块
const PermissionAction 	PermissionActionSessionCreate = "session.create"
const PermissionAction 	PermissionActionSessionDelete = "session.delete"
const PermissionAction 	PermissionActionSessionRead = "session.read"
const PermissionAction 	PermissionActionSessionUpdate = "session.update"
const PermissionAction 	PermissionActionSessionAllFile = "session.all_file"
const PermissionAction 	PermissionActionSessionChat = "session.chat"
const PermissionAction 	PermissionActionSessionVisualization = "session.visualization"
const PermissionAction 	PermissionActionSessionTemplateCreate = "session.template.create"
const PermissionAction 	PermissionActionSessionFeedback = "session.feedback"
const PermissionAction 	PermissionActionSessionDownload = "session.download"
const PermissionAction 	PermissionActionSessionShareCreate = "session.share.create"
const PermissionAction 	PermissionActionSessionVSCodeOpen = "session.vscode.open"
const PermissionAction 	PermissionActionSessionTraceRead = "session.trace.read"
const PermissionAction 	PermissionActionSessionMCPTraceRead = "session.mcp_trace.read"
// MCP 模块
const PermissionAction PermissionActionMCPCreate  = "mcp.create"
const PermissionAction PermissionActionMCPRead    = "mcp.read"
const PermissionAction PermissionActionMCPUpdate  = "mcp.update"
const PermissionAction PermissionActionMCPDelete  = "mcp.delete"
// Template 模块
const PermissionAction PermissionActionTemplateRead    = "template.read"
const PermissionAction PermissionActionTemplateUpdate  = "template.update"
const PermissionAction PermissionActionTemplateDelete  = "template.delete"
const PermissionAction PermissionActionTemplateCreate  = "template.create"
// Knowledgebase 模块
const PermissionAction PermissionActionKnowledgebaseRead    = "knowledgebase.read"
const PermissionAction PermissionActionKnowledgebaseUpdate  = "knowledgebase.update"
const PermissionAction PermissionActionKnowledgebaseDelete  = "knowledgebase.delete"
const PermissionAction PermissionActionKnowledgebaseCreate  = "knowledgebase.create"
// Space 模块
const PermissionAction PermissionActionSpaceCreate  = "space.create"
const PermissionAction PermissionActionSpaceUpdate  = "space.update"
const PermissionAction PermissionActionSpaceDelete  = "space.delete"
const PermissionAction PermissionActionSpaceRead    = "space.read"
const PermissionAction PermissionActionSpaceDevResourceUpdate    = "space.dev_resource_update"
// Agent模块
const PermissionAction PermissionActionAgentCreate  = "agent.create"
const PermissionAction PermissionActionAgentUpdate  = "agent.update"
const PermissionAction PermissionActionAgentDelete  = "agent.delete"
const PermissionAction PermissionActionAgentRead    = "agent.read"

typedef string PermissionType
const PermissionType PermissionTypeUnknown = "unknown"
const PermissionType PermissionTypeUser = "user"
const PermissionType PermissionTypeDepartment = "department"
const PermissionType PermissionTypeSpace = "space"
const PermissionType PermissionTypeServiceAccount = "service_accout"

enum PermissionRole {
    PermissionRoleUnknown = 0
    PermissionRoleVisitor = 1000
    PermissionRoleMember = 2000
    PermissionRoleMananger = 3000
}

struct ResourcePermission {
    1: required string ID  (go.tag="json:\"id\""),
    2: required string ResourceID  (go.tag="json:\"resource_id\""),
    3: required ResourceType ResourceType (go.tag="json:\"resource_type\""),
    4: required PermissionType Type (go.tag="json:\"type\""),
    // Usernme / DepartmentName / SpaceID
    5: required string ExternalID (go.tag="json:\"external_id\""),
    6: required PermissionRole Role (go.tag="json:\"role\""),
    7: required string CreatedAt (go.tag = "json:\"created_at\""),
    8: required string UpdatedAt (go.tag = "json:\"updated_at\""),

    9: optional list<PermissionAction> Actions (go.tag="json:\"actions\""),
}


typedef string ResourceType
const ResourceType ResourceTypeUnknown = "unknown"
const ResourceType ResourceTypeSession = "session"
const ResourceType ResourceTypeArtifact = "artifact"
const ResourceType ResourceTypeMCP = "mcp"
const ResourceType ResourceTypeTemplate = "template"
const ResourceType ResourceTypeSpace = "space"
const ResourceType ResourceTypeKnowledgebase = "knowledgebase"

typedef string ResourceStatus
const ResourceStatus ResourceStatusUnknown = "unknown"
const ResourceStatus ResourceStatusPublic = "public"
const ResourceStatus ResourceStatusPrivate = "private"

struct Group {
    1: required string ResourceID (go.tag="json:\"resource_id\""),
    2: required string GroupID (go.tag="json:\"group_id\""),
}

struct Resource {
    1: required string ID (go.tag="json:\"id\""),
    2: required ResourceType Type (go.tag="json:\"type\""),
    3: required string ExternalID (go.tag="json:\"external_id\""),
    4: required string Owner (go.tag="json:\"owner\""),
    5: required ResourceStatus Status (go.tag="json:\"status\""),
    6: required string CreatedAt (go.tag = "json:\"created_at\""),
    7: required string UpdatedAt (go.tag = "json:\"updated_at\""),

    // 关联数据，按需返回
    8: optional list<Group> Groups (go.tag="json:\"groups\""),
    // 一个用户可能存在多个不同的角色，不同的角色有不同的权限点取并集
    9: optional list<ResourcePermission> Permissions (go.tag="json:\"permissions\""),
}

struct GetUserResourcePermissionRequest {
    // User 取当前登陆的用户
    // ID 不存在时，使用 ExternalID + Type 的组合查询资源, 比如查当前用户所在空间的权限，external_id 为 space_id,type 为 space, 也可以直接使用 resource id
    1: optional string ResourceID (api.query="resource_id"),
    2: optional string ExternalID (api.query="external_id"),
    3: optional ResourceType Type (api.query="type"),
}

struct GetUserResourcePermissionResponse {
    1: required Resource Resource (go.tag="json:\"resource\""),
}