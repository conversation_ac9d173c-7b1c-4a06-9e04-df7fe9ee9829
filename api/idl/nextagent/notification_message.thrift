namespace go nextagent

typedef string ReceiveType
const ReceiveType ReceiveTypePersonal = "personal"
const ReceiveType ReceiveTypeAll = "all"
const ReceiveType ReceiveTypeDepartment = "department"

typedef string MessageType
const MessageType MessageTypeNotification = "notification"
const MessageType MessageTypeInvitation = "invitation"

struct CreateNotificationMessageRequest {
  1: required string Title (go.tag="json:\"title\"");
  2: required string Content (go.tag="json:\"content\"");             // 通知内容
  3: required list<ReceiveConfig> ReceiveConfig (go.tag="json:\"receive_config\"");
  4: optional bool IsTop (go.tag="json:\"is_top\"");                // 是否置顶
  5: optional bool SendLark (go.tag="json:\"send_lark\"");          // 是否发送 lark 通知
  6: optional LinkInfo LinkInfo (go.tag="json:\"link_info\"");
  7: required MessageType Type (go.tag="json:\"type\"");
}

struct ReceiveConfig {
  1: required ReceiveType ReceiveType (go.tag="json:\"receive_type\""); // 接收类型：个人，全体等
  2: optional list<string> Receivers (go.tag="json:\"receivers\"");
}

struct CreateNotificationMessageResponse {
  1: required string MessageID (go.tag="json:\"message_id\"");
}

struct ListNotificationMessagesRequest {}

typedef string MessageStatus
const MessageStatus MessageStatusUnread = "unread"
const MessageStatus MessageStatusRead = "read"
const MessageStatus MessageStatusRecalled = "recalled"

// 消息
struct NotificationMessage {
  1: required string MessageID (go.tag="json:\"message_id\"");
  2: required string Title (go.tag="json:\"title\"");
  3: required string Content (go.tag="json:\"content\"");              // 通知内容
  4: optional LinkInfo LinkInfo (go.tag="json:\"link_info\"");           // 跳转链接信息
  5: required MessageStatus Status (go.tag="json:\"status\"");        // 消息状态
  6: required bool IsTop (go.tag="json:\"is_top\"");
  7: required i64 CreatedAt (go.tag="json:\"created_at\"");
  8: required string Creator (go.tag="json:\"creator\"");
  9: required MessageType Type (go.tag="json:\"type\"");
}

struct LinkInfo {
  1: required string Link (go.tag="json:\"link\"");
  2: required string Name (go.tag="json:\"name\"");
}

struct ListNotificationMessagesResponse {
  1: optional list<NotificationMessage> Messages (go.tag="json:\"message\"");
}

struct UpdateNotificationMessageStatusRequest {
       1: required string MessageID (go.tag="json:\"messageid\"");
       2: required MessageStatus Status (go.tag="json:\"status\"");
}

struct UpdateNotificationMessageStatusResponse {}