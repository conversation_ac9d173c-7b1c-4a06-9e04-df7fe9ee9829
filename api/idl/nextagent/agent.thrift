namespace go nextagent

include "../common.thrift"
include "knowledgeset.thrift"
include "permission.thrift"

struct Agent {
    1: required string ID (go.tag="json:\"id\""),
    2: required string Name (go.tag="json:\"name\""),
    3: required string Description (go.tag="json:\"description\""),
    4: required string Creator (go.tag="json:\"creator\""),
    5: required string CreatedAt (go.tag = "json:\"created_at\""),
    6: required string UpdatedAt (go.tag = "json:\"updated_at\""),
}

typedef string AgentConfigType
const AgentConfigType AgentConfigTypeUnknown = "unknown"
const AgentConfigType AgentConfigTypeBase = "base"
const AgentConfigType AgentConfigTypeFeature = "feature"
const AgentConfigType AgentConfigTypeUser = "user"
const AgentConfigType AgentConfigTypeAbTest = "abtest"

typedef string AgentConfigStatus
const AgentConfigStatus AgentConfigStatusUnknown = "unknown"
const AgentConfigStatus AgentConfigStatusCreated = "created"
const AgentConfigStatus AgentConfigStatusOnline = "online"
const AgentConfigStatus AgentConfigStatusCanary = "canary"

struct AgentConfig {
    1: required string ID (go.tag="json:\"id\""),
    2: required string AgentID (go.tag="json:\"agent_id\""),
    3: required AgentConfigType Type (go.tag="json:\"type\""),
    4: required string Name (go.tag="json:\"name\""),
    5: required string Description (go.tag="json:\"description\""),
    6: required string Creator (go.tag="json:\"creator\""),
    7: required string CreatedAt (go.tag = "json:\"created_at\""),
    8: required string UpdatedAt (go.tag = "json:\"updated_at\""),
}

struct AgentConfigVersion {
    1: required string ID (go.tag="json:\"id\""),
    2: required string AgentConfigID (go.tag="json:\"agent_config_id\""),
    3: required string Description (go.tag="json:\"description\""),
    4: required string Creator (go.tag="json:\"creator\""),
    5: required i32 Version (go.tag="json:\"version\""),
    6: required bool Enabled (go.tag="json:\"enabled\""),
    7: required AgentConfigStatus Status (go.tag="json:\"status\""),
    8: required RuntimeConfig RuntimeConfig (go.tag="json:\"runtime_config\""),
    9: required common.JsonVariables CustomConfig (go.tag="json:\"custom_config\""),
    10: required PromptConfig PromptConfig (go.tag="json:\"prompt_config\""),
    11: required string CreatedAt (go.tag = "json:\"created_at\""),
    12: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    13: optional KnowledgesetConfig KnowledgesetConfig (go.tag="json:\"knowledgeset_config\""),
    14: optional list<knowledgeset.Knowledgeset> Knowledgesets (go.tag="json:\"knowledge_set\""),
    15: optional list<knowledgeset.KnowledgesetVersion> KnowledgesetVersions (go.tag="json:\"knowledge_set_version\""),
    16: optional string DeployID (go.tag="json:\"deploy_id\""),
}

struct ResourceQuantity {
    1: required string Requests (go.tag="json:\"requests\""),
    2: optional string Limits (go.tag="json:\"limits\""),
}
struct RuntimeResourceQuota {
    1: optional ResourceQuantity CPU (go.tag="json:\"cpu\""),
    2: optional ResourceQuantity MEM (go.tag="json:\"mem\""),
    3: optional ResourceQuantity PersistWorkspace (go.tag="json:\"persist_workspace\""),
    4: optional string StorageClass (go.tag="json:\"storage_class\""),
}
struct OrchestrationConfig {
    // 0 不限制
    1: required i32 MaxConcurrency (go.tag="json:\"max_concurrency\""),
    2: required i32 PoolSize (go.tag="json:\"max_poolsize\""),
    3: required string RuntimeStopTimeout (go.tag="json:\"runtime_stop_timeout\""),
    4: required string RuntimeDeleteTimeout (go.tag="json:\"runtime_delete_timeout\""),
}

typedef string RuntimeConfigType
const RuntimeConfigType RuntimeConfigTypeUnknown = "unknown"
const RuntimeConfigType RuntimeConfigTypeStratoCube = "stratocube"
const RuntimeConfigType RuntimeConfigTypeDocker = "docker"

struct RuntimeConfig {
    1: required string ID (go.tag="json:\"id\""),
    2: required string PSM (go.tag="json:\"psm\""),
    3: required RuntimeConfigType Type (go.tag="json:\"type\""),
    4: required bool InjectOpenAIToken (go.tag="json:\"inject_openai_token\""),
    5: optional string Image (go.tag="json:\"image\""),
    6: optional map<string, string> Envs (go.tag="json:\"envs\""),
    7: optional i32 Port (go.tag="json:\"port\""),
    8: optional RuntimeResourceQuota Quota (go.tag="json:\"runtime_resource_quota\""),
    9: optional string BinarySource (go.tag="json:\"binary_source\""),
    10: optional OrchestrationConfig OrchestrationConfig (go.tag="json:\"orchestration_config\""),
}

struct PromptConfig {
    1: required list<PromptConfigMetadata> prompts (go.tag="json:\"prompts\"")
}

struct PromptConfigMetadata {
    1: required string Key (go.tag="json:\"key\""),
    2: required string ID (go.tag="json:\"id\""),
    3: required i32 version (go.tag="json:\"version\""),
}

struct KnowledgesetConfig {
  1: required list<KnowledgesetMetadata> Knowledgesets (go.tag="json:\"knowledge_sets\""),
}

struct KnowledgesetMetadata{
  1: required string KnowledgesetID (go.tag="json:\"knowledge_set_id\""),
  2: required string KnowledgesetVersionID (go.tag="json:\"knowledge_set_version_id\""),
  3: required string KnowledgesetType (go.tag="json:\"knowledge_set_type\""),
}

struct CreateAgentRequest {
    1: required string Name (go.tag="json:\"name\""),
    2: required string Description (go.tag="json:\"description\""),
}
struct CreateAgentResponse {
    1: required Agent Agent (go.tag="json:\"agent\""),
}

// 创建不同类型的配置
struct CreateAgentConfigRequest {
    1: required string AgentID (go.tag="json:\"agent_id\""),
    2: required AgentConfigType Type (go.tag="json:\"type\""),
    3: required string Name (go.tag="json:\"name\""),
    4: required string Description (go.tag="json:\"description\""),
}
struct CreateAgentConfigResponse {
    1: required AgentConfig AgentConfig (go.tag="\"agent_config\""),
}

// 创建配置版本
struct CreateAgentConfigVersionRequest {
    1: required string AgentConfigID (go.tag = "json:\"agent_config_id\""),
    2: required string Description (go.tag="json:\"description\""),
    3: required bool Enabled (go.tag="json:\"enabled\""),
    4: required RuntimeConfig RuntimeConfig (go.tag="json:\"runtime_config\""),
    5: required common.JsonVariables CustomConfig (go.tag="json:\"custom_config\""),
    6: required PromptConfig PromptConfig (go.tag="json:\"prompt_config\""),
    7: optional KnowledgesetConfig KnowledgesetConfig (go.tag="json:\"knowledge_set_config\""),
}
struct CreateAgentConfigVersionResponse {
    1: required AgentConfigVersion AgentConfigVersion (go.tag="json:\"agent_config_version\""),
}

struct UpdateAgentRequest {
    1: required string AgentID (api.path = "agent_id"),
    2: optional string Name (go.tag="json:\"name\""),
    3: optional string Description (go.tag="json:\"description\""),
}
struct UpdateAgentResponse {
    1: required Agent Agent (go.tag="json:\"agent\""),
}

struct UpdateAgentConfigRequest {
    1: required string AgentConfigID (api.path = "agent_config_id"),
    2: optional string Name (go.tag="json:\"name\""),
    3: optional string Description (go.tag="json:\"description\""),
}
struct UpdateAgentConfigResponse {
    1: required AgentConfig AgentConfig (go.tag="\"agent_config\""),
}

// Base 版本只支持更新 enabled or description or name 其他都是创建新版本，其他类型支持直接更新，不需要创建新版
struct UpdateAgentConfigVersionRequest {
    1: required string AgentConfigVersionID (api.path = "agent_config_version_id"),
    2: optional bool Enabled (go.tag="json:\"enabled\""),
    3: optional string Description (go.tag="json:\"description\""),
    4: optional RuntimeConfig RuntimeConfig (go.tag="json:\"runtime_config\""),
    5: optional common.JsonVariables CustomConfig (go.tag="json:\"custom_config\""),
    6: optional PromptConfig PromptConfig (go.tag="json:\"prompt_config\""),
    7: optional KnowledgesetConfig KnowledgesetConfig (go.tag="json:\"knowledge_set_config\""),
    8: optional string UpdatedAt (go.tag="json:\"updated_at\""),
}
struct UpdateAgentConfigVersionResponse {
    1: required AgentConfigVersion AgentConfigVersion (go.tag="json:\"agent_config_version\""),
}

// TODO: 需要有个百分比的概念, 待设计，可能会复用 TCC
struct DeployAgentConfigVersionRequest {
    1: required string AgentConfigVersionID (api.path = "agent_config_version_id"),
    2: required AgentConfigStatus Status (go.tag="json:\"status\""),
}
struct DeployAgentConfigVersionResponse {
     1: required AgentConfigVersion AgentConfigVersion (go.tag="json:\"agent_config_version\""),
}

struct DeleteAgentRequest {
    1: required string AgentID (api.path = "agent_id"),
}
struct DeleteAgentResponse {}

struct DeleteAgentConfigRequest {
    1: required string AgentConfigID (api.path = "agent_config_id"),
}
struct DeleteAgentConfigResponse {}

struct GetAgentRequest {
    1: required string AgentID (api.path = "agent_id"),
}
struct GetAgentResponse {
    1: required Agent Agent (go.tag="json:\"agent\""),
}

struct ListAgentsRequest {
    1: optional string Creator (api.query = "creator"),
    // 根据名字进行匹配
    2: optional string Name (api.query = "name"),
    3: required i64 PageNum (api.query = "page_num"),
    4: required i64 PageSize (api.query = "page_size"),
}
struct ListAgentsResponse {
    1: required list<Agent> Agents (go.tag = "json:\"agents\""),
    2: required i64 Total (go.tag = "json:\"total\""),
}

struct GetAgentConfigRequest {
    1: required string AgentConfigID (api.path = "agent_config_id"),
    // 获取指定版本的具体配置内容
    2: optional i32 Version (api.query = "version"),
    // 获取最新版本，如果 Version 不为空则以 Version 为准, 如果都为空，则不返回具体配置
    3: optional bool Latest (api.query = "latest"),
    // 获取 Online 状态的具体配置内容
    4: optional bool Online (api.query = "online")
}
struct GetAgentConfigResponse {
    1: required AgentConfig AgentConfig (go.tag="json:\"agent_config\""),
    2: optional AgentConfigVersion AgentConfigVersion (go.tag="json:\"agent_config_version\""),
}

struct ListAgentConfigsRequest {
    1: optional string AgentID (api.query = "agent_id"),
    // 支持一组 Type 用逗号间隔
    2: optional string Type (api.query = "type"),
    3: optional string Name (api.query = "name"),
    4: optional string Creator (api.query = "creator"),
    5: required i64 PageNum (api.query = "page_num"),
    6: required i64 PageSize (api.query = "page_size"),
}
struct ListAgentConfigsResponse {
    1: required Agent Agent (go.tag = "json:\"agent\""),
    2: required list<AgentConfig> AgentConfigs (go.tag = "json:\"agent_configs\""),
    3: required i64 Total (go.tag = "json:\"total\""),
}

struct ListAgentConfigVersionsRequest {
    1: required string AgentConfigID (api.query = "agent_config_id"),
    2: optional string Creator (api.query = "creator"),
    3: optional bool Enabled (api.query = "enabled"),
    4: required i64 PageNum (api.query = "page_num"),
    5: required i64 PageSize (api.query = "page_size"),
    // 支持一组 Status 用逗号间隔
    6: optional string Status (api.query = "status"),
}

struct ListAgentConfigVersionsResponse {
    1: required AgentConfig AgentConfig (go.tag = "json:\"agent_config\""),
    2: required list<AgentConfigVersion> AgentConfigVersions (go.tag = "json:\"agent_config_versions\""),
    3: required i64 Total (go.tag = "json:\"total\""),
}

struct GetAgentConfigVersionRequest {
    1: required string AgentConfigVersionID (api.path = "agent_config_version_id"),
}

struct GetAgentConfigVersionResponse {
    1: required AgentConfigVersion AgentConfigVersion (go.tag="json:\"agent_config_version\"")
}

struct CopyAgentConfigVersionRequest {
    1: required string SourceID (go.tag="json:\"source_id\""),
    2: required string AgentConfigID (go.tag="json:\"agent_config_id\""),
    3: optional string Description (go.tag="json:\"description\""),
}

struct CopyAgentConfigVersionResponse {
    1: required string AgentConfigVersionID (go.tag="json:\"agent_config_version_id\""),
}

struct AddAgentPermissionRequest {
    1: required string AgentID (api.path = "agent_id"),
    2: required permission.PermissionType Type (go.tag="json:\"type\""),
    3: required string ExternalID (go.tag="json:\"external_id\""),
    4: required permission.PermissionRole Role (go.tag="json:\"role\""),
}

struct AddAgentPermissionResponse {
    1: required permission.Resource Resource (go.tag="json:\"resource\""),
}