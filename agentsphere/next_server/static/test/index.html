<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试平台 - 在线单测和E2E测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .card h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card h2::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .test-cases-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
        }

        .test-case-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .test-case-item:hover {
            background: #f7fafc;
            border-color: #cbd5e0;
        }

        .test-case-item.selected {
            background: #ebf8ff;
            border-color: #4299e1;
        }

        .test-case-item input[type="checkbox"] {
            margin-right: 12px;
            transform: scale(1.2);
        }

        .test-case-name {
            flex: 1;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
        }

        .test-case-name .name-text {
            flex: 1;
            min-width: 0;
        }

        .test-case-description {
            margin-top: 8px;
            font-size: 12px;
            color: #718096;
            line-height: 1.4;
            font-weight: 400;
        }

        .type-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            flex-shrink: 0;
        }

        .type-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .type-e2e {
            background: #ff6b6b;
            color: white;
            border: 1px solid #ff5252;
            box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
        }

        .type-module {
            background: #4ecdc4;
            color: white;
            border: 1px solid #26d0ce;
            box-shadow: 0 2px 4px rgba(78, 205, 196, 0.3);
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .execution-item {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }

        .execution-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .execution-name {
            font-weight: 600;
            color: #2d3748;
        }

        .execution-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-success {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-failed {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-running {
            background: #bee3f8;
            color: #2a4365;
        }

        .status-queued {
            background: #fef5e7;
            color: #744210;
        }

        .execution-time {
            font-size: 12px;
            color: #718096;
            margin-bottom: 8px;
        }

        .execution-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .execution-session {
            margin-bottom: 8px;
        }

        .session-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .session-link:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .error-message {
            background: #fed7d7;
            color: #742a2a;
            padding: 12px;
            border-radius: 6px;
            margin-top: 12px;
            border-left: 4px solid #e53e3e;
        }

        .success-message {
            background: #c6f6d5;
            color: #22543d;
            padding: 12px;
            border-radius: 6px;
            margin-top: 12px;
            border-left: 4px solid #38a169;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .refresh-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(180deg);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>🧪 测试平台</h1>
        <p>在线单测和E2E测试管理平台</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number" id="total-cases">-</div>
            <div class="stat-label">总测试用例</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="total-executions">-</div>
            <div class="stat-label">总执行次数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="success-rate">-</div>
            <div class="stat-label">成功率</div>
        </div>
    </div>

    <div class="main-content">
        <div class="card">
            <h2>📋 测试用例</h2>
            <div class="test-cases-list" id="test-cases-list">
                <div style="text-align: center; padding: 40px; color: #718096;">
                    <div class="loading"></div>
                    <p style="margin-top: 12px;">加载测试用例中...</p>
                </div>
            </div>
            <div class="button-group">
                <button class="btn btn-secondary" onclick="selectAll()">
                    <span>全选</span>
                </button>
                <button class="btn btn-secondary" onclick="deselectAll()">
                    <span>取消全选</span>
                </button>
                <button class="btn btn-primary" onclick="runSelectedTests()" id="run-btn">
                    <span>🚀 执行选中测试</span>
                </button>
            </div>
        </div>

        <div class="card">
            <h2>📊 执行结果</h2>
            <button class="refresh-btn" onclick="loadTestExecutions()" title="刷新结果">
                🔄
            </button>
            <div id="executions-list">
                <div style="text-align: center; padding: 40px; color: #718096;">
                    <div class="loading"></div>
                    <p style="margin-top: 12px;">加载执行结果中...</p>
                </div>
            </div>
        </div>
    </div>

    <div id="message-container"></div>
</div>

<script>
    // API配置
    const API_BASE = '/api/agents/v2/testing';
    const LOGIN_API = 'https://cloud.bytedance.net/auth/api/v1/jwt';

    let authToken = null;
    let testCases = [];
    let executions = [];

    // 从URL获取session_id
    function getSessionId() {
        const pathSegments = window.location.pathname.split('/');
        return pathSegments[pathSegments.length - 1] || '';
    }

    const session_id = getSessionId();
    console.log('session_id:', session_id);

    // 获取认证token
    async function fetchToken() {
        try {
            const response = await fetch(LOGIN_API, {
                credentials: 'include'
            });
            return "Byte-Cloud-JWT " + response.headers.get('x-jwt-token');
        } catch (error) {
            console.error('获取token失败:', error);
            return null;
        }
    }

    // 显示消息
    function showMessage(message, type = 'info') {
        const container = document.getElementById('message-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
        messageDiv.textContent = message;

        container.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }

    // 加载测试用例
    async function loadTestCases() {
        try {
            const response = await fetch(`${API_BASE}/cases?session_id=${encodeURIComponent(session_id)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            testCases = data.cases || [];

            renderTestCases();
            updateStats();
        } catch (error) {
            console.error('加载测试用例失败:', error);
            document.getElementById('test-cases-list').innerHTML =
                '<div class="error-message">加载测试用例失败: ' + error.message + '</div>';
        }
    }

    // 渲染测试用例列表
    function renderTestCases() {
        const container = document.getElementById('test-cases-list');

        if (testCases.length === 0) {
            container.innerHTML = '<div style="text-align: center; padding: 40px; color: #718096;">暂无测试用例</div>';
            return;
        }

        container.innerHTML = testCases.map((testCase, index) => {
            // 根据type设置标签样式
            let typeTag = '';
            if (testCase.type) {
                const typeClass = testCase.type === 'e2e' ? 'type-e2e' : 'type-module';
                typeTag = `<span class="type-tag ${typeClass}">${testCase.type}</span>`;
            }
            
            return `
                <div class="test-case-item" onclick="toggleTestCase(${index})">
                    <input type="checkbox" id="case-${index}" onchange="event.stopPropagation()">
                    <div class="test-case-name">
                        <div class="name-text">${testCase.name}</div>
                        ${typeTag}
                    </div>
                </div>
                ${testCase.description ? `<div class="test-case-description">${testCase.description}</div>` : ''}
            `;
        }).join('');
    }

    // 切换测试用例选择状态
    function toggleTestCase(index) {
        const checkbox = document.getElementById(`case-${index}`);
        checkbox.checked = !checkbox.checked;
    }

    // 全选
    function selectAll() {
        testCases.forEach((_, index) => {
            const checkbox = document.getElementById(`case-${index}`);
            if (checkbox) checkbox.checked = true;
        });
    }

    // 取消全选
    function deselectAll() {
        testCases.forEach((_, index) => {
            const checkbox = document.getElementById(`case-${index}`);
            if (checkbox) checkbox.checked = false;
        });
    }

    // 执行选中的测试
    async function runSelectedTests() {
        const selectedCases = [];
        testCases.forEach((_, index) => {
            const checkbox = document.getElementById(`case-${index}`);
            if (checkbox && checkbox.checked) {
                selectedCases.push(testCases[index].name);
            }
        });

        if (selectedCases.length === 0) {
            showMessage('请选择要执行的测试用例', 'error');
            return;
        }

        const runBtn = document.getElementById('run-btn');
        const originalText = runBtn.innerHTML;
        runBtn.innerHTML = '<span class="loading"></span> 执行中...';
        runBtn.disabled = true;

        try {
            // 获取认证token
            if (!authToken) {
                authToken = await fetchToken();
                if (!authToken) {
                    throw new Error('无法获取认证token');
                }
            }

            const response = await fetch(`${API_BASE}/cases/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authToken
                },
                body: JSON.stringify({
                    cases: selectedCases,
                    session_id: session_id
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            showMessage(`成功启动 ${selectedCases.length} 个测试用例的执行`, 'success');

            // 延迟加载执行结果
            setTimeout(() => {
                loadTestExecutions();
            }, 2000);

        } catch (error) {
            console.error('执行测试失败:', error);
            showMessage('执行测试失败: ' + error.message, 'error');
        } finally {
            runBtn.innerHTML = originalText;
            runBtn.disabled = false;
        }
    }

    // 加载测试执行结果
    async function loadTestExecutions() {
        try {
            const response = await fetch(`${API_BASE}/cases/executions?session_id=${encodeURIComponent(session_id)}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            executions = data.executions || [];

            renderExecutions();
            updateStats();
        } catch (error) {
            console.error('加载执行结果失败:', error);
            document.getElementById('executions-list').innerHTML =
                '<div class="error-message">加载执行结果失败: ' + error.message + '</div>';
        }
    }

    // 渲染执行结果
    function renderExecutions() {
        const container = document.getElementById('executions-list');

        if (executions.length === 0) {
            container.innerHTML = '<div style="text-align: center; padding: 40px; color: #718096;">暂无执行结果</div>';
            return;
        }

        container.innerHTML = executions.map(execution => {
            let statusClass, statusText;
            if (execution.status === 'queued') {
                statusClass = 'status-queued';
                statusText = '排队中';
            } else if (execution.status === 'running') {
                statusClass = 'status-running';
                statusText = '运行中';
            } else if (execution.failed) {
                statusClass = 'status-failed';
                statusText = '失败';
            } else {
                statusClass = 'status-success';
                statusText = '成功';
            }
            const startTime = new Date(execution.start_time).toLocaleString();
            const endTime = execution.end_time ? new Date(execution.end_time).toLocaleString() : '进行中';

            // 处理 session_url，提取路径的最后一截
            let sessionButton = '';
            if (execution.session_url) {
                const urlPath = execution.session_url.split('/').pop() || execution.session_url;
                sessionButton = `
                    <div class="execution-session">
                        <a href="${execution.session_url}" target="_blank" class="session-link">
                            session: ${urlPath}
                        </a>
                    </div>
                `;
            }

            return `
                    <div class="execution-item">
                        <div class="execution-header">
                            <div class="execution-name">${execution.name}</div>
                            <div class="execution-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="execution-time">
                            开始时间: ${startTime}<br>
                            结束时间: ${endTime}
                        </div>
                        ${sessionButton}
                        ${execution.output ? `
                            <div class="execution-output">${execution.output}</div>
                        ` : ''}
                    </div>
                `;
        }).join('');
    }

    // 更新统计信息
    function updateStats() {
        document.getElementById('total-cases').textContent = testCases.length;
        document.getElementById('total-executions').textContent = executions.length;

        if (executions.length > 0) {
            const successCount = executions.filter(e => !e.failed).length;
            const successRate = Math.round((successCount / executions.length) * 100);
            document.getElementById('success-rate').textContent = successRate + '%';
        } else {
            document.getElementById('success-rate').textContent = '-';
        }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        loadTestCases();
        loadTestExecutions();
    });
</script>
</body>
</html>