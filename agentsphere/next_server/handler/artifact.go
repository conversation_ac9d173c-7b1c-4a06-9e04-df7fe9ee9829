package serverhandler

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime"
	"net/http"
	"net/url"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/AlekSi/pointer"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/pkg/diff/ctxt"
	"github.com/pkg/diff/myers"
	"github.com/pkg/diff/write"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/iter"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	artifactService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/redis"
)

const artifactAttachmentExport = "_artifact_attachment_export"

var banFileDirs = []string{"node_modules", "venv", ".venv", ".mypy_cache", ".pytest_cache", ".gradle", ".idea", ".vscode", "__pycache__", ".next", "dist", "build", ".git"}

func (h *Handler) CreateArtifact(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateArtifactRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create artifact request: %+v", req)
	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "CreateArtifact")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	metadata, err := entity.UnmarshalMetadata(entity.ArtifactType(req.Type), []byte(req.Metadata))
	if err != nil {
		log.V1.CtxError(ctx, "failed to unmarshal metadata: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "failed to unmarshal metadata")
		return
	}

	source := entity.ArtifactSourceUser
	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		source = entity.ArtifactSourceAgent
	}

	// 校验session权限
	if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
		Account:   user,
		SessionID: req.GetSessionID(),
		Action:    entity.PermissionActionSessionUpdate,
	}) {
		return
	}

	var fileMetas entity.FileMetas
	if req.FileMetas != nil {
		if err = json.Unmarshal([]byte(*req.FileMetas), &fileMetas); err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal file metas: %v", err)
		}
	}

	artifact, err := h.ArtifactService.CreateArtifact(ctx, artifactService.CreateArtifactOption{
		SessionID: req.SessionID,
		Type:      entity.ArtifactType(req.Type),
		Source:    source,
		Key:       req.Key,
		Metadata:  metadata,
		FileMetas: fileMetas,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to create artifact: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create artifact")
		return
	}
	c.JSON(http.StatusOK, nextagent.CreateArtifactResponse{
		Artifact: h.GetArtifactEntity(artifact, ""),
	})
}

func (h *Handler) ListArtifacts(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListArtifactsRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list artifacts request: %+v", util.MarshalStruct(req))

	var (
		artifacts []*entity.Artifact
		err       error
	)
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "ListArtifacts")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	// 如果请求参数中没有 session_id 和 replay_id，返回错误
	if req.SessionID == nil && req.ReplayID == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "session_id or replay_id is required")
		return
	}

	// 如果有replay_id，优先用replay_id取artifacts
	if req.ReplayID != nil {
		artifacts, err = h.ArtifactService.ListReplayArtifacts(ctx, artifactService.ListReplayArtifactsOption{
			ReplayID: req.GetReplayID(),
		})
	} else {
		user, _ := h.AuthM.GetAccount(ctx, c)
		// artifact 权限校验看其所属session，如果是replay不校验
		if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
			Account:   user,
			SessionID: req.GetSessionID(),
			Action:    entity.PermissionActionSessionAllFile,
		}) {
			return
		}

		artifacts, err = h.ArtifactService.ListSessionArtifacts(ctx, artifactService.ListSessionArtifactsOption{
			SessionID: req.GetSessionID(),
		})
	}

	if req.GetDisplay() {
		filterArtifacts := []*entity.Artifact{}
		repeatMap := map[string]bool{}
		for _, artifact := range artifacts {
			if artifact.Display {
				for _, filemeta := range artifact.FileMetas {
					// TODO: use md5 instead of file key
					fileKey := fmt.Sprintf("%s-%d", filepath.Base(filemeta.Name), filemeta.Size)
					repeatMap[fileKey] = true
				}
			}
		}
		for _, artifact := range artifacts {
			// 如果 display 为 true，则需要返回
			if artifact.Display {
				// 对 display=true 的 artifact 也进行文件过滤
				filteredFileMetas := entity.FileMetas{}
				for _, filemeta := range artifact.FileMetas {
					if !entity.ShouldFilterFile(filemeta.Name) {
						filteredFileMetas = append(filteredFileMetas, filemeta)
					}
				}
				// 创建一个新的 artifact 副本，避免修改原始数据
				filteredArtifact := *artifact
				filteredArtifact.FileMetas = filteredFileMetas
				filterArtifacts = append(filterArtifacts, &filteredArtifact)
				continue
			}
			// 如果是 project artifact，直接返回
			if artifact.Type == entity.ArtifactTypeProject {
				filterArtifacts = append(filterArtifacts, artifact)
				continue
			}

			// 其他类型不返回，key为baseline的也不返回（用来做diff的）
			if artifact.Type != entity.ArtifactTypeFile || artifact.Key == entity.ArtifactKeyFileBaseline {
				continue
			}

			// 如果 display 为 false, 筛选 image + file 类型的文件返回
			filemetas := entity.FileMetas{}
			for _, filemeta := range artifact.FileMetas {
				// 如果包含禁止不展示的目录，则忽略不展示
				dirList := strings.Split(filepath.Dir(filemeta.Name), "/")
				if len(dirList) > 0 {
					if lo.Contains(banFileDirs, dirList[0]) {
						continue
					}
				}
				if entity.ShouldFilterFile(filemeta.Name) {
					continue
				}
				fileKey := fmt.Sprintf("%s-%d", filepath.Base(filemeta.Name), filemeta.Size)
				// 如果重复则忽略
				if repeatMap[fileKey] {
					continue
				}
				if filemeta.Type == entity.ArtifactTypeFile && artifactService.IsDocumentFile(filemeta.Name, []byte{}) {
					filemetas = append(filemetas, entity.FileMeta{
						Name:      filemeta.Name,
						Size:      filemeta.Size,
						Content:   filemeta.Content,
						LarkToken: filemeta.LarkToken,
						ImageXURI: filemeta.ImageXURI,
						Type:      filemeta.Type,
						SubType:   filemeta.SubType,
					})
					repeatMap[fileKey] = true
					continue
				}
			}
			artifact.FileMetas = filemetas
			if len(artifact.FileMetas) != 0 {
				filterArtifacts = append(filterArtifacts, artifact)
			}
		}
		artifacts = filterArtifacts
	}

	if err != nil {
		log.V1.CtxError(ctx, "failed to list artifacts: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list artifacts")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListArtifactsResponse{
		Artifacts: MergeArtifactsByKey(lo.Map(artifacts, func(artifact *entity.Artifact, _ int) *nextagent.Artifact {
			return h.GetArtifactEntity(artifact, "")
		})),
	})
}

// MergeArtifactsByKey 合并相同 key 的 artifacts, 保留最新的版本, 其余的作为历史版本
func MergeArtifactsByKey(artifacts []*nextagent.Artifact) []*nextagent.Artifact {
	// 1. 按 key 进行分组
	keyMap := make(map[string][]*nextagent.Artifact)
	for _, art := range artifacts {
		keyMap[art.Key] = append(keyMap[art.Key], art)
	}
	var result []*nextagent.Artifact
	// 2. 对每组 artifacts 按 version 倒序排序
	for _, group := range keyMap {
		sort.Slice(group, func(i, j int) bool {
			return group[i].Version > group[j].Version
		})
		// 3. 最新的 artifact 作为主 artifact
		mainArtifact := group[0]
		// 4. 其余的作为历史版本
		var history []*nextagent.HistoryVersionArtifact
		for _, his := range group[1:] {
			history = append(history, &nextagent.HistoryVersionArtifact{
				ID:        his.ID,
				Version:   his.Version,
				FileMetas: his.FileMetas,
				Metadata:  his.Metadata,
			})
		}
		mainArtifact.HistoryVersionArtifacts = history
		result = append(result, mainArtifact)
	}
	return result
}

func (h *Handler) GetArtifact(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetArtifactRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get artifact request: %+v", req)
	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "GetArtifact")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	artifact, err := h.ArtifactService.GetArtifact(ctx, artifactService.GetArtifactOption{
		ID:   req.ID,
		Sync: true,
	})

	if err != nil {
		log.V1.CtxError(ctx, "failed to get artifact: %v", err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "artifact not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get artifact")
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	// artifact 权限校验看其所属session
	if artifact.SessionID != "unknown" {
		if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
			Account:   user,
			SessionID: artifact.SessionID,
			Action:    entity.PermissionActionSessionRead,
		}) {
			return
		}
	}

	allVersionArtifacts := []*entity.Artifact{artifact}
	if artifact.Type == nextagent.ArtifactTypeProject {
		// get history project artifact with same artifact key
		allVersionArtifacts, err = h.ArtifactService.ListSessionArtifacts(ctx, artifactService.ListSessionArtifactsOption{
			SessionID:   artifact.SessionID,
			ArtifactKey: &artifact.Key,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to list history artifact with some key: %v", err)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session artifact not found")
				return
			}
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session artifact")
			return
		}
		allVersionArtifacts = lo.Filter(allVersionArtifacts, func(item *entity.Artifact, _ int) bool {
			return item.Version <= artifact.Version
		})
	}

	mergeArtifactsByKey := MergeArtifactsByKey(lo.Map(allVersionArtifacts, func(artifact *entity.Artifact, _ int) *nextagent.Artifact {
		return h.GetArtifactEntity(artifact, "")
	}))
	if len(mergeArtifactsByKey) == 0 {
		log.V1.CtxError(ctx, "failed to merge artifact list history with some key: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to merge artifact")
		return
	}

	c.JSON(http.StatusOK, nextagent.GetArtifactResponse{
		Artifact: mergeArtifactsByKey[0],
	})
}

func (h *Handler) UpdateArtifact(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateArtifactRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "update artifact request: %+v", util.ToJson(req))

	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "UpdateArtifact")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	var metadata map[string]any
	if req.Metadata != nil && len(*req.Metadata) > 0 {
		_ = json.Unmarshal([]byte(*req.Metadata), &metadata)
	}
	var (
		session *entity.Session
	)
	if req.GetSessionID() != "" {
		session, err = h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
			SessionID: req.GetSessionID(),
			Sync:      true,
		})
		if err != nil {
			if errors.Is(err, serverservice.ErrSessionNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
				return
			}
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session")
			return
		}
	}

	log.V1.CtxInfo(ctx, "update artifact, id: %s, status: %v, metadata: %v", req.ID, req.Status, req.Metadata)

	user, _ := h.AuthM.GetAccount(ctx, c)
	if session != nil && user != nil && user.Username != session.Creator { // 会话创建者不走权限校验，可能有主从问题导致误判为无权限
		// artifact 权限校验看其所属session
		if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
			Account:   user,
			SessionID: req.GetSessionID(),
			Action:    entity.PermissionActionSessionUpdate,
		}) {
			return
		}
	}

	artifact, err := h.ArtifactService.UpdateArtifact(ctx, artifactService.UpdateArtifactOption{
		ID:        req.ID,
		Status:    lo.TernaryF(req.Status != nil, func() *entity.ArtifactStatus { return lo.ToPtr(entity.ArtifactStatus(*req.Status)) }, func() *entity.ArtifactStatus { return nil }),
		Metadata:  lo.Ternary(req.Metadata != nil && len(*req.Metadata) > 0, metadata, nil),
		SessionID: req.SessionID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update artifact: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update artifact")
		return
	}
	c.JSON(http.StatusOK, nextagent.UpdateArtifactResponse{
		Artifact: h.GetArtifactEntity(artifact, ""),
	})
}

func (h *Handler) ExportDownloadArtifact(ctx context.Context, c *app.RequestContext) {
	c.Set(artifactAttachmentExport, artifactAttachmentExport)
}

func (h *Handler) DownloadArtifact(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DownloadArtifactFileRequest](ctx, c)
	if req == nil {
		return
	}
	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "DownloadArtifact")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	account, _ := h.AuthM.GetAccount(ctx, c)

	artifact, err := h.ArtifactService.GetArtifact(ctx, artifactService.GetArtifactOption{
		ID: req.ID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get artifact %s: %v", req.ID, err)
		hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
		return
	}

	// artifact 权限校验看其所属session
	if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
		Account:      account,
		SessionID:    artifact.SessionID,
		Action:       entity.PermissionActionSessionDownload,
		PassIfShared: true,
	}) {
		return
	}

	fileMetasMap := lo.SliceToMap(artifact.FileMetas, func(item entity.FileMeta) (string, entity.FileMeta) {
		return item.Name, item
	})

	splits := strings.Split(string(c.Request.URI().Path()), fmt.Sprintf("%s/raw/", req.ID))
	if len(splits) <= 1 {
		splits = strings.Split(string(c.Request.URI().Path()), fmt.Sprintf("%s/export/", req.ID))
		if len(splits) <= 1 {
			log.V1.CtxError(ctx, "failed to download artifact: invalid path: %s", string(c.Request.URI().Path()))
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
			return
		}
	}
	reqPath, err := url.PathUnescape(splits[len(splits)-1])
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid path parameter")
		return
	}
	if _, ok := fileMetasMap[reqPath]; !ok && artifact.Type != entity.ArtifactTypeProject {
		log.V1.CtxError(ctx, "requested file not found for %s: %s", req.ID, reqPath)
		hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
		return
	}

	tosCtx, cancel := context.WithTimeout(ctx, time.Minute*5)
	reader, fileMeta, err := h.ArtifactService.GetArtifactFile(tosCtx, artifactService.GetArtifactFileOption{
		Artifact: *artifact,
		Path:     reqPath,
		Account:  account,
	})
	if err != nil {
		cancel()
		log.V1.CtxError(ctx, "failed to download artifact: %v", err)
		if isContainerErr := handleContainerErr(c, err); isContainerErr {
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to download artifact")
		return
	}

	var newReader io.Reader
	newReader = reader
	if !req.GetRaw() && entity.IsMarkdownFile(reqPath) {
		referenceMap, err := h.ArtifactService.GetReferenceMap(ctx, artifact.SessionID)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get artifact reference: %v", err)
		}

		newReader, _, err = h.ArtifactService.ReplaceCiteTagsByLine(reader, fileMeta, referenceMap)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to replace artifact: %v", err)
			// 关闭旧reader，重新获取一次artifact reader兜底
			reader.Close()
			// 复用了tosCtx，超时时间可能会比较短了
			reader, _, err := h.ArtifactService.GetArtifactFile(tosCtx, artifactService.GetArtifactFileOption{
				Artifact: *artifact,
				Path:     reqPath,
				Account:  account,
			})
			if err != nil {
				cancel()
				log.V1.CtxError(ctx, "failed to get artifact: %v", err)
				hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to download artifact")
				return
			}
			newReader = reader
		}
	}

	fileName := reqPath
	if artifact.Type == entity.ArtifactTypeProject && fileMeta != nil {
		// 项目产物涉及文件夹下载，下载的name 与 req.path 并不一致
		fileName = fileMeta.Name
	}

	// If artifact is exported, set the Content-Disposition header to attachment.
	value, ok := c.Get(artifactAttachmentExport)
	if ok && value == artifactAttachmentExport {
		c.Response.Header.Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s; filename*=UTF-8''%s", url.QueryEscape(fileName), url.QueryEscape(fileName)))
		c.Response.Header.Set(http.CanonicalHeaderKey("X-Aime-Filename"), url.QueryEscape(fileName))
	} else if req.GetStream() {
		c.Response.Header.Set(http.CanonicalHeaderKey("Content-Type"), "application/octet-stream")
		c.Response.Header.Set(http.CanonicalHeaderKey("Content-Disposition"), fmt.Sprintf("attachment; filename=%s", url.QueryEscape(fileName)))
		c.Response.Header.Set(http.CanonicalHeaderKey("X-Aime-Filename"), url.QueryEscape(fileName))
	} else {
		mimetype := mime.TypeByExtension(filepath.Ext(fileName))
		c.Response.Header.Set("Content-Type", mimetype)
		c.Response.Header.Set(http.CanonicalHeaderKey("X-Aime-Filename"), url.QueryEscape(fileName))
	}
	c.SetBodyStream(artifactService.NewReaderWithCancel(newReader, cancel), -1)
}

func (h *Handler) RetrieveArtifactFiles(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.RetrieveArtifactFilesRequest](ctx, c)
	if req == nil {
		return
	}
	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "RetrieveArtifactFiles")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	log.V1.CtxInfo(ctx, "retrieve artifact files, id: %s, files: %v", req.ID, req.Files)
	artifact, err := h.ArtifactService.GetArtifact(ctx, artifactService.GetArtifactOption{
		ID:   req.ID,
		Sync: req.GetPreview(),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get artifact: %v", err)
		hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "artifact not found")
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	// artifact 权限校验看其所属session
	if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
		Account:      user,
		SessionID:    artifact.SessionID,
		Action:       entity.PermissionActionSessionRead,
		PassIfShared: true,
	}) {
		return
	}

	var files []*nextagent.FileMeta
	mapper := iter.Mapper[string, *nextagent.FileMeta]{
		MaxGoroutines: 4,
	}
	files, err = mapper.MapErr(req.Files, func(path *string) (*nextagent.FileMeta, error) {
		file, meta, err := h.ArtifactService.GetArtifactFileContent(ctx, artifactService.GetArtifactFileOption{
			Artifact: *artifact,
			Path:     *path,
			Account:  user,
		})
		if err != nil {
			return nil, err
		}
		encoded := base64.StdEncoding.EncodeToString(file)

		fileMeta := &nextagent.FileMeta{
			Name:      *path,
			Size:      meta.Size,
			Content:   encoded,
			LarkToken: meta.LarkToken,
			ImageXURI: meta.ImageURL(h.ImagexConf.GetValue().DefaultDomain, h.ImagexConf.GetValue().DefaultTemplateID),
			Type:      nextagent.ArtifactType(meta.Type),
			SubType:   meta.SubType,
		}

		// 增加patch
		if meta.Type == entity.ArtifactTypeCode && meta.SubType != "" && meta.MD5 != "" {
			patch, err := h.getArtifactFilePatch(ctx, artifact, meta, string(file))
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to get artifact file patch: %v", err)
			}
			if patch != "" {
				fileMeta.PatchFileResult = []*nextagent.FilePatch{
					{
						Patch:    patch,
						FilePath: *path,
					},
				}
			}
		}

		return fileMeta, nil
	})

	if err != nil {
		log.V1.CtxError(ctx, "failed to get artifact files: %v", err)
		if isContainerErr := handleContainerErr(c, err); isContainerErr {
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get artifact files")
		return
	}
	c.JSON(http.StatusOK, nextagent.RetrieveArtifactFilesResponse{
		Files: files,
	})
}

func (h *Handler) getArtifactFilePatch(ctx context.Context, artifact *entity.Artifact, meta *entity.FileMeta, content string) (string, error) {
	artifactBaseline, err := h.ArtifactService.GetArtifactByKey(ctx, artifactService.GetArtifactByKeyOption{
		SessionID:   artifact.SessionID,
		ArtifactKey: entity.ArtifactKeyFileBaseline,
		Sync:        false,
	})
	// 没有file-baseline是可能的，历史数据&无文件修改的场景
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", nil
		}
		return "", err
	}
	// 计算patch
	if artifactBaseline != nil {
		baseMeta, ok := lo.Find(artifactBaseline.FileMetas, func(item entity.FileMeta) bool {
			return item.Name == meta.Name
		})

		if !ok {
			return "", nil
		}

		if meta.MD5 != "" && meta.MD5 != baseMeta.MD5 {
			baseFile, _, err := h.ArtifactService.GetArtifactFileContent(ctx, artifactService.GetArtifactFileOption{
				Artifact: *artifactBaseline,
				Path:     meta.Name,
			})
			if err != nil {
				return "", err
			}

			as := strings.Split(string(baseFile), "\n")
			bs := strings.Split(content, "\n")
			ab := &diffStrings{a: as, b: bs}
			s := myers.Diff(ctx, ab)
			s = ctxt.Size(s, 50)
			buf := new(bytes.Buffer)
			err = write.Unified(s, buf, ab, write.Names("a/"+meta.Name, "b/"+meta.Name))
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to get artifact file patch: %v", err)
				return "", err
			}
			return buf.String(), nil
		}
	}
	return "", nil
}

func (h *Handler) UploadArtifact(ctx context.Context, c *app.RequestContext) {
	// 不走 hertz 的 bind tag 绑定 form body 中的内容 https://bytedance.larkoffice.com/wiki/Dw4Iw20AXihbkMkIrBfcsQMVnPb#share-YBMFdBBwRoo22FxGopncK7NrnNe
	req := nextagent.UploadArtifactRequest{}
	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "UploadArtifact")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()

	err = c.Bind(&req)
	log.V1.CtxInfo(ctx, "upload artifact request: %v", util.ToJson(req))
	id := req.ID
	user, _ := h.AuthM.GetAccount(ctx, c)
	artifact, err := h.ArtifactService.GetArtifact(ctx, artifactService.GetArtifactOption{
		ID:   req.ID,
		Sync: true, // 由于会在CreateArtifacts后立刻UploadArtifact，这里需要读主
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get artifact: %v, id: %s, path: %s", err, id, req.Path)
		if db.IsRecordNotFoundError(err) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "artifact not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get artifact")
		return
	}
	// artifact 权限校验
	if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
		Account:   user,
		SessionID: artifact.SessionID,
		Action:    entity.PermissionActionSessionUpdate,
	}) {
		return
	}

	path := string(c.FormValue("path"))
	if !utf8.Valid([]byte(path)) {
		log.V1.CtxError(ctx, "path contains invalid UTF-8 characters: %s", path)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "path contains invalid UTF-8 characters")
		return
	}
	file, _ := c.FormFile("content")
	if id == "" || path == "" || file == nil {
		log.V1.CtxError(ctx, "failed to upload artifact(%s: %s): incomplete upload parameters", id, path)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "incomplete upload parameters")
		return
	}
	content, err := file.Open()
	if err != nil {
		log.V1.CtxError(ctx, "failed to read file body: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "failed to read file body")
		return
	}
	defer content.Close()

	log.V1.CtxInfo(ctx, "upload artifact, id: %s, path: %s, size: %d", id, path, file.Size)

	artifact, err = h.ArtifactService.UploadArtifact(ctx, artifactService.UploadArtifactOption{
		// ID:          id,
		Artifact:    artifact,
		Path:        path,
		Content:     content,
		ContentSize: file.Size,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to upload artifact(%s: %s): %v", id, path, err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload artifact")
		return
	}

	c.JSON(http.StatusOK, nextagent.UploadArtifactResponse{
		Artifact: h.GetArtifactEntity(artifact, ""),
	})
}

func (h *Handler) UploadArtifactStream(ctx context.Context, c *app.RequestContext) {
	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "UploadArtifactStream")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	id := c.Param("artifact_id")
	path := c.Query("path")
	fileSize, err := strconv.ParseInt(c.Query("size"), 10, 64)
	log.V1.CtxInfo(
		ctx,
		"upload artifact stream request, id: %s, path: %s, size: %d, content-length: %s, content-type: %s, transfer-encoding: %s",
		id, path, fileSize,
		string(c.GetHeader("Content-Length")),
		string(c.GetHeader("Content-Type")),
		string(c.GetHeader("Transfer-Encoding")),
	)
	if err != nil {
		log.V1.CtxError(ctx, "failed to upload artifact(%s: %s): invalid size parameter '%s'", id, path, c.Query("size"))
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid size parameter")
		return
	}
	if id == "" || path == "" {
		log.V1.CtxError(ctx, "failed to upload artifact(%s: %s): incomplete upload parameters", id, path)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "incomplete upload parameters")
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	artifact, err := h.ArtifactService.GetArtifact(ctx, artifactService.GetArtifactOption{
		ID:   id,
		Sync: true, // 由于会在CreateArtifacts后立刻UploadArtifact，这里需要读主
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get artifact: %v, id: %s, path: %s", err, id, path)
		if db.IsRecordNotFoundError(err) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "artifact not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get artifact")
		return
	}
	// artifact 权限校验看其所属session
	if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
		Account:   user,
		SessionID: artifact.SessionID,
		Action:    entity.PermissionActionSessionUpdate,
	}) {
		return
	}

	file := c.RequestBodyStream()

	log.V1.CtxInfo(ctx, "upload artifact, id: %s, path: %s, size: %d", id, path, fileSize)
	artifact, err = h.ArtifactService.UploadArtifact(ctx, artifactService.UploadArtifactOption{
		// ID:          id,
		Artifact:    artifact,
		Path:        path,
		Content:     io.NopCloser(file),
		ContentSize: fileSize,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to upload artifact(%s: %s): %v", id, path, err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload artifact")
		return
	}

	c.JSON(http.StatusOK, nextagent.UploadArtifactResponse{
		Artifact: h.GetArtifactEntity(artifact, ""),
	})
}

// UpdateArtifactFile 转飞书文档
func (h *Handler) UpdateArtifactFile(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateArtifactFileRequest](ctx, c)
	if req == nil {
		return
	}

	var err error
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "UpdateArtifactFile")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()

	log.V1.CtxInfo(ctx, "update artifact file, id: %s, path: %s, upload lark: %v, grant permission: %v", req.ID, req.Path, req.GetUploadLark(), req.GetGrantPermission())
	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrCheckAuth), "no auth info")
		return
	}

	artifact, err := h.ArtifactService.GetArtifact(ctx, artifactService.GetArtifactOption{
		ID: req.ID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "artifact not found")
			return
		}
		log.V1.CtxError(ctx, "failed to get artifact file meta: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get artifact meta")
		return
	}

	// artifact 权限校验
	if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
		Account:   user,
		SessionID: artifact.SessionID,
		Action:    entity.PermissionActionSessionUpdate,
	}) {
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: artifact.SessionID,
		Sync:      false,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return
		}
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}
	// 只允许 session 的 owner 做更新 artifact file 的操作
	if session.Creator != user.Username {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "user has not permission")
		return
	}

	splits := strings.Split(string(c.Request.URI().Path()), fmt.Sprintf("%s/files/", req.ID))
	if len(splits) <= 1 {
		log.V1.CtxError(ctx, "failed to download artifact: invalid path: %s", string(c.Request.URI().Path()))
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrRecordNotFound), "resource not found")
	}
	reqPath, err := url.PathUnescape(splits[len(splits)-1])
	if err != nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid path parameter")
		return
	}
	var fileMeta *entity.FileMeta
	for _, file := range artifact.FileMetas {
		if file.Name == reqPath {
			fileMeta = &file
			break
		}
	}
	if fileMeta == nil {
		hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "artifact file meta not found")
		return
	}

	var larkToken string
	larkToken = fileMeta.LarkToken

	// 没有上传过飞书文档
	if req.GetUploadLark() && larkToken == "" {
		artifactFile, meta, err := h.ArtifactService.GetArtifactFile(ctx, artifactService.GetArtifactFileOption{
			Artifact: entity.Artifact{
				ID: req.ID,
			},
			Path:    req.Path,
			Account: user,
		})
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "old artifact file not found")
				return
			}
			log.V1.CtxError(ctx, "failed to get old artifact file: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get old artifact file")
			return
		}

		// 飞书限制上传文件大小为 ********
		if meta.Size > ******** {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrContentTooLong), "file size too large")
			return
		}
		var (
			newReader io.Reader
			size      int
		)
		if entity.IsMarkdownFile(fileMeta.Name) {
			referenceMap, err := h.ArtifactService.GetReferenceMap(ctx, artifact.SessionID)
			if err != nil {
				log.V1.CtxError(ctx, "failed to get artifact reference: %v", err)
			}
			// 关闭旧reader，重新获取一次artifact reader兜底
			newReader, size, err = h.ArtifactService.ReplaceCiteTagsByLine(artifactFile, meta, referenceMap)
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to replace artifact: %v", err)
				//
				artifactFile.Close()
				artifactFile, meta, err = h.ArtifactService.GetArtifactFile(ctx, artifactService.GetArtifactFileOption{
					Artifact: entity.Artifact{
						ID: req.ID,
					},
					Path:    req.Path,
					Account: user,
				})
				if err != nil {
					hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get old artifact file")
					return
				}
				newReader = artifactFile
				size = int(meta.Size)
			}
		} else {
			newReader = artifactFile
			size = int(meta.Size)
		}

		// https://bytedance.larkoffice.com/drive/folder/xxxx
		// 灵活生成目录
		folderToken, err := h.getOrCreateDateFolder(ctx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get or create date folder: %v", err)
			// fallback use folder token
			folderToken = h.NextAgentUploadLarkFileConfig.GetValue().FolderToken
		}

		larkTokenPointer, err := h.LarkClient.UploadLarkFile(ctx, req.Path, folderToken, size, newReader)
		if err != nil {
			log.V1.CtxError(ctx, "failed to upload artifact file: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload lark file")
			return
		}
		larkToken = pointer.Get(larkTokenPointer)

		artifact, err = h.ArtifactService.UpdateArtifactFile(ctx, artifactService.UpdateArtifactFileOption{
			ID:        req.ID,
			Name:      req.Path,
			LarkToken: larkToken,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to update artifact file: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update artifact file")
			return
		}
	}

	if req.GetGrantPermission() {
		if larkToken == "" {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid lark token to grant permission")
			return
		}
		// 文档类型为 file, 权限为管理权限
		err = h.LarkClient.AddLarkFilePermission(ctx, larkToken, user.Email, "full_access", "file")
		if err != nil {
			log.V1.CtxError(ctx, "failed to add lark file permission: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to add lark file permission")
			return
		}
	}

	c.JSON(http.StatusOK, nextagent.UpdateArtifactResponse{
		Artifact: h.GetArtifactEntity(artifact, req.Path),
	})
}

// getOrCreateDateFolder finds or creates a date-named folder within the "aime" folder
func (h *Handler) getOrCreateDateFolder(ctx context.Context) (string, error) {
	aimeFolderToken := h.NextAgentUploadLarkFileConfig.GetValue().FolderToken

	var dateFolderToken string
	var err error
	todayFolder := time.Now().Format("2006-01-02")
	dateFolderToken, err = h.RedisClient.Get(ctx, fmt.Sprintf("%s/%s", aimeFolderToken, todayFolder))
	if err != nil {
		if redis.IsNil(err) {
			// 创建 folder
			createResp, err := h.LarkClient.CreateFolder(ctx, todayFolder, aimeFolderToken)
			if err != nil {
				return "", fmt.Errorf("failed to create date folder: %v", err)
			}
			dateFolderToken = *createResp.Token
			log.V1.CtxInfo(ctx, "Created new date folder: %s with token: %s", todayFolder, dateFolderToken)
			err = h.RedisClient.Set(ctx, fmt.Sprintf("%s/%s", aimeFolderToken, todayFolder), dateFolderToken, 48*time.Hour)
			if err != nil {
				log.V1.CtxError(ctx, "failed to set hour folder: %v", err)
			}
		} else {
			return "", fmt.Errorf("failed to get date folder: %v", err)
		}
	}

	var hourFolderToken string
	hourFolder := time.Now().Format("15")
	hourFolderToken, err = h.RedisClient.Get(ctx, fmt.Sprintf("%s/%s/%s", aimeFolderToken, todayFolder, hourFolder))
	if err != nil {
		if redis.IsNil(err) {
			createResp, err := h.LarkClient.CreateFolder(ctx, hourFolder, dateFolderToken)
			if err != nil {
				log.V1.CtxError(ctx, "Failed to create hour folder: %v", err)
				// Return date folder token if hour folder creation fails
				return dateFolderToken, nil
			}
			hourFolderToken = *createResp.Token
			log.V1.CtxInfo(ctx, "Created new hour folder: %s with token: %s", hourFolder, hourFolderToken)
			err = h.RedisClient.Set(ctx, fmt.Sprintf("%s/%s/%s", aimeFolderToken, todayFolder, hourFolder), hourFolderToken, 48*time.Hour)
			if err != nil {
				log.V1.CtxError(ctx, "failed to set hour folder: %v", err)
			}
		} else {
			log.V1.CtxError(ctx, "failed to get hour folder: %v", err)
			return dateFolderToken, nil
		}
	}

	return hourFolderToken, nil

}

func (h *Handler) GetArtifactEntity(a *entity.Artifact, fileName string) *nextagent.Artifact {
	return &nextagent.Artifact{
		ID:      a.ID,
		Type:    nextagent.ArtifactType(a.Type),
		Status:  nextagent.ArtifactStatus(a.Status),
		Source:  nextagent.ArtifactSource(a.Source),
		Key:     a.Key,
		Version: a.Version,
		FileMetas: lo.FilterMap(a.FileMetas, func(item entity.FileMeta, index int) (*nextagent.FileMeta, bool) {
			if fileName != "" && item.Name != fileName { // 过滤掉非指定文件名的文件
				return nil, false
			}
			content := strings.ReplaceAll(item.Content, "neuma-boe.bytedance.net", "aime-boe-app.bytedance.net")
			content = strings.ReplaceAll(content, "neuma-app.bytedance.net", "aime-app.bytedance.net")
			content = strings.ReplaceAll(content, "devagent-boe-app.bytedance.net", "aime-boe-app.bytedance.net")
			content = strings.ReplaceAll(content, "devagent-app.bytedance.net", "aime-app.bytedance.net")
			var linkContent *nextagent.LinkContent
			if item.Type == entity.ArtifactTypeLink {
				_ = json.Unmarshal([]byte(content), &linkContent)
			}
			return &nextagent.FileMeta{
				Name:      item.Name,
				Size:      item.Size,
				Content:   content,
				LarkToken: item.LarkToken,
				ImageXURI: item.ImageURL(h.ImagexConf.GetValue().DefaultDomain, h.ImagexConf.GetValue().DefaultTemplateID),
				ImageXResizeURL: lo.ToPtr(item.ResizeImageURL(h.ImagexConf.GetValue().DefaultDomain, h.ImagexConf.GetValue().ResizeTemplateID,
					h.ImagexConf.GetValue().ResizeWidth, h.ImagexConf.GetValue().ResizeHeight)),
				Type:        nextagent.ArtifactType(item.Type),
				SubType:     item.SubType,
				LinkContent: linkContent,
			}, true
		}),
		CreatedAt: a.CreatedAt.Format(time.RFC3339),
		Metadata:  conv.JSONString(a.Metadata),
	}
}

func (h *Handler) DownloadArtifactBatch(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DownloadArtifactBatchRequest](ctx, c)
	if req == nil {
		return
	}

	// Get artifacts based on session_id or replay_id
	// log.V1.CtxInfo(ctx, "download artifacts as zip request: %+v", req)
	var (
		session *entity.Session
		err     error
	)
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleArtifact, "DownloadArtifactBatch")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	if req.SessionID != nil && *req.SessionID != "" {
		session, err = h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
			SessionID: *req.SessionID,
			Sync:      false,
		})
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session record not found")
				return
			}
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to download: get session failed")
			return
		}

		h.AuthM.RequireAccount()(ctx, c)
		account, _ := h.AuthM.GetAccount(ctx, c)
		if account == nil {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to download: access reject")
			return
		}
		// if session.Creator != account.Username && !h.UserService.IsDeveloper(account) {
		// 	hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to download: access reject")
		// 	return
		// }

		// 校验是否有下载artifact的权限
		if !h.checkArtifactPermission(ctx, c, ArtifactPermissionCheckOption{
			Account:   account,
			SessionID: session.ID,
			Action:    entity.PermissionActionSessionDownload,
		}) {
			return
		}
	} else if req.ReplayID != nil && *req.ReplayID != "" {
		// 检查replay_id是否存在
		session, err = h.SessionService.GetSessionByReplay(ctx, *req.ReplayID)
		if err != nil {
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to download: failed to get replay")
			return
		}
	} else {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "session_id or replay_id is required")
		return
	}

	if err != nil {
		log.V1.CtxError(ctx, "failed to list artifacts: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list artifacts")
		return
	}

	// Extract artifact IDs and paths from the request
	artifactIDToPaths := make(map[string][]string)

	for _, artifactFiles := range req.Artifacts {
		if _, exists := artifactIDToPaths[artifactFiles.ID]; !exists {
			artifactIDToPaths[artifactFiles.ID] = make([]string, 0, len(artifactFiles.Paths))
		}
		artifactIDToPaths[artifactFiles.ID] = append(artifactIDToPaths[artifactFiles.ID], artifactFiles.Paths...)
	}

	// Generate a unique ZIP filename
	zipName := fmt.Sprintf("%s.zip", lo.Ternary(session.Title != "", session.Title, session.ID))
	// zipName := fmt.Sprintf("artifacts_%s.zip", time.Now().Format("**************"))

	// Download artifacts as ZIP
	zipReader, err := h.ArtifactService.BatchDownloadArtifactsAsZip(ctx, artifactService.BatchDownloadArtifactZipOption{
		SessionID:         session.ID,
		ArtifactIDToPaths: artifactIDToPaths,
		ZipName:           zipName,
		// Account:     account,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to download artifacts as ZIP: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to download artifacts as ZIP")
		return
	}
	defer zipReader.Close()

	// Set response headers for ZIP download
	c.Response.Header.Set("Content-Type", "application/zip")
	c.Response.Header.Set("X-Aime-Filename", url.QueryEscape(zipName))
	c.Response.Header.Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s; filename*=UTF-8''%s", url.QueryEscape(zipName), url.QueryEscape(zipName)))

	// Stream the ZIP file to the client
	c.SetBodyStream(zipReader, -1)
}

type ArtifactPermissionCheckOption struct {
	Account      *authentity.Account
	SessionID    string
	Action       entity.PermissionAction
	PassIfShared bool
}

func (h *Handler) checkArtifactPermission(ctx context.Context, c *app.RequestContext, opt ArtifactPermissionCheckOption) bool {
	// 如果是内部系统的artifact接口调用，跳过校验
	internalCall, exist := c.Get(internalCallKey)
	if exist && internalCall.(bool) {
		return true
	}

	if opt.SessionID == "unknown" && opt.Action == entity.PermissionActionSessionUpdate { // create和uploadartifact时可能传unknown，主要是用户传附件，此时允许执行
		return true
	}

	if opt.PassIfShared {
		shared, err := h.isSessionShared(ctx, opt.SessionID)
		if err != nil {
			log.V1.CtxError(ctx, "failed to check session shared: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check session shared")
			return false
		}
		if shared {
			return true
		}
	}

	// artifact权限校验和所属session一致
	_, allowed := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   opt.Account,
		SessionID: lo.ToPtr(opt.SessionID),
		Action:    opt.Action,
	})
	return allowed
}

func (h *Handler) UploadImageByURL(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UploadImageByURLRequest](ctx, c)
	if req == nil {
		return
	}

	// 上传图片到 ImageX 并创建产物
	artifacts, err := h.ArtifactService.UploadImageByURL(ctx, req.URLs, req.SessionID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to upload image by url: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload image by url")
		return
	}
	c.JSON(http.StatusOK, nextagent.UploadImageByURLResponse{
		Artifacts: lo.Map(artifacts, func(artifact *entity.Artifact, _ int) *nextagent.Artifact {
			return h.GetArtifactEntity(artifact, "")
		}),
	})
}
