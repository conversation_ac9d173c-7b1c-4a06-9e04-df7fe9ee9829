package serverhandler

import (
	"context"
	"net/http"
	"strconv"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	artifactService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	mcpservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mcp"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	templateservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/template"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/util"
)

func (h *Handler) CreateMessageWithTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateMessageWithTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	user, _ := h.AuthM.GetAccount(ctx, c)
	logID, _ := ctxvalues.LogID(ctx)
	_ = metrics.NSM.SessionWithTemplateRate.WithTags(&metrics.NextServerSessionTag{
		Method:      "CreateMessageWithTemplate",
		ErrorReason: "",
		Username:    user.Username,
		FromApp:     req.GetFromApp(), // 调用方
	}).Add(1)

	// 校验模板权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceID:         nil,
		ResourceExternalID: &req.TemplateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
		Action:             entity.PermissionActionTemplateRead,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		log.V1.CtxInfo(ctx, "permission denied to create message with template")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	template, err := h.TemplateService.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}
	// 获取模板MCPs
	// 剔除请求声明的MCPs
	excludeMCPMap := gslice.ToMap(req.ExcludedMCPs, func(t *nextagent.MCPKey) (entity.MCPKey, bool) {
		return entity.MCPKey{
			MCPID:  t.ID,
			Source: entity.MCPSource(t.Source),
		}, true
	})
	mcpKeys := gslice.Filter(template.SupportMCPs, func(key *entity.MCPKey) bool {
		return !excludeMCPMap[gptr.Indirect(key)]
	})
	// 实时从 DB 获取 MCPs
	mcps, err := h.MCPService.GetMCPsByKeys(ctx, &mcpservice.GetMCPsByKeysOption{MCPKeys: mcpKeys,
		UserScopedMCPQuery: &mcpservice.UserScopedMCPQuery{
			Username: gptr.Indirect(user).Username, SpaceID: gptr.Indirect(req.SpaceID),
		}})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get MCPs: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get MCPs")
		return
	}
	// 过滤限定角色的MCP
	mcps = entity.FilterMCPsBySessionRole(mcps, gptr.Indirect(req.Role))

	// create session
	session := h.createSession(ctx, c, user, createSessionOption{
		Role:            req.Role,
		UseInternalTool: req.UseInternalTool,
		MCPs:            mcps,
		TemplateID:      req.TemplateID,
		LogID:           logID,
		SpaceID:         req.SpaceID,
	})
	if session == nil {
		return
	}
	// save template variables
	var templateVariables map[string]*entity.TemplateVariableValue
	if req.FormValue != nil {
		formValue := getTemplateFormValueFromDto(ctx, req.FormValue, h.ArtifactService, session.ID)
		templateVariables = formValue.Variables
		_, err := h.TemplateService.CreateOrUpdateTemplateVariable(ctx, templateservice.CreateTemplateVariableOptions{
			TemplateID: req.TemplateID,
			Creator:    user.Username,
			Value:      formValue,
			SpaceID:    req.GetSpaceID(),
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to create template variable: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create template variable")
			return
		}
	}

	// create message
	var attachments []*nextagent.AttachmentRequired
	if req.FormValue != nil {
		for _, v := range req.FormValue.Variables {
			if v == nil || len(v.Attachments) == 0 {
				continue
			}
			attachments = append(attachments, v.Attachments...)
		}
	}

	message := h.createMessage(ctx, c, user, createMessageOption{
		SessionID:         session.ID,
		Content:           req.Content,
		ToolCalls:         nil,
		Attachments:       attachments,
		Options:           req.Options,
		EventOffset:       0,
		TemplateID:        req.TemplateID,
		TemplateVariables: templateVariables,
		SpaceID:           req.GetSpaceID(),
	})
	if message == nil {
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateMessageWithTemplateResponse{
		Message: getMessageFromEntity(message),
		Session: getSessionFromEntity(session, nil),
	})
}

func (h *Handler) ListTemplates(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListTemplatesRequest](ctx, c)
	if req == nil {
		return
	}
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "ListTemplates")
	var (
		err       error
		templates []*entity.TemplateVersion
		total     int64
	)
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()
	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	templates, total, err = h.TemplateService.ListTemplates(ctx, templateservice.ListTemplatesOptions{
		PageNum:  lo.FromPtr(req.PageNum),
		PageSize: lo.FromPtr(req.PageSize),
		Category: req.Category,
		Search:   req.Search,
		Source:   pack.ConvertTemplateSourceToEntity(req.Source),
		Username: user.Username,
		Label:    req.Label,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list templates: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list templates")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListTemplatesResponse{
		Templates: pack.ConvertTemplatesEntityToDTO(templates),
		Total:     total,
	})
}

func (h *Handler) CountTemplates(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CountTemplatesRequest](ctx, c)
	if req == nil {
		return
	}
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "CountTemplates")
	var (
		myTotal, starTotal, allTotal int64
		err                          error
	)
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()
	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	myTotal, err = h.TemplateService.CountTemplates(ctx, templateservice.CountTemplatesOptions{
		Source:   entity.TemplateSourceMy,
		Username: user.Username,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to count templates: %v", err)
	}
	starTotal, err = h.TemplateService.CountTemplates(ctx, templateservice.CountTemplatesOptions{
		Source:   entity.TemplateSourceStar,
		Username: user.Username,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to count templates: %v", err)
	}
	allTotal, err = h.TemplateService.CountTemplates(ctx, templateservice.CountTemplatesOptions{
		Source:   entity.TemplateSourceAll,
		Username: user.Username,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to count templates: %v", err)
	}

	c.JSON(http.StatusOK, nextagent.CountTemplatesResponse{
		MyTotal:   myTotal,
		StarTotal: starTotal,
		AllTotal:  allTotal,
	})
}

func (h *Handler) GetHistoryTemplateVariables(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetHistoryTemplateVariablesRequest](ctx, c)
	if req == nil {
		return
	}
	user, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 校验模板权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceID:         nil,
		ResourceExternalID: &req.TemplateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
		Action:             entity.PermissionActionTemplateRead,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		log.V1.CtxInfo(ctx, "permission denied to get history template variables")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	// get template variables from history
	variables, err := h.TemplateService.ListTemplateVariables(ctx, req.TemplateID, user.Username, req.GetSpaceID())
	if err != nil {
		log.V1.CtxError(ctx, "failed to list template variables: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list template variables")
		return
	}
	formValues := make([]*nextagent.TemplateFormValueDetail, 0)
	for _, v := range variables {
		if v == nil {
			continue
		}
		formValues = append(formValues, pack.ConvertTemplateFormValueDetailToDTO(v.Value))
	}

	c.JSON(http.StatusOK, nextagent.GetHistoryTemplateVariablesResponse{FormValue: formValues})
}

// GetTemplate 获取模板详情
func (h *Handler) GetTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get template request: %+v", req)

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 校验模板权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceID:         nil,
		ResourceExternalID: &req.TemplateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
		Action:             entity.PermissionActionTemplateRead,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		log.V1.CtxInfo(ctx, "permission denied to get template")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 获取模板详情
	template, err := h.TemplateService.GetTemplateWithShared(ctx, req.TemplateID, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}

	mcps, err := h.MCPService.GetMCPsByKeys(ctx, &mcpservice.GetMCPsByKeysOption{MCPKeys: template.SupportMCPs})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get MCPs: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get MCPs")
		return
	}
	template.MCPs = mcps
	star, err := h.TemplateService.IsTemplateStarred(ctx, template.TemplateID, user.Username, req.GetSpaceID())
	if err != nil {
		log.V1.CtxError(ctx, "failed to get is template started: %v", err)
	} else {
		template.Starred = &star
	}
	// TODO(dingbo): 从权限服务获取模板权限

	// 转换为API响应
	c.JSON(http.StatusOK, nextagent.GetTemplateResponse{
		Template:    pack.ConvertTemplateEntityToDTO(template, true),
		Permissions: pack.GetTemplatePermissionsDTO(user.Username, template),
	})
}

// CreateTemplateDraft 创建模板草稿
func (h *Handler) CreateTemplateDraft(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateTemplateDraftRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create template draft request: %s", util.ToJson(req))
	if req.TemplateKey == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template key is required")
		return
	}
	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 检查会话权限
	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: req.TemplateKey.SessionID,
		Sync:      false,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}
	if session.Creator != user.Username {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// TODO: 后面需要改成获取实际调用过的MCP
	chosenMCPs := session.Context.MCPs
	mcpKeys := lo.Map(chosenMCPs, func(mcp *entity.MCP, _ int) *entity.MCPKey {
		return &entity.MCPKey{
			MCPID:  mcp.MCPID,
			Source: mcp.Source,
		}
	})
	// 创建模板草稿
	template, err := h.TemplateService.CreateTemplateDraft(ctx, req.TemplateKey.SessionID, req.TemplateKey.LatestEventTimestamp, user.Username, mcpKeys, req.GetSpaceID())
	if err != nil {
		log.V1.CtxError(ctx, "failed to create template draft: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create template draft")
		return
	}
	if template.Name == "" {
		template.Name = session.Title
	}
	// 转换为API响应
	c.JSON(http.StatusOK, nextagent.CreateTemplateDraftResponse{
		Template: pack.ConvertTemplateEntityToDTO(template, true),
	})
}

// GetTemplateDraft 获取模板草稿
func (h *Handler) GetTemplateDraft(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetTemplateDraftRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get template draft request: %s", util.ToJson(req))

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// 获取模板草稿
	template, err := h.TemplateService.GetTemplateDraft(ctx, templateservice.GetTemplateDraftOption{
		TemplateID: req.GetTemplateID(),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template draft: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template draft not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template draft")
		return
	}

	// 检查用户权限
	if template.Creator != user.Username {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	mcps, err := h.MCPService.GetMCPsByKeys(ctx, &mcpservice.GetMCPsByKeysOption{MCPKeys: template.SupportMCPs})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get MCPs: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get MCPs")
		return
	}
	template.MCPs = mcps
	// 转换为API响应
	c.JSON(http.StatusOK, nextagent.GetTemplateDraftResponse{
		Template: pack.ConvertTemplateEntityToDTO(template, true),
	})
}

// CreateTemplate 创建模板
func (h *Handler) CreateTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create template request: %+v", util.ToJson(req))

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	// 创建模板
	template, err := h.TemplateService.CreateTemplate(ctx, templateservice.CreateTemplateOption{
		SessionID:       lo.FromPtr(req.SessionID),
		Username:        user.Username,
		FromTemplateID:  req.GetFromTemplateID(),
		DraftTemplateID: req.GetDraftTemplateID(),
		ModifyTemplate:  pack.ConvertModifyTemplateToEntity(req.Template),
		SpaceID:         req.GetSpaceID(),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to create template: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create template")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateTemplateResponse{
		Template:    pack.ConvertTemplateEntityToDTO(template, true),
		Permissions: pack.GetTemplatePermissionsDTO(user.Username, template),
	})
}

// UpdateTemplate 更新模板
func (h *Handler) UpdateTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "update template request: %s", util.ToJson(req))

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 校验模板权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceID:         nil,
		ResourceExternalID: &req.TemplateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
		Action:             entity.PermissionActionTemplateUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		log.V1.CtxInfo(ctx, "permission denied to update template")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 更新模板
	updateOpts := templateservice.UpdateTemplateOptions{
		TemplateID:             req.TemplateID,
		Username:               user.Username,
		ModifyTemplate:         pack.ConvertModifyTemplateToEntity(req.Template),
		NeedGenerateExperience: req.GetNeedGenerateExperience(),
	}
	if req.TemplateKey != nil {
		updateOpts.TemplateKey = &entity.TemplateKey{
			SessionID:            req.TemplateKey.SessionID,
			LatestEventTimestamp: req.TemplateKey.LatestEventTimestamp,
		}
	}
	template, err := h.TemplateService.UpdateTemplate(ctx, updateOpts)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update template: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update template")
		return
	}
	if template != nil {
		mcps, err := h.MCPService.GetMCPsByKeys(ctx, &mcpservice.GetMCPsByKeysOption{MCPKeys: template.SupportMCPs})
		if err != nil {
			log.V1.CtxError(ctx, "failed to get MCPs: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get MCPs")
			return
		}
		template.MCPs = mcps
	}
	c.JSON(http.StatusOK, nextagent.UpdateTemplateResponse{
		Template: pack.ConvertTemplateEntityToDTO(template, true),
	})
}

// DeleteTemplate 删除模板
func (h *Handler) DeleteTemplate(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteTemplateRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "delete template request: %+v", req)

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 校验模板权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceID:         nil,
		ResourceExternalID: &req.TemplateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
		Action:             entity.PermissionActionTemplateDelete,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		log.V1.CtxInfo(ctx, "permission denied to delete template")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 删除模板
	err = h.TemplateService.DeleteTemplate(ctx, req.TemplateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to delete template: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete template")
		return
	}

	c.JSON(http.StatusOK, nextagent.DeleteTemplateResponse{})
}

// UpdateTemplateExperience 更新模板经验
func (h *Handler) UpdateTemplateExperience(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateTemplateExperienceRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "update template exp sop request: %s", util.ToJson(req))
	var (
		err    error
		expSop *agententity.ExpSOP
	)
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleTemplate, "UpdateTemplateExperience")
	defer func() {
		reportMetricsFunc(err != nil, "")
	}()
	expSop, err = pack.ConvertExpSOPToEntity(req.ExpSOP)
	if err != nil {
		log.V1.CtxError(ctx, "failed to convert exp sop: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid exp sop")
		return
	}
	var changed *bool
	if req.ForceActive != nil && *req.ForceActive {
		changed = lo.ToPtr(false)
	}
	// 更新模板经验SOP
	err = h.TemplateService.UpdateTemplateExperienceSOP(ctx, templateservice.UpdateTemplateExperienceOption{
		TemplateID:       req.TemplateID,
		ProgressPlan:     lo.FromPtr(req.ProgressPlan),
		ExpSOP:           expSop,
		ExperienceStatus: pack.ConvertExperienceStatusToEntity(req.Status),
		ExperienceError:  req.GetError(),
		Expired:          changed,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update template exp sop: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update template exp sop")
		return
	}
	c.JSON(http.StatusOK, nextagent.UpdateTemplateExperienceResponse{})
}

// UploadTemplateExperienceFileStream 上传模板文件流
func (h *Handler) UploadTemplateExperienceFileStream(ctx context.Context, c *app.RequestContext) {
	templateID := c.Param("template_id")
	path := c.Query("path")
	size, err := strconv.ParseInt(c.Query("size"), 10, 64)
	log.V1.CtxInfo(
		ctx,
		"upload template file stream request, template id: %s, path: %s, size: %d, content-length: %s, content-type: %s, transfer-encoding: %s",
		templateID, path, size,
		string(c.GetHeader("Content-Length")),
		string(c.GetHeader("Content-Type")),
		string(c.GetHeader("Transfer-Encoding")),
	)
	if err != nil {
		log.V1.CtxError(ctx, "failed to parse size: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid size")
		return
	}

	// 获取模板信息
	template, err := h.TemplateService.GetTemplate(ctx, templateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}

	// 上传文件
	file, err := h.TemplateService.UploadAndSaveTemplateFile(ctx, template.TemplateID, path, c.Request.BodyStream(), size)
	if err != nil {
		log.V1.CtxError(ctx, "failed to upload template file: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload template file")
		return
	}

	c.JSON(http.StatusOK, nextagent.UploadTemplateExperienceFileStreamResponse{File: pack.ConvertTemplateFileToDTO(file)})
}

// DownloadTemplateExperienceFile 下载模板文件流
func (h *Handler) DownloadTemplateExperienceFile(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DownloadTemplateExperienceFileStreamRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "download template file stream request: %s", util.ToJson(req))

	// 如果是流式下载
	reader, _, err := h.TemplateService.GetAndDownloadTemplateFile(ctx, req.FileID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to download template file: %v", err)
		if errors.Is(err, serverservice.ErrTemplateFileNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "template file not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to download template file")
		return
	}

	// 设置响应头
	c.Response.Header.Set(http.CanonicalHeaderKey("Content-Type"), "application/octet-stream")
	c.SetBodyStream(reader, -1)
}

func getTemplateFormValueFromDto(ctx context.Context, formValue *nextagent.TemplateFormValue, artifactService *artifactService.Service, sessionID string) entity.TemplateFormValue {
	if formValue == nil {
		return entity.TemplateFormValue{}
	}
	variables := make(map[string]*entity.TemplateVariableValue)
	for k, v := range formValue.Variables {
		if v == nil || (lo.FromPtr(v.Content) == "" && len(v.Attachments) == 0) { // 过滤空变量
			continue
		}
		variables[k] = &entity.TemplateVariableValue{
			Content: v.Content,
			Attachments: lo.Map(v.Attachments, func(a *nextagent.AttachmentRequired, _ int) *entity.Attachment {
				detail, _ := getArtifactDetailEntity(ctx, artifactService, sessionID, a)
				if detail == nil {
					return nil
				}
				return detail
			}),
		}
	}
	return entity.TemplateFormValue{Variables: variables}
}

func getArtifactDetailEntity(ctx context.Context, artifactService *artifactService.Service, sessionID string, attachment *nextagent.AttachmentRequired) (*entity.Attachment, error) {
	if attachment == nil {
		return nil, nil
	}
	attachmentDetail, err := artifactService.GetAndUpdateAttachmentArtifact(ctx, sessionID, agententity.Attachment{
		ArtifactID: attachment.ID,
		Path:       attachment.FileName,
		Type:       agententity.AttachmentTypeFile, // 用户上传的目前都是文件
	}, false)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update or get attachment detail: %s", err)
		return nil, err
	}
	return attachmentDetail, nil
}
