package serverhandler

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	knowledgebaseservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	devaiutil "code.byted.org/devgpt/kiwis/devai/common/util"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/page"
	"code.byted.org/devgpt/kiwis/port/lark"
)

func (h *Handler) UploadDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UploadDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "upload documents request: %+v", req)
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		log.V1.CtxInfo(ctx, "permission denied to upload documents")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}

	eg := errgroup.Group{}
	eg.SetLimit(5)
	var docs []*entity.Document

	eg.Go(func() error {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()

		docs, err = h.KnowledgebaseService.CreateLarkDocSync(ctx, &nextagent.LarkDocConfig{
			SingleDocs: req.GetDocumentURLs(),
			WikiList:   req.GetWikiList(),
		}, req.GetDatasetID(), user.Username)
		if err != nil {
			return err
		}

		return nil
	})

	eg.Go(func() error {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()

		err = h.KnowledgebaseService.CreateLarkDocAsync(ctx, &nextagent.LarkDocConfig{
			SingleDocs: req.GetDocumentURLs(),
			WikiList:   req.GetWikiList(),
		}, req.GetDatasetID(), user.Username)
		if err != nil {
			return err
		}

		return nil
	})

	if err := eg.Wait(); err != nil {
		logs.V1.CtxError(ctx, "[UploadDocuments] failed to upload documents, err: %v", err)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}
		if errors.Is(err, lark.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrDocumentNotExist), "document not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload documents")
		return
	}

	resp := &nextagent.UploadDocumentsResponse{
		Documents: lo.Map(docs, func(doc *entity.Document, _ int) *nextagent.KnowledgeBaseDocument {
			return getDocumentFromEntity(doc)
		}),
	}
	c.JSON(http.StatusOK, resp)
}

type OpsUploadDocumentRequest struct {
	nextagent.UploadDocumentsRequest `json:",inline"`
	Username                         string `json:"username"`
}

func (h *Handler) AdminUploadDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[OpsUploadDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "ops upload documents request: %+v", req)
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	if !(user.Username == "xiazihao" || user.Username == "heyuying") {
		h.AuthM.GetAccount(ctx, c)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}
	if len(req.Username) == 0 {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid username")
		return
	}
	opts := make([]knowledgebaseservice.UploadDocumentOption, 0, len(req.GetDocumentURLs()))
	for _, link := range req.GetDocumentURLs() {
		docType, sourceKey := devaiutil.ParseLarkDocURL(ctx, link)
		if docType == "" || sourceKey == "" {
			logs.V1.CtxError(ctx, "[UploadDocuments] failed to parse url, link: %s", link)
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid url")
			return
		}
		documentSourceType, isSupported := entity.GetDocumentSourceType(docType)
		if !isSupported {
			logs.V1.CtxError(ctx, "[UploadDocuments] failed to parse url, link: %s", link)
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrDocumentTypeNotSupport), "invalid url")
			return
		}
		opts = append(opts, knowledgebaseservice.UploadDocumentOption{
			SourceType: documentSourceType,
			SourceUid:  sourceKey,
			ImportType: entity.ImportTypeSingle,
		})
	}
	docs, err := h.KnowledgebaseService.UploadDocuments(ctx, req.GetDatasetID(), req.Username, opts)
	if err != nil {
		logs.V1.CtxError(ctx, "[UploadDocuments] failed to upload documents, err: %v", err)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}
		if errors.Is(err, lark.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrDocumentNotExist), "document not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload documents")
		return
	}
	resp := &nextagent.UploadDocumentsResponse{
		Documents: lo.Map(docs, func(doc *entity.Document, _ int) *nextagent.KnowledgeBaseDocument {
			return getDocumentFromEntity(doc)
		}),
	}
	c.JSON(http.StatusOK, resp)
}

func (h *Handler) DeleteDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "[DeleteDocument] request: %v", req)
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}
	err = h.KnowledgebaseService.DeleteDocument(ctx, req.GetDatasetID(), req.GetDocumentID())
	if err != nil {
		logs.V1.CtxError(ctx, "[DeleteDocument] failed to delete document, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.DeleteDocumentResponse{})
}

func (h *Handler) BatchDeleteDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BatchDeleteDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "[BatchDeleteDocument] request: %v", req)
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}
	err = h.KnowledgebaseService.BatchDeleteDocument(ctx, req.GetDatasetID(), req.GetDocumentIDs())
	if err != nil {
		logs.V1.CtxError(ctx, "[BatchDeleteDocument] failed to batch delete document, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to batch delete document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.BatchDeleteDocumentResponse{})
}

func (h *Handler) UpdateDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "[UpdateDocument] request: %v", req)
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	//权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(account),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}

	err = h.KnowledgebaseService.UpdateDocument(ctx, req.GetDatasetID(), req.GetDocumentID(), account.Username)
	if err != nil {
		logs.V1.CtxError(ctx, "[UpdateDocument] failed to update document, err: %v", err)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}
		if errors.Is(err, lark.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrDocumentNotExist), "document not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.UpdateDocumentResponse{})
}

func (h *Handler) BatchUpdateDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BatchUpdateDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "[BatchUpdateDocument] request: %v", req)
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	//权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(account),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}

	err = h.KnowledgebaseService.BatchUpdateDocument(ctx, req.GetDatasetID(), account.Username, req.GetDocumentIDs())
	if err != nil {
		logs.V1.CtxError(ctx, "[BatchUpdateDocument] failed to batch update document, err: %v", err)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}
		if errors.Is(err, lark.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrDocumentNotExist), "document not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to batch update document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.BatchUpdateDocumentResponse{})
}

func (h *Handler) GetKnowledgeTaskStatus(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetKnowledgeTaskStatusRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "[GetKnowledgeTaskStatus] request: %v", req)

	status, err := h.KnowledgebaseService.GetKnowledgeTaskStatus(ctx, req.GetDatasetID())
	if err != nil {
		logs.V1.CtxError(ctx, "[GetKnowledgeTaskStatus] failed to get task status, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get task status")
		return
	}
	c.JSON(http.StatusOK, &nextagent.GetKnowledgeTaskStatusResponse{
		Status: status,
	})
}

func (h *Handler) GetDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "[GetDocument] request: %v", req)
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	// 权限检查
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceExternalID: &req.DatasetID,
		ResourceType:       entity.ResourceTypeKnowledgebase.Ptr(),
		Action:             entity.PermissionActionKnowledgebaseRead,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNoAuth), "permission denied")
		return
	}
	doc, err := h.KnowledgebaseService.GetDocument(ctx, req.GetDatasetID(), req.GetDocumentID())
	if err != nil {
		logs.V1.CtxError(ctx, "[GetDocument] failed to get document, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.GetDocumentResponse{
		Document: getDocumentFromEntity(doc),
	})
}

func (h *Handler) InternalGetDocument(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetDocumentRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "[GetDocument] request: %v", req)
	doc, err := h.KnowledgebaseService.GetDocument(ctx, req.GetDatasetID(), req.GetDocumentID())
	if err != nil {
		logs.V1.CtxError(ctx, "[GetDocument] failed to get document, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get document")
		return
	}
	c.JSON(http.StatusOK, &nextagent.GetDocumentResponse{
		Document: getDocumentFromEntity(doc),
	})
}

func getDocumentFromEntity(doc *entity.Document) *nextagent.KnowledgeBaseDocument {
	if doc == nil {
		return nil
	}
	ret := &nextagent.KnowledgeBaseDocument{
		ID:            doc.ID,
		Title:         doc.Title,
		Creator:       doc.Creator,
		URL:           doc.GetDocumentURL(),
		UpdatedAt:     doc.UpdatedAt.Format("2006-01-02 15:04:05"),
		Owner:         doc.Owner,
		CreatedAt:     doc.CreatedAt.Format("2006-01-02 15:04:05"),
		DatasetID:     doc.DatasetID,
		ProcessStatus: nextagent.ProcessStatus(doc.ProcessStatus),
		ImportType:    nextagent.ImportType(doc.ImportType),
		WikiSpaceName: lo.ToPtr(doc.WikiSpaceName),
	}

	ret.ContentType = doc.ContentType.ToIDL()
	if doc.DocumentContent != nil {
		ret.Content = doc.DocumentContent
	}

	if doc.Heat != nil {
		ret.Heat = doc.Heat
	}

	if doc.FaildReason != nil {
		ret.FailedReason = lo.ToPtr(getDocumentErrorMsg(doc.FaildReason.Error))
	}

	return ret
}

func getDocumentErrorMsg(errCode entity.DocumentFailedReasonError) string {
	if env.GetCurrentVRegion() == env.VREGION_SINGAPORECENTRAL {
		switch errCode {
		case entity.DocumentFailedReasonErrorCodeNoPermission:
			return "no permission"
		case entity.DocumentFailedReasonErrorCodeNotFound:
			return "resource not found"
		case entity.DocumentFailedReasonErrorCodeLarkAuthFailed, entity.DocumentFailedReasonErrorCodeLarkFrequencyLimit:
			return "call lark failed"
		default:
			return "internal error"
		}
	}

	switch errCode {
	case entity.DocumentFailedReasonErrorCodeNoPermission:
		return "没有权限"
	case entity.DocumentFailedReasonErrorCodeNotFound:
		return "文档丢失"
	case entity.DocumentFailedReasonErrorCodeLarkAuthFailed, entity.DocumentFailedReasonErrorCodeLarkFrequencyLimit:
		return "飞书接口失败"
	default:
		return "平台导入失败"
	}
}

func (h *Handler) SearchLarkDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SearchLarkDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "search lark documents request: %v", req)
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	searchPassages, wikiSpace, err := h.KnowledgebaseService.SearchDocsFromLark(ctx, account.Username, req.Query, req.DatasetID, entity.ImportType(req.GetImportType()))
	if err != nil {
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}

		logs.V1.CtxError(ctx, "[SearchLarkDocuments] failed to search lark document, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to search lark document")
		return
	}

	resp := &nextagent.SearchLarkDocumentsResponse{
		Documents: lo.Map(searchPassages, func(searchPassage *entity.LarkDocument, _ int) *nextagent.LarkDocument {
			return getLarkDocumentFromEntity(searchPassage)
		}),
	}

	if wikiSpace != nil {
		resp.WikiSpace = &nextagent.WikiSpace{
			SpaceID:   wikiSpace.ID,
			SpaceName: wikiSpace.Name,
		}
	}

	c.JSON(http.StatusOK, resp)
}

func (h *Handler) RecallDataset(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.RecallDatasetRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "recall dataset: %v", req)
	recallSegments, err := h.KnowledgebaseService.RecallDataset(ctx, &knowledgebaseservice.RecallDatasetOption{
		DatasetID: req.GetDatasetID(),
		Query:     req.GetQuery(),
		TopK:      int(req.GetTopK()),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[RecallDataset] failed to recall, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to recall")
		return
	}
	c.JSON(http.StatusOK, &nextagent.RecallDatasetResponse{
		Segments: lo.FilterMap(recallSegments, func(segment *entity.Segment, _ int) (*nextagent.RecallSegment, bool) {
			if segment == nil || segment.Document == nil {
				logs.V1.CtxError(ctx, "[RecallDataset] segment or document is nil")
				return nil, false
			}
			return &nextagent.RecallSegment{
				DatasetID:         segment.DatasetID,
				DocumentID:        segment.DocumentID,
				Content:           segment.Content.Text,
				Title:             segment.Document.Title,
				URL:               segment.Document.GetDocumentURL(),
				DocumentCreatedAt: segment.Document.DocumentCreatedAt.Format(time.RFC3339),
				Owner:             segment.Document.Owner,
				LastUpdatedAt:     segment.Document.LastUpdatedAt.Format(time.RFC3339),
			}, true
		}),
	})
}

func (h *Handler) RecommendDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.RecommendDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "recommend documents request: %v", req)
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	referenceDocuments := lo.Map(req.GetReferenceDocuments(), func(refDoc *nextagent.ReferenceDocument, _ int) *knowledgebaseservice.ReferenceDocument {
		return &knowledgebaseservice.ReferenceDocument{
			Title: refDoc.Title,
			URL:   refDoc.URL,
		}
	})
	searchPassages, err := h.KnowledgebaseService.RecommendDocuments(ctx, account.Username, req.DatasetID, account.CloudUserJWT, referenceDocuments)
	if err != nil {
		logs.V1.CtxError(ctx, "[RecommendDocuments] failed to recommend documents, err: %v", err)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrPermissionDenied) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "lark auth failed")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to recommend documents")
		return
	}
	c.JSON(http.StatusOK, &nextagent.RecommendDocumentsResponse{
		Documents: lo.Map(searchPassages, func(searchPassage *entity.LarkDocument, _ int) *nextagent.LarkDocument {
			return getLarkDocumentFromEntity(searchPassage)
		}),
	})
}

func getLarkDocumentFromEntity(doc *entity.LarkDocument) *nextagent.LarkDocument {
	return &nextagent.LarkDocument{
		Title:       doc.Title,
		Content:     doc.Content,
		URL:         doc.URL,
		IsUploaded:  doc.IsUploaded,
		ContentType: doc.ContentType,
		OwnerName:   doc.OwnerName,
		IsRoot:      doc.IsRoot,
		HasChild:    doc.HasChild,
	}
}

func (h *Handler) ListDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "list documents request: %v", req)
	offset, limit := page.GetOffsetAndLimitFromPage(int(req.PageNum), int(req.PageSize))
	docs, totalCount, err := h.KnowledgebaseService.ListDocuments(ctx, req.GetDatasetID(), &knowledgebaseservice.ListDocumentsOption{
		Query:         req.Query,
		Creators:      req.Creators,
		DescOrderBy:   req.DescOrderBy,
		ProcessStatus: req.ProcessStatus,
		Offset:        offset,
		Limit:         limit,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "[ListDocuments] failed to list documents, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list documents")
		return
	}
	c.JSON(http.StatusOK, &nextagent.ListDocumentsResponse{
		Documents: lo.Map(docs, func(doc *entity.Document, _ int) *nextagent.KnowledgeBaseDocument {
			return getDocumentFromEntity(doc)
		}),
		Total: totalCount,
	})
}

func (h *Handler) CountDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CountDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "count documents request: %v", req)
	account, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	allTotal, myTotal, err := h.KnowledgebaseService.CountDocuments(ctx, req.GetDatasetID(), account.Username)
	if err != nil {
		logs.V1.CtxError(ctx, "[CountDocuments] failed to count documents, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to count documents")
		return
	}
	c.JSON(http.StatusOK, &nextagent.CountDocumentsResponse{
		AllTotal: int64(allTotal),
		MyTotal:  int64(myTotal),
	})
}
