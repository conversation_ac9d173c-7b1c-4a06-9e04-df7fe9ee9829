package serverhandler

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"

	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	agentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/page"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

func (h *Handler) GetAgent(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetAgentRequest](ctx, c)
	if req == nil {
		return
	}

	agent, err := h.AgentService.GetAgent(ctx, agentservice.GetAgentOption{ID: req.AgentID})
	if err != nil {
		if errors.Is(err, agentservice.ErrAgentNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent not found")
			return
		}
		log.V1.CtxError(ctx, "get agent error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get agent error")
		return
	}

	c.JSON(http.StatusOK, nextagent.GetAgentResponse{
		Agent: agent.ToIDL(),
	})
}

func (h *Handler) UpdateAgent(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateAgentRequest](ctx, c)
	if req == nil {
		return
	}

	if req.Name == nil && req.Description == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "have no update filed")
		return
	}

	agent, err := h.AgentService.UpdateAgent(ctx, agentservice.UpdateAgentOption{
		ID:          req.AgentID,
		Name:        req.Name,
		Description: req.Description,
	})
	if err != nil {
		if errors.Is(err, agentservice.ErrAgentNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent not found")
			return
		}
		log.V1.CtxError(ctx, "update agent error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "update agent error")
		return
	}
	c.JSON(http.StatusOK, nextagent.UpdateAgentResponse{
		Agent: agent.ToIDL(),
	})
}

func (h *Handler) DeleteAgent(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteAgentRequest](ctx, c)
	if req == nil {
		return
	}

	err := h.AgentService.DeleteAgent(ctx, agentservice.DeleteAgentOption{
		ID: req.AgentID,
	})
	if err != nil {
		if errors.Is(err, agentservice.ErrAgentConfigExists) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "agent config exist")
			return
		}
		log.V1.CtxError(ctx, "delete agent error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "delete agent error")
		return
	}
	c.JSON(http.StatusOK, nextagent.DeleteAgentResponse{})
}

func (h *Handler) ListAgent(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListAgentsRequest](ctx, c)
	if req == nil {
		return
	}

	offset, limit := page.GetOffsetAndLimitFromPage(int(req.PageNum), int(req.PageSize))
	total, agents, err := h.AgentService.ListAgents(ctx, agentservice.ListAgentsOption{
		Creator: req.Creator,
		Name:    req.Name,
		Offset:  offset,
		Limit:   limit,
	})
	if err != nil {
		log.V1.CtxError(ctx, "list agent error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "list agent error")
		return
	}
	c.JSON(http.StatusOK, nextagent.ListAgentsResponse{
		Agents: lo.Map(agents, func(item *nextentity.Agent, index int) *nextagent.Agent {
			return item.ToIDL()
		}),
		Total: total,
	})
}

func (h *Handler) CreateAgent(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateAgentRequest](ctx, c)
	if req == nil {
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "no auth")
		return
	}

	agent, err := h.AgentService.CreateAgent(ctx, agentservice.CreateAgentOption{
		Creator:     user.Username,
		Name:        req.Name,
		Description: req.Description,
	})
	if err != nil {
		log.V1.CtxError(ctx, "create agent error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "create agent error")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateAgentResponse{
		Agent: agent.ToIDL(),
	})
}

func (h *Handler) CreateAgentConfig(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateAgentConfigRequest](ctx, c)
	if req == nil {
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "no auth")
		return
	}

	agentConfig, err := h.AgentService.CreateAgentConfig(ctx, agentservice.CreateAgentConfigOption{
		AgentID:     req.AgentID,
		Type:        nextentity.ParseAgentType(req.Type),
		Creator:     user.Username,
		Name:        req.Name,
		Description: req.Description,
	})
	if err != nil {
		if errors.Is(err, agentservice.ErrAgentNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent not found")
			return
		}
		if errors.Is(err, agentservice.ErrAgentConfigExists) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "agent config base type exists")
			return
		}
		if errors.Is(err, agentservice.ErrAgentConfigBaseTypeExists) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "already have base agent config")
			return
		}
		log.V1.CtxError(ctx, "create agent config error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "create agent config error")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateAgentConfigResponse{
		AgentConfig: agentConfig.ToIDL(),
	})
}

func (h *Handler) UpdateAgentConfig(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateAgentConfigRequest](ctx, c)
	if req == nil {
		return
	}

	if req.Name == nil && req.Description == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "have no update filed")
		return
	}

	agentConfig, err := h.AgentService.UpdateAgentConfig(ctx, agentservice.UpdateAgentConfigOption{
		ID:          req.AgentConfigID,
		Name:        req.Name,
		Description: req.Description,
	})
	if err != nil {
		if errors.Is(err, agentservice.ErrAgentConfigNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent config not found")
			return
		}
		log.V1.CtxError(ctx, "update agent config error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "update agent config error")
		return
	}
	c.JSON(http.StatusOK, nextagent.UpdateAgentConfigResponse{
		AgentConfig: agentConfig.ToIDL(),
	})
}

func (h *Handler) DeleteAgentConfig(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteAgentConfigRequest](ctx, c)
	if req == nil {
		return
	}

	err := h.AgentService.DeleteAgentConfig(ctx, agentservice.DeleteAgentConfigOption{
		ID: req.AgentConfigID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "delete agent config error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "delete agent config error")
		return
	}
	c.JSON(http.StatusOK, nextagent.DeleteAgentConfigResponse{})
}

func (h *Handler) ListAgentConfigs(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListAgentConfigsRequest](ctx, c)
	if req == nil {
		return
	}

	var types []*nextentity.AgentConfigType
	if req.Type != nil {
		typeStrs := strings.Split(*req.Type, ",")
		for _, typeStr := range typeStrs {
			if typeStr == "" {
				continue
			}
			agentType := nextentity.ParseAgentType(typeStr)
			if agentType == nextentity.AgentConfigTypeUnknown {
				hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid type format")
				return
			}
			types = append(types, &agentType)
		}
	}

	offset, limit := page.GetOffsetAndLimitFromPage(int(req.PageNum), int(req.PageSize))
	total, agentConfigs, err := h.AgentService.ListAgentConfigs(ctx, agentservice.ListAgentConfigsOption{
		AgentID: req.AgentID,
		Creator: req.Creator,
		Name:    req.Name,
		Type:    types,
		Offset:  offset,
		Limit:   limit,
	})
	if err != nil {
		log.V1.CtxError(ctx, "list agent configs error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "list agent configs error")
		return
	}
	var agentIDL *nextagent.Agent
	if req.AgentID != nil {
		agent, err := h.AgentService.GetAgent(ctx, agentservice.GetAgentOption{
			ID: *req.AgentID,
		})
		if err != nil {
			log.V1.CtxError(ctx, "get agent error: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get agent error")
			return
		}
		agentIDL = agent.ToIDL()
	}

	// 将 Type 等于 base 类型的放到首位
	var baseConfigs []*nextagent.AgentConfig
	var otherConfigs []*nextagent.AgentConfig
	for _, item := range agentConfigs {
		config := item.ToIDL()
		if item.Type == nextentity.AgentConfigTypeBase {
			baseConfigs = append(baseConfigs, config)
		} else {
			otherConfigs = append(otherConfigs, config)
		}
	}

	data := append(baseConfigs, otherConfigs...)

	c.JSON(http.StatusOK, nextagent.ListAgentConfigsResponse{
		Agent:        agentIDL,
		AgentConfigs: data,
		Total:        total,
	})
}

func (h *Handler) GetAgentConfig(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetAgentConfigRequest](ctx, c)
	if req == nil {
		return
	}

	agentConfig, agentConfigVersion, err := h.AgentService.GetAgentConfig(ctx, agentservice.GetAgentConfigOption{
		ID:      req.AgentConfigID,
		Latest:  req.Latest,
		Version: req.Version,
		Online:  req.Online,
	})
	if err != nil {
		if errors.Is(err, agentservice.ErrAgentConfigNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent config not found")
			return
		}
		if errors.Is(err, agentservice.ErrAgentConfigVersionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent config version not found")
			return
		}
		log.V1.CtxError(ctx, "get agent config error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get agent config error")
		return
	}

	c.JSON(http.StatusOK, nextagent.GetAgentConfigResponse{
		AgentConfig: agentConfig.ToIDL(),
		AgentConfigVersion: lo.TernaryF(agentConfigVersion != nil,
			func() *nextagent.AgentConfigVersion { return agentConfigVersion.ToIDL() },
			func() *nextagent.AgentConfigVersion { return nil },
		),
	})
}

func (h *Handler) CreateAgentConfigVersion(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateAgentConfigVersionRequest](ctx, c)
	if req == nil {
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "no auth")
		return
	}

	if len(req.CustomConfig) != 0 {
		var temp interface{}
		err := json.Unmarshal([]byte(req.CustomConfig), &temp)
		if err != nil {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "custom config json unmarshal error")
			return
		}
	}

	agentConfigVersion, err := h.AgentService.CreateAgentConfigVersion(ctx, agentservice.CreateAgentConfigVersionOption{
		AgentConfigID: req.AgentConfigID,
		Description:   req.Description,
		Creator:       user.Username,
		Enabled:       req.Enabled,
		Status:        nextentity.AgentConfigStatusCreated,
		RuntimeConfig: lo.TernaryF(req.RuntimeConfig != nil,
			func() nextentity.RuntimeConfig { return *nextentity.ParseRuntimeConfigFromIDL(req.RuntimeConfig) },
			func() nextentity.RuntimeConfig { return nextentity.RuntimeConfig{} },
		),
		CustomConfig: nextentity.CustomConfig(lo.ToPtr(json.RawMessage(req.CustomConfig))),
		PromptConfig: lo.TernaryF(req.PromptConfig != nil,
			func() nextentity.PromptConfig { return *nextentity.ParsePromptConfigFromIDL(req.PromptConfig) },
			func() nextentity.PromptConfig { return nextentity.PromptConfig{} },
		),
		KnowledgesetConfig: lo.TernaryF(req.KnowledgesetConfig != nil,
			func() nextentity.KnowledgesetConfig {
				return *nextentity.ParseKnowledgesetConfigFromIDL(req.KnowledgesetConfig)
			},
			func() nextentity.KnowledgesetConfig { return nextentity.KnowledgesetConfig{} },
		),
	})
	if err != nil {
		if h.checkAgentBusinessError(c, err) {
			return
		}
		log.V1.CtxError(ctx, "create agent config version error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "create agent config version error")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateAgentConfigVersionResponse{
		AgentConfigVersion: agentConfigVersion.ToIDL(),
	})
}

func (h *Handler) GetAgentConfigVersion(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetAgentConfigVersionRequest](ctx, c)
	if req == nil {
		return
	}

	agentConfigVersion, err := h.AgentService.GetAgentConfigVersion(ctx, agentservice.GetAgentConfigVersionOption{
		AgentConfigVersionID: &req.AgentConfigVersionID,
	})
	if err != nil {
		if errors.Is(err, agentservice.ErrAgentConfigVersionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent config version not found")
			return
		}
		log.V1.CtxError(ctx, "get agent config version error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get agent config version error")
		return
	}

	c.JSON(http.StatusOK, nextagent.UpdateAgentConfigVersionResponse{
		AgentConfigVersion: agentConfigVersion.ToIDL(),
	})
}

func (h *Handler) UpdateAgentConfigVersion(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateAgentConfigVersionRequest](ctx, c)
	if req == nil {
		return
	}

	if req.CustomConfig != nil {
		var temp interface{}
		err := json.Unmarshal([]byte(*req.CustomConfig), &temp)
		if err != nil {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "custom config json unmarshal error")
			return
		}
	}

	agentConfigVersion, err := h.AgentService.UpdateAgentConfigVersion(ctx, agentservice.UpdateAgentConfigVersionOption{
		ID:            req.AgentConfigVersionID,
		Description:   req.Description,
		Enabled:       req.Enabled,
		RuntimeConfig: nextentity.ParseRuntimeConfigFromIDL(req.RuntimeConfig),
		CustomConfig: lo.TernaryF(req.CustomConfig != nil,
			func() *nextentity.CustomConfig {
				return lo.ToPtr(nextentity.CustomConfig(lo.ToPtr(json.RawMessage(*req.CustomConfig))))
			},
			func() *nextentity.CustomConfig { return nil },
		),
		PromptConfig:       nextentity.ParsePromptConfigFromIDL(req.PromptConfig),
		KnowledgesetConfig: nextentity.ParseKnowledgesetConfigFromIDL(req.KnowledgesetConfig),
		UpdatedAt:          req.UpdatedAt,
	})
	if err != nil {
		if h.checkAgentBusinessError(c, err) {
			return
		}

		log.V1.CtxError(ctx, "update agent config version error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "update agent config version error")
		return
	}
	c.JSON(http.StatusOK, nextagent.UpdateAgentConfigVersionResponse{
		AgentConfigVersion: agentConfigVersion.ToIDL(),
	})
}

func (h *Handler) DeployAgentConfigVersion(ctx context.Context, c *app.RequestContext) {
	c.JSON(http.StatusOK, nextagent.DeployAgentConfigVersionResponse{
		AgentConfigVersion: &nextagent.AgentConfigVersion{},
	})
	// req := hertz.BindValidate[nextagent.DeployAgentConfigVersionRequest](ctx, c)
	// if req == nil {
	// 	return
	// }

	// if req.Status != nextagent.AgentConfigStatusOnline {
	// 	hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid param status")
	// 	return
	// }

	// user, _ := h.AuthM.GetAccount(ctx, c)
	// if user == nil {
	// 	hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "no auth")
	// 	return
	// }

	// agentConfigVersion, err := h.AgentService.DeployAgent(ctx, agentservice.DeployAgentOption{
	// 	AgentConfigVersionID: req.AgentConfigVersionID,
	// 	Actor:                user.Username,
	// })
	// if err != nil {
	// 	if errors.Is(err, agentservice.ErrAgentConfigVersionNotFound) {
	// 		hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent config version not found")
	// 		return
	// 	}
	// 	if errors.Is(err, agentservice.ErrAgentConfigNotAllowedUseDisablePrompt) {
	// 		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "not allowed use disable prompt")
	// 		return
	// 	}
	// 	if errors.Is(err, agentservice.ErrAgentConfigNotAllowedDeploy) {
	// 		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "not allowed deploy")
	// 		return
	// 	}
	// 	if errors.Is(err, agentservice.ErrAgentConfigPromptNotExists) {
	// 		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "not exist prompt config")
	// 		return
	// 	}
	// 	if errors.Is(err, agentservice.ErrKnowledgesetVersionNotOnline) {
	// 		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "knowledgeset version status should be online")
	// 		return
	// 	}
	// 	log.V1.CtxError(ctx, "deploy agent config version error: %v", err)
	// 	hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "deploy agent config version error")
	// 	return
	// }

	// // 上线后清理预热容器
	// go func() {
	// 	defer func() {
	// 		if err := recover(); err != nil {
	// 			log.V1.CtxError(ctx, "recover from panic: %v", err)
	// 		}
	// 	}()
	// 	newCtx := ctxvalues.SetLogID(context.Background(), ctxvalues.LogIDDefault(ctx))

	// 	// 获取部署 Agent 对应的 Role
	// 	role, err := h.AgentService.GetRoleByAgentConfigVersion(newCtx, *agentConfigVersion)
	// 	if err != nil {
	// 		log.V1.CtxError(newCtx, "get role error: %v", err)
	// 		return
	// 	}
	// 	if role == nil {
	// 		log.V1.CtxError(newCtx, "role not found")
	// 		return
	// 	}

	// 	// 删除预热容器
	// 	_, failedSessions, err := h.SessionService.DeleteAllPreparedSessions(newCtx, role)
	// 	if err != nil {
	// 		log.V1.CtxError(newCtx, "delete prepares session error: %v", err)
	// 		return
	// 	}
	// 	if len(failedSessions) > 0 {
	// 		log.V1.CtxWarn(newCtx, "delete prepares sessions partial failure, failed sessions: %v", failedSessions)
	// 		return
	// 	}
	// }()

	// c.JSON(http.StatusOK, nextagent.DeployAgentConfigVersionResponse{
	// 	AgentConfigVersion: agentConfigVersion.ToIDL(),
	// })

}

func (h *Handler) ListAgentConfigVersion(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListAgentConfigVersionsRequest](ctx, c)
	if req == nil {
		return
	}

	var statuses []*nextentity.AgentConfigStatus
	if req.Status != nil {
		statusStrs := strings.Split(*req.Status, ",")
		for _, statusStr := range statusStrs {
			if statusStr == "" {
				continue
			}
			agentStatus := nextentity.ParseAgentConfigStatus(statusStr)
			if agentStatus == nextentity.AgentConfigStatusUnknown {
				hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid status format")
				return
			}
			statuses = append(statuses, &agentStatus)
		}
	}

	agentConfig, _, err := h.AgentService.GetAgentConfig(ctx, agentservice.GetAgentConfigOption{
		ID: req.AgentConfigID,
	})
	if err != nil {
		if errors.Is(err, agentservice.ErrAgentConfigNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent config not found")
			return
		}
		log.V1.CtxError(ctx, "get agent config error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get agent config error")
		return
	}

	offset, limit := page.GetOffsetAndLimitFromPage(int(req.PageNum), int(req.PageSize))
	total, agentConfigVersions, err := h.AgentService.ListAgentConfigVersions(ctx, agentservice.ListAgentConfigVersionsOption{
		AgentConfigID: &req.AgentConfigID,
		Creator:       req.Creator,
		Enabled:       req.Enabled,
		Status:        statuses,
		Offset:        offset,
		Limit:         limit,
	})
	if err != nil {
		log.V1.CtxError(ctx, "list agent config versions error: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "list agent config versions error")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListAgentConfigVersionsResponse{
		AgentConfig: agentConfig.ToIDL(),
		AgentConfigVersions: lo.Map(agentConfigVersions, func(item *nextentity.AgentConfigVersion, index int) *nextagent.AgentConfigVersion {
			return item.ToIDL()
		}),
		Total: total,
	})
}

func (h *Handler) CopyAgentConfigVersion(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CopyAgentConfigVersionRequest](ctx, c)
	if req == nil {
		return
	}
	if req.AgentConfigID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "agent config id is empty")
		return
	}
	if req.SourceID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "agent config version id is empty")
		return
	}
	description := ""
	if req.Description != nil {
		description = *req.Description
	}
	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "no auth")
		return
	}
	agentConfigVersion, err := h.AgentService.CopyAgentConfigVersion(ctx, &agentservice.CopyAgentConfigVersionOption{
		SourceID:      req.SourceID,
		AgentConfigID: req.AgentConfigID,
		Creator:       user.Name,
		Description:   description,
	})

	if err != nil {
		log.V1.CtxError(ctx, "copy agent config version error: %v", err)
		if errors.Is(err, agentservice.ErrAgentConfigNotAllowedCopy) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "agent base config not allowed copy from other env")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "copy agent config version error")
		return
	}

	c.JSON(http.StatusOK, nextagent.CopyAgentConfigVersionResponse{
		AgentConfigVersionID: agentConfigVersion.ID,
	})
}

func (h *Handler) AddAgentPermission(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.AddAgentPermissionRequest](ctx, c)
	if req == nil {
		return
	}

	res, err := h.PermissionService.AddResourcePermission(ctx, permissionservice.AddResourcePermissionOption{
		ResourceExternalID: lo.ToPtr(req.AgentID),
		ResourceType:       lo.ToPtr(nextentity.ResourceTypeAgent),
		PermissionMetas: []nextentity.PermissionMeta{{
			Type:       nextentity.PermissionType(req.Type),
			ExternalID: req.ExternalID,
			Role:       nextentity.PermissionRoleFromIDL(req.Role),
		}},
	})
	if err != nil {
		log.V1.CtxError(ctx, "add agent permission error: %v", err)

		if errors.Is(err, permissionservice.ErrResourceNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "agent resource not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "add agent permission error")
		return
	}

	c.JSON(http.StatusOK, nextagent.AddAgentPermissionResponse{
		Resource: res.ToIDL(),
	})
}

func (h *Handler) checkAgentBusinessError(c *app.RequestContext, err error) (hasHandle bool) {
	hasHandle = true
	var agentBusinessError *agentservice.AgentBusinessError
	if errors.As(err, &agentBusinessError) {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	hasHandle = false
	return
}
