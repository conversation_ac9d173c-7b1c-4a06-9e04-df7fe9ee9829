package serverhandler

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/iesarch/krakend/consts"
	"code.byted.org/middleware/hertz/pkg/app"
	purl "code.byted.org/security/go-polaris/url"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	message_parser "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/hertz"

	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
)

type State struct {
	Next     string `json:"next"`     // 重定向地址
	Username string `json:"username"` // 用户名
	Email    string `json:"email"`    // 邮箱
}

const ErrAuthorizationDenied = "access_denied"

func (h *Handler) LarkAuth(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.LarkAuthRequest](ctx, c)
	if req == nil || (req.GetCode() == "" && req.GetState() == "" && req.GetError() == "") {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "request param is required")
		return
	}
	if req.GetCode() == "" && req.GetError() == "" {
		log.V1.CtxError(ctx, "code or error is required")
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "code or error is required")
		return
	}
	if req.GetError() != "" && req.GetError() != ErrAuthorizationDenied {
		log.V1.CtxError(ctx, "lark err: %v", req.GetError())
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), req.GetError())
		return
	}
	var state State
	stateStr, err := url.QueryUnescape(req.GetState())
	if err != nil {
		log.V1.CtxError(ctx, "failed to decode state value: %s", req.GetState())
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), err.Error())
		return
	}
	err = json.Unmarshal([]byte(stateStr), &state)
	if err != nil {
		log.V1.CtxError(ctx, "failed to decode state value: %s", req.GetState())
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), err.Error())
		return
	}
	if state.Username == "" {
		log.V1.CtxError(ctx, "username is required")
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "username is required")
		return
	}

	// 检查重定向地址是否合法
	parsedURL, err := url.Parse(h.LarkConf.GetValue().RedirectURI)
	if err != nil {
		log.V1.CtxError(ctx, "failed to parse redirect uri: %s", h.LarkConf.GetValue().RedirectURI)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	urlChecker := purl.NewHTTPRedirectChecker(parsedURL.Host)
	if ok, _ := urlChecker.CheckStr(state.Next); !ok {
		log.V1.CtxError(ctx, "invalid redirect uri: %s", state.Next)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid redirect uri")
		return
	}
	_, err = h.LarkService.LarkAuth(ctx, state.Username, state.Email, req.GetCode(), req.GetError() == ErrAuthorizationDenied)
	if err != nil {
		log.V1.CtxError(ctx, "failed to lark auth: %+v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.Redirect(http.StatusTemporaryRedirect, []byte(state.Next))
}

func (h *Handler) CheckLarkAuth(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CheckLarkAuthRequest](ctx, c)
	if req == nil {
		log.V1.CtxInfo(ctx, "[CheckLarkAuth] request is nil")
		return
	}
	if req.URL == "" {
		log.V1.CtxError(ctx, "[CheckLarkAuth] url is required")
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "url is required")
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		log.V1.CtxInfo(ctx, "[CheckLarkAuth] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}
	authorizationDenied, authorization, err := h.LarkService.CheckLarkAuth(ctx, account.Username, account.Email)
	if err != nil {
		log.V1.CtxError(ctx, "[CheckLarkAuth] failed to check lark auth err: %v, username: %v", err, account.Username)
	}
	state := State{
		Next:     req.GetURL(),
		Username: account.Username,
		Email:    account.Email,
	}
	stateBytes, err := json.Marshal(state)
	if err != nil {
		log.V1.CtxError(ctx, "[CheckLarkAuth] failed to marshal state err: %v, state %v", err, state)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	stateParam := url.QueryEscape(string(stateBytes))
	//新版本
	authURL := fmt.Sprintf("https://accounts.feishu.cn/open-apis/authen/v1/authorize?client_id=%s&redirect_uri=%s&scope=%s&state=%s",
		h.LarkConf.GetValue().AppID, h.LarkConf.GetValue().RedirectURI, h.LarkConf.GetValue().Scope, stateParam)

	log.V1.CtxInfo(ctx, "[CheckLarkAuth] Current time is %v, user is %v, authorization is %v, authorizationDenied is %v, authURL is %s", time.Now(), account.Username, authorization, authorizationDenied, authURL)

	c.JSON(http.StatusOK, nextagent.CheckLarkAuthResponse{
		Authorization:       authorization,
		RedirectURL:         &authURL,
		AuthorizationDenied: &authorizationDenied,
	})
}

func (h *Handler) GetLarkTicket(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetLarkTicketRequest](ctx, c)
	if req == nil {
		log.V1.CtxInfo(ctx, "[GetLarkTicket] request is nil")
		return
	}

	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		log.V1.CtxInfo(ctx, "[GetLarkTicket] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}

	// 如果没有给飞书授权，返回错误
	// authorizationDenied, authorization, err := h.LarkService.CheckLarkAuth(ctx, account.Username)
	ticket, err := h.LarkService.GetLarkTicket(ctx, account.Username, account.Email)
	if err != nil {
		log.V1.CtxError(ctx, "[GetLarkTicket] failed to get lark ticket err: %v, email: %v", err, account.Email)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	c.JSON(http.StatusOK, nextagent.GetLarkTicketResponse{
		Authorization: ticket.Authorization,
		JSApiTicket: &nextagent.JSApiTicket{
			Ticket:   ticket.JSApiTicket.Ticket,
			ExpireIn: ticket.JSApiTicket.ExpireIn,
		},
		OpenID:    ticket.OpenID,
		AppID:     ticket.AppID,
		Signature: lo.ToPtr(""),
	})
}

func (h *Handler) SendLarkReplayLinkMessage(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SendLarkReplayLinkMessageRequest](ctx, c)
	if req == nil {
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}
	if req.Username == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "username is required")
		return
	}
	if req.ReplayLink == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "replay_link is required")
		return
	}
	if req.TaskName == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "task_name is required")
		return
	}
	if req.ToType != "user" && req.ToType != "group" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "to_type is required")
		return
	}
	err := h.LarkService.SendLarkReplayLinkMessage(ctx, req.Username, req.ReplayLink, req.TaskName, req.ToType)
	if err != nil {
		log.V1.CtxError(ctx, "failed to send lark replay link message. err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.JSON(http.StatusOK, nextagent.SendLarkReplayLinkMessageResponse{})
}

func (h *Handler) GetUserLarkURL(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetUserLarkURLRequest](ctx, c)
	if req == nil {
		log.V1.CtxInfo(ctx, "[GetUserLarkURL] request is nil")
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		log.V1.CtxInfo(ctx, "[GetUserLarkURL] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}

	sessionID := req.GetSessionID()
	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: sessionID,
		Sync:      false,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "[GetUserLarkURL] failed to get session: %v, session_id: %s", err, sessionID)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), err.Error())
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	if session.Creator != account.Username {
		log.V1.CtxWarn(ctx, "[GetUserLarkURL] session.creator is not equal to username, cannot transfer lark owner")
		// hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "cannot get lark url")
		c.JSON(http.StatusOK, nextagent.GetUserLarkURLResponse{
			LarkURL: req.LarkURL,
		})
		return
	}

	larkURL, err := h.LarkService.GetUserLarkURL(ctx, req.LarkURL, account.Username, account.Email)
	if err != nil {
		// 这里记录错误，但是不返回错误，把url返回给用户
		log.V1.CtxError(ctx, "[GetUserLarkURL] failed to get user lark url err: %v", err)
		// hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		// return
	}

	// 如果larkURL为空，使用请求中的larkURL，否则使用转移所有者后得到的larkURL（目前2个链接相同）
	larkURL = lo.Ternary(larkURL == "", req.LarkURL, larkURL)

	c.JSON(http.StatusOK, nextagent.GetUserLarkURLResponse{
		LarkURL: larkURL,
	})
}

func (h *Handler) GetLarkDocxBlocks(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetLarkDocxBlocksRequest](ctx, c)
	if req == nil {
		log.V1.CtxInfo(ctx, "[GetLarkDocxBlocks] request is nil")
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		log.V1.CtxInfo(ctx, "[GetLarkDocxBlocks] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}

	if account.Username != "testai" {
		log.V1.CtxInfo(ctx, "[GetLarkDocxBlocks] account has no permission")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account has no permission")
		return
	}

	blocks, err := h.LarkService.GetLarkDocxBlocks(ctx, req.DocumentID)
	if err != nil {
		log.V1.CtxError(ctx, "[GetLarkDocxBlocks] failed to get user lark url err: %v", err)
		hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), err.Error())
		return
	}

	c.JSON(http.StatusOK, blocks)
}

func (h *Handler) larkEventHandler() *dispatcher.EventDispatcher {
	verificationToken := h.LarkConf.GetValue().VerificationToken
	// verificationToken := "d2NSb3z2jGI4OTKpwrNxJbRdTS5UCPWE"
	eventEncryptKey := "" //未启用加密事件或回调的请求内容
	handler := dispatcher.NewEventDispatcher(verificationToken, eventEncryptKey).OnP2MessageReceiveV1(func(ctx context.Context, event *larkim.P2MessageReceiveV1) error {
		if event == nil || event.Event == nil || event.Event.Message == nil || event.Event.Sender == nil {
			log.V1.CtxError(ctx, "[larkEventHandler:OnP2MessageReceiveV1] event is nil")
			return nil
		}

		log.V1.CtxInfo(ctx, "[larkEventHandler:OnP2MessageReceiveV1]: RequestID: %s, P2MessageReceiveV1 event: %v", event.RequestId(), larkcore.Prettify(event))
		// 判断是不是话题消息，只有话题消息的root消息才算反馈
		if event.Event.Message.ThreadId == nil { // 不是话题消息
			log.V1.CtxInfo(ctx, "[larkEventHandler:OnP2MessageReceiveV1]: The event message is not a thread message.")
			return nil
		}
		if event.Event.Message.RootId != nil { // 是话题但不是根消息
			log.V1.CtxInfo(ctx, "[larkEventHandler:OnP2MessageReceiveV1]: The event message is not a root message.")
			return nil
		}

		chatID := lo.FromPtr(event.Event.Message.ChatId)
		if open, exist := h.NextOncallConfig.GetValue().ChatIDs[chatID]; !exist || !open {
			log.V1.CtxInfo(ctx, "[larkEventHandler:OnP2MessageReceiveV1]: chat_id %s not in config or not open", chatID)
			return nil
		}

		senderID := lo.FromPtr(lo.FromPtr(event.Event.Sender.SenderId).UserId)
		senderName, err := h.getLarkSenderUserName(ctx, senderID)
		if err != nil {
			log.V1.CtxError(ctx, "[larkEventHandler:OnP2MessageReceiveV1]: failed to getLarkSenderUserName: %v", err)
			return nil
		}

		if h.UserService.IsDeveloper(&authentity.Account{Username: senderName}) ||
			h.UserService.IsGroupOperator(&authentity.Account{Username: senderName}) {
			log.V1.CtxInfo(ctx, "[larkEventHandler:OnP2MessageReceiveV1]: sender_id[%s] %s in ignored", senderID, senderName)
			return nil
		}

		triggerMessage, imageKeys, err := h.genFeedbackDataFromEvent(ctx, event)
		if err != nil {
			// 如果有错误，triggerMessage为空，仍然发送applink，但triggerMessage为空
			log.V1.CtxError(ctx, "[genOncallApplink] failed to genFeedbackDataFromEvent: %v", err)
		}

		// 飞书表格添加反馈
		go func() {
			baseCtx := context.WithValue(context.Background(), consts.LOGIDKEY, ctx.Value(consts.LOGIDKEY)) //带上logid
			newCtx, cancel := context.WithTimeout(baseCtx, 20*time.Second)                                  // 新建一个ctx，超时时间为60s
			defer cancel()
			defer func() {
				if err := recover(); err != nil {
					log.V1.CtxError(newCtx, "[larkEventHandler:OnP2MessageReceiveV1]: failed to addFeedbackToLarkSheet: %v", err)
				}
			}()
			if err := h.addFeedbackToLarkSheet(newCtx, senderName, triggerMessage, imageKeys, event); err != nil {
				log.V1.CtxError(newCtx, "[larkEventHandler:OnP2MessageReceiveV1]: failed to addFeedbackToLarkSheet: %v", err)
			}
		}()

		oncallAppLink := h.genOncallApplink(ctx, triggerMessage, lo.FromPtr(event.Event.Message.MessageId))
		log.V1.CtxInfo(ctx, "[larkEventHandler:OnP2MessageReceiveV1]: oncallAppLink: %s", oncallAppLink)
		err = h.LarkService.SendOncallCardNotification(ctx, *event.Event.Message.MessageId, oncallAppLink)
		if err != nil {
			log.V1.CtxError(ctx, "[larkEventHandler:OnP2MessageReceiveV1]: failed to SendOncallCardNotification: %v", err)
		}
		return err

	}).OnP2MessageReadV1(func(ctx context.Context, event *larkim.P2MessageReadV1) error {
		// 处理飞书消息已读时间，使用aime机器人推送给用户的单聊消息，代理到消息平台统计阅读数
		// 消息平台地址https://notify.bytedance.net/restful/api/bots/cli_9a31b280a1f3d101/event_callback/
		log.V1.CtxInfo(ctx, "[larkEventHandler:OnP2MessageReadV1]: RequestID: %s, P2MessageReadV1 event: %v", event.RequestId(), larkcore.Prettify(event))
		err := h.proxyMessageReadEventToNotify(ctx, event)
		if err != nil {
			log.V1.CtxError(ctx, "[larkEventHandler:OnP2MessageReadV1]: failed to proxyMessageReadEventToNotify: %v", err)
		}
		return err
	})

	return handler
}

func (h *Handler) genFeedbackDataFromEvent(ctx context.Context, event *larkim.P2MessageReceiveV1) (triggerMessage string, imageKeys []string, err error) {
	if event == nil || event.Event == nil || event.Event.Message == nil {
		log.V1.CtxError(ctx, "[genFeedbackDataFromEvent] event is nil")
		return "", nil, errors.New("event is nil")
	}

	messageType := lo.FromPtr(event.Event.Message.MessageType)
	switch messageType {
	case larkim.MsgTypeText: // 普通文本消息
		var text struct {
			Text string `json:"text"`
		}
		if err := json.Unmarshal([]byte(lo.FromPtr(event.Event.Message.Content)), &text); err != nil {
			log.V1.CtxError(ctx, "[genFeedbackDataFromEvent] failed to unmarshal text: %v", err)
		} else {
			triggerMessage = text.Text
		}
	case larkim.MsgTypePost: // 富文本
		var err error
		triggerMessage, imageKeys, err = message_parser.RichTextToOncallData(lo.FromPtr(event.Event.Message.Content), true, true)
		if err != nil {
			log.V1.CtxError(ctx, "[genFeedbackDataFromEvent] failed to RichTextToPlainText: %v", err)
			return "", nil, err
		}
	default:
		log.V1.CtxError(ctx, "[genFeedbackDataFromEvent] unsupport message type: %v", messageType)
		return "", nil, errors.New("unsupport message type")
	}

	return triggerMessage, imageKeys, nil
}

func (h *Handler) genOncallApplink(ctx context.Context, triggerMessage string, messageID string) string {
	oncallCfg := h.NextOncallConfig.GetValue()
	oncallUrl, err := url.Parse(oncallCfg.OncallBaseUrl)
	if err != nil {
		log.V1.CtxError(ctx, "[genOncallApplink] failed to parse OncallBaseUrl: %v", err)
		return ""
	}
	oncallParams := url.Values{
		"bizFrom":  []string{"custom_lark_dialog"},
		"directly": []string{"true"},
		"tenant":   []string{oncallCfg.OncallTernantID},
	}
	contextJson, err := json.Marshal(map[string]string{
		"trigger_message": triggerMessage,
		"message_id":      messageID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[genOncallApplink] failed to marshal context: %v", err)
		return ""
	}
	oncallParams.Add("context", string(contextJson))
	oncallUrl.RawQuery = oncallParams.Encode()

	appLinkUrl, err := url.Parse(oncallCfg.AppLinkBaseUrl)
	if err != nil {
		log.V1.CtxError(ctx, "[genOncallApplink] failed to parse AppLinkBaseUrl: %v", err)
		return ""
	}
	appLinkParams := url.Values{
		"mode": []string{"sidebar-semi"},
		"url":  []string{oncallUrl.String()},
	}
	appLinkUrl.RawQuery = appLinkParams.Encode()

	return appLinkUrl.String()
}

// proxyMessageReadEventToNotify 处理飞书消息已读时间，aime机器人推送给用户的单聊消息已读，代理到消息平台统计阅读数
func (h *Handler) proxyMessageReadEventToNotify(ctx context.Context, event *larkim.P2MessageReadV1) error {
	// 处理飞书消息已读时间，使用aime机器人推送给用户的单聊消息，代理到消息平台统计阅读数
	// 消息平台地址到https://notify.bytedance.net/restful/api/bots/cli_9a31b280a1f3d101/event_callback/
	botAppID := h.LarkConf.GetValue().AppID
	url := fmt.Sprintf("https://notify.bytedance.net/restful/api/bots/%s/event_callback/", botAppID)
	body := event.EventReq.Body

	client := &http.Client{
		Timeout: 3 * time.Second,
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(body))
	if err != nil {
		log.V1.CtxError(ctx, "[proxyMessageReadEventToNotify] failed to create request: %v", err)
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		log.V1.CtxError(ctx, "[proxyMessageReadEventToNotify] http post failed: %v", err)
		return err
	}
	defer resp.Body.Close()

	log.V1.CtxInfo(ctx, "[proxyMessageReadEventToNotify] response status: %s", resp.Status)
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		respBody, _ := io.ReadAll(resp.Body)
		log.V1.CtxError(ctx, "[proxyMessageReadEventToNotify] non-2xx response: %d, body: %s", resp.StatusCode, string(respBody))
		return fmt.Errorf("notify response status: %d", resp.StatusCode)
	}

	log.V1.CtxInfo(ctx, "[proxyMessageReadEventToNotify] success")
	return nil
}

func (h *Handler) addFeedbackToLarkSheet(ctx context.Context, senderName string, message string, imageKeys []string, event *larkim.P2MessageReceiveV1) error {
	if event == nil || event.Event == nil || event.Event.Message == nil {
		log.V1.CtxError(ctx, "[genFeedbackDataFromEvent] event is nil")
		return errors.New("event is nil")
	}

	chatId := lo.FromPtr(event.Event.Message.ChatId)
	threadId := lo.FromPtr(event.Event.Message.ThreadId)
	messageId := lo.FromPtr(event.Event.Message.MessageId)
	createdTime := lo.FromPtr(event.Event.Message.CreateTime)
	createdTimeInt64, err := strconv.ParseInt(createdTime, 10, 64)
	if err != nil {
		log.V1.CtxError(ctx, "[addFeedbackToLarkSheet] failed to parse createdTime: %v", err)
		createdTimeInt64 = 0
	}
	createdTimeStr := time.Unix(createdTimeInt64/1000, 0).Format("2006-01-02 15:04:05")

	folderToken := h.NextOncallConfig.GetValue().OncallLarkFolderToken
	return h.LarkService.AddFeedbackToLarkSheet(ctx, folderToken, messageId, []any{message, senderName, createdTimeStr, chatId, threadId, messageId}, imageKeys)
}

// getLarkSenderUserName 根据飞书senderID(userID类型)，对齐字节云username
func (h *Handler) getLarkSenderUserName(ctx context.Context, senderID string) (string, error) {
	userInfo, err := h.LarkClient.ListLarkUsers(ctx, []string{senderID}, "user_id")
	if err != nil {
		log.V1.CtxError(ctx, "getLarkSenderUserName: failed to ListLarkUsers: %v", err)
		return "", err
	}
	if len(userInfo) == 0 {
		log.V1.CtxError(ctx, "getLarkSenderUserName: userInfo is empty, senderID: %s", senderID)
		return "", errors.New("userInfo is empty")
	}
	userEmail := lo.FromPtr(userInfo[0].Email)
	part := strings.Split(userEmail, "@")
	if len(part) != 2 {
		log.V1.CtxError(ctx, "getLarkSenderUserName: invalid email, senderID: %s, email: %s", senderID, userEmail)
		return "", errors.New("invalid email")
	}

	return part[0], nil
}
