package serverhandler

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kite/kitex/client/callopt"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/overpass/bits_ai_graph/kitex_gen/bits/ai/graph"
	"code.byted.org/overpass/bits_ai_graph/rpc/bits_ai_graph"
	"github.com/samber/lo"
)

func (h *Handler) SearchCodeRepo(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SearchCodeRepoRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "SearchCodeRepo req: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	codeRepos, total, err := h.CodeRepoService.SearchCodeRepo(ctx, req, user.NextCodeUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "[SearchCodeRepo] failed to search code repo: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to search code repo")
		return
	}

	c.JSON(http.StatusOK, nextagent.SearchCodeRepoResponse{
		CodeRepos: codeRepos,
		Total:     total,
	})
}

func (h *Handler) ListCodeRepo(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListCodeRepoRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "ListCodeRepo req: %v", req)

	codeRepos, total, err := h.CodeRepoService.ListCodeRepo(ctx, req)
	if err != nil {
		log.V1.CtxError(ctx, "[ListCodeRepo] failed to list code repo: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list code repo")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListCodeRepoResponse{
		CodeRepos: codeRepos,
		Total:     total,
	})
}

func (h *Handler) DeleteCodeRepo(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteCodeRepoRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "DeleteCodeRepo req: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.GetSpaceID()),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceDevResourceUpdate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	err = h.CodeRepoService.DeleteCodeRepo(ctx, req)
	if err != nil {
		log.V1.CtxError(ctx, "[DeleteCodeRepo] failed to delete code repo: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete code repo")
		return
	}

	go updateKnowledgeGraph(ctx, gslice.Map(req.GetCodeRepos(), func(repo *nextagent.CodeRepo) *graph.UpdateResource {
		return &graph.UpdateResource{
			ResourceType: graph.ResourceType_Code,
			Code: &graph.Code{
				RepoName: repo.GetRepoName(),
			},
			OperationType: graph.OperationType_Delete,
		}
	}), req.GetSpaceID(), user.Username)

	c.JSON(http.StatusOK, nextagent.DeleteCodeRepoResponse{})
}

func (h *Handler) UploadCodeRepo(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UploadCodeRepoRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "UploadCodeRepo req: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.GetSpaceID()),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceDevResourceUpdate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	err = h.CodeRepoService.UploadCodeRepo(ctx, req, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "[UploadCodeRepo] failed to upload code repo: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to upload code repo")
		return
	}

	go updateKnowledgeGraph(ctx, gslice.Map(req.GetCodeRepos(), func(repo *nextagent.CodeRepo) *graph.UpdateResource {
		return &graph.UpdateResource{
			ResourceType: graph.ResourceType_Code,
			Code: &graph.Code{
				RepoName: repo.GetRepoName(),
			},
			OperationType: graph.OperationType_Add,
		}
	}), req.GetSpaceID(), user.Username)

	c.JSON(http.StatusOK, nextagent.UploadCodeRepoResponse{})
}

func (h *Handler) SearchMeegoSpace(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SearchMeegoSpaceRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "SearchMeegoSpace req: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	meegoSpaces, err := h.PlatformConfigService.SearchMeegoSpace(ctx, req, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "[SearchMeegoSpace] failed to search meego space: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to search meego space")
		return
	}

	c.JSON(http.StatusOK, nextagent.SearchMeegoSpaceResponse{
		MeegoSpaces: meegoSpaces,
	})
}

func (h *Handler) SearchService(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SearchServiceRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "SearchService req: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	services, total, err := h.DevServiceService.SearchDevService(ctx, req, user.Username, user.CloudUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "[SearchService] failed to search service: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to search service")
		return
	}

	c.JSON(http.StatusOK, nextagent.SearchServiceResponse{
		Services: services,
		Total:    total,
	})
}

func (h *Handler) ListService(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListServiceRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "ListService req: %v", req)

	services, total, err := h.DevServiceService.ListDevService(ctx, req)
	if err != nil {
		log.V1.CtxError(ctx, "[SearchService] failed to search service: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to search service")
		return
	}

	c.JSON(http.StatusOK, nextagent.ListServiceResponse{
		Services: services,
		Total:    total,
	})
}

func (h *Handler) DeleteService(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteServiceRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "DeleteService req: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.GetSpaceID()),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceDevResourceUpdate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	err = h.DevServiceService.DeleteDevService(ctx, req)
	if err != nil {
		log.V1.CtxError(ctx, "[SearchService] failed to delete service: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete service")
		return
	}

	go updateKnowledgeGraph(ctx, gslice.Map(req.GetServices(), func(s *nextagent.Service) *graph.UpdateResource {
		return &graph.UpdateResource{
			ResourceType: graph.ResourceType_TceService,
			TceService: &graph.TceService{
				Psm:          s.GetName(),
				ControlPlane: graph.ControlPlane_CN,
			},
			OperationType: graph.OperationType_Delete,
		}
	}), req.GetSpaceID(), user.Username)

	c.JSON(http.StatusOK, nextagent.DeleteServiceResponse{})
}

func (h *Handler) UploadService(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UploadServiceRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "UploadService req: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.GetSpaceID()),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceDevResourceUpdate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	err = h.DevServiceService.CreateDevService(ctx, req.GetServices(), req.GetSpaceID(), user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "[UploadService] failed to create service: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create service")
		return
	}

	go updateKnowledgeGraph(ctx, gslice.Map(req.GetServices(), func(s *nextagent.Service) *graph.UpdateResource {
		return &graph.UpdateResource{
			ResourceType: graph.ResourceType_TceService,
			TceService: &graph.TceService{
				Psm:          s.GetName(),
				ControlPlane: graph.ControlPlane_CN,
			},
			OperationType: graph.OperationType_Add,
		}
	}), req.GetSpaceID(), user.Username)

	c.JSON(http.StatusOK, nextagent.UploadServiceResponse{})
}

func (h *Handler) GetPlatformConfig(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetPlatformConfigRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "GetPlatformConfig req: %v", req)

	platformConfig, err := h.PlatformConfigService.GetPlatformConfig(ctx, req.GetSpaceID())
	if err != nil {
		log.V1.CtxError(ctx, "[GetPlatformConfig] failed to get platform config: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get platform config")
		return
	}

	c.JSON(http.StatusOK, nextagent.GetPlatformConfigResponse{
		PlatformConfig: platformConfig,
	})
}

func (h *Handler) UpdatePlatformConfig(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdatePlatformConfigRequest](ctx, c)
	if req == nil {
		return
	}
	logs.V1.CtxInfo(ctx, "UpdatePlatformConfig req: %v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist || user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not auth")
		return
	}

	permRes, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            *user,
		ResourceExternalID: lo.ToPtr(req.GetSpaceID()),
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		Action:             entity.PermissionActionSpaceDevResourceUpdate,
	})
	if err != nil || permRes == nil || !permRes.Allowed {
		message := lo.TernaryF(err != nil, func() string {
			return err.Error()
		}, func() string {
			return "permission denied"
		})
		log.V1.CtxError(ctx, "failed to check permission: %v", message)
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "failed to check permission")
		return
	}

	err = h.PlatformConfigService.UpdatePlatformConfig(ctx, req, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "[UpdatePlatformConfig] failed to update platform config: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update platform config")
		return
	}

	c.JSON(http.StatusOK, nextagent.UpdatePlatformConfigResponse{})
}

func updateKnowledgeGraph(ctx context.Context, updateResources []*graph.UpdateResource, spaceID, username string) error {
	defer func() {
		if err := recover(); err != nil {
			logs.V1.CtxError(ctx, "[updateKnowledgeGraph]recover from panic: %v", err)
		}
	}()

	resp, err := bits_ai_graph.RawCall.CreateGraphUpdateTaskByAime(ctx, &graph.CreateGraphUpdateTaskByAimeRequest{
		Username:        username,
		UpdateResources: updateResources,
		InitGraph:       false,
		AimeSpaceId:     spaceID,
	}, callopt.WithRPCTimeout(30*time.Second))
	if err != nil {
		logs.V1.CtxError(ctx, "[updateKnowledgeGraph]failed to update knowledge graph, err:%v", err)
		return err
	}

	logs.V1.CtxInfo(ctx, "[updateKnowledgeGraph]update knowledge graph success, spaceID:%s, taskID:%s", spaceID, resp.GetTaskId())

	return nil
}
