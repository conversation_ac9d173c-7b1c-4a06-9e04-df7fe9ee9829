package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"github.com/pkg/errors"
)

type ListDevServiceOption struct {
	SpaceID  string
	Type     string
	PageNum  int32
	PageSize int32
}

func (d *DAO) ListDevService(ctx context.Context, opts ListDevServiceOption) ([]*po.NextDevServicePO, int64, error) {
	var res []*po.NextDevServicePO

	if opts.SpaceID == "" {
		return nil, 0, errors.New("SpaceID is required")
	}

	req := d.Conn.NewRequest(ctx).Model(&po.NextDevServicePO{}).Where("space_id = ?", opts.SpaceID)
	if opts.PageNum > 0 && opts.PageSize > 0 {
		req = req.Offset(int((opts.PageNum - 1) * opts.PageSize)).Limit(int(opts.PageSize))
	}

	if opts.Type != "" {
		req = req.Where("type = ?", opts.Type)
	}

	var total int64
	err := req.Count(&total).Error
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to count NextDevServicePO")
	}

	if err = req.Find(&res).Error; err != nil {
		return nil, 0, errors.WithMessage(err, "failed to list ListDevService")
	}

	return res, total, nil
}

func (d *DAO) CreateDevServices(ctx context.Context, devServices []*po.NextDevServicePO) error {
	if len(devServices) == 0 {
		return nil
	}

	err := d.Conn.NewRequest(ctx).Create(devServices).Error
	if err != nil {
		return errors.WithMessage(err, "failed to CreateDevServices")
	}

	return nil
}

func (d *DAO) DeleteDevServices(ctx context.Context, spaceID string, bizIDs []string) error {
	if len(bizIDs) == 0 {
		return nil
	}

	if err := d.Conn.NewRequest(ctx).Where("space_id =? and biz_id IN (?)", spaceID, bizIDs).Delete(&po.NextDevServicePO{}).Error; err != nil {
		return errors.WithMessage(err, "failed to delete dev service")
	}

	return nil
}
