// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_agent_config_version`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextAgentConfigVersionPO is agent config version manage.
type NextAgentConfigVersionPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is uniq id.
	//
	// index: uk_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// AgentConfigID is agent config id.
	//
	// index: uk_agent_config_id_version, priority: 1.
	//
	// index: idx_agent_config_id_enabled, priority: 1.
	//
	// index: idx_agent_config_id_status, priority: 1.
	//
	AgentConfigID string `gorm:"column:agent_config_id;size:40"`
	// Creator is creator username.
	Creator string `gorm:"column:creator;size:128"`
	// Version is agent_config_id + version.
	//
	// index: uk_agent_config_id_version, priority: 2.
	//
	Version int `gorm:"column:version"`
	// Status is status prepared, online, rollback.
	//
	// index: idx_agent_config_id_status, priority: 2.
	//
	Status string `gorm:"column:status;size:32"`
	// Enabled is 对应 Agent 版本的状态，1 开启 or 0 关闭.
	//
	// index: idx_agent_config_id_enabled, priority: 2.
	//
	Enabled int `gorm:"column:enabled"`
	// Description is description.
	Description string `gorm:"column:description;size:256"`
	// RuntimeConfig is runtime 配置信息.
	RuntimeConfig *datatypes.JSONType[NextAgentConfigVersionRuntimeConfig] `gorm:"column:runtime_config"`
	// PromptConfig is prompt 配置信息.
	PromptConfig *datatypes.JSONType[NextAgentConfigVersionPromptConfig] `gorm:"column:prompt_config"`
	// KnowledgesetConfig is knowledgeset_config 配置信息.
	KnowledgesetConfig *datatypes.JSONType[NextAgentConfigVersionKnowledgesetConfig] `gorm:"column:knowledgeset_config"`
	// CustomConfig is 自定义配置信息.
	CustomConfig *datatypes.JSONType[NextAgentConfigVersionCustomConfig] `gorm:"column:custom_config"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
	// CreateSource is 创建来源信息.
	CreateSource *string `gorm:"column:create_source;size:256"`
}

// TableName returns PO's corresponding DB table name: `next_agent_config_version`.
func (*NextAgentConfigVersionPO) TableName() string {
	return "next_agent_config_version"
}
