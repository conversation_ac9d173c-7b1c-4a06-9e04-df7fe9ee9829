// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_code_repo`.
package po

import (
	gorm "gorm.io/gorm"
	"time"
)

// NextCodeRepoPO is code repo.
type NextCodeRepoPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id"`
	// SpaceID is 空间 ID.
	//
	// index: idx_space_id, priority: 1.
	//
	SpaceID string `gorm:"column:space_id;size:40"`
	// Creator is 创建人.
	Creator string `gorm:"column:creator;size:64"`
	// RepoName is 仓库名称.
	//
	// index: idx_repo_name, priority: 1.
	//
	RepoName string `gorm:"column:repo_name;size:256"`
	// RepoID is 仓库 ID.
	//
	// index: idx_repo_id, priority: 1.
	//
	RepoID string `gorm:"column:repo_id;size:256"`
	// CreatedAt is 创建时间.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 更新时间.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is 删除时间.
	DeletedAt *gorm.DeletedAt `gorm:"column:deleted_at"`
}

// TableName returns PO's corresponding DB table name: `next_code_repo`.
func (*NextCodeRepoPO) TableName() string {
	return "next_code_repo"
}
