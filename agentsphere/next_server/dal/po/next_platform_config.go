// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_platform_config`.
package po

import (
	datatypes "gorm.io/datatypes"
	gorm "gorm.io/gorm"
	"time"
)

// NextPlatformConfigPO is platform config.
type NextPlatformConfigPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id"`
	// SpaceID is 空间 ID.
	//
	// index: uk_space_id, priority: 1.
	//
	SpaceID string `gorm:"column:space_id;size:40"`
	// Creator is 创建人.
	Creator string `gorm:"column:creator;size:64"`
	// MeegoSpaceList is 关联的 meego 空间链接列表.
	MeegoSpaceList *datatypes.JSONType[NextPlatformConfigMeegoSpaceList] `gorm:"column:meego_space_list"`
	// CreatedAt is 创建时间.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 更新时间.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is 删除时间.
	DeletedAt *gorm.DeletedAt `gorm:"column:deleted_at"`
}

// TableName returns PO's corresponding DB table name: `next_platform_config`.
func (*NextPlatformConfigPO) TableName() string {
	return "next_platform_config"
}
