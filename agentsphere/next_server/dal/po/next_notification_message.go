// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_notification_message`.
package po

import "time"

// NextNotificationMessagePO is 消息通知表.
type NextNotificationMessagePO struct {
	// ID is ID.
	ID int64 `gorm:"column:id"`
	// Uid is 消息的唯一标识符.
	//
	// index: uniq_idx_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:64"`
	// Title is 消息标题.
	Title string `gorm:"column:title;size:256"`
	// Content is 通知内容.
	Content string `gorm:"column:content;size:256"`
	// Link is 跳转链接.
	Link string `gorm:"column:link;size:256"`
	// LinkName is 跳转按钮名称.
	LinkName string `gorm:"column:link_name;size:256"`
	// Type is 消息类型: 通知notification/邀请invitation.
	Type string `gorm:"column:type;size:64"`
	// Creator is 消息创建者.
	Creator string `gorm:"column:creator;size:64"`
	// IsTop is 消息是否置顶.
	IsTop *bool `gorm:"column:is_top"`
	// CreatedAt is 创建时间 (Unix 时间戳).
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 更新时间 (Unix 时间戳).
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

// TableName returns PO's corresponding DB table name: `next_notification_message`.
func (*NextNotificationMessagePO) TableName() string {
	return "next_notification_message"
}
