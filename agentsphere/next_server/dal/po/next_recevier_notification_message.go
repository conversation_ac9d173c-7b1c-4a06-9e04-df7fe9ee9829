// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_receiver_notification_message`.
package po

import "time"

// NextReceiverNotificationMessagePO is 人员消息关联表.
type NextReceiverNotificationMessagePO struct {
	// ID is ID.
	ID int64 `gorm:"column:id"`
	// ReceiveType is 接收类型： personal 个人, department 部门，all 全体.
	//
	// index: idx_receiver_status, priority: 2.
	//
	ReceiveType string `gorm:"column:receive_type;size:64"`
	// Receiver is 接收者.
	//
	// index: idx_message_id_receiver, priority: 2.
	//
	// index: idx_receiver_status, priority: 1.
	//
	Receiver *string `gorm:"column:receiver;size:256"`
	// MessageID is 消息的唯一标识符.
	//
	// index: idx_message_id_receiver, priority: 1.
	//
	MessageID string `gorm:"column:message_id;size:64"`
	// Status is 消息状态：unread 未读, read 已读, recalled 已撤回.
	//
	// index: idx_receiver_status, priority: 3.
	//
	Status string `gorm:"column:status;size:64"`
	// CreatedAt is 创建时间 (Unix 时间戳).
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 更新时间 (Unix 时间戳).
	UpdatedAt time.Time `gorm:"column:updated_at"`
}

// TableName returns PO's corresponding DB table name: `next_receiver_notification_message`.
func (*NextReceiverNotificationMessagePO) TableName() string {
	return "next_receiver_notification_message"
}
