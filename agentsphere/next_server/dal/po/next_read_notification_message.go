// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_read_notification_message`.
package po

import "time"

// NextReadNotificationMessagePO is 已读消息关联表.
type NextReadNotificationMessagePO struct {
	// ID is ID.
	ID int64 `gorm:"column:id"`
	// Receiver is 接收者.
	//
	// index: idx_message_id_receiver, priority: 2.
	//
	Receiver *string `gorm:"column:receiver;size:256"`
	// MessageID is 消息的唯一标识符.
	//
	// index: idx_message_id_receiver, priority: 1.
	//
	MessageID string `gorm:"column:message_id;size:64"`
	// CreatedAt is 创建时间 (Unix 时间戳).
	CreatedAt time.Time `gorm:"column:created_at"`
}

// TableName returns PO's corresponding DB table name: `next_read_notification_message`.
func (*NextReadNotificationMessagePO) TableName() string {
	return "next_read_notification_message"
}
