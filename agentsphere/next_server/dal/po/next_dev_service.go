// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_dev_service`.
package po

import (
	gorm "gorm.io/gorm"
	"time"
)

// NextDevServicePO is service.
type NextDevServicePO struct {
	// ID is ID.
	ID int64 `gorm:"column:id"`
	// SpaceID is 空间 ID.
	//
	// index: idx_space_id_type, priority: 1.
	//
	SpaceID string `gorm:"column:space_id;size:40"`
	// Creator is 创建人.
	Creator string `gorm:"column:creator;size:64"`
	// BizID is 服务唯一ID.
	//
	// index: idx_biz_id, priority: 1.
	//
	BizID string `gorm:"column:biz_id;size:256"`
	// Type is 服务类型.
	//
	// index: idx_space_id_type, priority: 2.
	//
	Type string `gorm:"column:type;size:64"`
	// Name is 服务名称.
	Name string `gorm:"column:name;size:256"`
	// ControlPlane is 服务控制面.
	ControlPlane string `gorm:"column:control_plane;size:64"`
	// CreatedAt is 创建时间.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 更新时间.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is 删除时间.
	DeletedAt *gorm.DeletedAt `gorm:"column:deleted_at"`
}

// TableName returns PO's corresponding DB table name: `next_dev_service`.
func (*NextDevServicePO) TableName() string {
	return "next_dev_service"
}
