package po

import (
	"encoding/json"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

type NextSessionContext = entity.SessionContext

type NextEventEventData = entity.EventData

type NextMessageContent = entity.MessageContent

type NextMessageAttachments = []*entity.Attachment

type NextMessageOptions = entity.MessageOptions

type NextTaskContext = entity.TaskContext

type NextSessionRuntimeMetadata = entity.SessionRuntimeMetadata

type NextArtifactFileMetas = entity.FileMetas

type NextArtifactMetadata = *json.RawMessage // no unmarshal until artifact type is determined

type NextSessionCollectionAttachments = []*entity.Attachment

type NextReplaySnapshotMeta = entity.ReplaySnapshotMeta

type NextTemplateVariableFormValue = entity.TemplateFormValue

type NextAgentConfigVersionCustomConfig = *json.RawMessage

type NextAgentConfigVersionPromptConfig = entity.PromptConfig

type NextAgentConfigVersionRuntimeConfig = entity.RuntimeConfig

type NextPromptVersionVariable = entity.PromptVariable

type NextSessionCollectionRunFeedback = entity.SessionCollectionRunFeedback

type TemplateFileMetadata = entity.TemplateFileMetadata

type TemplateVersionPromptVariables = entity.TemplateVariableSchemaList

type TemplateVersionPlanSteps = []string

type TemplateVersionExpSop = agententity.ExpSOP

type TemplateVersionSupportMcps = []*entity.MCPKey

type DocumentFailedReason = entity.DocumentFailedReason

type SegmentContent = entity.SegmentContent

type UserSettingsLoginInfo = entity.UserSettingsLoginInfo

type NextMessageMentions = []*agententity.Mention

type NextAgentConfigVersionKnowledgesetConfig = entity.KnowledgesetConfig

type NextKnowledgeTags = entity.NextKnowledgeTags

type NextKnowledgeContent = entity.NextKnowledgeContent

type NextDeploymentMetadata = entity.DeploymentMetadata

type NextKnowledgeRecallMethod = []string

type NextKnowledgesetTags = []string

type UserSettingsKeyboardShortcut = entity.KeyboardShortcut

type NextSpaceConfigBaseConfig = entity.SpaceBaseConfig
type NextAgentDeployExtraInfo = entity.DeployExtraInfo

type NextPlatformConfigMeegoSpaceList = []*entity.MeegoSpace
