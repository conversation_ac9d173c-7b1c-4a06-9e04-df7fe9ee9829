// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_message`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextMessagePO is message.
type NextMessagePO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: idx_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// SessionID is session ID.
	//
	// index: idx_session_id_role, priority: 1.
	//
	SessionID string `gorm:"column:session_id;size:40"`
	// TaskID is task ID.
	//
	// index: idx_task_id, priority: 1.
	//
	TaskID string `gorm:"column:task_id;size:40"`
	// Role is message role.
	//
	// index: idx_session_id_role, priority: 2.
	//
	Role string `gorm:"column:role;size:20"`
	// Content is message content.
	Content datatypes.JSONType[NextMessageContent] `gorm:"column:content"`
	// Creator is session creator.
	Creator string `gorm:"column:creator;size:64"`
	// Attachments is message attachments.
	Attachments datatypes.JSONType[NextMessageAttachments] `gorm:"column:attachments"`
	// Options is message options.
	Options *datatypes.JSONType[NextMessageOptions] `gorm:"column:options"`
	// Mentions is message mentions.
	Mentions *datatypes.JSONType[NextMessageMentions] `gorm:"column:mentions"`
	// Status is status 0 sent, 1 wait.
	Status int `gorm:"column:status"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `next_message`.
func (*NextMessagePO) TableName() string {
	return "next_message"
}
