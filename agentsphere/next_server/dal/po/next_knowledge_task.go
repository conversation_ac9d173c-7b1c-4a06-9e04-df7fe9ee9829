// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_knowledge_task`.
package po

import (
	gorm "gorm.io/gorm"
	"time"
)

// NextKnowledgeTaskPO is 知识库异步任务表.
type NextKnowledgeTaskPO struct {
	// ID is 主键.
	ID int64 `gorm:"column:id"`
	// Uid is uid.
	//
	// index: uk_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// TaskType is 导入 wiki 等.
	TaskType string `gorm:"column:task_type;size:40"`
	// DatasetID is 知识库 id.
	//
	// index: idx_dataset_id, priority: 1.
	//
	DatasetID string `gorm:"column:dataset_id;size:40"`
	// TaskStatus is 任务状态.
	//
	// index: idx_status, priority: 1.
	//
	TaskStatus string `gorm:"column:task_status;size:40"`
	// Params is 任务操作参数.
	Params *string `gorm:"column:params"`
	// Creator is 创建人.
	Creator string `gorm:"column:creator;size:64"`
	// FailedCount is 失败次数.
	FailedCount int `gorm:"column:failed_count"`
	// CreatedAt is 创建时间.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 更新时间.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is 删除时间.
	DeletedAt *gorm.DeletedAt `gorm:"column:deleted_at"`
}

// TableName returns PO's corresponding DB table name: `next_knowledge_task`.
func (*NextKnowledgeTaskPO) TableName() string {
	return "next_knowledge_task"
}
