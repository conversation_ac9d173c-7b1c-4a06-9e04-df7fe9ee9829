package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"github.com/pkg/errors"
)

type ListKnowledgeTaskOption struct {
	DatasetID string
	Status    []string
}

func (d *DAO) ListKnowledgeTask(ctx context.Context, opts ListKnowledgeTaskOption) ([]*po.NextKnowledgeTaskPO, error) {
	var res []*po.NextKnowledgeTaskPO

	if opts.DatasetID == "" {
		return nil, errors.New("datasetID is required")
	}

	req := d.Conn.NewRequest(ctx).Where("dataset_id =?", opts.DatasetID)

	if len(opts.Status) > 0 {
		req = req.Where("task_status IN ?", opts.Status)
	}

	if err := req.Find(&res).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to list ListKnowledgeTask")
	}

	return res, nil
}

func (d *DAO) CreateKnowledgeTask(ctx context.Context, knowledgeTask []*po.NextKnowledgeTaskPO) error {
	if len(knowledgeTask) == 0 {
		return nil
	}

	err := d.Conn.NewRequest(ctx).Create(knowledgeTask).Error
	if err != nil {
		return errors.WithMessage(err, "failed to CreateKnowledgeTask")
	}

	return nil
}

type UpdateKnowledgeTaskOption struct {
	UID       string
	Status    string
	FailedCnt int
}

func (d *DAO) UpdateKnowledgeTask(ctx context.Context, opts UpdateKnowledgeTaskOption) error {
	if opts.UID == "" {
		return errors.New("taskID is required")
	}

	updateMap := map[string]any{}
	if opts.Status != "" {
		updateMap["task_status"] = opts.Status
	}

	if err := d.Conn.NewRequest(ctx).Model(&po.NextKnowledgeTaskPO{}).Where("uid =?", opts.UID).Updates(updateMap).Error; err != nil {
		return errors.WithMessage(err, "failed to UpdateKnowledgeTask")
	}

	return nil
}

func (d *DAO) GetKnowledgeTask(ctx context.Context, uid string) (*po.NextKnowledgeTaskPO, error) {
	var res *po.NextKnowledgeTaskPO

	if err := d.Conn.NewRequest(ctx).Model(&po.NextKnowledgeTaskPO{}).Where("uid =?", uid).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to get task")
	}

	return res, nil
}
