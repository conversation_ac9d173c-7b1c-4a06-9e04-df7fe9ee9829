package dal

import (
	"context"
	"testing"

	"github.com/samber/lo"
	"gotest.tools/v3/assert"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

func TestDAO_CreateUserSettings(t *testing.T) {
	dao := NewMockDAO(t)
	var (
		ctx      = context.Background()
		username = "test_user"
	)
	settings, err := dao.CreateUserSettings(ctx, &entity.UserSettings{
		ID:        "test_1",
		Username:  username,
		Locale:    entity.UserSettingsLocaleEn,
		LoginInfo: []*entity.LoginInfo{{Website: entity.LoginWebsiteTypeBytedance, KeepLogin: true}},
	})
	assert.NilError(t, err)
	assert.Equal(t, settings.ID, "test_1")

	settings, err = dao.GetUserSettingsByUsername(ctx, "test_user", false)
	assert.NilError(t, err)
	assert.Equal(t, settings.ID, "test_1")
	assert.Equal(t, settings.Username, "test_user")
	assert.Equal(t, settings.Locale, entity.UserSettingsLocaleEn)
	assert.Equal(t, len(settings.LoginInfo), 1)
	assert.Equal(t, settings.LoginInfo[0].KeepLogin, true)

	settings, err = dao.UpdateUserSettings(ctx, settings.ID, UpdateUserSettingsOption{
		Locale:           lo.ToPtr(entity.UserSettingsLocaleZh),
		LoginInfo:        &entity.UserSettingsLoginInfo{{Website: entity.LoginWebsiteTypeBytedance, KeepLogin: false}},
		KeyboardShortcut: &entity.KeyboardShortcut{Mac: map[string]*entity.Shortcut{"send": {Keys: []string{"ctrl"}}}},
	})
	assert.NilError(t, err)
	assert.Equal(t, settings.Locale, entity.UserSettingsLocaleZh)
	assert.Equal(t, settings.LoginInfo[0].KeepLogin, false)
	assert.DeepEqual(t, settings.KeyboardShortcut.Mac, map[string]*entity.Shortcut{"send": {Keys: []string{"ctrl"}}})
}
