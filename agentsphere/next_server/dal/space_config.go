package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

type CreateOrUpdateSpaceConfigOptions struct {
	SpaceID    string
	BaseConfig *entity.SpaceBaseConfig
}

// CreateOrUpdateSpaceConfig 存在大量已经创建，但是没有 config 的 space，因此直接支持一个 CreateOrUpdate 的接口，兼容该场景
func (d *DAO) CreateOrUpdateSpaceConfig(ctx context.Context, opt CreateOrUpdateSpaceConfigOptions) (*entity.SpaceConfig, error) {
	// GetSpaceConfig
	spaceConfig, err := d.GetSpaceConfig(ctx, opt.SpaceID, false)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		// 不存在则创建
		var baseConfig *datatypes.JSONType[entity.SpaceBaseConfig]
		if opt.BaseConfig != nil {
			baseConfig = lo.ToPtr(datatypes.NewJSONType(*opt.BaseConfig))
		}

		p := &po.NextSpaceConfigPO{
			SpaceID:    opt.SpaceID,
			BaseConfig: baseConfig,
		}

		res := d.Conn.NewRequest(ctx).Create(p)
		if res.Error != nil {
			return nil, res.Error
		}
		return getSpaceConfigFromPO(p), nil
	}

	// 更新
	updater := make(map[string]interface{})
	if opt.BaseConfig != nil {
		updater["base_config"] = lo.ToPtr(datatypes.NewJSONType(opt.BaseConfig))
	}

	if len(updater) == 0 {
		return spaceConfig, nil
	}

	res := d.Conn.NewRequest(ctx).Model(&po.NextSpaceConfigPO{}).Where("space_id = ?", opt.SpaceID).Updates(updater)
	if res.Error != nil {
		return nil, res.Error
	}
	if res.RowsAffected <= 0 {
		return nil, errors.New("no rows affected")
	}
	return d.GetSpaceConfig(ctx, opt.SpaceID, true)
}

func (d *DAO) GetSpaceConfig(ctx context.Context, spaceID string, sync bool) (*entity.SpaceConfig, error) {
	p := &po.NextSpaceConfigPO{}
	req := d.Conn.NewRequest(ctx).Where("space_id = ?", spaceID)
	if sync {
		req = req.Clauses(dbresolver.Write) // Read from primary if sync is true
	}
	res := req.First(p)
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, res.Error
		}
		return nil, errors.WithMessage(res.Error, "failed to get space config")
	}
	return getSpaceConfigFromPO(p), nil
}

func (d *DAO) BatchGetSpaceConfigBySpaceIDs(ctx context.Context, ids []string) (map[string]*entity.SpaceConfig, error) {
	var pos []*po.NextSpaceConfigPO
	req := d.Conn.NewRequest(ctx).Where("space_id in (?)", ids)
	if err := req.Find(&pos).Error; err != nil {
		return nil, err
	}
	result := make(map[string]*entity.SpaceConfig)
	for _, p := range pos {
		result[p.SpaceID] = getSpaceConfigFromPO(p)
	}
	return result, nil
}

func getSpaceConfigFromPO(p *po.NextSpaceConfigPO) *entity.SpaceConfig {
	res := &entity.SpaceConfig{
		SpaceID:   p.SpaceID,
		CreatedAt: p.CreatedAt,
		UpdatedAt: p.UpdatedAt,
	}
	if p.BaseConfig != nil {
		res.BaseConfig = pointer.To(p.BaseConfig.Data())
	}

	return res
}
