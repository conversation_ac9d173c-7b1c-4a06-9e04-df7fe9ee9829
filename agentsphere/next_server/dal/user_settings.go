package dal

import (
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/gopkg/context"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/plugin/dbresolver"
)

func (d *DAO) CreateUserSettings(ctx context.Context, settings *entity.UserSettings) (*entity.UserSettings, error) {
	p := getUserSettingsPOFromEntity(settings)
	res := d.Conn.NewRequest(ctx).Create(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getUserSettingsFromPO(p), nil
}

func (d *DAO) GetUserSettingsByUsername(ctx context.Context, username string, sync bool) (*entity.UserSettings, error) {
	p := &po.UserSettingsPO{}
	req := d.Conn.NewRequest(ctx)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.Where("username =?", username).First(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getUserSettingsFromPO(p), nil
}

type UpdateUserSettingsOption struct {
	Locale           *string
	LoginInfo        *entity.UserSettingsLoginInfo
	KeyboardShortcut *entity.KeyboardShortcut
}

func (d *DAO) UpdateUserSettings(ctx context.Context, id string, opt UpdateUserSettingsOption) (*entity.UserSettings, error) {
	updates := make(map[string]interface{})
	if opt.Locale != nil {
		updates["locale"] = *opt.Locale
	}
	if opt.LoginInfo != nil {
		updates["login_info"] = datatypes.NewJSONType(*opt.LoginInfo)
	}
	if opt.KeyboardShortcut != nil {
		updates["keyboard_shortcut"] = datatypes.NewJSONType(*opt.KeyboardShortcut)
	}
	p := &po.UserSettingsPO{}
	res := d.Conn.NewRequest(ctx).Model(p).Where("uid = ?", id).Updates(updates)
	if res.Error != nil {
		return nil, res.Error
	}
	return getUserSettingsFromPO(p), nil
}

func getUserSettingsPOFromEntity(settings *entity.UserSettings) *po.UserSettingsPO {
	return &po.UserSettingsPO{
		Uid:              settings.ID,
		Username:         settings.Username,
		Locale:           settings.Locale,
		LoginInfo:        datatypes.NewJSONType(settings.LoginInfo),
		KeyboardShortcut: lo.ToPtr(datatypes.NewJSONType(settings.KeyboardShortcut)),
	}
}

func getUserSettingsFromPO(po *po.UserSettingsPO) *entity.UserSettings {
	e := &entity.UserSettings{
		ID:        po.Uid,
		Username:  po.Username,
		Locale:    po.Locale,
		LoginInfo: po.LoginInfo.Data(),
		CreatedAt: po.CreatedAt,
		UpdatedAt: po.UpdatedAt,
	}
	if po.KeyboardShortcut != nil {
		e.KeyboardShortcut = po.KeyboardShortcut.Data()
	}
	return e
}
