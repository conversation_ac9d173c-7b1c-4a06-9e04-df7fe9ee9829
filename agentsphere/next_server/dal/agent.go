package dal

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/plugin/dbresolver"
)

type GetAgentOption struct {
	ID   string
	Sync bool
}

func (d *DAO) GetAgent(ctx context.Context, opt GetAgentOption) (*entity.Agent, error) {
	req := d.Conn.NewRequest(ctx).Where("uid = ?", opt.ID)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	p := new(po.NextAgentPO)
	if err := req.Take(p).Error; err != nil {
		return nil, err
	}
	return getAgentFromPO(p)
}

type UpdateAgentOption struct {
	ID          string
	Name        *string
	Description *string
}

func (d *DAO) UpdateAgent(ctx context.Context, opt UpdateAgentOption) (*entity.Agent, error) {
	if opt.ID == "" {
		return nil, errors.New("ID is required")
	}

	updateMap := map[string]any{}
	if opt.Name != nil {
		updateMap["name"] = *opt.Name
	}
	if opt.Description != nil {
		updateMap["description"] = *opt.Description
	}

	res := d.Conn.NewRequest(ctx).Model(&po.NextAgentPO{}).Where("uid = ?", opt.ID).Updates(updateMap)
	if res.Error != nil {
		return nil, res.Error
	}

	return d.GetAgent(ctx, GetAgentOption{ID: opt.ID, Sync: true})
}

func (d *DAO) DeleteAgent(ctx context.Context, id string) error {
	err := d.Conn.NewRequest(ctx).Where("uid = ?", id).Delete(&po.NextAgentPO{}).Error
	if err != nil {
		return err
	}
	return nil
}

type CreateAgentOption struct {
	ID          string
	Creator     string
	Name        string
	Description string
}

func (d *DAO) CreateAgent(ctx context.Context, opt CreateAgentOption) (*entity.Agent, error) {
	p := &po.NextAgentPO{
		Uid:         opt.ID,
		Creator:     opt.Creator,
		Name:        opt.Name,
		Description: opt.Description,
	}

	if err := d.Conn.NewRequest(ctx).Create(p).Error; err != nil {
		return nil, err
	}
	return getAgentFromPO(p)
}

type ListAgentOption struct {
	Creator *string
	Name    *string
	Limit   int
	Offset  int
}

func (d *DAO) ListAgents(ctx context.Context, opt ListAgentOption) (int64, []*entity.Agent, error) {
	var agents []*po.NextAgentPO

	req := d.Conn.NewRequest(ctx).Model(&po.NextAgentPO{})
	if opt.Creator != nil {
		req = req.Where("creator = ?", *opt.Creator)
	}
	if opt.Name != nil {
		req = req.Where("name like ?", "%"+*opt.Name+"%")
	}

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	if err := req.Offset(opt.Offset).Limit(opt.Limit).Find(&agents).Error; err != nil {
		return 0, nil, err
	}

	return total, lo.Map(agents, func(item *po.NextAgentPO, index int) *entity.Agent {
		a, _ := getAgentFromPO(item)
		return a
	}), nil
}

func getAgentFromPO(p *po.NextAgentPO) (*entity.Agent, error) {
	if p == nil {
		return nil, errors.New("get agent entity from empty PO")
	}
	return &entity.Agent{
		ID:          p.Uid,
		Creator:     p.Creator,
		Name:        p.Name,
		Description: p.Description,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}, nil
}

type GetAgentConfigOption struct {
	ID      *string
	AgentID *string
	Type    *entity.AgentConfigType
	Sync    bool
}

func (d *DAO) GetAgentConfig(ctx context.Context, opt GetAgentConfigOption) (*entity.AgentConfig, error) {
	req := d.Conn.NewRequest(ctx)
	if opt.ID != nil {
		req = req.Where("uid = ?", *opt.ID)
	}
	if opt.AgentID != nil {
		req = req.Where("agent_id = ?", *opt.AgentID)
	}
	if opt.Type != nil {
		req = req.Where("type = ?", *opt.Type)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	p := new(po.NextAgentConfigPO)
	if err := req.Take(p).Error; err != nil {
		return nil, err
	}
	return getAgentConfigFromPO(p)
}

type CreateAgentConfigOption struct {
	ID          string
	AgentID     string
	Type        entity.AgentConfigType
	Creator     string
	Name        string
	Description string
}

func (d *DAO) CreateAgentConfig(ctx context.Context, opt CreateAgentConfigOption) (*entity.AgentConfig, error) {
	p := &po.NextAgentConfigPO{
		Uid:         opt.ID,
		AgentID:     opt.AgentID,
		Creator:     opt.Creator,
		Type:        string(opt.Type),
		Name:        opt.Name,
		Description: opt.Description,
	}
	if err := d.Conn.NewRequest(ctx).Create(p).Error; err != nil {
		return nil, err
	}
	return getAgentConfigFromPO(p)
}

type UpdateAgentConfigOption struct {
	ID          string
	Name        *string
	Description *string
}

func (d *DAO) UpdateAgentConfig(ctx context.Context, opt UpdateAgentConfigOption) (*entity.AgentConfig, error) {
	if opt.ID == "" {
		return nil, errors.New("ID is required")
	}

	updateMap := map[string]any{}
	if opt.Name != nil {
		updateMap["name"] = *opt.Name
	}
	if opt.Description != nil {
		updateMap["description"] = *opt.Description
	}

	res := d.Conn.NewRequest(ctx).Model(&po.NextAgentConfigPO{}).Where("uid = ?", opt.ID).Updates(updateMap)
	if res.Error != nil {
		return nil, res.Error
	}

	return d.GetAgentConfig(ctx, GetAgentConfigOption{ID: &opt.ID, Sync: true})
}

func (d *DAO) DeleteAgentConfig(ctx context.Context, id string) error {
	err := d.Conn.NewRequest(ctx).Where("uid = ?", id).Delete(&po.NextAgentConfigPO{}).Error
	if err != nil {
		return err
	}
	return nil
}

type CountAgentConfigOption struct {
	AgentID *string
	Type    *entity.AgentConfigType
}

func (d *DAO) CountAgentConfigs(ctx context.Context, opt CountAgentConfigOption) (int64, error) {
	p := &po.NextAgentConfigPO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	if opt.AgentID != nil {
		req = req.Where("agent_id = ?", *opt.AgentID)
	}
	if opt.Type != nil {
		req = req.Where("type = ?", *opt.Type)
	}

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, err
	}

	return total, nil
}

type ListAgentConfigOption struct {
	AgentID *string
	Creator *string
	Type    []*entity.AgentConfigType
	Name    *string
	Offset  int
	Limit   int
}

func (d *DAO) ListAgentConfigs(ctx context.Context, opt ListAgentConfigOption) (int64, []*entity.AgentConfig, error) {
	var agentConfigs []*po.NextAgentConfigPO

	req := d.Conn.NewRequest(ctx).Model(&po.NextAgentConfigPO{})
	if opt.AgentID != nil {
		req = req.Where("agent_id = ?", *opt.AgentID)
	}
	if opt.Creator != nil {
		req = req.Where("creator = ?", *opt.Creator)
	}
	if opt.Name != nil {
		req = req.Where("name like ?", "%"+*opt.Name+"%")
	}
	if len(opt.Type) > 0 {
		req = req.Where("type in (?)", opt.Type)
	}

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	if err := req.Offset(opt.Offset).Limit(opt.Limit).Order("id DESC").Find(&agentConfigs).Error; err != nil {
		return 0, nil, err
	}

	return total, lo.Map(agentConfigs, func(item *po.NextAgentConfigPO, index int) *entity.AgentConfig {
		a, _ := getAgentConfigFromPO(item)
		return a
	}), nil
}

func getAgentConfigFromPO(p *po.NextAgentConfigPO) (*entity.AgentConfig, error) {
	if p == nil {
		return nil, errors.New("get agent config entity from empty PO")
	}
	return &entity.AgentConfig{
		ID:          p.Uid,
		AgentID:     p.AgentID,
		Type:        entity.ParseAgentType(p.Type),
		Name:        p.Name,
		Description: p.Description,
		Creator:     p.Creator,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}, nil
}

type CreateAgentConfigVersionOption struct {
	ID                 string
	AgentConfigID      string
	Description        string
	Creator            string
	Version            int
	Enable             bool
	Status             entity.AgentConfigStatus
	RuntimeConfig      entity.RuntimeConfig
	CustomConfig       entity.CustomConfig
	PromptConfig       entity.PromptConfig
	KnowledgesetConfig entity.KnowledgesetConfig
	CreateSource       *string
}

func (d *DAO) CreateAgentConfigVersion(ctx context.Context, opt CreateAgentConfigVersionOption) (*entity.AgentConfigVersion, error) {
	customConfig, err := json.Marshal(opt.CustomConfig)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal custom config")
	}

	p := &po.NextAgentConfigVersionPO{
		Uid:                opt.ID,
		AgentConfigID:      opt.AgentConfigID,
		Creator:            opt.Creator,
		Version:            opt.Version,
		Status:             string(opt.Status),
		Enabled:            lo.Ternary(opt.Enable, 1, 0),
		Description:        opt.Description,
		RuntimeConfig:      lo.ToPtr(datatypes.NewJSONType(opt.RuntimeConfig)),
		PromptConfig:       lo.ToPtr(datatypes.NewJSONType(opt.PromptConfig)),
		CustomConfig:       lo.ToPtr(datatypes.NewJSONType(lo.ToPtr(json.RawMessage(customConfig)))),
		KnowledgesetConfig: lo.ToPtr(datatypes.NewJSONType(opt.KnowledgesetConfig)),
	}
	if opt.CreateSource != nil {
		p.CreateSource = opt.CreateSource
	}
	if err := d.Conn.NewRequest(ctx).Create(p).Error; err != nil {
		return nil, err
	}
	return getAgentConfigVersionFromPO(p)
}

type GetAgentConfigVersionOption struct {
	ID            *string
	Version       *int
	AgentConfigID *string
	Status        *entity.AgentConfigStatus
	Sync          bool
	CreateSource  *string
}

func (d *DAO) GetAgentConfigVersion(ctx context.Context, opt GetAgentConfigVersionOption) (*entity.AgentConfigVersion, error) {
	req := d.Conn.NewRequest(ctx)
	if opt.ID != nil {
		req = req.Where("uid = ?", *opt.ID)
	}
	if opt.AgentConfigID != nil {
		req = req.Where("agent_config_id = ?", *opt.AgentConfigID)
	}
	if opt.Version != nil {
		req = req.Where("version = ?", *opt.Version)
	}
	if opt.Status != nil {
		req = req.Where("status = ?", *opt.Status)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	if opt.CreateSource != nil {
		req = req.Where("create_source = ?", *opt.CreateSource)
	}

	p := new(po.NextAgentConfigVersionPO)
	if err := req.Take(p).Error; err != nil {
		return nil, err
	}
	return getAgentConfigVersionFromPO(p)
}

type UpdateAgentConfigVersionOption struct {
	ID                 string
	Enabled            *bool
	Description        *string
	RuntimeConfig      *entity.RuntimeConfig
	CustomConfig       *entity.CustomConfig
	PromptConfig       *entity.PromptConfig
	KnowledgesetConfig *entity.KnowledgesetConfig
	Status             *entity.AgentConfigStatus
	UpdatedAt          *time.Time
}

func (d *DAO) UpdateAgentConfigVersion(ctx context.Context, opt UpdateAgentConfigVersionOption) (*entity.AgentConfigVersion, error) {
	if opt.ID == "" {
		return nil, errors.New("ID is required")
	}

	updateMap := map[string]any{}
	if opt.Description != nil {
		updateMap["description"] = *opt.Description
	}
	if opt.Enabled != nil {
		updateMap["enabled"] = lo.Ternary(*opt.Enabled, 1, 0)
	}
	if opt.RuntimeConfig != nil {
		updateMap["runtime_config"] = lo.ToPtr(datatypes.NewJSONType(opt.RuntimeConfig))
	}
	if opt.CustomConfig != nil {
		customConfig, err := json.Marshal(opt.CustomConfig)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to marshal custom config")
		}
		updateMap["custom_config"] = lo.ToPtr(datatypes.NewJSONType(lo.ToPtr(json.RawMessage(customConfig))))
	}
	if opt.PromptConfig != nil {
		updateMap["prompt_config"] = lo.ToPtr(datatypes.NewJSONType(opt.PromptConfig))
	}
	if opt.KnowledgesetConfig != nil {
		updateMap["knowledgeset_config"] = lo.ToPtr(datatypes.NewJSONType(opt.KnowledgesetConfig))
	}
	if opt.Status != nil {
		updateMap["status"] = *opt.Status
	}

	req := d.Conn.NewRequest(ctx).Model(&po.NextAgentConfigVersionPO{}).Where("uid = ?", opt.ID)
	if opt.UpdatedAt != nil {
		req.Where("updated_at = ?", *opt.UpdatedAt)
	}
	res := req.Updates(updateMap)
	if res.Error != nil {
		return nil, res.Error
	}

	if res.RowsAffected <= 0 {
		return nil, ErrRowsAffectedZero
	}

	return d.GetAgentConfigVersion(ctx, GetAgentConfigVersionOption{ID: &opt.ID, Sync: true})
}

type GetLatestAgentConfigVersionOption struct {
	AgentConfigID string
	Enable        *bool
}

func (d *DAO) GetLatestAgentConfigVersion(ctx context.Context, opt GetLatestAgentConfigVersionOption) (*entity.AgentConfigVersion, error) {
	req := d.Conn.NewRequest(ctx).Where("agent_config_id = ?", opt.AgentConfigID)
	req = req.Clauses(dbresolver.Write)
	if opt.Enable != nil {
		req = req.Where("enabled = ?", lo.Ternary(*opt.Enable, 1, 0))
	}

	p := new(po.NextAgentConfigVersionPO)
	if err := req.Order("version desc").First(p).Error; err != nil {
		return nil, err
	}
	return getAgentConfigVersionFromPO(p)
}

type ListAgentConfigVersionOption struct {
	AgentConfigID *string
	Creator       *string
	Enabled       *bool
	Status        []*entity.AgentConfigStatus
	Offset        int
	Limit         int
}

func (d *DAO) ListAgentConfigVersion(ctx context.Context, opt ListAgentConfigVersionOption) (int64, []*entity.AgentConfigVersion, error) {
	var agents []*po.NextAgentConfigVersionPO

	req := d.Conn.NewRequest(ctx).Model(&po.NextAgentConfigVersionPO{})
	if opt.AgentConfigID != nil {
		req = req.Where("agent_config_id = ?", *opt.AgentConfigID)
	}
	if opt.Creator != nil {
		req = req.Where("creator = ?", *opt.Creator)
	}
	if opt.Enabled != nil {
		req = req.Where("enabled = ?", lo.Ternary(*opt.Enabled, 1, 0))
	}
	if len(opt.Status) > 0 {
		req = req.Where("status in (?)", opt.Status)
	}

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	if err := req.Offset(opt.Offset).Limit(opt.Limit).Order("id desc").Find(&agents).Error; err != nil {
		return 0, nil, err
	}

	return total, lo.Map(agents, func(item *po.NextAgentConfigVersionPO, index int) *entity.AgentConfigVersion {
		a, _ := getAgentConfigVersionFromPO(item)
		return a
	}), nil
}

type CreateAgentDeployOption struct {
	ID                         string
	AgentConfigID              string
	AgentConfigVersionID       string
	Actor                      string
	SourceStatus               entity.AgentConfigStatus
	TargetStatus               entity.AgentConfigStatus
	DeleteRelationIDs          []string
	CreateRelationOptions      []CreateAgentDeployRelationOption
	Status                     entity.AgentDeployStatus
	WorkflowID                 int64
	ExtraInfo                  *entity.DeployExtraInfo
	AgentConfigVersionOnlineID *string
}

func (d *DAO) CreateAgentDeploy(ctx context.Context, opt CreateAgentDeployOption) error {
	return d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		// 如果 TargetStatus 为 Online 则修改老的线上节点
		if opt.TargetStatus == entity.AgentConfigStatusOnline {
			updateOldMap := map[string]any{
				"status": entity.AgentConfigStatusCreated,
			}
			if err := tx.Model(&po.NextAgentConfigVersionPO{}).
				Where("agent_config_id = ?", opt.AgentConfigID).
				Where("status = ?", entity.AgentConfigStatusOnline).
				Updates(updateOldMap).Error; err != nil {
				return err
			}

			updateNewMap := map[string]any{
				"status": entity.AgentConfigStatusOnline,
			}
			if err := tx.Model(&po.NextAgentConfigVersionPO{}).
				Where("uid = ?", opt.AgentConfigVersionID).
				Updates(updateNewMap).Error; err != nil {
				return err
			}
		}

		// 记录状态
		p := &po.NextAgentDeployPO{
			Uid:                  opt.ID,
			AgentConfigID:        opt.AgentConfigID,
			Actor:                opt.Actor,
			AgentConfigVersionID: opt.AgentConfigVersionID,
			SourceStatus:         string(opt.SourceStatus),
			TargetStatus:         string(opt.TargetStatus),
			Status:               lo.ToPtr(string(opt.Status)),
			WorkflowID:           &opt.WorkflowID,
		}

		if opt.ExtraInfo != nil {
			p.ExtraInfo = lo.ToPtr(datatypes.NewJSONType(*opt.ExtraInfo))
		}

		if err := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "uid"}},
			DoUpdates: clause.AssignmentColumns([]string{"extra_info", "status"}),
		}).Create(p).Error; err != nil {
			return err
		}

		// 新建关联关系
		if len(opt.CreateRelationOptions) > 0 {
			if err := tx.Create(lo.Map(opt.CreateRelationOptions, func(item CreateAgentDeployRelationOption, index int) po.NextAgentDeployRelationPO {
				return po.NextAgentDeployRelationPO{
					Uid:                  item.ID,
					AgentConfigID:        item.AgentConfigID,
					AgentConfigVersionID: item.AgentConfigVersionID,
					RelationType:         item.RelationType,
					RelationID:           item.RelationID,
					Creator:              item.Creator,
				}
			})).Error; err != nil {
				return err
			}
		}
		// 删除关联关系
		if len(opt.DeleteRelationIDs) > 0 {
			if err := tx.Where("uid in (?)", opt.DeleteRelationIDs).Delete(&po.NextAgentDeployRelationPO{}).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// only create agent deploy
func (d *DAO) CreateAgentDeployOnly(ctx context.Context, opt CreateAgentDeployOption) error {
	p := &po.NextAgentDeployPO{
		Uid:                        opt.ID,
		AgentConfigID:              opt.AgentConfigID,
		Actor:                      opt.Actor,
		AgentConfigVersionID:       opt.AgentConfigVersionID,
		SourceStatus:               string(opt.SourceStatus),
		TargetStatus:               string(opt.TargetStatus),
		Status:                     lo.ToPtr(string(opt.Status)),
		WorkflowID:                 &opt.WorkflowID,
		AgentConfigVersionOnlineID: opt.AgentConfigVersionOnlineID,
	}

	if opt.ExtraInfo != nil {
		p.ExtraInfo = lo.ToPtr(datatypes.NewJSONType(*opt.ExtraInfo))
	}

	if err := d.Conn.NewRequest(ctx).Create(p).Error; err != nil {
		return err
	}
	return nil
}

type UpdateAgentDeployOption struct {
	ID                       string
	Status                   entity.AgentDeployStatus
	ExtraInfo                *entity.DeployExtraInfo
	AgentConfigVersionID     string
	AgentConfigVersionStatus entity.AgentConfigStatus
	AgentConfigVersionEnable *bool
}

func (d *DAO) UpdateAgentDeploy(ctx context.Context, opt UpdateAgentDeployOption) error {
	// 记录状态
	updateMap := map[string]any{}
	updateMap["status"] = string(opt.Status)
	if opt.ExtraInfo != nil {
		updateMap["extra_info"] = lo.ToPtr(datatypes.NewJSONType(*opt.ExtraInfo))
	}

	configVersionUpdateMap := map[string]any{}
	if opt.AgentConfigVersionEnable != nil {
		configVersionUpdateMap["enabled"] = lo.Ternary(*opt.AgentConfigVersionEnable, 1, 0)
	}

	if opt.AgentConfigVersionStatus != "" {
		configVersionUpdateMap["status"] = opt.AgentConfigVersionStatus
	}

	return d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&po.NextAgentDeployPO{}).Where("uid = ?", opt.ID).Updates(updateMap).Error; err != nil {
			return err
		}

		if opt.AgentConfigVersionID != "" {
			if err := tx.Model(&po.NextAgentConfigVersionPO{}).Where("uid = ?", opt.AgentConfigVersionID).Updates(configVersionUpdateMap).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

type GetAgentDeployOption struct {
	ID            string
	AgentConfigID string
	Status        []string
}

func (d *DAO) GetAgentDeploy(ctx context.Context, opt GetAgentDeployOption) (*entity.AgentDeploy, error) {
	req := d.Conn.NewRequest(ctx)
	if opt.ID != "" {
		req = req.Where("uid = ?", opt.ID)
	}
	if opt.AgentConfigID != "" {
		req = req.Where("agent_config_id = ?", opt.AgentConfigID)
	}
	if len(opt.Status) > 0 {
		req = req.Where("status in (?)", opt.Status)
	}
	var p po.NextAgentDeployPO
	if err := req.Model(&p).First(&p).Error; err != nil {
		return nil, err
	}
	return getAgentDeployFromPO(p), nil
}

type GetAgentDeployListOption struct {
	AgentConfigID string
	Status        *string
	Limit         int
	Offset        int
}

func (d *DAO) GetAgentDeployList(ctx context.Context, opt GetAgentDeployListOption) ([]*entity.AgentDeploy, int64, error) {
	var p []po.NextAgentDeployPO
	req := d.Conn.NewRequest(ctx).Model(&p)
	if opt.AgentConfigID != "" {
		req = req.Where("agent_config_id = ?", opt.AgentConfigID)
	}
	if opt.Status != nil {
		req = req.Where("status = ?", *opt.Status)
	}
	var count int64
	if err := req.Count(&count).Error; err != nil {
		return nil, 0, err
	}
	if err := req.Offset(opt.Offset).Limit(opt.Limit).Order("id DESC").Find(&p).Error; err != nil {
		return nil, 0, err
	}

	return lo.Map(p, func(item po.NextAgentDeployPO, index int) *entity.AgentDeploy {
		return getAgentDeployFromPO(item)
	}), count, nil
}

func getAgentConfigVersionFromPO(p *po.NextAgentConfigVersionPO) (*entity.AgentConfigVersion, error) {
	if p == nil {
		return nil, errors.New("get agent config version from empty PO")
	}

	config := &entity.AgentConfigVersion{
		ID:            p.Uid,
		AgentConfigID: p.AgentConfigID,
		Description:   p.Description,
		Creator:       p.Creator,
		Version:       p.Version,
		Enabled:       lo.Ternary(p.Enabled == 1, true, false),
		Status:        entity.ParseAgentConfigStatus(p.Status),
		RuntimeConfig: lo.Ternary(p.RuntimeConfig != nil, p.RuntimeConfig.Data(), entity.RuntimeConfig{}),
		CustomConfig:  p.CustomConfig.Data(),
		PromptConfig:  p.PromptConfig.Data(),
		CreatedAt:     p.CreatedAt,
		UpdatedAt:     p.UpdatedAt,
	}
	if p.KnowledgesetConfig != nil {
		config.KnowledgesetConfig = p.KnowledgesetConfig.Data()
	}
	return config, nil
}

func getAgentDeployFromPO(p po.NextAgentDeployPO) *entity.AgentDeploy {

	deploy := &entity.AgentDeploy{
		ID:                   p.Uid,
		AgentConfigID:        p.AgentConfigID,
		AgentConfigVersionID: p.AgentConfigVersionID,
		Actor:                p.Actor,
		SourceStatus:         entity.ParseAgentConfigStatus(p.SourceStatus),
		TargetStatus:         entity.ParseAgentConfigStatus(p.TargetStatus),
		CreatedAt:            p.CreatedAt,
	}

	if p.ExtraInfo != nil {
		deploy.ExtraInfo = lo.ToPtr(p.ExtraInfo.Data())
	}

	if p.WorkflowID != nil {
		deploy.WorkflowID = *p.WorkflowID
	}

	if p.Status != nil {
		deploy.Status = entity.ParseAgentDeployStatus(*p.Status)
	}
	if p.AgentConfigVersionOnlineID != nil {
		deploy.AgentConfigVersionOnlineID = *p.AgentConfigVersionOnlineID
	}
	return deploy
}
