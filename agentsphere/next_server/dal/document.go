package dal

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
)

func (d *DAO) CreateDocument(ctx context.Context, document *entity.Document) (*entity.Document, error) {
	if document == nil {
		return nil, errors.New("document is nil")
	}
	documentPO := getDocumentPOFromEntity(document)
	if err := d.Conn.NewRequest(ctx).Create(documentPO).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to create document")
	}
	return getDocumentEntityFromPO(documentPO), nil
}

func (d *DAO) BatchCreateDocument(ctx context.Context, documents []*entity.Document) ([]*entity.Document, error) {
	if len(documents) == 0 {
		return nil, nil
	}
	documentPOs := lo.Map(documents, func(document *entity.Document, index int) *po.DocumentPO {
		return getDocumentPOFromEntity(document)
	})
	if err := d.Conn.NewRequest(ctx).Create(documentPOs).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to create documents")
	}
	return lo.Map(documentPOs, func(documentPO *po.DocumentPO, index int) *entity.Document {
		return getDocumentEntityFromPO(documentPO)
	}), nil
}

func (d *DAO) ListDocumentsByDatasetIDSourceUids(ctx context.Context, datasetID string, sourceUid []string) ([]*entity.Document, error) {
	if len(sourceUid) == 0 {
		return nil, nil
	}
	var documentPOs []*po.DocumentPO
	if err := d.Conn.NewRequest(ctx).Where("dataset_id = ? and source_uid in ?", datasetID, sourceUid).Find(&documentPOs).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to list documents")
	}
	return lo.Map(documentPOs, func(documentPO *po.DocumentPO, index int) *entity.Document {
		return getDocumentEntityFromPO(documentPO)
	}), nil
}

func (d *DAO) DeleteDocument(ctx context.Context, datasetID, documentID string) error {
	if err := d.Conn.NewRequest(ctx).Where("dataset_id =? and uid =?", datasetID, documentID).Delete(&po.DocumentPO{}).Error; err != nil {
		return errors.WithMessage(err, "failed to delete document")
	}
	return nil
}

func (d *DAO) BatchDeleteDocument(ctx context.Context, datasetID string, documentIDs []string) error {
	if len(documentIDs) == 0 {
		return nil
	}

	if err := d.Conn.NewRequest(ctx).Where("dataset_id =? and uid IN (?)", datasetID, documentIDs).Delete(&po.DocumentPO{}).Error; err != nil {
		return errors.WithMessage(err, "failed to delete document")
	}
	return nil
}

func (d *DAO) GetDocument(ctx context.Context, datasetID, documentID string) (*entity.Document, error) {
	var documentPO *po.DocumentPO
	if err := d.Conn.NewRequest(ctx).Where("dataset_id =? and uid =?", datasetID, documentID).First(&documentPO).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to get document")
	}
	return getDocumentEntityFromPO(documentPO), nil
}

type UpdateDocumentOption struct {
	Title             *string
	ProcessStatus     *entity.DocumentProcessStatus
	FailedReason      *entity.DocumentFailedReason
	LastUpdatedAt     *time.Time
	Content           *string
	Owner             *string
	DocumentCreatedAt *time.Time
	ContentType       *entity.DocumentContentType
}

func (d *DAO) UpdateDocument(ctx context.Context, id string, opt *UpdateDocumentOption) error {
	if opt == nil {
		return errors.New("UpdateDocumentOption is nil")
	}
	updateMap := map[string]any{}
	if opt.Title != nil {
		updateMap["title"] = *opt.Title
	}
	if opt.ProcessStatus != nil {
		updateMap["process_status"] = string(*opt.ProcessStatus)
		if *opt.ProcessStatus == entity.DocumentProcessStatusProcessing {
			updateMap["failed_reason"] = nil
		}
	}
	if opt.FailedReason != nil {
		updateMap["failed_reason"] = lo.ToPtr(datatypes.NewJSONType(opt.FailedReason))
	}
	if opt.LastUpdatedAt != nil {
		updateMap["last_updated_at"] = *opt.LastUpdatedAt
	}
	if opt.Content != nil {
		updateMap["content"] = *opt.Content
	}
	if opt.Owner != nil {
		updateMap["owner"] = *opt.Owner
	}
	if opt.DocumentCreatedAt != nil {
		updateMap["document_created_at"] = *opt.DocumentCreatedAt
	}
	if opt.ContentType != nil {
		updateMap["content_type"] = string(*opt.ContentType)
	}
	err := d.Conn.NewRequest(ctx).Model(&po.DocumentPO{}).Where("uid =?", id).Updates(updateMap).Error
	if err != nil {
		return errors.WithMessage(err, "failed to update document")
	}
	return nil
}

func (d *DAO) MGetDocument(ctx context.Context, ids []string) (map[string]*entity.Document, error) {
	documentPOs := make([]*po.DocumentPO, 0, len(ids))
	err := d.Conn.NewRequest(ctx).Where("uid in (?)", ids).Find(&documentPOs).Error
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get documents")
	}
	return lo.SliceToMap(documentPOs, func(documentPO *po.DocumentPO) (string, *entity.Document) {
		return documentPO.Uid, getDocumentEntityFromPO(documentPO)
	}), nil
}

func (d *DAO) ListDocumentsByDatasetID(ctx context.Context, datasetID string) ([]*entity.Document, error) {
	var documentPOs []*po.DocumentPO
	if err := d.Conn.NewRequest(ctx).Where("dataset_id =?", datasetID).Find(&documentPOs).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to list documents")
	}
	return lo.Map(documentPOs, func(documentPO *po.DocumentPO, index int) *entity.Document {
		return getDocumentEntityFromPO(documentPO)
	}), nil
}

func (d *DAO) GetLastDocumentBySourceUID(ctx context.Context, sourceUID string) (*entity.Document, error) {
	var documentPO *po.DocumentPO
	if err := d.Conn.NewRequest(ctx).Where("source_uid =? and process_status=?", sourceUID, entity.DocumentProcessStatusSuccess).Order("last_updated_at desc").First(&documentPO).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to get document")
	}
	return getDocumentEntityFromPO(documentPO), nil
}

type CountDocumentsOption struct {
	Creator   *string
	DatasetID *string
}

func (d *DAO) CountDocuments(ctx context.Context, opt *CountDocumentsOption) (int, error) {
	var count int64
	req := d.Conn.NewRequest(ctx).Model(&po.DocumentPO{})
	if opt.Creator != nil {
		req = req.Where("creator =?", *opt.Creator)
	}
	if opt.DatasetID != nil {
		req = req.Where("dataset_id =?", *opt.DatasetID)
	}
	if err := req.Count(&count).Error; err != nil {
		return 0, errors.WithMessage(err, "failed to count documents")
	}
	return int(count), nil
}

func getDocumentPOFromEntity(document *entity.Document) *po.DocumentPO {
	ret := &po.DocumentPO{
		Uid:               document.ID,
		DatasetID:         document.DatasetID,
		Creator:           document.Creator,
		Title:             document.Title,
		SourceType:        string(document.SourceType),
		SourceUid:         document.SourceUid,
		Content:           document.Content,
		LastUpdatedAt:     document.LastUpdatedAt,
		ProcessStatus:     string(document.ProcessStatus),
		Owner:             document.Owner,
		ContentType:       string(document.ContentType),
		DocumentCreatedAt: document.DocumentCreatedAt,
		ImportType:        string(document.ImportType),
		WikiSpaceName:     document.WikiSpaceName,
	}
	if document.FaildReason != nil {
		ret.FailedReason = lo.ToPtr(datatypes.NewJSONType(lo.FromPtr(document.FaildReason)))
	}
	return ret
}

func getDocumentEntityFromPO(documentPO *po.DocumentPO) *entity.Document {
	ret := &entity.Document{
		ID:                documentPO.Uid,
		DatasetID:         documentPO.DatasetID,
		Creator:           documentPO.Creator,
		Title:             documentPO.Title,
		SourceType:        entity.DocumentSourceType(documentPO.SourceType),
		SourceUid:         documentPO.SourceUid,
		Content:           documentPO.Content,
		LastUpdatedAt:     documentPO.LastUpdatedAt,
		ProcessStatus:     entity.DocumentProcessStatus(documentPO.ProcessStatus),
		Owner:             documentPO.Owner,
		CreatedAt:         documentPO.CreatedAt,
		UpdatedAt:         documentPO.UpdatedAt,
		ContentType:       entity.DocumentContentType(documentPO.ContentType),
		DocumentCreatedAt: documentPO.DocumentCreatedAt,
		ImportType:        entity.ImportType(documentPO.ImportType),
		WikiSpaceName:     documentPO.WikiSpaceName,
	}
	if documentPO.FailedReason != nil {
		ret.FaildReason = lo.ToPtr(documentPO.FailedReason.Data())
	}
	return ret
}
