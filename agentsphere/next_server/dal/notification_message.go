package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

func (d *DAO) CreateNotificationMessage(ctx context.Context, messagePO *po.NextNotificationMessagePO) error {
	err := d.Conn.NewRequest(ctx).Create(messagePO).Error
	if err != nil {
		return err
	}

	return nil
}

func (d *DAO) BatchCreateReceiverNotificationMessage(ctx context.Context, receiverNotificationMessages []*po.NextReceiverNotificationMessagePO) error {
	err := d.Conn.NewRequest(ctx).Create(receiverNotificationMessages).Error
	if err != nil {
		return err
	}

	return nil
}

type ListReceiverNotificationMessage struct {
	Username    string
	Department  string
	ReceiveType entity.ReceiveType
	MessageIDs  []string
}

func (d *DAO) ListReceiverNotificationMessage(ctx context.Context, opts ListReceiverNotificationMessage) ([]*po.NextReceiverNotificationMessagePO, error) {
	var res []*po.NextReceiverNotificationMessagePO

	switch opts.ReceiveType {
	case entity.ReceiveTypeAll:
		if err := d.Conn.NewRequest(ctx).Model(&po.NextReceiverNotificationMessagePO{}).Where("receive_type =?", string(opts.ReceiveType)).Find(&res).Error; err != nil {
			return nil, errors.WithMessage(err, "failed to list ReceiverNotificationMessagePOs")
		}
	case entity.ReceiveTypeDepartment:
		if err := d.Conn.NewRequest(ctx).Model(&po.NextReceiverNotificationMessagePO{}).Where("receiver =? and receive_type =?", opts.Department, string(opts.ReceiveType)).Find(&res).Error; err != nil {
			return nil, errors.WithMessage(err, "failed to list ReceiverNotificationMessagePOs")
		}
	case entity.ReceiveTypePersonal:
		if err := d.Conn.NewRequest(ctx).Model(&po.NextReceiverNotificationMessagePO{}).Where("receiver =? and receive_type =?", opts.Username, string(opts.ReceiveType)).Find(&res).Error; err != nil {
			return nil, errors.WithMessage(err, "failed to list ReceiverNotificationMessagePOs")
		}
	default:
		if err := d.Conn.NewRequest(ctx).Model(&po.NextReceiverNotificationMessagePO{}).Where("message_id IN (?)", opts.MessageIDs).Find(&res).Error; err != nil {
			return nil, errors.WithMessage(err, "failed to list ReceiverNotificationMessagePOs")
		}
	}

	return res, nil
}

func (d *DAO) UpdateReceiverNotificationMessageStatus(ctx context.Context, messageID, status string) error {
	if err := d.Conn.NewRequest(ctx).Model(&po.NextReceiverNotificationMessagePO{}).Where("message_id =?", messageID).Update("status", status).Error; err != nil {
		return errors.WithMessage(err, "failed to UpdateReceiverNotificationMessageStatus")
	}

	return nil
}

type CreateReadNotificationMessageOpts struct {
	Username  string
	MessageID string
}

func (d *DAO) CreateReadNotificationMessage(ctx context.Context, opts CreateReadNotificationMessageOpts) error {
	if err := d.Conn.NewRequest(ctx).Create(&po.NextReadNotificationMessagePO{
		Receiver:  lo.ToPtr(opts.Username),
		MessageID: opts.MessageID,
	}).Error; err != nil {
		return errors.WithMessage(err, "failed to create ReadNotificationMessagePOs")
	}

	return nil
}

type ListReadNotificationMessage struct {
	Username   string
	MessageIDs []string
}

func (d *DAO) ListReadNotificationMessage(ctx context.Context, opts ListReadNotificationMessage) ([]*po.NextReadNotificationMessagePO, error) {
	if len(opts.MessageIDs) == 0 {
		return nil, nil
	}

	var res []*po.NextReadNotificationMessagePO
	if err := d.Conn.NewRequest(ctx).Model(&po.NextReadNotificationMessagePO{}).Where("message_id IN (?) and receiver =?", opts.MessageIDs, opts.Username).Find(&res).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to list ReadNotificationMessagePOs")
	}

	return res, nil
}

func (d *DAO) ListNotificationMessage(ctx context.Context, uids []string) ([]*po.NextNotificationMessagePO, error) {
	if len(uids) == 0 {
		return nil, nil
	}

	var res []*po.NextNotificationMessagePO
	if err := d.Conn.NewRequest(ctx).Model(&po.NextNotificationMessagePO{}).Where("uid IN (?)", uids).Find(&res).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to list NextNotificationMessagePO")
	}

	return res, nil
}
