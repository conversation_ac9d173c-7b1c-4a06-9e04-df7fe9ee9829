package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func (d *DAO) CreatePlatformConfig(ctx context.Context, platformConfig *po.NextPlatformConfigPO) error {
	err := d.Conn.NewRequest(ctx).Create(platformConfig).Error
	if err != nil {
		return errors.WithMessage(err, "failed to CreatePlatformConfig")
	}

	return nil
}

func (d *DAO) GetPlatformConfig(ctx context.Context, spaceID string) (*po.NextPlatformConfigPO, error) {
	var res *po.NextPlatformConfigPO

	if err := d.Conn.NewRequest(ctx).Where("space_id =?", spaceID).First(&res).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, errors.WithMessage(err, "failed to list GetPlatformConfig")
	}

	return res, nil
}

func (d *DAO) CreateOrUpdatePlatformConfig(ctx context.Context, spaceID string, meegoSpaceList []*entity.MeegoSpace, username string) error {
	var oldPlatformConfig *po.NextPlatformConfigPO
	if err := d.Conn.NewRequest(ctx).Where("space_id =?", spaceID).First(&oldPlatformConfig).Error; err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err := d.Conn.NewRequest(ctx).Create(&po.NextPlatformConfigPO{
			SpaceID:        spaceID,
			Creator:        username,
			MeegoSpaceList: lo.ToPtr(datatypes.NewJSONType(meegoSpaceList)),
		}).Error
		if err != nil {
			return errors.WithMessage(err, "failed to create platform config")
		}

		return nil
	}

	updateMap := make(map[string]any)
	if meegoSpaceList == nil {
		meegoSpaceList = []*entity.MeegoSpace{}
	}
	updateMap["meego_space_list"] = lo.ToPtr(datatypes.NewJSONType(meegoSpaceList))

	if err := d.Conn.NewRequest(ctx).Model(&po.NextPlatformConfigPO{}).Where("space_id =?", spaceID).Updates(updateMap).Error; err != nil {
		return errors.WithMessage(err, "failed to UpdatePlatformConfig")
	}

	return nil
}
