package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

// CreateSpaceOption defines options for creating a space.
type CreateSpaceOption struct {
	ID          string
	Name        string
	NameEn      string
	Description string
	Creator     string
	Type        entity.SpaceType
	Status      entity.SpaceStatus
}

// CreateSpace creates a new space.
func (d *DAO) CreateSpace(ctx context.Context, opt CreateSpaceOption) (*entity.Space, error) {
	p := &po.NextSpacePO{
		Uid:         opt.ID,
		Name:        opt.Name,
		NameEn:      opt.NameEn,
		Description: opt.Description,
		Creator:     opt.Creator,
		Type:        string(opt.Type),
		Status:      string(opt.Status),
	}

	res := d.Conn.NewRequest(ctx).Create(p)
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrDuplicatedKey) {
			logs.V1.CtxWarn(ctx, "create space with duplicate key: %v", p.Uid)
			// Try to get the existing one if it's a duplicate key error
			existing, err := d.GetSpace(ctx, opt.ID, true)
			if err != nil {
				return nil, errors.WithMessagef(err, "failed to get existing space after duplicate key error for id: %s", opt.ID)
			}
			return existing, nil
		}
		return nil, errors.WithMessage(res.Error, "failed to create space")
	}
	return getSpaceFromPO(p), nil
}

// UpdateSpaceOption defines options for updating a space.
type UpdateSpaceOption struct {
	ID          string
	Name        *string
	NameEN      *string
	Description *string
	Status      *entity.SpaceStatus
}

// UpdateSpace updates an existing space.
func (d *DAO) UpdateSpace(ctx context.Context, opt UpdateSpaceOption) (*entity.Space, error) {
	if opt.ID == "" {
		return nil, errors.New("space id is empty")
	}

	updater := make(map[string]interface{})
	if opt.Name != nil {
		updater["name"] = *opt.Name
	}
	if opt.NameEN != nil {
		updater["name_en"] = *opt.NameEN
	}
	if opt.Description != nil {
		updater["description"] = *opt.Description
	}
	if opt.Status != nil {
		updater["status"] = opt.Status
	}

	if len(updater) == 0 {
		// No fields to update, just get the space
		return d.GetSpace(ctx, opt.ID, false)
	}

	res := d.Conn.NewRequest(ctx).Model(&po.NextSpacePO{}).Where("uid = ?", opt.ID).Updates(updater)
	if res.Error != nil {
		return nil, errors.WithMessage(res.Error, "failed to update space")
	}
	if res.RowsAffected == 0 {
		// It might be that the record doesn't exist, or the values are the same
		// Try to get it to confirm existence
		return d.GetSpace(ctx, opt.ID, false) // Use read replica if no rows affected
	}
	return d.GetSpace(ctx, opt.ID, true) // Get updated from primary
}

// GetSpace retrieves a space by its ID.
func (d *DAO) GetSpace(ctx context.Context, id string, sync bool) (*entity.Space, error) {
	p := &po.NextSpacePO{}
	req := d.Conn.NewRequest(ctx).Where("uid = ?", id)
	if sync {
		req = req.Clauses(dbresolver.Write) // Read from primary if sync is true
	}
	res := req.First(p)
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, errors.WithMessagef(res.Error, "space with id %s not found", id)
		}
		return nil, errors.WithMessage(res.Error, "failed to get space")
	}
	return getSpaceFromPO(p), nil
}

type GetSpaceByConditionsOption struct {
	Creator *string
	Status  *entity.SpaceStatus
	Type    *entity.SpaceType
	Sync    bool
}

func (d *DAO) GetSpaceByConditions(ctx context.Context, opt GetSpaceByConditionsOption) (*entity.Space, error) {
	p := &po.NextSpacePO{}
	req := d.Conn.NewRequest(ctx).Model(p)

	if opt.Creator != nil {
		req = req.Where("creator = ?", *opt.Creator)
	}
	if opt.Status != nil {
		req = req.Where("status = ?", string(*opt.Status))
	}
	if opt.Type != nil {
		req = req.Where("type = ?", string(*opt.Type))
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.First(p)
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, errors.WithMessagef(res.Error, "space with conditions %v not found", opt)
		}
		return nil, errors.WithMessage(res.Error, "failed to get space")
	}
	return getSpaceFromPO(p), nil
}

// DeleteSpace marks a space as deleted.
func (d *DAO) DeleteSpace(ctx context.Context, id string) error {
	if id == "" {
		return errors.New("space id is empty")
	}
	// 开启事务
	err := d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		// 先更新 status
		if err := tx.Model(&po.NextSpacePO{}).
			Where("uid = ?", id).
			Update("status", "inactive").Error; err != nil {
			return errors.WithMessage(err, "failed to update space status")
		}

		// 再执行软删除
		res := tx.Where("uid = ?", id).Delete(&po.NextSpacePO{})
		if res.Error != nil {
			return errors.WithMessage(res.Error, "failed to delete space")
		}

		if res.RowsAffected == 0 {
			return errors.WithMessagef(gorm.ErrRecordNotFound, "space with id %s not found for deletion", id)
		}
		return nil
	})

	return err
}

// ListSpacesOption defines options for listing spaces.
type ListSpacesOption struct {
	Creator *string
	Type    *entity.SpaceType
	Status  *entity.SpaceStatus
	Offset  int
	Limit   int
	Sync    bool
}

// ListSpaces lists spaces with pagination.
func (d *DAO) ListSpaces(ctx context.Context, opt ListSpacesOption) ([]*entity.Space, int64, error) {
	query := d.Conn.NewRequest(ctx).Model(&po.NextSpacePO{})

	if opt.Creator != nil {
		query = query.Where("creator = ?", *opt.Creator)
	}
	if opt.Type != nil {
		query = query.Where("type = ?", string(*opt.Type))
	}
	if opt.Status != nil {
		query = query.Where("status = ?", string(*opt.Status))
	}
	if opt.Sync {
		query = query.Clauses(dbresolver.Write)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errors.Wrap(err, "failed to count spaces")
	}

	var pos []*po.NextSpacePO
	if err := query.Order("id DESC").Offset(opt.Offset).Limit(opt.Limit).Find(&pos).Error; err != nil {
		return nil, 0, errors.Wrap(err, "failed to list spaces")
	}

	spaces := make([]*entity.Space, 0, len(pos))
	for _, p := range pos {
		spaces = append(spaces, getSpaceFromPO(p))
	}
	return spaces, total, nil
}

// ListSpacesByIDOption defines options for listing spaces by ID.
type ListSpacesByIDOption struct {
	ID          []string
	Type        *entity.SpaceType // Optional: filter by space type
	Status      []string          // Optional: filter by space status
	WithDeleted bool              // Optional: include deleted spaces
}

// ListSpacesByID lists spaces by their IDs.
func (d *DAO) ListSpacesByID(ctx context.Context, opt ListSpacesByIDOption) ([]*entity.Space, error) {
	if len(opt.ID) == 0 {
		return make([]*entity.Space, 0), nil
	}

	var pos []*po.NextSpacePO
	query := d.Conn.NewRequest(ctx).Model(&po.NextSpacePO{}).Where("uid IN ?", opt.ID)

	if opt.Type != nil {
		query = query.Where("type = ?", string(*opt.Type))
	}
	if len(opt.Status) > 0 {
		query = query.Where("status IN ?", opt.Status)
	}

	if opt.WithDeleted {
		query = query.Unscoped()
	}
	if err := query.Find(&pos).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to list spaces by ids")
	}

	spaces := make([]*entity.Space, 0, len(pos))
	for _, p := range pos {
		spaces = append(spaces, getSpaceFromPO(p))
	}
	return spaces, nil
}

// getSpaceFromPO converts a po.NextSpacePO to an entity.Space.
func getSpaceFromPO(p *po.NextSpacePO) *entity.Space {
	if p == nil {
		return nil
	}
	space := &entity.Space{
		ID:          p.Uid,
		Name:        p.Name,
		NameEN:      p.NameEn,
		Description: p.Description,
		Creator:     p.Creator,
		Type:        entity.SpaceType(p.Type),
		Status:      entity.SpaceStatus(p.Status),
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}

	return space
}
