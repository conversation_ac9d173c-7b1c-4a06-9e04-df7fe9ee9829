package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"github.com/pkg/errors"
)

type ListCodeRepoOption struct {
	SpaceID  string
	PageNum  int32
	PageSize int32
}

func (d *DAO) ListCodeRepo(ctx context.Context, opts ListCodeRepoOption) ([]*po.NextCodeRepoPO, int64, error) {
	var res []*po.NextCodeRepoPO

	if opts.SpaceID == "" {
		return nil, 0, errors.New("spaceID is required")
	}

	req := d.Conn.NewRequest(ctx)
	if opts.PageNum > 0 && opts.PageSize > 0 {
		req = req.Offset(int((opts.PageNum - 1) * opts.PageSize)).Limit(int(opts.PageSize))
	}

	var total int64
	err := req.Model(&po.NextCodeRepoPO{}).Where("space_id =?", opts.SpaceID).Count(&total).Error
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to count NextCodeRepoPOs")
	}

	if err = req.Where("space_id =?", opts.SpaceID).Find(&res).Error; err != nil {
		return nil, 0, errors.WithMessage(err, "failed to list ListCodeRepo")
	}

	return res, total, nil
}

func (d *DAO) CreateCodeRepos(ctx context.Context, codeRepos []*po.NextCodeRepoPO) error {
	if len(codeRepos) == 0 {
		return nil
	}

	err := d.Conn.NewRequest(ctx).Create(codeRepos).Error
	if err != nil {
		return errors.WithMessage(err, "failed to CreateCodeRepos")
	}

	return nil
}

func (d *DAO) DeleteCodeRepo(ctx context.Context, spaceID string, repoIDs []string) error {
	if len(repoIDs) == 0 {
		return nil
	}

	if err := d.Conn.NewRequest(ctx).Where("space_id =? and repo_id IN (?)", spaceID, repoIDs).Delete(&po.NextCodeRepoPO{}).Error; err != nil {
		return errors.WithMessage(err, "failed to delete code repo")
	}

	return nil
}
