package entity

import (
	"errors"
	"fmt"
	"time"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/port/lark"
)

type Document struct {
	ID                string                `json:"id"`
	DatasetID         string                `json:"dataset_id"`
	Creator           string                `json:"creator"`
	Title             string                `json:"title"`
	SourceType        DocumentSourceType    `json:"source_type"`
	SourceUid         string                `json:"source_uid"`
	Content           *string               `json:"content"`
	Owner             string                `json:"owner"`
	CreatedAt         time.Time             `json:"created_at"`
	DocToken          *string               `json:"doc_token"`
	ContentType       DocumentContentType   `json:"content_type"`
	ImportType        ImportType            `json:"import_type"`
	LastUpdatedAt     time.Time             `json:"last_updated_at"`
	ProcessStatus     DocumentProcessStatus `json:"process_status"`
	FaildReason       *DocumentFailedReason `json:"faild_reason"`
	DocumentContent   *string               `json:"document_content"`
	Heat              *int64                `json:"heat"`
	UpdatedAt         time.Time             `json:"update_time"`
	DocumentCreatedAt time.Time             `json:"document_created_at"`
	WikiSpaceName     string                `json:"wiki_space_name"`
}

type DocumentProcessStatus string

const (
	DocumentProcessStatusProcessing DocumentProcessStatus = "processing"
	DocumentProcessStatusSuccess    DocumentProcessStatus = "success"
	DocumentProcessStatusFailed     DocumentProcessStatus = "failed"
)

type DocumentSourceType string

const (
	DocumentSourceTypeLarkWiki    DocumentSourceType = "lark_wiki"
	DocumentSourceTypeLarkDocx    DocumentSourceType = "lark_docx"
	DocumentSourceTypeLarkDoc     DocumentSourceType = "lark_doc"
	DocumentSourceTypeLarkSheet   DocumentSourceType = "lark_sheet"
	DocumentSourceTypeLarkBitable DocumentSourceType = "lark_bitable"
)

func GetDocumentSourceType(docType string) (DocumentSourceType, bool) {
	switch docType {
	case "docx":
		return DocumentSourceTypeLarkDocx, true
	case "doc":
		return DocumentSourceTypeLarkDoc, true
	case "wiki":
		return DocumentSourceTypeLarkWiki, true
	case "sheet":
		return DocumentSourceTypeLarkSheet, true
	case "bitable":
		return DocumentSourceTypeLarkBitable, true
	default:
		return "", false
	}
}

func (s *DocumentSourceType) GetDocType() string {
	if s == nil {
		return ""
	}
	switch *s {
	case DocumentSourceTypeLarkWiki:
		return "wiki"
	case DocumentSourceTypeLarkDoc:
		return "doc"
	case DocumentSourceTypeLarkDocx:
		return "docx"
	case DocumentSourceTypeLarkSheet:
		return "sheet"
	case DocumentSourceTypeLarkBitable:
		return "bitable"
	default:
		return string(*s)
	}
}

func (d *Document) GetDocumentURL() string {
	if d == nil {
		return ""
	}
	switch d.SourceType {
	case DocumentSourceTypeLarkWiki:
		return fmt.Sprintf("https://bytedance.larkoffice.com/wiki/%s", d.SourceUid)
	case DocumentSourceTypeLarkDocx:
		return fmt.Sprintf("https://bytedance.larkoffice.com/docx/%s", d.SourceUid)
	case DocumentSourceTypeLarkDoc:
		return fmt.Sprintf("https://bytedance.feishu.cn/doc/%v", d.SourceUid)
	case DocumentSourceTypeLarkSheet:
		return fmt.Sprintf("https://bytedance.larkoffice.com/sheets/%s", d.SourceUid)
	case DocumentSourceTypeLarkBitable:
		return fmt.Sprintf("https://bytedance.larkoffice.com/base/%s", d.SourceUid)
	default:
		return ""
	}
}

type DocumentContentType string

const (
	DocumentContentTypeDoc     DocumentContentType = "doc"
	DocumentContentTypeDocx    DocumentContentType = "docx"
	DocumentContentTypeSheet   DocumentContentType = "sheet"
	DocumentContentTypeBitable DocumentContentType = "bitable"
)

func (contentType *DocumentContentType) IsSupport() bool {
	if contentType == nil {
		return false
	}
	switch *contentType {
	case DocumentContentTypeDoc, DocumentContentTypeDocx, DocumentContentTypeSheet, DocumentContentTypeBitable:
		return true
	default:
		return false
	}
}

func (contentType *DocumentContentType) ToIDL() nextagent.DocumentContentType {
	if contentType == nil {
		return ""
	}
	switch *contentType {
	case DocumentContentTypeDoc, DocumentContentTypeDocx:
		return nextagent.DocumentContentTypeDoc
	case DocumentContentTypeSheet:
		return nextagent.DocumentContentTypeSheet
	case DocumentContentTypeBitable:
		return nextagent.DocumentContentTypeBitable
	default:
		return ""
	}
}

type DocumentFailedReason struct {
	Error DocumentFailedReasonError `json:"error"`
	LogID string                    `json:"log_id"`
}

type DocumentFailedReasonError string

const (
	DocumentFailedReasonErrorCodeNoPermission       DocumentFailedReasonError = "no_permission"
	DocumentFailedReasonErrorCodeNotFound           DocumentFailedReasonError = "resource_not_found"
	DocumentFailedReasonErrorCodeInternalError      DocumentFailedReasonError = "internal_error"
	DocumentFailedReasonErrorCodeLarkAuthFailed     DocumentFailedReasonError = "lark_auth_failed"
	DocumentFailedReasonErrorCodeLarkFrequencyLimit DocumentFailedReasonError = "lark_frequency_limit"
)

func GetDocumentFailedReasonError(err error) DocumentFailedReasonError {
	switch {
	case errors.Is(err, lark.ErrLarkAuthFailed):
		return DocumentFailedReasonErrorCodeLarkAuthFailed
	case errors.Is(err, lark.ErrPermissionDenied):
		return DocumentFailedReasonErrorCodeNoPermission
	case errors.Is(err, lark.ErrResourceNotFound):
		return DocumentFailedReasonErrorCodeNotFound
	case errors.Is(err, lark.ErrFrequencyLimit):
		return DocumentFailedReasonErrorCodeLarkFrequencyLimit
	default:
		return DocumentFailedReasonErrorCodeInternalError
	}
}

type LarkDocument struct {
	Title       string        `json:"title"`
	Content     string        `json:"content"`
	URL         string        `json:"url"`
	IsUploaded  bool          `json:"is_uploaded"`
	ContentType string        `json:"content_type"`
	OwnerName   string        `json:"owner_name"`
	Score       float64       `json:"score"`
	IsRoot      bool          `json:"is_root"`
	HasChild    bool          `json:"has_child"`
	Meta        *DocumentMeta `json:"meta"` // 文档元数据， 可选项
}

type DocumentMeta struct {
	Title       string              `json:"title"`
	SourceUid   string              `json:"source_uid"`
	ContentType DocumentContentType `json:"content_type"`
	Owner       string              `json:"owner"`
	CreateTime  time.Time           `json:"create_time"`
	UpdateTime  time.Time           `json:"update_time"`
	SourceType  DocumentSourceType  `json:"source_type"`
	DocToken    string              `json:"doc_token"`
}

type WikiSpace struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type ImportType string

const (
	ImportTypeSingle   ImportType = nextagent.ImportTypeSingle
	ImportTypeWikiTree ImportType = nextagent.ImportTypeWikiTree
)
