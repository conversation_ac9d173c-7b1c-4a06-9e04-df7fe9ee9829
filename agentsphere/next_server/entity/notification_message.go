package entity

import (
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

type ReceiveType string

const (
	ReceiveTypePersonal   ReceiveType = nextagent.ReceiveTypePersonal
	ReceiveTypeAll        ReceiveType = nextagent.ReceiveTypeAll
	ReceiveTypeDepartment ReceiveType = nextagent.ReceiveTypeDepartment
)

type NotificationMessageStatus string

const (
	MessageStatusRead     NotificationMessageStatus = nextagent.MessageStatusRead
	MessageStatusUnread   NotificationMessageStatus = nextagent.MessageStatusUnread
	MessageStatusRecalled NotificationMessageStatus = nextagent.MessageStatusRecalled
)
