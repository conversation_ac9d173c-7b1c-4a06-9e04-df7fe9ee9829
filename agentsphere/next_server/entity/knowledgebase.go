package entity

import "time"

type Knowledgebase<PERSON>vent struct {
	DocumentContentEvent *DocumentContentEvent `json:"document_content_event,omitempty"`
	ImportWikiEvent      *ImportWikiEvent      `json:"import_wiki_event,omitempty"`
}

type DocumentContentEvent struct {
	ID               string                `json:"id"`
	DatasetID        string                `json:"dataset_id"`
	Creator          string                `json:"creator"`
	SourceType       DocumentSourceType    `json:"source_type"`
	SourceUid        string                `json:"source_uid"`
	DocToken         *string               `json:"doc_token,omitempty"`
	ContentType      DocumentContentType   `json:"content_type"`
	LastUpdatedAt    time.Time             `json:"last_updated_at"`
	Title            string                `json:"title"`
	OldProcessStatus DocumentProcessStatus `json:"old_process_status"`
	ForceUpdate      bool                  `json:"force_update"`
}

type ImportWikiEvent struct {
	TaskID     string `json:"task_id"`
	DatasetID  string `json:"dataset_id"`
	Creator    string `json:"creator"`
	WikiDocUrl string `json:"wiki_doc_url"`
	SpaceName  string `json:"space_name"`
}

type KnowledgeTaskType string

const (
	KnowledgeTaskTypeImportWiki KnowledgeTaskType = "import_wiki"
	MaxRetryTimes               int               = 16
)

type KnowledgeTaskStatus string

const (
	KnowledgeTaskStatusCreated   KnowledgeTaskStatus = "created"
	KnowledgeTaskStatusRunning   KnowledgeTaskStatus = "running"
	KnowledgeTaskStatusFailed    KnowledgeTaskStatus = "failed"
	KnowledgeTaskStatusSucceeded KnowledgeTaskStatus = "succeeded"
)

type ImportWikiParams struct {
	WikiDocUrl string `json:"wiki_doc_url"`
	SpaceName  string `json:"space_name"`
}
