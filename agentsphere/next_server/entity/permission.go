package entity

import (
	"time"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

type ResourceType string

const (
	ResourceTypeUnknown       ResourceType = "unknown"
	ResourceTypeSpace         ResourceType = "space"
	ResourceTypeSession       ResourceType = "session"
	ResourceTypeArtifact      ResourceType = "artifact"
	ResourceTypeMCP           ResourceType = "mcp"
	ResourceTypeTemplate      ResourceType = "template"
	ResourceTypeKnowledgebase ResourceType = "knowledgebase"
	ResourceTypeAgent         ResourceType = "agent"
)

func ParseResourceTypeIDLToEntity(idl nextagent.ResourceType) ResourceType {
	return ResourceType(idl)
}

func (r ResourceType) ToIDL() nextagent.ResourceType {
	return nextagent.ResourceType(r)
}

func (r ResourceType) DefaultStatus() ResourceStatus {
	switch r {
	case ResourceTypeSpace:
		return ResourceStatusPrivate
	case ResourceTypeSession:
		return ResourceStatusPrivate
	case ResourceTypeArtifact:
		return ResourceStatusPrivate
	case ResourceTypeMCP:
		return ResourceStatusPrivate
	case ResourceTypeTemplate:
		return ResourceStatusPrivate
	case ResourceTypeKnowledgebase:
		return ResourceStatusPrivate
	case ResourceTypeAgent:
		return ResourceStatusPublic
	default:
		return ResourceStatusUnknown
	}
}

func (r ResourceType) CanChangeStatus() bool {
	switch r {
	case ResourceTypeSpace, ResourceTypeSession, ResourceTypeArtifact, ResourceTypeKnowledgebase:
		return false
	case ResourceTypeMCP, ResourceTypeTemplate, ResourceTypeAgent:
		return true
	default:
		return false
	}
}

func (r ResourceType) Ptr() *ResourceType {
	return &r
}

type ResourceStatus string

const (
	ResourceStatusUnknown ResourceStatus = "unknown"
	ResourceStatusPublic  ResourceStatus = "public"
	ResourceStatusPrivate ResourceStatus = "private"
)

func (r ResourceStatus) ToIDL() nextagent.ResourceStatus {
	return nextagent.ResourceStatus(r)
}

type PermissionType string

const (
	PermissionTypeUnknown        PermissionType = "unknown"
	PermissionTypeUser           PermissionType = "user"
	PermissionTypeServiceAccount PermissionType = "service_account" //服务账号
	PermissionTypeDepartment     PermissionType = "department"
	PermissionTypeSpace          PermissionType = "space"
)

func (r PermissionType) ToIDL() nextagent.PermissionType {
	return nextagent.PermissionType(r)
}

type PermissionRole int

const (
	PermissionRoleUnknown PermissionRole = 0
	PermissionRoleVisitor PermissionRole = 1000
	PermissionRoleMember  PermissionRole = 2000
	PermissionRoleAdmin   PermissionRole = 3000

	// 内部角色, 不对外暴露
	PermissionRoleDeveloper  PermissionRole = 30000
	PermissionRoleOperator   PermissionRole = 20000
	PermissionRoleMCPPartner PermissionRole = 10000
)

func (pr PermissionRole) ToIDL() nextagent.PermissionRole {
	return nextagent.PermissionRole(pr)
}

func PermissionRoleFromIDL(role nextagent.PermissionRole) PermissionRole {
	return PermissionRole(role)
}

func ParseStringToPermissionRole(s string) PermissionRole {
	switch s {
	case "visitor":
		return PermissionRoleVisitor
	case "member":
		return PermissionRoleMember
	case "admin":
		return PermissionRoleAdmin
	case "developer":
		return PermissionRoleDeveloper
	case "operator":
		return PermissionRoleOperator
	case "mcp_partner":
		return PermissionRoleMCPPartner
	default:
		return PermissionRoleUnknown
	}
}

func (r PermissionRole) String() string {
	switch r {
	case PermissionRoleVisitor:
		return "visitor"
	case PermissionRoleMember:
		return "member"
	case PermissionRoleAdmin:
		return "admin"
	case PermissionRoleDeveloper:
		return "developer"
	case PermissionRoleOperator:
		return "operator"
	case PermissionRoleMCPPartner:
		return "mcp_partner"
	default:
		return "unknown"
	}
}

type PermissionMeta struct {
	Type       PermissionType
	ExternalID string // 用户名，部门名，空间 ID
	Role       PermissionRole
}

type Permission struct {
	ID           string
	ResourceID   string
	ResourceType ResourceType
	Type         PermissionType
	ExternalID   string
	Role         PermissionRole
	CreatedAt    time.Time
	UpdatedAt    time.Time

	// 外部关联数据，按需返回
	PermissionActions []PermissionAction
}

func (p *Permission) ToIDL() *nextagent.ResourcePermission {
	return &nextagent.ResourcePermission{
		ID:           p.ID,
		ResourceID:   p.ResourceID,
		ResourceType: p.ResourceType.ToIDL(),
		Type:         p.Type.ToIDL(),
		ExternalID:   p.ExternalID,
		Role:         p.Role.ToIDL(),
		CreatedAt:    p.CreatedAt.Format(time.RFC3339),
		UpdatedAt:    p.UpdatedAt.Format(time.RFC3339),
		Actions: lo.Map(p.PermissionActions, func(item PermissionAction, index int) nextagent.PermissionAction {
			return item.ToIDL()
		}),
	}
}

type GroupResourceRelation struct {
	ResourceID string
	GroupID    string
}

func (g *GroupResourceRelation) ToIDL() *nextagent.Group {
	return &nextagent.Group{
		ResourceID: g.ResourceID,
		GroupID:    g.GroupID,
	}
}

type Resource struct {
	ID         string
	Type       ResourceType
	ExternalID string
	Owner      string
	Status     ResourceStatus
	CreatedAt  time.Time
	UpdatedAt  time.Time

	// 外部关联数据，按需返回
	GroupRelation []*GroupResourceRelation
	Permissions   []*Permission // 返回所有权限
}

func (r *Resource) ToIDL() *nextagent.Resource {
	return &nextagent.Resource{
		ID:         r.ID,
		Type:       r.Type.ToIDL(),
		ExternalID: r.ExternalID,
		Owner:      r.Owner,
		Status:     r.Status.ToIDL(),
		CreatedAt:  r.CreatedAt.Format(time.RFC3339),
		UpdatedAt:  r.UpdatedAt.Format(time.RFC3339),
		Groups: lo.Map(r.GroupRelation, func(item *GroupResourceRelation, index int) *nextagent.Group {
			return item.ToIDL()
		}),
		Permissions: lo.Map(r.Permissions, func(item *Permission, index int) *nextagent.ResourcePermission {
			return item.ToIDL()
		}),
	}
}

type PermissionAction string

func (a PermissionAction) ToIDL() nextagent.PermissionAction {
	return nextagent.PermissionAction(a)
}

func (a PermissionAction) ResourceType() ResourceType {
	switch a {
	case PermissionActionSessionDelete, PermissionActionSessionRead, PermissionActionSessionUpdate,
		PermissionActionSessionTemplateCreate, PermissionActionSessionAllFile, PermissionActionSessionChat,
		PermissionActionSessionVSCodeOpen, PermissionActionSessionTraceRead, PermissionActionSessionMCPTraceRead,
		PermissionActionSessionVisualization, PermissionActionSessionFeedback, PermissionActionSessionDownload,
		PermissionActionSessionShareCreate:
		return ResourceTypeSession
	case PermissionActionMCPRead, PermissionActionMCPUpdate, PermissionActionMCPDelete:
		return ResourceTypeMCP
	case PermissionActionTemplateRead, PermissionActionTemplateDelete, PermissionActionTemplateUpdate:
		return ResourceTypeTemplate
	case PermissionActionKnowledgebaseRead, PermissionActionKnowledgebaseUpdate, PermissionActionKnowledgebaseDelete:
		return ResourceTypeKnowledgebase
	case PermissionActionSpaceUpdate, PermissionActionSpaceDelete, PermissionActionSpaceRead, PermissionActionSpaceDevResourceUpdate,
		PermissionActionSessionCreate, PermissionActionTemplateCreate, PermissionActionMCPCreate, PermissionActionKnowledgebaseCreate:
		return ResourceTypeSpace
	default:
		return ResourceTypeUnknown
	}
}

const (
	PermissionActionUnknown PermissionAction = "unknown"

	// Session 模块
	PermissionActionSessionCreate         PermissionAction = "session.create"
	PermissionActionSessionDelete         PermissionAction = "session.delete"
	PermissionActionSessionRead           PermissionAction = "session.read"
	PermissionActionSessionUpdate         PermissionAction = "session.update"
	PermissionActionSessionAllFile        PermissionAction = "session.all_file"
	PermissionActionSessionChat           PermissionAction = "session.chat"
	PermissionActionSessionVisualization  PermissionAction = "session.visualization"
	PermissionActionSessionTemplateCreate PermissionAction = "session.template.create"
	PermissionActionSessionFeedback       PermissionAction = "session.feedback"
	PermissionActionSessionDownload       PermissionAction = "session.download"
	PermissionActionSessionShareCreate    PermissionAction = "session.share.create"
	PermissionActionSessionVSCodeOpen     PermissionAction = "session.vscode.open"

	PermissionActionSessionTraceRead    PermissionAction = "session.trace.read"
	PermissionActionSessionMCPTraceRead PermissionAction = "session.mcp_trace.read"

	// MCP 模块
	PermissionActionMCPCreate PermissionAction = "mcp.create"
	PermissionActionMCPRead   PermissionAction = "mcp.read"
	PermissionActionMCPUpdate PermissionAction = "mcp.update"
	PermissionActionMCPDelete PermissionAction = "mcp.delete"

	// Template 模块
	PermissionActionTemplateRead   PermissionAction = "template.read"
	PermissionActionTemplateUpdate PermissionAction = "template.update"
	PermissionActionTemplateDelete PermissionAction = "template.delete"
	PermissionActionTemplateCreate PermissionAction = "template.create"

	// Knowledgebase 模块
	PermissionActionKnowledgebaseRead   PermissionAction = "knowledgebase.read"
	PermissionActionKnowledgebaseUpdate PermissionAction = "knowledgebase.update"
	PermissionActionKnowledgebaseDelete PermissionAction = "knowledgebase.delete"
	PermissionActionKnowledgebaseCreate PermissionAction = "knowledgebase.create"

	// Space 模块
	PermissionActionSpaceCreate            PermissionAction = "space.create"
	PermissionActionSpaceUpdate            PermissionAction = "space.update"
	PermissionActionSpaceDelete            PermissionAction = "space.delete"
	PermissionActionSpaceRead              PermissionAction = "space.read"
	PermissionActionSpaceDevResourceUpdate PermissionAction = "space.dev_resource_update"

	// Agent 模块
	PermissionActionAgentCreate PermissionAction = "agent.create"
	PermissionActionAgentUpdate PermissionAction = "agent.update"
	PermissionActionAgentDelete PermissionAction = "agent.delete"
	PermissionActionAgentRead   PermissionAction = "agent.read"
)
