package space

import (
	"context"
	"fmt"
	"sort"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/code_repo"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/dev_service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/notification_message"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/platform_config"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/hulkcloud"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/v2/operatorx"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/kite/kitex/client/callopt"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bits_ai_graph/kitex_gen/bits/ai/graph"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	knowledge "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lock"
	permservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/overpass/bits_ai_graph/rpc/bits_ai_graph"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"
)

// SpaceService handles business logic for spaces.
type Service struct {
	idGen                 uuid.Generator
	dao                   *dal.DAO
	permService           *permservice.Service
	knowledgeService      *knowledge.Service
	hulkCloudClient       hulkcloud.Client
	locker                lock.Lock
	notificationService   *notification_message.Service
	conf                  *config.AgentSphereConfig
	codeRepoService       *code_repo.Service
	platformConfigService *platform_config.Service
	devServiceService     *dev_service.Service
}

type CreateServiceOption struct {
	fx.In
	DAO                   *dal.DAO
	PermService           *permservice.Service
	KnowledgeService      *knowledge.Service
	HulkCloudClient       hulkcloud.Client
	Locker                lock.Lock
	NotificationService   *notification_message.Service
	Conf                  *config.AgentSphereConfig
	CodeRepoService       *code_repo.Service
	PlatformConfigService *platform_config.Service
	DevServiceService     *dev_service.Service
}

// NewSpaceService creates a new SpaceService.
func NewService(opt CreateServiceOption) (*Service, error) {
	return &Service{
		idGen:                 uuid.GetDefaultGenerator(nil),
		dao:                   opt.DAO,
		permService:           opt.PermService,
		knowledgeService:      opt.KnowledgeService,
		hulkCloudClient:       opt.HulkCloudClient,
		locker:                opt.Locker,
		notificationService:   opt.NotificationService,
		conf:                  opt.Conf,
		codeRepoService:       opt.CodeRepoService,
		platformConfigService: opt.PlatformConfigService,
		devServiceService:     opt.DevServiceService,
	}, nil
}

// UpdateSpaceMemberRoleOption defines the options for updating a space member's role
type UpdateSpaceMemberRoleOption struct {
	SpaceID  string
	Username string
	Role     entity.PermissionRole
	ActorID  string
}

// CreateSpaceOption defines the options for creating a space
type CreateSpaceOption struct {
	Name        string
	NameEN      string
	Description string
	Type        entity.SpaceType
	UserName    string
	Config      *entity.SpaceConfig
}

// CreateSpace creates a new space.
func (s *Service) CreateSpace(ctx context.Context, opt CreateSpaceOption) (*entity.Space, error) {
	spaceEntity := &entity.Space{
		ID:          s.idGen.NewID(),
		Name:        opt.Name,
		NameEN:      opt.NameEN,
		Description: opt.Description,
		Creator:     opt.UserName,
		Type:        opt.Type,
		Status:      operatorx.IfThen(opt.Type == entity.SpaceTypeProject, entity.SpaceStatusUninit, entity.SpaceStatusActive),
	}

	createdSpace, err := s.dao.CreateSpace(ctx, dal.CreateSpaceOption{
		ID:          spaceEntity.ID,
		Name:        spaceEntity.Name,
		NameEn:      spaceEntity.NameEN,
		Description: spaceEntity.Description,
		Creator:     spaceEntity.Creator,
		Type:        spaceEntity.Type,
		Status:      spaceEntity.Status,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create space")
	}

	var baseConfig *entity.SpaceBaseConfig
	if opt.Config != nil && opt.Config.BaseConfig != nil {
		baseConfig = opt.Config.BaseConfig
	}

	// create space config
	spaceConfig, err := s.dao.CreateOrUpdateSpaceConfig(ctx, dal.CreateOrUpdateSpaceConfigOptions{
		SpaceID:    createdSpace.ID,
		BaseConfig: baseConfig,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create space config")
	}
	createdSpace.Config = spaceConfig

	// 创建空间resource
	status := entity.ResourceStatusPrivate
	err = backoff.Retry(func() error {
		_, err = s.permService.CreateResource(ctx, permservice.CreateResourceOption{
			ExternalID: createdSpace.ID,
			Type:       entity.ResourceTypeSpace,
			Status:     &status,
			Owner:      opt.UserName,
		})
		return err
	}, backoff.WithContext(backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 2), ctx))
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to create resource for space %s", createdSpace.ID)
	}

	//TODO: 创建知识库
	// _, err = s.knowledgeService.CreateDataset(ctx)
	// if err != nil {
	// 	return nil, errors.WithMessagef(err, "failed to create dataset for space %s", createdSpace.ID)
	// }
	dataset, err := s.knowledgeService.CreateDataset(ctx, createdSpace.ID, opt.UserName)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to create dataset for space %s", createdSpace.ID)
	}
	createdSpace.DataSetID = dataset.ID

	return createdSpace, nil
}

func (s *Service) CreatePersonalSpace(ctx context.Context, userName string) (*entity.Space, error) {
	// 由于个人空间只有一个，需要保证并发安全
	lockKey := fmt.Sprintf("next_server:personal_space:%s", userName)
	l, err := s.locker.Acquire(ctx, lockKey)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to lock personal space for user %s", userName)
	}
	defer l.Release(ctx)
	// 如果已存在个人空间
	space, err := s.GetPersonalSpace(ctx, GetPersonalSpaceOption{
		Owner: userName,
		Sync:  true,
	})
	if err != nil && !errors.Is(err, serverservice.ErrSpaceNotFound) {
		return nil, errors.WithMessagef(err, "failed to get personal space for user %s", userName)
	}

	if space != nil {
		return space, nil
	}

	userInfo, _ := s.hulkCloudClient.GetUserInfo(ctx, userName)
	userNameZH, userNameEN := userName, userName
	if userInfo != nil {
		userNameZH = userInfo.Name
		userNameEN = userInfo.EnName
	}
	// 创建个人空间&resource&知识库
	space, err = s.CreateSpace(ctx, CreateSpaceOption{
		Name:        userNameZH + " 的工作空间",
		NameEN:      userNameEN + "'s Workspace",
		Description: userName + "的工作空间",
		Type:        entity.SpaceTypePersonal,
		UserName:    userName,
	})
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to create personal space for user %s", userName)
	}

	return space, nil
}

// UpdateSpaceOption defines the options for updating a space
type UpdateSpaceOption struct {
	SpaceID     string
	Name        *string
	NameEN      *string
	Description *string
	Status      *entity.SpaceStatus
	Config      *entity.SpaceConfig
}

// UpdateSpace updates an existing space.
func (s *Service) UpdateSpace(ctx context.Context, opt UpdateSpaceOption) (*entity.Space, error) {
	_, err := s.dao.GetSpace(ctx, opt.SpaceID, false)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, serverservice.ErrSpaceNotFound
		}
		return nil, errors.WithMessagef(err, "failed to get space %s for update", opt.SpaceID)
	}

	updatedSpace, err := s.dao.UpdateSpace(ctx, dal.UpdateSpaceOption{
		ID:          opt.SpaceID,
		Name:        opt.Name,
		NameEN:      opt.NameEN,
		Description: opt.Description,
		Status:      opt.Status,
	})
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to update space %s ", opt.SpaceID)
	}

	if opt.Config != nil && opt.Config.BaseConfig != nil {
		cfg, err := s.dao.CreateOrUpdateSpaceConfig(ctx, dal.CreateOrUpdateSpaceConfigOptions{
			SpaceID:    updatedSpace.ID,
			BaseConfig: opt.Config.BaseConfig,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update space config")
		}
		updatedSpace.Config = cfg
	}

	return updatedSpace, nil
}

type GetSpaceOption struct {
	SpaceID     string
	NeedDataset bool
	NeedMembers bool
	NeedConfig  bool
	Sync        bool
}

// GetSpace retrieves a space.
func (s *Service) GetSpace(ctx context.Context, opt GetSpaceOption) (*entity.Space, error) {
	space, err := s.dao.GetSpace(ctx, opt.SpaceID, opt.Sync)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, serverservice.ErrSpaceNotFound
		}
		return nil, errors.WithMessagef(err, "failed to get space %s", opt.SpaceID)
	}

	if opt.NeedDataset {
		dataset, err := s.knowledgeService.GetDatasetBySpaceID(ctx, space.ID)
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get dataset for space %s", space.ID)
		}

		space.DataSetID = dataset.ID
	}

	if opt.NeedMembers {
		space.Members, _, err = s.ListSpaceMembers(ctx, ListSpaceMembersOption{
			SpaceID: space.ID,
			Offset:  0,
			Limit:   -1,
		})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get members for space %s", space.ID)
		}
	}

	if opt.NeedConfig {
		cfg, err := s.dao.GetSpaceConfig(ctx, opt.SpaceID, false)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.WithMessagef(err, "failed to get space config for space %s", opt.SpaceID)
		}
		space.Config = cfg
	}

	return space, nil
}

// DeleteSpace deletes a space by its ID.
func (s *Service) DeleteSpace(ctx context.Context, spaceID string, userID string) error {
	_, err := s.dao.GetSpace(ctx, spaceID, true)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return serverservice.ErrSpaceNotFound
		}
		return errors.Wrapf(err, "failed to get space %s for deletion check", spaceID)
	}

	// backoff
	resourceType := entity.ResourceTypeSpace
	err = backoff.Retry(func() error {
		err = s.permService.DeleteResource(ctx, permservice.DeleteResourceOption{
			ResourceExternalID: &spaceID,
			ResourceType:       &resourceType,
		})
		return err
	}, backoff.WithContext(backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 2), ctx))
	if err != nil {
		return errors.WithMessagef(err, "failed to delete resource for space %s", spaceID)
	}

	return s.dao.DeleteSpace(ctx, spaceID)
}

// ListSpacesOption defines the options for listing spaces
type ListAllProjectSpacesOption struct {
	Limit  int
	Offset int
}

// ListSpaces lists spaces based on criteria, typically for a user.
func (s *Service) ListAllProjectSpaces(ctx context.Context, opt ListAllProjectSpacesOption) ([]*entity.Space, int64, error) {
	// 查询实体
	res, count, err := s.dao.ListSpaces(ctx, dal.ListSpacesOption{
		Type:   lo.ToPtr(entity.SpaceTypeProject),
		Offset: opt.Offset,
		Limit:  opt.Limit,
	})
	if err != nil {
		return nil, 0, errors.WithMessagef(err, "failed to list project spaces")
	}

	spaceConfigMap, err := s.dao.BatchGetSpaceConfigBySpaceIDs(ctx, lo.Map(res, func(item *entity.Space, index int) string {
		return item.ID
	}))
	if err != nil {
		return nil, 0, errors.WithMessagef(err, "failed to batch get space configs")
	}
	for _, r := range res {
		r.Config = spaceConfigMap[r.ID]
	}
	return res, count, nil
}

type GetPersonalSpaceOption struct {
	Owner       string
	NeedMembers bool
	Sync        bool
}

func (s *Service) GetPersonalSpace(ctx context.Context, opt GetPersonalSpaceOption) (*entity.Space, error) {
	// TODO: 从permission服务获取用户的个人空间ID
	space, err := s.dao.GetSpaceByConditions(ctx, dal.GetSpaceByConditionsOption{
		Creator: &opt.Owner,
		Type:    lo.ToPtr(entity.SpaceTypePersonal),
		Status:  lo.ToPtr(entity.SpaceStatusActive),
		Sync:    opt.Sync,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, serverservice.ErrSpaceNotFound
		}
		return nil, errors.WithMessage(err, "failed to get personal space")
	}
	var dataset *entity.Dataset
	err = backoff.Retry(func() error {
		dataset, err = s.knowledgeService.GetDatasetBySpaceID(ctx, space.ID)
		if err != nil {
			if db.IsRecordNotFoundError(err) {
				return err
			}
			return backoff.Permanent(errors.WithMessagef(err, "failed to get dataset for space %s", space.ID))
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Millisecond*100), 3))

	if err != nil {
		return nil, err
	}
	space.DataSetID = dataset.ID

	if opt.NeedMembers {
		space.Members, _, err = s.ListSpaceMembers(ctx, ListSpaceMembersOption{
			SpaceID: space.ID,
			Offset:  0,
			Limit:   -1,
		})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get members for space %s", space.ID)
		}
	}

	return space, nil
}

type ListUserSpacesOption struct {
	Account authentity.Account // Optional: filter by account
	StartID *string
	Limit   int
}

func (s *Service) ListUserSpaces(ctx context.Context, opt ListUserSpacesOption) ([]*entity.Space, string, error) {
	resources, err := s.permService.GetUserPermissions(ctx, permservice.GetUserPermissionsOption{
		Account:      opt.Account,
		ResourceType: entity.ResourceTypeSpace,
	})

	if err != nil {
		return nil, "", err
	}

	// 记录PermissionAction
	spaceActionMap := lo.SliceToMap(resources, func(item *entity.Resource) (string, []entity.PermissionAction) {
		actions := make([]entity.PermissionAction, 0)
		lo.ForEach(item.Permissions, func(perm *entity.Permission, _ int) {
			actions = append(actions, perm.PermissionActions...)
		})
		return item.ExternalID, lo.Uniq(actions)
	})

	// sort resources by creation time in descending order
	sort.Slice(resources, func(i, j int) bool {
		return resources[i].CreatedAt.After(resources[j].CreatedAt)
	})
	spaceIDs := lo.Map(resources, func(item *entity.Resource, _ int) string {
		return item.ExternalID
	})

	// 从StartID开始拉取数据
	startIndex := 0
	if opt.StartID != nil {
		if idx := lo.IndexOf(spaceIDs, *opt.StartID); idx >= 0 {
			startIndex = idx
		}
	}
	limit := lo.Ternary(opt.Limit < 0, len(spaceIDs), opt.Limit)
	endIndex := startIndex + limit
	endIndex = lo.Ternary(endIndex > len(spaceIDs), len(spaceIDs), endIndex)

	pagedIDs := spaceIDs[startIndex:endIndex]
	spaces, err := s.dao.ListSpacesByID(ctx, dal.ListSpacesByIDOption{
		ID:     pagedIDs,
		Status: []string{string(entity.SpaceStatusActive), string(entity.SpaceStatusUninit)},
	})

	if err != nil {
		return nil, "", errors.WithMessage(err, "failed to list spaces by IDs")
	}

	datasetIDs, err := s.knowledgeService.MGetDatasetBySpaceIDs(ctx, pagedIDs)
	if err != nil {
		return nil, "", errors.WithMessage(err, "failed to list datasets by space IDs")
	}
	lo.ForEach(spaces, func(sp *entity.Space, _ int) {
		if datasetID, ok := datasetIDs[sp.ID]; ok {
			sp.DataSetID = datasetID.ID
		}

		sp.PermissionActions = spaceActionMap[sp.ID]
	})

	// 按pagedIDs顺序返回
	spaceMap := lo.KeyBy(spaces, func(sp *entity.Space) string { return sp.ID })
	orderedSpaces := lo.FilterMap(pagedIDs, func(id string, _ int) (*entity.Space, bool) {
		sp, ok := spaceMap[id]
		return sp, ok
	})

	nextID := ""
	if endIndex < len(spaceIDs) {
		nextID = spaceIDs[endIndex]
	}
	return orderedSpaces, nextID, nil
}

type ListSpacesByIDOption struct {
	ID []string
}

// ListSpaces lists spaces based on criteria, typically for a user.
func (s *Service) ListSpacesByIDs(ctx context.Context, opt ListSpacesByIDOption) ([]*entity.Space, error) {
	return s.dao.ListSpacesByID(ctx, dal.ListSpacesByIDOption{
		ID: opt.ID,
	})
}

// AddSpaceMemberOption defines the options for adding a space member
type AddSpaceMembersOption struct {
	SpaceID string                // Space ID to which the member will be added
	Members []*entity.SpaceMember // List of members to be added
	Actor   string                // Actor is the user performing the action, typically an admin or owner
}

// AddSpaceMember adds a user to a space.
func (s *Service) AddSpaceMembers(ctx context.Context, opt AddSpaceMembersOption) ([]*entity.SpaceMember, error) {
	metas := lo.Map(opt.Members, func(member *entity.SpaceMember, _ int) entity.PermissionMeta {
		return entity.PermissionMeta{
			Type:       member.Type,
			ExternalID: member.Name,
			Role:       member.Role,
		}
	})
	resource, err := s.permService.AddResourcePermission(ctx, permservice.AddResourcePermissionOption{
		ResourceExternalID: &opt.SpaceID,
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		PermissionMetas:    metas,
	})

	if err != nil {
		return nil, errors.WithMessagef(err, "failed to add members to space %s", opt.SpaceID)
	}

	return lo.Map(resource.Permissions, func(item *entity.Permission, _ int) *entity.SpaceMember {
		return &entity.SpaceMember{
			Type: item.Type,
			Name: item.ExternalID,
			Role: item.Role,
		}
	}), nil
}

// RemoveSpaceMemberOption defines the options for removing a space member
type RemoveSpaceMembersOption struct {
	SpaceID string
	Actor   string
	Members []*entity.SpaceMember // List of members to be removed
}

// RemoveSpaceMember removes a user from a space.
func (s *Service) RemoveSpaceMembers(ctx context.Context, opt RemoveSpaceMembersOption) error {
	metas := lo.Map(opt.Members, func(member *entity.SpaceMember, _ int) entity.PermissionMeta {
		return entity.PermissionMeta{
			Type:       member.Type,
			ExternalID: member.Name,
			Role:       member.Role,
		}
	})
	_, err := s.permService.RemoveResourcePermission(ctx, permservice.RemoveResourcePermissionOption{
		ResourceExternalID: &opt.SpaceID,
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		PermissionMetas:    metas,
	})

	if err != nil {
		return errors.WithMessagef(err, "failed to remove members to space %s", opt.SpaceID)
	}

	return nil
}

// UpdateSpaceMemberRole updates a user's role in a space.
func (s *Service) UpdateSpaceMemberRole(ctx context.Context, opt UpdateSpaceMemberRoleOption) (*entity.SpaceMember, error) {
	// TODO: not implemented yet
	return nil, nil
}

// ListSpaceMembersOption defines the options for listing space members
type ListSpaceMembersOption struct {
	SpaceID string
	Role    *entity.PermissionRole
	Offset  int
	Limit   int
}

// ListSpaceMembers lists members of a space.
func (s *Service) ListSpaceMembers(ctx context.Context, opt ListSpaceMembersOption) ([]*entity.SpaceMember, int64, error) {
	resource, err := s.permService.GetResource(ctx, permservice.GetResourceOption{
		ResourceExternalID: &opt.SpaceID,
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
		NeedPermission:     true,
	})

	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, 0, permservice.ErrResourceNotFound
		}
		return nil, 0, errors.WithMessagef(err, "failed to get permissions for space %s", opt.SpaceID)
	}

	// Convert all permissions to space members first
	allMembers := lo.Map(resource.Permissions, func(item *entity.Permission, _ int) *entity.SpaceMember {
		return &entity.SpaceMember{
			Type: item.Type,
			Name: item.ExternalID,
			Role: item.Role,
		}
	})

	// Get total count
	totalCount := int64(len(allMembers))

	// Handle pagination
	start := opt.Offset
	end := lo.Ternary(opt.Limit >= 0, opt.Offset+opt.Limit, len(allMembers))
	if start >= len(allMembers) {
		return []*entity.SpaceMember{}, totalCount, nil
	}
	if end > len(allMembers) {
		end = len(allMembers)
	}

	// Return paginated results
	return allMembers[start:end], totalCount, nil
}

// checkAdminPermission verifies if the actorID is an admin of the space or the space owner.
func (s *Service) CheckAdminPermission(ctx context.Context, spaceID string, user *authentity.Account) (bool, error) {
	resource, err := s.permService.GetUserResourcePermission(ctx, permservice.GetUserResourcePermissionOption{
		Account:            *user,
		ResourceExternalID: &spaceID,
		ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
	})
	if err != nil {
		return false, err
	}
	if resource == nil {
		return false, errors.WithMessagef(serverservice.ErrSpaceUserNoAuth, "user %s does not have permission for space %s", user.Username, spaceID)
	}

	return lo.ContainsBy(resource.Permissions, func(item *entity.Permission) bool {
		return item.Role == entity.PermissionRoleAdmin
	}), nil

}

// MustGetSpaceIDWithDefault 确保获取到空间ID，如果为空则获取个人空间ID，如果个人空间 ID 不存在，则创建一个个人空间
func (s *Service) MustGetSpaceIDWithDefault(ctx context.Context, spaceID, username string) (string, error) {
	if spaceID != "" {
		return spaceID, nil
	}
	space, err := s.GetPersonalSpace(ctx, GetPersonalSpaceOption{Owner: username})
	if err != nil && !errors.Is(err, serverservice.ErrSpaceNotFound) {
		return "", errors.WithMessage(err, "failed to get personal space")
	}
	if space == nil {
		space, err = s.CreatePersonalSpace(ctx, username)
		if err != nil {
			return "", errors.WithMessage(err, "failed to create personal space")
		}
	}
	return space.ID, nil
}

// InitSpace 初始化项目空间
func (s *Service) InitSpace(ctx context.Context, req *nextagent.InitSpaceRequest, username string) error {
	space, err := s.dao.GetSpace(ctx, req.GetSpaceID(), true)
	if err != nil {
		return errors.WithMessage(err, "failed to get space")
	}

	if space == nil || space.Type == entity.SpaceTypePersonal || space.Status != entity.SpaceStatusUninit {
		return nil
	}

	dataset, err := s.dao.GetDatasetBySpaceID(ctx, req.GetSpaceID())
	if err != nil {
		return errors.WithMessage(err, "failed to get dataset")
	}

	eg := errgroup.Group{}
	eg.SetLimit(10)
	// 1. 添加基础信息和人员
	eg.Go(func() error {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()

		err := s.updateSpaceBasicAndMembers(ctx, req)
		if err != nil {
			return errors.WithMessage(err, "failed to update space and members")
		}

		return nil
	})

	// 2-1.处理可以直接添加的文档
	eg.Go(func() error {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()

		_, err := s.knowledgeService.CreateLarkDocSync(ctx, req.GetLarkDocConfig(), dataset.ID, username)
		if err != nil {
			return errors.WithMessage(err, "failed to create Lark document")
		}

		return nil
	})

	// 2-2.处理需要异步处理的文档
	eg.Go(func() error {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()

		err := s.knowledgeService.CreateLarkDocAsync(ctx, req.GetLarkDocConfig(), dataset.ID, username)
		if err != nil {
			return errors.WithMessage(err, "failed to create Lark document async")
		}

		return nil
	})

	// 3. 添加仓库
	eg.Go(func() error {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()

		err := s.codeRepoService.CreateCodeRepos(ctx, req.GetRepos(), req.GetSpaceID(), username)
		if err != nil {
			return errors.WithMessage(err, "failed to create code repos")
		}

		return nil
	})

	// 4. 添加服务
	eg.Go(func() error {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()

		err := s.devServiceService.CreateDevService(ctx, req.GetServices(), req.GetSpaceID(), username)
		if err != nil {
			return errors.WithMessage(err, "failed to create dev services")
		}

		return nil
	})

	// 5. 添加平台配置
	eg.Go(func() error {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()

		err := s.platformConfigService.CreatePlatformConfig(ctx, req, username)
		if err != nil {
			return errors.WithMessage(err, "failed to create platform config")
		}

		return nil
	})

	if err = eg.Wait(); err != nil {
		return err
	}

	// 6. 更新空间状态
	_, err = s.dao.UpdateSpace(ctx, dal.UpdateSpaceOption{
		ID:     req.GetSpaceID(),
		Status: lo.ToPtr(entity.SpaceStatusActive),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update space")
	}

	// 7. 发送站内信邀请, 创建知识图谱
	go s.batchCreateNotificationMessage(ctx, req.GetMembers(), req.GetSpaceID(), username, req.GetBasic().GetName())
	go s.createKnowledgeGraph(ctx, req.GetRepos(), req.GetServices(), req.GetSpaceID(), username)

	logs.V1.CtxInfo(ctx, "init space success, spaceID: %s", req.GetSpaceID())
	return nil
}

func (s *Service) updateSpaceBasicAndMembers(ctx context.Context, req *nextagent.InitSpaceRequest) error {
	// 更新基础信息
	_, err := s.dao.UpdateSpace(ctx, dal.UpdateSpaceOption{
		ID:          req.GetSpaceID(),
		Name:        lo.ToPtr(req.GetBasic().GetName().GetCn()),
		NameEN:      lo.ToPtr(req.GetBasic().GetName().GetEn()),
		Description: lo.ToPtr(req.GetBasic().GetDescription().GetCn()),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update space")
	}

	existMembers, _, err := s.ListSpaceMembers(ctx, ListSpaceMembersOption{
		SpaceID: req.GetSpaceID(),
		Offset:  0,
		Limit:   -1,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list space members")
	}
	existMap := make(map[string]struct{})
	for _, member := range existMembers {
		if member.Role != entity.PermissionRoleMember {
			continue
		}

		existMap[genSpaceMemberUniqueID(member)] = struct{}{}
	}

	// 添加成员
	toAddMembers := gslice.FilterMap(req.GetMembers(), func(m *nextagent.SpaceMember) (*entity.SpaceMember, bool) {
		// 本期只添加空间成员
		if m.GetRole() != nextagent.PermissionRole_PermissionRoleMember {
			return nil, false
		}

		sm := &entity.SpaceMember{
			Name: m.GetName(),
			Type: entity.PermissionType(m.GetType()),
			Role: entity.PermissionRole(m.GetRole()),
		}

		if _, ok := existMap[genSpaceMemberUniqueID(sm)]; ok {
			return nil, false
		}

		return sm, true
	})
	if len(toAddMembers) == 0 {
		return nil
	}

	_, err = s.AddSpaceMembers(ctx, AddSpaceMembersOption{
		SpaceID: req.GetSpaceID(),
		Members: gslice.UniqBy(toAddMembers, func(m *entity.SpaceMember) string {
			return genSpaceMemberUniqueID(m)
		}),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to add space members")
	}

	return nil
}

func (s *Service) batchCreateNotificationMessage(ctx context.Context, members []*nextagent.SpaceMember, spaceID, username string, spaceName *nextagent.StringInMultiLang) error {
	var personals, departments []string
	for _, member := range members {
		if member.GetRole() != nextagent.PermissionRole_PermissionRoleMember {
			continue
		}

		switch member.GetType() {
		case nextagent.PermissionTypeUser:
			personals = append(personals, member.GetName())
		case nextagent.PermissionTypeDepartment:
			departments = append(departments, member.GetName())
		default:
		}
	}

	var receiveConfig []*nextagent.ReceiveConfig
	if len(personals) > 0 {
		receiveConfig = append(receiveConfig, &nextagent.ReceiveConfig{
			ReceiveType: nextagent.ReceiveTypePersonal,
			Receivers:   personals,
		})
	}

	if len(departments) > 0 {
		receiveConfig = append(receiveConfig, &nextagent.ReceiveConfig{
			ReceiveType: nextagent.ReceiveTypeDepartment,
			Receivers:   departments,
		})
	}

	var title, content, linkName string
	switch env.GetCurrentVRegion() {
	case env.VREGION_CHINANORTH:
		title = fmt.Sprintf("%s邀请你加入 「%s」", username, spaceName.GetCn())
		content = "欢迎使用项目空间"
		linkName = "进入空间"
	case env.VREGION_SINGAPORECENTRAL:
		title = fmt.Sprintf("%s invites you to join 「%s」", username, spaceName.GetEn())
		content = "Welcome to the project space"
		linkName = "Enter Space"
	default:
	}

	_, err := s.notificationService.CreateNotificationMessage(ctx, &nextagent.CreateNotificationMessageRequest{
		Title:         title,
		Content:       content,
		ReceiveConfig: receiveConfig,
		IsTop:         lo.ToPtr(true),
		LinkInfo: &nextagent.LinkInfo{
			Link: fmt.Sprintf("%s/chat?spaceId=%s", s.conf.BaseConfig.Domain, spaceID),
			Name: linkName,
		},
		Type:     nextagent.MessageTypeInvitation,
		SendLark: lo.ToPtr(true),
	}, username)
	if err != nil {
		return errors.WithMessage(err, "failed to create department notification message")
	}

	return nil
}

func (s *Service) createKnowledgeGraph(ctx context.Context, codeRepos []*nextagent.CodeRepo, tceServices []*nextagent.Service, spaceID, username string) error {
	var updateResources []*graph.UpdateResource
	updateResources = append(updateResources, gslice.Map(codeRepos, func(r *nextagent.CodeRepo) *graph.UpdateResource {
		return &graph.UpdateResource{
			ResourceType: graph.ResourceType_Code,
			Code: &graph.Code{
				RepoName: r.GetRepoName(),
			},
			OperationType: graph.OperationType_Add,
		}
	})...)

	updateResources = append(updateResources, gslice.Map(tceServices, func(s *nextagent.Service) *graph.UpdateResource {
		return &graph.UpdateResource{
			ResourceType: graph.ResourceType_TceService,
			TceService: &graph.TceService{
				Psm:          s.GetName(),
				ControlPlane: graph.ControlPlane_CN,
			},
			OperationType: graph.OperationType_Add,
		}
	})...)

	resp, err := bits_ai_graph.RawCall.CreateGraphUpdateTaskByAime(ctx, &graph.CreateGraphUpdateTaskByAimeRequest{
		Username:        username,
		UpdateResources: updateResources,
		InitGraph:       true,
		AimeSpaceId:     spaceID,
	}, callopt.WithRPCTimeout(30*time.Second), callopt.WithVRegion(env.VREGION_CHINANORTH))
	if err != nil {
		logs.V1.CtxError(ctx, "[createKnowledgeGraph]failed to create knowledge graph, spaceId:%s, err:%v", spaceID, err)
		return err
	}
	logs.V1.CtxInfo(ctx, "[createKnowledgeGraph]create knowledge graph successfully, taskID:%s", resp.GetTaskId())

	return nil
}

func genSpaceMemberUniqueID(m *entity.SpaceMember) string {
	return fmt.Sprintf("%s-%v-%v", m.Name, m.Type, m.Role)
}
