package session

import (
	"context"
	_ "embed"
	"fmt"
	"runtime/debug"
	"strconv"
	"sync"
	"time"

	"code.byted.org/bytedance/redislock"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/AlekSi/pointer"
	"github.com/cenkalti/backoff/v4"
	poolsdk "github.com/panjf2000/ants/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/panics"
	"go.uber.org/fx"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/ab"
	agentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	artifactservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lock"
	monitorservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/monitor"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/service/runtimestarter"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/redis"
)

const (
	SessionListLimitMax = 10000
)

type Service struct {
	idGen             uuid.Generator
	dao               *dal.DAO
	tccConf           *config.AgentSphereTCCConfig
	runtime           *runtimeservice.Service
	artifact          *artifactservice.Service
	userService       *userservice.Service
	agentService      *agentservice.Service
	monitorService    *monitorservice.Service
	larkService       *larkservice.Service
	permissionService *permissionservice.Service
	redis             redis.Client
	llmService        llm.Service
	locker            lock.Lock
	deployCanaryDAO   dal.DeployCanaryDAO
}

type CreateServiceOption struct {
	fx.In
	DAO               *dal.DAO
	TccConf           *config.AgentSphereTCCConfig
	Runtime           *runtimeservice.Service
	Artifact          *artifactservice.Service
	UserService       *userservice.Service
	AgentService      *agentservice.Service
	LarkService       *larkservice.Service
	LlmService        llm.Service
	MonitorService    *monitorservice.Service
	PermissionService *permissionservice.Service
	Redis             redis.Client
	Locker            lock.Lock
	DeployCanaryDAO   dal.DeployCanaryDAO
}

func NewService(opt CreateServiceOption) (*Service, error) {
	return &Service{
		idGen:             uuid.GetDefaultGenerator(nil),
		dao:               opt.DAO,
		tccConf:           opt.TccConf,
		runtime:           opt.Runtime,
		artifact:          opt.Artifact,
		userService:       opt.UserService,
		agentService:      opt.AgentService,
		llmService:        opt.LlmService,
		monitorService:    opt.MonitorService,
		larkService:       opt.LarkService,
		permissionService: opt.PermissionService,
		redis:             opt.Redis,
		locker:            opt.Locker,
		deployCanaryDAO:   opt.DeployCanaryDAO,
	}, nil
}

type CreateSessionOption struct {
	User                      *authentity.Account
	Context                   entity.SessionContext
	Role                      *entity.SessionRole
	SkipMaxRunningAgentsLimit bool
	SkipPrepareCube           bool
	AgentVersion              string
	TemplateID                string
	SpaceID                   string
	LogID                     string
	DisableTesting            bool
}

func (s *Service) CreateSession(ctx context.Context, opt CreateSessionOption) (session *entity.Session, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("CreateSession", err != nil, serverservice.ErrorToErrorReason(err), opt.User.Username)
	}()
	var agentConfigVersion *entity.AgentConfigVersion
	var oldAgentConfig *config.AgentConfig
	// 如果指定了版本，优先指定的版本
	var version = opt.AgentVersion
	skipPrepareCube := opt.SkipPrepareCube
	isCanary := false
	var abParams map[string]any
	if version == "" && !opt.DisableTesting {
		// 获取版本信息（AB测试或金丝雀发布）
		var versionInfo VersionInfo
		versionInfo, err = s.getVersionInfo(ctx, opt.User.Username, opt.User.Department, opt.Role)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get version info: %v", err)
		} else {
			version = versionInfo.Version
			skipPrepareCube = versionInfo.SkipPrepareCube || skipPrepareCube
			isCanary = versionInfo.IsCanary
			abParams = versionInfo.ABParams
		}
	}

	agentConfigVersion, err = s.agentService.GetAgentConfigVersionByRole(ctx, opt.Role, version)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed get agent config version by role: %v", err)
		// 兜底兼容使用老版本配置，后续删除
		conf := serverservice.GetGeneralAgentByRole(opt.Role, opt.AgentVersion)
		if conf == nil {
			return nil, err
		}
		oldAgentConfig = s.runtime.GetAgentConfig(conf.Name, conf.Version)
		if oldAgentConfig == nil {
			return nil, serverservice.ErrAgentVersionNotFound
		}
	}

	if !opt.SkipMaxRunningAgentsLimit {
		// 检查全局会话数限制
		reach, err := s.CheckGlobalSessionLimit(ctx, opt.User, pointer.Get(opt.Role), true)
		if err != nil {
			// return nil, errors.WithMessage(err, "failed to check global session limit")
			// 如果检查出现错误，先临时放行
			logs.V1.CtxError(ctx, "failed to check global session limit: %v", err)
		}
		if reach {
			return nil, serverservice.ErrGlobalMaximumRunningSessionsReached
		}

		var roles []entity.SessionRole
		if opt.Role != nil {
			roles = append(roles, *opt.Role)
		}
		// 避免并发导致突破session限制，确保在count计数前锁住，创建session后释放锁
		lockKey := fmt.Sprintf("next:session_limit_%s", opt.User.Username)
		locker, err := s.locker.Acquire(ctx, lockKey)
		if err != nil {
			log.V1.CtxError(ctx, "failed to acquire lock(%s): %v", lockKey, err)
			if errors.Is(err, redislock.ErrLockHeldByOthers) {
				return nil, errors.WithMessage(serverservice.ErrSessionCreateError, "lock held by others")
			}
		} else {
			defer locker.Release(ctx)
		}
		result, err := s.CanCreateSession(ctx, CanCreateSessionOption{
			Account:         opt.User,
			Roles:           roles,
			UseInternalTool: opt.Context.UseInternalTool,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create session")
		}
		// 老逻辑
		if !result.Allowed {
			return nil, serverservice.ErrMaximumRunningSessionsReached
		}

		// 新逻辑
		if opt.Role != nil {
			var roleRule *entity.CreateSessionAllowed
			for _, r := range result.Results {
				if r.Role == *opt.Role {
					roleRule = &r
					break
				}
			}
			if roleRule == nil {
				return nil, errors.New("role not found")
			}
			if !roleRule.Allowed {
				if roleRule.Type == entity.NotAllowedTypeUseInternalTool {
					return nil, serverservice.ErrNotAllowedUseInternalTool
				} else {
					return nil, serverservice.ErrMaximumRunningSessionsReached
				}
			}
		}

		// 非离线链路，尝试从资源池获取数据
		// 只在 TCE 环境创建
		var poolSize int
		if agentConfigVersion != nil {
			if agentConfigVersion.RuntimeConfig.OrchestrationConfig != nil {
				poolSize = agentConfigVersion.RuntimeConfig.OrchestrationConfig.PoolSize
			}
		} else {
			poolSize = s.tccConf.StratoCubeConfig.GetValue().PoolSize
		}
		if env.InTCE() {
			if !skipPrepareCube && poolSize > 0 {
				session, err = s.dao.GetPreparedSession(ctx, dal.GetPreparedSessionOption{
					Creator:       opt.User.Username,
					Context:       opt.Context,
					Role:          opt.Role,
					TemplateID:    opt.TemplateID,
					SourceSpaceID: opt.SpaceID,
					LogID:         opt.LogID,
					New:           lo.Ternary(agentConfigVersion != nil, true, false),
				})
				if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
					log.V1.CtxError(ctx, "failed to get pending session: %v", err)
				}
				// 拿不到预热容器，打点
				if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
					_ = metrics.NSM.PrepareCubeNotExist.WithTags(&metrics.NextServerPrepareCubeTag{
						Role: int(pointer.Get(opt.Role)),
					}).Store(float64(1))
				}

				if session != nil || (err != nil && errors.Is(err, gorm.ErrRecordNotFound)) {
					go func() {
						// 创建新的预热资源
						defer func() {
							if r := recover(); r != nil {
								log.V1.CtxError(ctx, "panic with create pending session: %+v, %s", r, string(debug.Stack()))
							}
						}()
						// 使用新的 Ctx 传入，防止外部 Ctx 超时后，后续逻辑被取消
						newCtx := context.Background()
						err = s.PrepareSession(newCtx, opt.Role, PrepareSessionOption{
							NewAgentConfig: agentConfigVersion,
							OldAgentConfig: oldAgentConfig,
						})
						if err != nil {
							log.V1.CtxError(ctx, "failed to prepare session: %v", err)
						}
					}()
				}
				if session != nil {
					_ = metrics.NSM.PrepareCubeNotExist.WithTags(&metrics.NextServerPrepareCubeTag{
						Role: int(pointer.Get(opt.Role)),
					}).Store(float64(0))
					return session, nil
				}
			}
		}
	}

	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
		ID: &agentConfigVersion.AgentConfigID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent config")
	}

	runtimeMetadata := entity.SessionRuntimeMetadata{}
	if agentConfigVersion != nil {
		runtimeMetadata = entity.SessionRuntimeMetadata{
			AgentConfigID:        agentConfigVersion.AgentConfigID,
			AgentConfigName:      agentConfig.Name,
			AgentConfigVersion:   agentConfigVersion.Version,
			AgentConfigVersionID: agentConfigVersion.ID,
			LogID:                opt.LogID,
			ABParams:             abParams,
		}
	}
	session, err = s.dao.CreateSession(ctx, dal.CreateSessionOption{
		ID:                 s.idGen.NewID(),
		Creator:            opt.User.Username,
		Status:             entity.SessionStatusCreated,
		Context:            opt.Context,
		RuntimeMetadata:    runtimeMetadata,
		Role:               opt.Role,
		TemplateID:         opt.TemplateID,
		SourceSpaceID:      opt.SpaceID,
		CanResume:          true,
		CanNotResumeReason: entity.SessionCanNotResumeReasonNotAllowed,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create session")
	}
	// 增加灰度任务数
	if isCanary {
		err = s.deployCanaryDAO.StartCanaryTask(ctx, version, session.ID)
		if err != nil {
			log.V1.CtxError(ctx, "incr deploy canary task count failed, err: %v", err)
		}
	}

	return session, nil
}

func (s *Service) getABVersion(ctx context.Context, username, department string, role *entity.SessionRole) (*ab.ABAgentConfig, error) {
	// 获取 AB Test 实验配置
	abConfig := s.tccConf.NextAimeAbConfig.GetPointer()
	info, err := ab.GetABVersion(ctx, abConfig.Token, username, department, abConfig.AppID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get AB test version")
	}

	// 如果没有 AB 测试信息或配置为空，直接返回空版本
	if info == nil || info.AgentConfigs == nil {
		return nil, nil
	}

	log.V1.CtxInfo(ctx, "ab test version: %v", info)

	// 获取 agent ID
	agentID, err := s.agentService.GetAgentIDByRole(ctx, role)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get agent ID by role")
	}

	// 使用 agent ID 查找对应的配置版本
	agentConfigKey := fmt.Sprintf("agent_%s", agentID)
	v, ok := info.AgentConfigs[agentConfigKey]
	if !ok {
		log.V1.CtxInfo(ctx, "ab test not found agent config version, agentID: %s", agentID)
		return nil, nil
	}

	if v.AgentVersionID == "" && v.AgentConfigID == "" {
		log.V1.CtxInfo(ctx, "ab test not found agent config version, agentID: %s", agentID)
		return nil, nil
	}

	// 校验 AB 版本的状态
	opt := dal.GetAgentConfigVersionOption{
		Status: lo.ToPtr(entity.AgentConfigStatusOnline),
	}
	if v.AgentVersionID != "" {
		opt.ID = &v.AgentVersionID
	}
	if v.AgentConfigID != "" {
		opt.AgentConfigID = &v.AgentConfigID
	}
	configVersion, err := s.dao.GetAgentConfigVersion(ctx, opt)

	if err != nil {
		return nil, errors.Wrap(err, "ab test get agent config version failed")
	}

	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
		ID: &configVersion.AgentConfigID,
	})

	if err != nil {
		return nil, errors.Wrap(err, "ab test get config version failed")
	}

	// 校验 agent id 和 agent config version id 是否匹配
	if agentConfig.AgentID != agentID {
		return nil, errors.New("ab test agent id and agent config version id not match")
	}

	// 校验 agent config 类型是否为 abtest
	if agentConfig.Type != entity.AgentConfigTypeAbTest {
		return nil, errors.New("ab test agent config type is not abtest")
	}

	v.AgentVersionID = configVersion.ID
	return &v, nil
}

// VersionInfo 包含版本相关信息
type VersionInfo struct {
	Version         string // 版本ID
	SkipPrepareCube bool   // 是否跳过预热容器
	IsCanary        bool   // 是否是金丝雀版本
	ABParams        map[string]any
}

// getVersionInfo 获取版本信息，整合 AB 测试和金丝雀发布的版本获取逻辑
func (s *Service) getVersionInfo(ctx context.Context, username, department string, role *entity.SessionRole) (VersionInfo, error) {
	result := VersionInfo{}

	// 1. 尝试获取 AB 测试版本
	abVersion, err := s.getABVersion(ctx, username, department, role)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get ab test version: %v", err)
	} else if abVersion != nil {
		// 命中 AB 测试版本，跳过预热容器
		result.Version = abVersion.AgentVersionID
		result.SkipPrepareCube = true
		result.ABParams = abVersion.ABParams
		return result, nil
	}

	// 2. 尝试获取金丝雀版本
	canaryVersion, err := s.getCanaryVersion(ctx, username, role)
	if err != nil {
		return result, errors.Wrap(err, "failed to get canary version")
	} else if canaryVersion != "" {
		result.Version = canaryVersion
		result.IsCanary = true
		result.SkipPrepareCube = true
	}

	return result, nil
}

func (s *Service) getCanaryVersion(ctx context.Context, userName string, role *entity.SessionRole) (string, error) {
	// 获取 agent ID
	agentID, err := s.agentService.GetAgentIDByRole(ctx, role)
	if err != nil {
		return "", errors.Wrap(err, "failed to get agent ID by role")
	}

	// 使用 agent ID 查找对应的配置版本
	canaryVersion, ratioLimit, err := s.deployCanaryDAO.GetDeployCanaryConfig(ctx, agentID)
	if err != nil {
		return "", errors.Wrap(err, "failed to get canary version")
	}
	if canaryVersion == "" {
		return "", nil
	}

	// 根据灰度比例判断是否返回 canary 版本
	successCount, failCount, runningCount, err := s.deployCanaryDAO.GetCanaryTaskStatus(ctx, canaryVersion)
	if err != nil {
		return "", errors.Wrap(err, "failed to get canary task status")
	}

	opt := ab.CheckHitCanaryVersionOption{
		NewVersion: ab.RollingStatus{
			Succeed: successCount,
			Failed:  failCount,
			Running: runningCount,
		},
		MaxProbability: ratioLimit,
	}
	var canaryProbeCount int64 = 0
	// 从 tcc 配置中获取 agent 对应的灰度配置
	rationConfig := s.tccConf.NextDeployReviewConfig.GetValue().CanaryVersionRationConfig
	if r, ok := rationConfig[strconv.Itoa(int(*role))]; ok {
		opt.NewVersion.RunningWeight = r.NewVersion.RunningWeight
		opt.OldVersion = &ab.RollingStatus{
			Succeed:       r.OldVersion.Succeed,
			Failed:        r.OldVersion.Failed,
			Running:       r.OldVersion.Running,
			RunningWeight: r.OldVersion.RunningWeight,
		}
		canaryProbeCount = r.NewVersion.CanaryProbeCount
	}

	// 前期放过一些探测流量
	if successCount+runningCount < canaryProbeCount && failCount == 0 {
		return canaryVersion, nil
	}

	isHit := ab.CheckHitCanaryVersionWithOpt(opt)
	if isHit {
		return canaryVersion, nil
	}

	return "", nil
}

func (s *Service) GetSessionWithDeleted(ctx context.Context, sessionID string, sync bool) (result *entity.Session, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("GetSessionWithDeleted", err != nil, serverservice.ErrorToErrorReason(err), "")
	}()
	session, err := s.dao.GetSessionWithDeleted(ctx, dal.GetSessionOption{
		ID:   sessionID,
		Sync: sync,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to get session: %v", sessionID, err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, serverservice.ErrSessionNotFound
		}
		return nil, errors.WithMessage(err, "failed to get session")
	}
	return session, nil
}

type GetSessionOption struct {
	Account               *authentity.Account
	SessionID             string
	NeedPermissionActions bool
	Sync                  bool
}

func (s *Service) GetSession(ctx context.Context, opt GetSessionOption) (result *entity.Session, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("GetSession", err != nil, serverservice.ErrorToErrorReason(err), "")
	}()
	session, err := s.dao.GetSession(ctx, dal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: opt.Sync,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.V1.CtxWarn(ctx, "session %s not found: %v", opt.SessionID, err)
			return nil, serverservice.ErrSessionNotFound
		}
		logs.V1.CtxError(ctx, "session %s: failed to get session: %v", opt.SessionID, err)
		return nil, errors.WithMessage(err, "failed to get session")
	}

	// get permission action
	if opt.NeedPermissionActions {
		resource, err := s.permissionService.GetUserResourcePermission(ctx, permissionservice.GetUserResourcePermissionOption{
			Account:            lo.FromPtr(opt.Account),
			ResourceExternalID: lo.ToPtr(opt.SessionID),
			ResourceType:       lo.ToPtr(entity.ResourceTypeSession),
		})
		if err != nil {
			logs.V1.CtxError(ctx, "session %s: failed to get permission: %v", opt.SessionID, err)
			return nil, errors.WithMessage(err, "failed to get permission")
		}

		// session.Permissions = resource.Permissions
		lo.ForEach(resource.Permissions, func(perm *entity.Permission, _ int) {
			session.PermissionActions = append(session.PermissionActions, perm.PermissionActions...)
		})
		session.PermissionActions = lo.Uniq(session.PermissionActions)
	}

	return session, nil
}

func (s *Service) GetSessionByReplay(ctx context.Context, replayID string) (result *entity.Session, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() { reportMetricsFunc("GetSessionByReplay", err != nil, serverservice.ErrorToErrorReason(err), "") }()

	replay, err := s.dao.GetReplay(ctx, dal.GetReplayOption{
		ID:    replayID,
		Sync:  true,
		Cache: true,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.V1.CtxWarn(ctx, "replay %s not found", replay)
			return nil, serverservice.ErrReplayNotFound
		}
		logs.V1.CtxError(ctx, "replay %s: failed to get replay: %v", replayID, err)
		return nil, errors.WithMessage(err, "failed to get replay")
	}

	session, err := s.dao.GetSessionWithDeleted(ctx, dal.GetSessionOption{
		ID: replay.SessionID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to get session in replay %d: %v", replay.SessionID, replayID, err)
		return nil, errors.WithMessage(err, "failed to get session")
	}

	return session, nil
}

func (s *Service) DeleteSession(ctx context.Context, sessionID string) error {
	err := s.runtime.SendNextRuntimeImmediatelyDeleteWorkspace(ctx, runtimeservice.ScheduleOption{
		SessionID: sessionID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to send next runtime immediately delete event: %v", sessionID, err)
		return errors.WithMessage(err, "failed to send next runtime immediately delete event")
	}

	err = s.dao.DeleteSession(ctx, dal.DeleteSessionOption{
		ID: sessionID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to delete session: %v", sessionID, err)
		return errors.WithMessage(err, "failed to delete session")
	}
	return nil
}

func (s *Service) BatchDeleteSession(ctx context.Context, sessionIDs []string) error {
	pool, err := poolsdk.NewPool(5)
	if err != nil {
		return errors.WithMessage(err, "failed to create pool")
	}
	defer pool.Release()
	wg := sync.WaitGroup{}
	for _, sessionID := range sessionIDs {
		wg.Add(1)
		_ = pool.Submit(func() {
			defer wg.Done()
			err = s.DeleteSession(ctx, sessionID)
			if err != nil {
				logs.V1.CtxError(ctx, "session %s: failed to delete session: %v", sessionID, err)
				return
			}
			err = s.permissionService.DeleteResource(ctx, permissionservice.DeleteResourceOption{
				ResourceType:       lo.ToPtr(entity.ResourceTypeSession),
				ResourceExternalID: &sessionID,
			})
			if err != nil {
				logs.V1.CtxError(ctx, "session %s: failed to delete resource: %v", sessionID, err)
			}
		})
	}
	wg.Wait()
	return nil
}

type ListSessionsOption struct {
	User                 *authentity.Account
	Offset               int
	Limit                int
	OrderByLastMessageAt *bool
}

func (s *Service) ListSessions(ctx context.Context, opt ListSessionsOption) (c int64, result []*entity.Session, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("ListSessions", err != nil, serverservice.ErrorToErrorReason(err), opt.User.Username)
	}()

	total, sessions, err := s.dao.ListSessions(ctx, dal.ListSessionsOption{
		Creators:             []string{opt.User.Username},
		Offset:               opt.Offset,
		Limit:                opt.Limit,
		OrderByLastMessageAt: opt.OrderByLastMessageAt,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to list sessions: %v", err)
		return 0, nil, errors.WithMessage(err, "failed to list sessions")
	}
	return total, sessions, nil
}

type ListUserSessionsOption struct {
	Offset               int
	Limit                int
	OrderByLastMessageAt *bool
	AgentConfigVersionID *string
	StartTime            *string
	Endtime              *string
	Operator             *authentity.Account
	Status               *entity.SessionStatus
	OrderByCreatedAt     *bool
}

func (s *Service) ListUserSessions(ctx context.Context, opt ListUserSessionsOption) (c int64, result []*entity.Session, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		if opt.Operator != nil {
			reportMetricsFunc("ListUserSessions", err != nil, serverservice.ErrorToErrorReason(err), opt.Operator.Username)
		}
	}()

	daoOpt := dal.ListSessionsOption{
		Offset:               opt.Offset,
		Limit:                opt.Limit,
		OrderByLastMessageAt: opt.OrderByLastMessageAt,
		AgentConfigVersionID: opt.AgentConfigVersionID,
		StartTime:            opt.StartTime,
		EndTime:              opt.Endtime,
		Status:               opt.Status,
		OrderByCreatedAt:     opt.OrderByCreatedAt,
	}

	total, sessions, err := s.dao.ListSessions(ctx, daoOpt)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to list sessions: %v", err)
		return 0, nil, errors.WithMessage(err, "failed to list sessions")
	}
	return total, sessions, nil
}

type ListSessionsInSpaceOption struct {
	User              *authentity.Account
	SpaceID           string
	LastMessageBefore *time.Time
	Limit             int
	// 以下都是过滤条件
	FilterType     *entity.ListSessionFilterType
	Search         *string
	Tab            entity.SessionTab
	Creators       []string
	CreatedAtStart *time.Time
	CreatedAtEnd   *time.Time
	Statuses       []entity.SessionStatus
	Scopes         []entity.SessionScope
	UpdatedAtStart *time.Time
	UpdatedAtEnd   *time.Time
}

type ListSessionInSpaceResult struct {
	Sessions       []*entity.Session
	SpacePublicCnt int64
	SelfCreatedCnt int64
	BeforeTime     *time.Time
}

func (s *Service) ListSessionsInProjectSpace(ctx context.Context, opt ListSessionsInSpaceOption) (result *ListSessionInSpaceResult, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("ListSessionsInProjectSpace", err != nil, serverservice.ErrorToErrorReason(err), opt.User.Username)
	}()

	// get sessions in space from permission service
	resources, err := s.permissionService.GetUserPermissions(ctx, permissionservice.GetUserPermissionsOption{
		Account:      *opt.User,
		ResourceType: entity.ResourceTypeSession,
		GroupID:      lo.ToPtr(opt.SpaceID),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get user permissions: %v", err)
		return nil, errors.WithMessage(err, "failed to get user permissions")
	}

	if len(resources) == 0 {
		return &ListSessionInSpaceResult{}, nil
	}

	// 记录PermissionAction
	sessionActionMap := lo.SliceToMap(resources, func(item *entity.Resource) (string, []entity.PermissionAction) {
		actions := make([]entity.PermissionAction, 0)
		lo.ForEach(item.Permissions, func(perm *entity.Permission, _ int) {
			actions = append(actions, perm.PermissionActions...)
		})
		return item.ExternalID, actions
	})

	selfCreatedIDMap := make(map[string]entity.SessionScope)
	projectAccessIDs := make([]string, 0, len(resources)) // 个人在项目中可见的sessionID，包括公开和自己创建的
	// 扫描resource，找到是用户自己创建的session，根据permission查看是否项目内公开
	lo.ForEach(resources, func(item *entity.Resource, _ int) {
		if item.Owner == opt.User.Username {
			if lo.SomeBy(item.Permissions, func(perm *entity.Permission) bool {
				return perm.Type == entity.PermissionTypeSpace
			}) {
				selfCreatedIDMap[item.ExternalID] = entity.SessionScopeProjectPublic
			} else {
				selfCreatedIDMap[item.ExternalID] = entity.SessionScopePrivate
			}
		}
		projectAccessIDs = append(projectAccessIDs, item.ExternalID)
	})
	// 如果 tab=starred，则只获取收藏过的会话
	if opt.Tab == entity.SessionTabStarred || opt.Tab == entity.SessionTabUnStarred {
		starredIDMap := make(map[string]bool)
		sessionIDs, err := s.ListStarSessionIDsByUsername(ctx, opt.User.Username)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to list starred session ids: %v", err)
			return nil, errors.WithMessage(err, "failed to list starred session ids")
		}
		for _, sessionID := range sessionIDs {
			starredIDMap[sessionID] = true
		}
		var filterSessionIDs []string
		for _, sessionID := range projectAccessIDs {
			if opt.Tab == entity.SessionTabStarred {
				if len(sessionIDs) == 0 {
					return &ListSessionInSpaceResult{}, nil
				}
				if starredIDMap[sessionID] {
					filterSessionIDs = append(filterSessionIDs, sessionID)
				}
			} else {
				if !starredIDMap[sessionID] {
					filterSessionIDs = append(filterSessionIDs, sessionID)
				}
			}
		}
		projectAccessIDs = filterSessionIDs
	}

	all := lo.Ternary(len(projectAccessIDs) > SessionListLimitMax, SessionListLimitMax, len(projectAccessIDs)) //限制一下，如果触发了上限，计数会不准
	total, sessions, err := s.dao.ListSessions(ctx, dal.ListSessionsOption{
		SessionIDs:           projectAccessIDs,
		Limit:                all,
		Offset:               0,
		OrderByLastMessageAt: lo.ToPtr(true),
		Search:               opt.Search,
		Creators:             opt.Creators,
		Statuses:             opt.Statuses,
		CreatedAtStart:       opt.CreatedAtStart,
		CreatedAtEnd:         opt.CreatedAtEnd,
		UpdatedAtStart:       opt.UpdatedAtStart,
		UpdatedAtEnd:         opt.UpdatedAtEnd,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to list sessions: %v", err)
		return nil, errors.WithMessage(err, "failed to list sessions")
	}

	// 过滤
	selfCreatedSessions := lo.Filter(sessions, func(item *entity.Session, _ int) bool { //我创建的session
		_, ok := selfCreatedIDMap[item.ID]
		return ok
	})
	selfCnt := int64(len(selfCreatedSessions))
	if opt.FilterType != nil && *opt.FilterType == entity.SessionListFilterTypeSelfCreated {
		sessions = selfCreatedSessions
	}
	lo.ForEach(sessions, func(item *entity.Session, _ int) {
		item.PermissionActions = sessionActionMap[item.ID]
		if scope, ok := selfCreatedIDMap[item.ID]; ok {
			// 如果是用户自己创建的session，使用之前保存在map中的scope
			item.Scope = scope
		} else {
			// 非用户自己创建的，默认项目内公开
			item.Scope = entity.SessionScopeProjectPublic
		}
	})
	if len(opt.Scopes) > 0 {
		sessions = lo.Filter(sessions, func(item *entity.Session, _ int) bool {
			return lo.Contains(opt.Scopes, item.Scope)
		})
	}

	// 分页逻辑：根据LastMessageBefore的，startIdx开始取x条
	var startIdx int
	if opt.LastMessageBefore != nil {
		_, id, ok := lo.FindIndexOf(sessions, func(item *entity.Session) bool {
			return item.LastMessageAt.Before(*opt.LastMessageBefore)
		})
		startIdx = lo.Ternary(ok, id, 0)
	}
	startIdx = lo.Ternary(startIdx > 0, startIdx, 0)
	endIdx := lo.Ternary(startIdx+opt.Limit < len(sessions), startIdx+opt.Limit, len(sessions))

	var beforeTime *time.Time
	if endIdx < len(sessions) {
		beforeTime = lo.ToPtr(sessions[endIdx].LastMessageAt) //返回下次拉取使用的时间戳
	}

	ret := sessions[startIdx:endIdx]

	return &ListSessionInSpaceResult{
		Sessions:       ret,
		SpacePublicCnt: total,
		SelfCreatedCnt: selfCnt,
		BeforeTime:     beforeTime,
	}, nil
}

// ListSessionsInPersonalSpace
func (s *Service) ListSessionsInPersonalSpace(ctx context.Context, opt ListSessionsInSpaceOption) (result *ListSessionInSpaceResult, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("ListSessionsInPersonalSpace", err != nil, serverservice.ErrorToErrorReason(err), opt.User.Username)
	}()
	var (
		starredSessionIDs []string
		listOption        = dal.ListSessionsOption{
			SourceSpaceID:        lo.ToPtr(opt.SpaceID),
			OrderByLastMessageAt: lo.ToPtr(true),
			Limit:                opt.Limit + 1,
			LastMessageBefore:    opt.LastMessageBefore,
			Search:               opt.Search,
			Creators:             opt.Creators,
			Statuses:             opt.Statuses,
			CreatedAtStart:       opt.CreatedAtStart,
			CreatedAtEnd:         opt.CreatedAtEnd,
			UpdatedAtStart:       opt.UpdatedAtStart,
			UpdatedAtEnd:         opt.UpdatedAtEnd,
		}
	)
	if opt.Tab == entity.SessionTabStarred || opt.Tab == entity.SessionTabUnStarred { // 如果 tab=starred，则只获取收藏过的会话
		starredSessionIDs, err = s.ListStarSessionIDsByUsername(ctx, opt.User.Username)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list starred session ids")
		}
		if opt.Tab == entity.SessionTabStarred {
			if len(starredSessionIDs) == 0 {
				return &ListSessionInSpaceResult{}, nil
			}
			listOption.SessionIDs = starredSessionIDs
		} else {
			listOption.NotInSessionIDs = starredSessionIDs
		}
	}
	count, err := s.dao.CountSessions(ctx, dal.CountSessionsOption{
		SourceSpaceID: lo.ToPtr(opt.SpaceID),
		Creator:       lo.ToPtr(opt.User.Username),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to count sessions: %v", err)
		return nil, errors.WithMessage(err, "failed to count sessions")
	}

	_, sessions, err := s.dao.ListSessions(ctx, listOption)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to list sessions: %v", err)
		return nil, errors.WithMessage(err, "failed to list sessions")
	}

	endIdx := lo.Ternary(opt.Limit < len(sessions), opt.Limit, len(sessions))
	var beforeTime *time.Time
	if endIdx < len(sessions) {
		beforeTime = lo.ToPtr(sessions[endIdx].LastMessageAt) //返回下次拉取使用的时间戳
	}

	actions := s.permissionService.PackPermissionAction(ctx, permissionservice.PackPermissionActionOption{
		PermissionRole: entity.PermissionRoleAdmin,
		ResourceType:   entity.ResourceTypeSession,
	})

	lo.ForEach(sessions, func(item *entity.Session, _ int) {
		item.PermissionActions = actions
	})

	return &ListSessionInSpaceResult{
		Sessions:       sessions[0:endIdx],
		SpacePublicCnt: count,
		SelfCreatedCnt: 0,
		BeforeTime:     beforeTime,
	}, nil
}

type CountSessionOption struct {
	Username string
}

func (s *Service) CountRunningSessions(ctx context.Context, opt CountSessionOption) (int64, error) {
	return s.dao.CountSessions(ctx, dal.CountSessionsOption{
		Creator: lo.ToPtr(opt.Username),
		Status:  []entity.SessionStatus{entity.SessionStatusRunning, entity.SessionStatusWaiting, entity.SessionStatusCreated, entity.SessionStatusIdle},
	})
}

type ListRunningSessionStatusOption struct {
	Username string
}

func (s *Service) ListRunningSessionPartials(ctx context.Context, opt ListRunningSessionStatusOption) ([]*entity.SessionPartial, error) {
	return s.dao.ListSessionPartials(ctx, dal.ListSessionPartialsOption{
		Creator: lo.ToPtr(opt.Username),
		Status:  []entity.SessionStatus{entity.SessionStatusRunning, entity.SessionStatusWaiting, entity.SessionStatusCreated, entity.SessionStatusIdle},
	})
}

type CountTodaySessionOption struct {
	Username string
	Role     entity.SessionRole
	Sync     bool
}

func (s *Service) CountTodayDaySessions(ctx context.Context, opt CountTodaySessionOption) (int64, error) {
	now := time.Now()
	year, month, day := now.Date()
	hour := now.Hour()
	// 0-3 点, 使用前一天时间, 4-23 点使用当天时间
	if hour < 4 {
		day = now.AddDate(0, 0, -1).Day()
	}
	todayFourAM := time.Date(year, month, day, 4, 0, 0, 0, now.Location())
	return s.dao.CountSessions(ctx, dal.CountSessionsOption{
		Creator:             lo.ToPtr(opt.Username),
		LatestAgentResumeAt: &todayFourAM,
		Role:                &opt.Role,
		Status:              []entity.SessionStatus{entity.SessionStatusCreated, entity.SessionStatusRunning, entity.SessionStatusWaiting, entity.SessionStatusIdle, entity.SessionStatusStopped, entity.SessionStatusCanceled},
		Sync:                opt.Sync,
		Unscoped:            true,
	})
}

func (s *Service) CountGlobalRunningSessions(ctx context.Context, role entity.SessionRole, after time.Time) (int64, error) {
	return s.dao.CountSessions(ctx, dal.CountSessionsOption{
		CreatedAt: &after,
		Role:      &role,
		Status:    []entity.SessionStatus{entity.SessionStatusCreated, entity.SessionStatusRunning, entity.SessionStatusWaiting, entity.SessionStatusIdle},
		Unscoped:  true,
	})
}

type CanCreateSessionOption struct {
	Account         *authentity.Account
	Roles           []entity.SessionRole
	UseInternalTool *bool
}

// IsReachGlobalSessionLimit 检查是否达到全局会话数限制
func (s *Service) CheckGlobalSessionLimit(ctx context.Context, user *authentity.Account, role entity.SessionRole, isNewSession bool) (bool, error) {
	limit := s.tccConf.NextGlobalSessionLimitConfig.GetValue()
	// 检查内部developer是否受限
	if !limit.DeveloperLimit.Enabled && s.userService.IsDeveloper(user) {
		return false, nil
	}

	// 检查分角色开关
	var roleLimit *config.RoleLimit
	for _, r := range limit.RolesLimit {
		if r.Role == int(role) {
			roleLimit = &r
			break
		}
	}
	if roleLimit == nil || !roleLimit.Enabled {
		// 未读取到配置，或者该role不开启limit
		return false, nil
	}

	// 是否只检查新建session
	if roleLimit.NewSessionOnly && !isNewSession {
		return false, nil
	}

	recentHoursToCheck := lo.Ternary(roleLimit.RecentHoursToCheck > 0, roleLimit.RecentHoursToCheck, 24)
	timeAfter := time.Now().Add(-1 * time.Duration(recentHoursToCheck) * time.Hour)
	count, err := s.CountGlobalRunningSessions(ctx, role, timeAfter)
	if err != nil {
		return false, errors.WithMessage(err, "failed to count today global running sessions")
	}
	if count >= int64(roleLimit.MaxRunningSessions) {
		return true, nil
	}

	return false, nil
}

func (s *Service) CanCreateSession(ctx context.Context, opt CanCreateSessionOption) (*entity.CanCreateSessionResult, error) {
	if len(opt.Roles) == 0 {
		maxSessionPerDay := s.userService.GetOldUserFeatures(opt.Account).SessionLimit

		if maxSessionPerDay != nil {
			total, err := s.CountTodayDaySessions(ctx, CountTodaySessionOption{
				Username: opt.Account.Username,
				Sync:     true,
			})
			if err != nil {
				return nil, errors.WithMessage(err, "failed to count today day sessions")
			}
			if total >= *maxSessionPerDay {
				return &entity.CanCreateSessionResult{Allowed: false}, nil
			}
		}
		return &entity.CanCreateSessionResult{Allowed: true}, nil
	}

	userFeature := s.userService.GetUserFeatures(ctx, opt.Account)
	if !userFeature.Invited {
		return &entity.CanCreateSessionResult{
			Allowed: false,
		}, nil
	}

	roleMaps := lo.SliceToMap(userFeature.Roles, func(item entity.RoleConfig) (entity.SessionRole, entity.RoleConfig) {
		return item.Role, item
	})
	result := make([]entity.CreateSessionAllowed, 0, len(opt.Roles))
	for _, role := range opt.Roles {
		roleRule, ok := roleMaps[role]
		if !ok {
			result = append(result, entity.CreateSessionAllowed{
				Role:    role,
				Allowed: false,
				Type:    entity.NotAllowedTypeReachedMaximum,
			})
			continue
		}

		count, err := s.CountTodayDaySessions(ctx, CountTodaySessionOption{
			Username: opt.Account.Username,
			Role:     role,
			Sync:     true,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to count today sessions")
		}

		if roleRule.SessionLimit != nil && count >= *roleRule.SessionLimit {
			result = append(result, entity.CreateSessionAllowed{
				Role:    role,
				Allowed: false,
				Type:    entity.NotAllowedTypeReachedMaximum,
			})
			continue
		}

		// 规则不允许使用 internal tool, 但是请求里面为 true 则返回不允许
		if opt.UseInternalTool != nil && *opt.UseInternalTool && !roleRule.AllowUseInternalTool {
			result = append(result, entity.CreateSessionAllowed{
				Role:    role,
				Allowed: false,
				Type:    entity.NotAllowedTypeUseInternalTool,
			})
			continue
		}

		var remainingTimes *int32
		if roleRule.SessionLimit != nil {
			remainingTimes = pointer.ToInt32(int32(*roleRule.SessionLimit - count))
		}
		result = append(result, entity.CreateSessionAllowed{
			Role:           role,
			Allowed:        true,
			RemainingTimes: remainingTimes,
		})
	}

	return &entity.CanCreateSessionResult{
		Allowed: true,
		Results: result,
	}, nil
}

type CanCreateMessageOption struct {
	Session entity.Session
	Account authentity.Account
}

func (s *Service) CanCreateMessage(ctx context.Context, opt CanCreateMessageOption) (bool, error) {
	totalMessage, err := s.CountMessages(ctx, CountMessagesOption{
		SessionID: opt.Session.ID,
		Role:      lo.ToPtr(entity.MessageRoleUser),
		Sync:      true,
	})
	if err != nil {
		return false, errors.WithMessage(err, "failed to count messages")
	}

	// 兼容老逻辑
	if opt.Session.Role == nil {
		userFeature := s.userService.GetOldUserFeatures(&opt.Account)
		if userFeature.MessageLimit != nil && totalMessage >= *userFeature.MessageLimit {
			return false, nil
		}
		return true, nil
	}

	// 新逻辑
	userFeature := s.userService.GetUserFeatures(ctx, &opt.Account)
	if !userFeature.Invited {
		return false, nil
	}
	var role *entity.RoleConfig
	for _, roleConfig := range userFeature.Roles {
		if roleConfig.Role == *opt.Session.Role {
			role = &roleConfig
			break
		}
	}
	if role == nil {
		return false, errors.New("role not found")
	}
	if role.MessageLimit != nil && totalMessage >= *role.MessageLimit {
		_, err = s.UpdateSession(ctx, UpdateSessionOption{ // 达到最大轮数，更新会话状态为 closed
			SessionID: opt.Session.ID,
			Status:    lo.ToPtr(entity.SessionStatusClosed),
		})
		if err != nil {
			return false, errors.WithMessage(err, "failed to update session to closed")
		}
		return false, nil
	}
	return true, nil
}

type CountMessagesOption struct {
	SessionID string
	Role      *entity.MessageRole
	Sync      bool
}

func (s *Service) CountMessages(ctx context.Context, opt CountMessagesOption) (int64, error) {
	return s.dao.CountMessages(ctx, dal.CountMessagesOption{
		SessionID: opt.SessionID,
		Role:      opt.Role,
		Sync:      opt.Sync,
	})

}

type BatchGetMessagesOption struct {
	SessionIDs []string
	Role       entity.MessageRole
	Sync       bool
}

// BatchGetFirstMessage 返回对应 Session 的第一条 Message, map 的 key 为 session
func (s *Service) BatchGetFirstMessage(ctx context.Context, opt BatchGetMessagesOption) (map[string]*entity.Message, error) {
	if len(opt.SessionIDs) == 0 {
		return make(map[string]*entity.Message), nil
	}
	msgs, err := s.dao.BatchGetMessages(ctx, dal.BatchGetMessagesOption{
		SessionIDs: opt.SessionIDs,
		Role:       opt.Role,
		Sync:       opt.Sync,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get messages")
	}
	result := make(map[string]*entity.Message)
	for _, msg := range msgs {
		if _, ok := result[msg.SessionID]; ok {
			// 已经存在，则跳过
			continue
		}
		result[msg.SessionID] = msg
	}
	return result, nil
}

type UpdateSessionOption struct {
	SessionID           string
	Context             *entity.SessionContext
	Title               *string
	Status              *entity.SessionStatus
	RuntimeMetadata     *entity.SessionRuntimeMetadata
	StartedAt           *time.Time
	LastMessageAt       *time.Time
	LatestAgentResumeAt *time.Time
	OperateCube         bool
}

func (s *Service) UpdateSession(ctx context.Context, opt UpdateSessionOption) (session *entity.Session, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()

	if opt.Status != nil && *opt.Status == entity.SessionStatusStopped && opt.OperateCube {
		defer func() { reportMetricsFunc("StopSession", err != nil, serverservice.ErrorToErrorReason(err), "") }()

		err = s.runtime.SendStopRuntime(ctx, runtimeservice.ScheduleOption{
			SessionID: opt.SessionID,
			Immediate: true,
		})

		if err != nil {
			logs.V1.CtxError(ctx, "session %s: failed to send stop runtime message: %v", opt.SessionID, err)
			return nil, errors.WithMessage(err, "failed to send stop runtime message")
		}

		session, err = s.dao.UpdateSession(ctx, dal.UpdateSessionOption{
			ID:              opt.SessionID,
			Status:          opt.Status,
			Title:           opt.Title,
			RuntimeMetadata: opt.RuntimeMetadata,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "session %s: failed to update session: %v", opt.SessionID, err)
			return nil, errors.WithMessage(err, "failed to update session")
		}
	} else if opt.Status != nil && *opt.Status == entity.SessionStatusCanceled && opt.OperateCube {
		defer func() { reportMetricsFunc("CancelSession", err != nil, serverservice.ErrorToErrorReason(err), "") }()

		// 如果是取消，先同步更新DB，之后再发PauseAgent，避免在Agent返回idle时间后，读到旧的session状态
		session, err = s.dao.UpdateSession(ctx, dal.UpdateSessionOption{
			ID:              opt.SessionID,
			Status:          opt.Status,
			Title:           opt.Title,
			RuntimeMetadata: opt.RuntimeMetadata,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "session %s: failed to update session: %v", opt.SessionID, err)
			return nil, errors.WithMessage(err, "failed to update session")
		}

		err := s.runtime.NextPauseAgent(ctx, runtimeservice.NextPauseAgent{
			SessionID: opt.SessionID,
			Exit:      false,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "session %s: failed to send pause runtime message: %v", opt.SessionID, err)
			return nil, errors.WithMessage(err, "failed to send pause runtime message")
		}
	} else {
		defer func() { reportMetricsFunc("UpdateSession", err != nil, serverservice.ErrorToErrorReason(err), "") }()

		// 只更新状态
		session, err = s.dao.UpdateSession(ctx, dal.UpdateSessionOption{
			ID:                  opt.SessionID,
			Status:              opt.Status,
			Title:               opt.Title,
			RuntimeMetadata:     opt.RuntimeMetadata,
			StartedAt:           opt.StartedAt,
			LastMessageAt:       opt.LastMessageAt,
			LatestAgentResumeAt: opt.LatestAgentResumeAt,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "session %s: failed to update session: %v", opt.SessionID, err)
			return nil, errors.WithMessage(err, "failed to update session")
		}
	}

	return session, nil
}

type CreateMessageOption struct {
	SessionID         string
	TaskID            string
	Role              entity.MessageRole
	Content           entity.MessageContent
	Attachments       []*entity.Attachment
	User              *authentity.Account
	EventOffset       int64
	Options           entity.MessageOptions
	AgentVersion      string
	Status            entity.MessageStatus
	TemplateID        string
	TemplateVariables map[string]*entity.TemplateVariableValue
	SpaceID           string
	Mentions          []*agententity.Mention
}

func (s *Service) CreateMessage(ctx context.Context, opt CreateMessageOption) (result *entity.Message, err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("CreateMessage", err != nil, serverservice.ErrorToErrorReason(err), opt.User.Username)
	}()

	session, err := s.dao.GetSession(ctx, dal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: true,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get session `%s`: %v", opt.SessionID, err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, serverservice.ErrSessionNotFound
		}
		return nil, errors.WithMessage(err, "failed to get session")
	}
	if session.Status.IsStopped() && !session.CanResume { // 无法对已停止且无法唤醒的会话发送消息
		return nil, serverservice.ErrSessionStopped
	}
	if session.Status.IsFinal() { // 无法对进入终态的会话继续发送消息
		return nil, serverservice.ErrSessionTerminated
	}

	isNewSession, shouldCheckGlobalSessionLimit := s.shouldCheckGlobalSessionLimit(session)
	if shouldCheckGlobalSessionLimit {
		reach, err := s.CheckGlobalSessionLimit(ctx, opt.User, *session.Role, isNewSession)
		if err != nil {
			// 有错误先放行
			logs.V1.CtxError(ctx, "session %s: failed to check global session limit: %v", session.ID, err)
		}
		if reach {
			logs.V1.CtxWarn(ctx, "session %s: global session limit reached, cannot start agent", session.ID)
			return nil, serverservice.ErrGlobalMaximumRunningSessionsReached
		}
	}

	for i, attachment := range opt.Attachments {
		if attachment == nil {
			continue
		}
		attachmentEntity, err := s.artifact.GetAndUpdateAttachmentArtifact(ctx, opt.SessionID, agententity.Attachment{
			ArtifactID: attachment.ID,
			Path:       attachment.FileName,
			Type:       agententity.AttachmentTypeFile, // 用户上传的目前都是文件
		}, false)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to update or get attachment detail: %s", err)
			continue
		}
		opt.Attachments[i] = attachmentEntity
	}

	message, err := s.dao.CreateMessage(ctx, dal.CreateMessageOption{
		ID:          s.idGen.NewID(),
		SessionID:   opt.SessionID,
		TaskID:      opt.TaskID,
		Role:        opt.Role,
		Content:     opt.Content,
		Creator:     opt.User.Username,
		Attachments: opt.Attachments,
		Options:     opt.Options,
		Status:      opt.Status,
		Mentions:    opt.Mentions,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to create message: %v", opt.SessionID, err)
		return nil, errors.WithMessage(err, "failed to create message")
	}

	// 生成会话标题
	if session.Title == "" {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logs.V1.CtxError(ctx, "recover from panic: %v", err)
				}
			}()
			rewriteQuery := rewriteMentionsInQuery(opt.Content.Content, opt.Mentions)
			title, err := s.GenerateSessionTitle(ctx, GenerateSessionTitleOption{
				Agent:     "General Agent",
				UserInput: rewriteQuery,
				Language:  message.Options.Locale,
			})
			if err != nil {
				logs.V1.CtxError(ctx, "failed to generate session title: %v", err)
				// 兜底使用用户输入
				title = rewriteQuery
			}

			_, err = s.UpdateSession(ctx, UpdateSessionOption{
				SessionID: session.ID,
				Title:     &title,
			})
			if err != nil {
				logs.V1.CtxError(ctx, "failed to update session title: %v", err)
				return
			}
		}()
	}

	// monitor 特定的message
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logs.V1.CtxError(ctx, "recover from panic: %v\n%s", err, debug.Stack())
			}
		}()

		newCtx, cancel := context.WithTimeout(ctxvalues.SetLogID(context.Background(), ctxvalues.LogIDDefault(ctx)), 120*time.Second)
		defer cancel()
		err := s.ProcessScenarioDetectionAndNotification(newCtx, message, session.ID)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to process scenario detection and notification: %v", err)
		}
	}()

	var isSessionFirstMessage bool
	var isSessionResume bool
	defer func() {
		// todo 后续有唤醒操作是，这里追加条件巡检
		if !isSessionFirstMessage {
			return
		}
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logs.V1.CtxError(ctx, "recover from panic: %v", err)
				}
			}()
			// when session first message created, send mq message for session check
			err = s.monitorService.SendDelayedMessage(ctx, conv.JSONBytes(entity.ServerMonitorEvent{
				EventName: entity.MonitorEventTypeSessionCheck,
				SessionCheckEvent: &entity.SessionCheckEvent{
					SessionID: session.ID,
				},
			}))
			if err != nil {
				log.V1.CtxError(ctx, "failed to send delay message for session check: %v", err)
			}
		}()
	}()
	var toolName string
	if len(opt.Content.ToolCalls) > 0 {
		toolName = opt.Content.ToolCalls[0].Name
	}

	switch {
	case session.Status == entity.SessionStatusCreated: // 待启动容器和 agent
		isSessionFirstMessage = true
		isSessionResume = true
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logs.V1.CtxError(ctx, "recover from panic: %v\n%s", err, debug.Stack())
				}
			}()
			err := s.startAgent(ctx, session, message, opt)
			if err != nil {
				logs.V1.CtxError(ctx, "failed to run agent: %v", err)
				return
			}
		}()
	case session.Status == entity.SessionStatusBound: // 容器已绑定，直接启动 agent
		isSessionFirstMessage = true
		isSessionResume = true
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logs.V1.CtxError(ctx, "recover from panic: %v", err)
				}
			}()

			codebaseMentions := s.runtime.GetCodebaseMentions(ctx, opt.SessionID, opt.User)
			// 删除预热的旧pod，创建新的更大内存的pod，或者有大仓缓存的pod
			reCreateCubeStratoPod := false
			// 克隆多个仓库（不一定是大仓）时，需要给pod设置更大内存，每个多的仓库+8G
			if len(codebaseMentions) > 1 {
				reCreateCubeStratoPod = true
			}
			// 大仓预热
			for _, codebaseMention := range codebaseMentions {
				if workspace.RepoInWarmupList(ctx, opt.SessionID, codebaseMention.RepoName) {
					reCreateCubeStratoPod = true
					break
				}
			}
			if reCreateCubeStratoPod {
				err = s.runtime.NextRuntimeDeleteOldContainer(ctx, runtimeservice.ScheduleOption{SessionID: opt.SessionID})
				if err != nil {
					logs.V1.CtxError(ctx, "bigrepo_warmup: failed to delete old container for session %s: %v", opt.SessionID, err)
					return
				}

				err := s.startAgent(ctx, session, message, opt)
				if err != nil {
					logs.V1.CtxError(ctx, "bigrepo_warmup: failed to start new agent for session %s: %v", opt.SessionID, err)
					return
				}
			} else {
				err := s.startAgentWithPrepare(ctx, session, message, opt)
				if err != nil {
					logs.V1.CtxError(ctx, "failed to run agent with prepare: %v", err)
					return
				}
			}
		}()
	case session.Status == entity.SessionStatusStopped: // 容器休眠，重新唤醒容器
		// 不使用协程，同步执行，防止乱序
		isSessionFirstMessage = true
		isSessionResume = true
		nerr := s.resumeAgent(ctx, session, message, opt)
		if nerr != nil {
			logs.V1.CtxError(ctx, "failed to resume agent with stopped: %v", nerr)
		}
	case session.Status == entity.SessionStatusWaiting && toolName == runtimeservice.ToolCallRequiredNameAskUser: // 等待与用户自然语言交互
		go func() {
			defer func() {
				if err := recover(); err != nil {
					logs.V1.CtxError(ctx, "recover from panic: %v", err)
				}
			}()
			err := s.submitToolCallToAgent(ctx, session, message, opt)
			if err != nil {
				logs.V1.CtxError(ctx, "session %s: failed to submit tool call to agent: %v", session.ID, err)
				return
			}
		}()
	default: // 用户打断
		// 存在不需要发消息就可以唤醒的能力，因此需要判断用户这次对话是否是通过不发消息唤醒的，如果是则需要更新 LatestAgentResumeAt 的时间
		// 如果存在 Complete 事件，并且 LatestAgentResumeAt 时间在 Complete 时间之前，则说明需要更新 LatestAgentResumeAt
		latestCompleteEvent, err := s.dao.GetLatestEventByOption(ctx, dal.GetLatestEventByOption{
			SessionID: opt.SessionID,
			EventName: lo.ToPtr(nextagent.EventNameSessionCompleted),
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.V1.CtxError(ctx, "failed to get latest complete event: %v", err)
		}
		if latestCompleteEvent != nil && session.LatestAgentResumeAt != nil && session.LatestAgentResumeAt.Before(latestCompleteEvent.CreatedAt) {
			isSessionResume = true
		}

		go func() {
			defer func() {
				if err := recover(); err != nil {
					logs.V1.CtxError(ctx, "recover from panic: %v", err)
				}
			}()
			err := s.interruptAgent(ctx, session, message, opt)
			if err != nil {
				logs.V1.CtxError(ctx, "session %s: failed to interrupt agent: %v", session.ID, err)
				return
			}

		}()
	}

	// 更新session时间
	timeNow := time.Now()
	updateOption := UpdateSessionOption{
		SessionID:     session.ID,
		LastMessageAt: lo.ToPtr(timeNow),
	}
	if isSessionFirstMessage {
		updateOption.StartedAt = lo.ToPtr(timeNow)
	}
	if isSessionResume {
		updateOption.LatestAgentResumeAt = lo.ToPtr(timeNow)
	}
	_, err = s.UpdateSession(ctx, updateOption)
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to update session time: %v", session.ID, err)
	}
	return message, nil
}

func (s *Service) submitToolCallToAgent(
	ctx context.Context, session *entity.Session, message *entity.Message, opt CreateMessageOption,
) (err error) {
	var (
		eventOffset = opt.EventOffset
		toolCalls   = opt.Content.ToolCalls
		account     = opt.User
	)
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		if err != nil {
			reportMetricsFunc("SubmitToolCallToAgent", err != nil, serverservice.ErrorToErrorReason(err), account.Username)
		}
	}()

	var offset int64
	if eventOffset > 0 {
		offset = eventOffset
	} else {
		event, err := s.dao.GetLatestEventBySessionID(ctx, session.ID, true)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get latest event by session id %d: %v", session.ID, err)
		}
		if event != nil {
			offset = event.EventOffset
		}
	}
	newCtx := s.getNewCtxFromSession(ctx, session)
	for _, toolCall := range toolCalls {
		err = s.SubmitToolCall(newCtx, session, toolCall, account, message, opt.Mentions)
		if err != nil {
			return err
		}
		eventID := s.idGen.NewID()
		err = s.dao.BatchCreateEvent(ctx, []*entity.Event{{
			ID:        eventID,
			SessionID: session.ID,
			TaskID:    message.TaskID,
			EventName: nextagent.EventNameMessageCreate,
			EventData: entity.EventData{
				Event: nextagent.EventNameMessageCreate,
				Data: nextagent.MessageCreateEvent{
					EventID: eventID,
					Message: &nextagent.Message{
						MessageID: message.ID,
						SessionID: message.SessionID,
						TaskID:    message.TaskID,
						Role:      string(message.Role),
						Content:   toolCall.Content,
						Attachments: lo.Map(message.Attachments, func(a *entity.Attachment, _ int) *nextagent.Attachment {
							return &nextagent.Attachment{
								ID:            a.ID,
								FileName:      a.FileName,
								Path:          a.Path,
								Type:          a.Type,
								URL:           a.URL,
								ContentType:   a.ContentType,
								ContentLength: a.ContentLength,
								ParentStepIDs: a.ParentStepIDs,
							}
						}),
						Creator:   message.Creator,
						CreatedAt: message.CreatedAt.String(),
						UpdatedAt: message.UpdatedAt.String(),
						Mentions:  pack.ConvertMentionsToDTO(opt.Mentions),
					},
					Timestamp:   time.Now().Unix(),
					EventOffset: offset,
					EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, offset),
				},
			},
			EventOffset: offset,
			EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, offset),
		}})
		if err != nil {
			logs.V1.CtxError(ctx, "session %s: failed to create message event: %v", session.ID, err)
			return errors.WithMessage(err, "failed to create message event")
		}
	}

	_, err = s.UpdateSession(ctx, UpdateSessionOption{
		SessionID: session.ID,
		Status:    lo.ToPtr(entity.SessionStatusRunning),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to update session status: %v", session.ID, err)
		return errors.WithMessage(err, "failed to update session status")
	}

	return nil
}

func (s *Service) SubmitToolCall(ctx context.Context, session *entity.Session, toolCall *entity.ToolCall, account *authentity.Account,
	message *entity.Message, mentions []*agententity.Mention) error {
	if toolCall == nil || session == nil || account == nil {
		return errors.New("tool call or session or account is nil")
	}
	var (
		newCtx      = s.getNewCtxFromSession(ctx, session)
		results     = map[string]any{}
		messageID   string
		attachments []agententity.AttachmentMeta
	)
	if toolCall.Content != "" {
		results["answer"] = toolCall.Content
	}
	if message != nil {
		attachmentsWithMention := message.Attachments
		for _, m := range mentions { // 将 attachments mention 也作为 attachment 传给 agent
			if m.AttachmentMention != nil {
				attachmentsWithMention = append(attachmentsWithMention, &entity.Attachment{
					ID:       m.AttachmentMention.ArtifactID,
					FileName: m.AttachmentMention.Path,
				})
			}
		}
		results["message_id"] = message.ID
		results["attachments"] = lo.Map(attachmentsWithMention, func(a *entity.Attachment, _ int) map[string]string {
			return map[string]string{
				"artifact_id": a.ID,
				"path":        a.FileName,
			}
		})
		results["mentions"] = pack.ConvertMentionsToDTO(mentions)
		messageID = message.ID
		attachments = lo.FilterMap(attachmentsWithMention, func(a *entity.Attachment, _ int) (agententity.AttachmentMeta, bool) {
			if a == nil {
				return agententity.AttachmentMeta{}, false
			}
			return agententity.AttachmentMeta{
				ArtifactID: a.ID,
				Filename:   a.FileName,
			}, true
		})
	}
	results["save_cookies"] = toolCall.NeedKeepLogin
	switch toolCall.Action {
	case agententity.ToolCallActionReject:
		results["action"] = iris.ToolCallActionReject
	case agententity.ToolCallActionConfirm:
		results["action"] = iris.ToolCallActionConfirm
	case agententity.ToolCallActionTimeout:
		results["action"] = iris.ToolCallActionTimeout
	}

	var larkTokenPointer *string
	larkToken, err := s.larkService.GetUserAccessToken(ctx, session.Creator)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lark user access token: %v", err)
	} else if larkToken != "" {
		larkTokenPointer = &larkToken
	}
	var agentMsg *agententity.Message
	if len(mentions) > 0 {
		agentMsg = &agententity.Message{
			ID:             messageID,
			ConversationID: session.ID,
			Type:           agententity.MessageTypeNormal,
			Creator: agententity.User{
				Type:     agententity.UserTypeUser,
				Username: account.Username,
			},
			Content: agententity.MessageContent{
				Content: toolCall.Content,
			},
			Attachments: attachments,
			Mentions:    mentions,
			Options:     agententity.MessageOptions{},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
	}

	err = s.runtime.NextSubmitToolResults(newCtx, runtimeservice.NextSubmitToolResultsOption{
		SessionID: session.ID,
		URI:       "",
		ID:        toolCall.ID,
		Name:      toolCall.Name,
		Results:   results,
		User:      account,
		LarkToken: larkTokenPointer,
		Message:   agentMsg,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to submit tool result for session `%s`: %v", session.ID, err)
		return errors.WithMessage(err, "failed to submit tool result")
	}
	return nil
}

func (s *Service) interruptAgent(
	ctx context.Context, session *entity.Session, message *entity.Message, opt CreateMessageOption,
) (err error) {
	var (
		user         = opt.User
		agentVersion = opt.AgentVersion
		eventOffset  = opt.EventOffset
	)
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("InterruptAgent", err != nil, serverservice.ErrorToErrorReason(err), user.Username)
	}()
	var content = lo.TernaryF(len(message.Content.ToolCalls) > 0, func() string {
		return message.Content.ToolCalls[0].Content
	}, func() string {
		return message.Content.Content
	})
	// 1. send event
	agentConfig := serverservice.GetGeneralAgentByRole(session.Role, agentVersion)
	newCtx := s.getNewCtxFromSession(ctx, session)
	var larkAPPTokenPointer *string
	larkAPPToken, err := s.larkService.GetUserAccessToken(ctx, session.Creator)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lark user access token: %v", err)
	} else if larkAPPToken != "" {
		larkAPPTokenPointer = &larkAPPToken
	}
	attachments := lo.Map(message.Attachments, func(a *entity.Attachment, _ int) agententity.AttachmentMeta {
		return agententity.AttachmentMeta{
			ArtifactID: a.ID,
			Filename:   a.FileName,
		}
	})
	for _, m := range opt.Mentions { // 将 attachments mention 也作为 attachment 传给 agent
		if m.AttachmentMention != nil {
			attachments = append(attachments, agententity.AttachmentMeta{
				ArtifactID: m.AttachmentMention.ArtifactID,
				Filename:   m.AttachmentMention.Path,
			})
		}
	}
	err = s.runtime.NextSendNewMessage(newCtx, runtimeservice.NextSendMessageOption{
		URI:         "",
		SessionID:   session.ID,
		MessageID:   message.ID,
		Agent:       agentConfig.Name,
		Version:     agentConfig.Version,
		InitialMsg:  content,
		Attachments: attachments,
		User:        user,
		Options: agententity.MessageOptions{
			Locale: message.Options.Locale,
		},
		LarkToken: larkAPPTokenPointer,
		Mentions:  opt.Mentions,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to send new message: %v", session.ID, err)
		return errors.WithMessage(err, "failed to send new message")
	}

	// 2. create user message event.
	var offset int64
	if eventOffset > 0 {
		offset = eventOffset
	} else {
		event, err := s.dao.GetLatestEventBySessionID(ctx, session.ID, true)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get latest event by session id: %v", err)
		}
		if event != nil {
			offset = event.EventOffset
		}
	}

	eventID := s.idGen.NewID()
	err = s.dao.BatchCreateEvent(ctx, []*entity.Event{{
		ID:        eventID,
		SessionID: session.ID,
		TaskID:    message.TaskID,
		EventName: nextagent.EventNameMessageCreate,
		EventData: entity.EventData{
			Event: nextagent.EventNameMessageCreate,
			Data: nextagent.MessageCreateEvent{
				EventID: eventID,
				Message: &nextagent.Message{
					MessageID: message.ID,
					SessionID: message.SessionID,
					TaskID:    message.TaskID,
					Role:      string(message.Role),
					Content:   content,
					Attachments: lo.Map(message.Attachments, func(a *entity.Attachment, _ int) *nextagent.Attachment {
						return &nextagent.Attachment{
							ID:            a.ID,
							FileName:      a.FileName,
							Path:          a.Path,
							Type:          a.Type,
							URL:           a.URL,
							ContentType:   a.ContentType,
							ContentLength: a.ContentLength,
							ParentStepIDs: a.ParentStepIDs,
						}
					}),
					Creator:   message.Creator,
					CreatedAt: message.CreatedAt.String(),
					UpdatedAt: message.UpdatedAt.String(),
					Mentions:  pack.ConvertMentionsToDTO(opt.Mentions),
				},
				Timestamp:   time.Now().Unix(),
				EventOffset: offset,
				EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, offset),
			},
		},
		EventOffset: offset,
		EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, offset),
	}})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to create message event: %v", session.ID, err)
		return errors.WithMessage(err, "failed to create message event")
	}

	_, err = s.UpdateSession(ctx, UpdateSessionOption{
		SessionID: session.ID,
		Status:    lo.ToPtr(entity.SessionStatusRunning),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to update session status: %v", session.ID, err)
		return errors.WithMessage(err, "failed to update session status")
	}

	return nil
}

func (s *Service) startAgent(
	ctx context.Context, session *entity.Session, message *entity.Message, opt CreateMessageOption) error {
	var (
		err          error
		user         = opt.User
		agentVersion = opt.AgentVersion
		templateID   = opt.TemplateID
		eventOffset  = opt.EventOffset
	)

	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("StartAgent", err != nil, serverservice.ErrorToErrorReason(err), user.Username)

		if err != nil {
			logs.V1.CtxError(ctx, "failed to start agent for session `%s`: %v, set session to error", session.ID, err)
			_, updateErr := s.dao.UpdateSession(ctx, dal.UpdateSessionOption{
				ID:     session.ID,
				Status: lo.ToPtr(entity.SessionStatusError),
				RuntimeMetadata: &entity.SessionRuntimeMetadata{
					Error:                err.Error(),
					AgentConfigID:        session.RuntimeMetaData.AgentConfigID,
					AgentConfigVersion:   session.RuntimeMetaData.AgentConfigVersion,
					AgentConfigVersionID: session.RuntimeMetaData.AgentConfigVersionID,
					LogID:                session.RuntimeMetaData.LogID,
				},
			})
			if updateErr != nil {
				logs.V1.CtxError(ctx, "session %s: failed to update session: %v", session.ID, updateErr)
				return
			}
		}
	}()

	// 1. create agent run
	var (
		agent, version string
		params         = make(map[agententity.RuntimeParameterKey]any)
	)
	// get template experience and put to params
	if templateID != "" {
		expParameters, err := s.getExpRunParameters(ctx, templateID, user.Username, opt.TemplateVariables)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get exp run parameters: %v", err)
		}
		if expParameters != nil {
			params[agententity.RuntimeParametersRunWithExperience] = expParameters
		}
	}
	if len(opt.SpaceID) > 0 {
		dataset, err := s.dao.GetDatasetBySpaceID(ctx, opt.SpaceID)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get dataset by spaceID `%s`: %v", opt.SpaceID, err)
		}
		if dataset != nil {
			params[agententity.RuntimeParametersDatasetID] = dataset.ID
		}

		params[agententity.RuntimeParametersSpaceInfomation] = s.getSpaceInfo(ctx, opt.SpaceID)
	}

	if session.RuntimeMetaData.AgentConfigID == "" {
		agentConfig := serverservice.GetGeneralAgentByRole(session.Role, agentVersion)
		agent, version = agentConfig.Name, agentConfig.Version
	}
	attachments := lo.Map(message.Attachments, func(a *entity.Attachment, _ int) agententity.AttachmentMeta {
		return agententity.AttachmentMeta{
			ArtifactID: a.ID,
			Filename:   a.FileName,
		}
	})
	for _, m := range opt.Mentions { // 将 attachments mention 也作为 attachment 传给 agent
		if m.AttachmentMention != nil {
			attachments = append(attachments, agententity.AttachmentMeta{
				ArtifactID: m.AttachmentMention.ArtifactID,
				Filename:   m.AttachmentMention.Path,
			})
		}
	}
	_, err = s.runtime.NextCreateAgentRun(ctx, runtimeservice.NextCreateAgentRunOption{
		SessionID:   session.ID,
		MessageID:   message.ID,
		InitialMsg:  message.Content.Content,
		Agent:       agent,
		Version:     version,
		Attachments: attachments,
		Options: agententity.MessageOptions{
			Locale: message.Options.Locale,
		},
		User:                user,
		EnableInternalTools: session.Context.UseInternalTool,
		MCPs:                session.Context.MCPs,
		Params:              params,
		SpaceID:             opt.SpaceID,
		Mentions:            opt.Mentions,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to create agent run: %v", session.ID, err)
		return errors.WithMessage(err, "failed to create agent run")
	}

	// start to respond to the user in parallel
	go panics.Try(func() {
		err = s.runtime.RunDetectAgent(ctx, runtimeservice.RunDetectAgentOption{
			SessionID: session.ID,
			// minimal message for detect agent
			Message: agententity.Message{
				ID: message.ID,
				Content: agententity.MessageContent{
					Content: message.Content.Content,
				},
				Attachments: lo.Map(message.Attachments, func(a *entity.Attachment, _ int) agententity.AttachmentMeta {
					return agententity.AttachmentMeta{
						ArtifactID: a.ID,
						Filename:   a.FileName,
					}
				}),
				Options: agententity.MessageOptions{
					Locale: message.Options.Locale,
				},
			},
		})
		if err != nil {
			logs.V1.CtxError(ctx, "session %s: failed to run detect agent: %v", session.ID, err)
			// if this fails, fallback to runtime built-in detect
		}
	})

	// 2. send event
	err = s.runtime.NextSchedule(ctx, runtimeservice.ScheduleOption{
		Agent:     agent,
		Version:   version,
		SessionID: session.ID,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to schedule agent: %v", session.ID, err)
		return errors.WithMessage(err, "failed to schedule agent")
	}

	// 3. create user message event.
	eventID := s.idGen.NewID()
	err = s.dao.BatchCreateEvent(ctx, []*entity.Event{{
		ID:        eventID,
		SessionID: session.ID,
		TaskID:    message.TaskID,
		EventName: nextagent.EventNameMessageCreate,
		EventData: entity.EventData{
			Event: nextagent.EventNameMessageCreate,
			Data: nextagent.MessageCreateEvent{
				EventID: eventID,
				Message: &nextagent.Message{
					MessageID: message.ID,
					SessionID: message.SessionID,
					TaskID:    message.TaskID,
					Role:      string(message.Role),
					Content:   message.Content.Content,
					Attachments: lo.Map(message.Attachments, func(a *entity.Attachment, _ int) *nextagent.Attachment {
						return &nextagent.Attachment{
							ID:            a.ID,
							FileName:      a.FileName,
							Path:          a.Path,
							Type:          a.Type,
							URL:           a.URL,
							ContentType:   a.ContentType,
							ContentLength: a.ContentLength,
							ParentStepIDs: a.ParentStepIDs,
						}
					}),
					Creator:   message.Creator,
					CreatedAt: message.CreatedAt.String(),
					UpdatedAt: message.UpdatedAt.String(),
					Mentions:  pack.ConvertMentionsToDTO(opt.Mentions),
				},
				Timestamp:   time.Now().Unix(),
				EventOffset: eventOffset, // 这里默认是从 0 开始，前端一般不用传
				EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, eventOffset),
			},
		},
		EventOffset: eventOffset,
		EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, eventOffset),
	}})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to create message event: %v", session.ID, err)
		return errors.WithMessage(err, "failed to create message event")
	}

	return nil
}

func (s *Service) ListMessages(ctx context.Context, sessionID string) ([]*entity.Message, error) {
	messages, err := s.dao.ListMessages(ctx, dal.ListMessageOption{
		SessionID: sessionID,
		Sync:      false,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list messages")
	}
	return messages, nil
}

type PrepareSessionOption struct {
	NewAgentConfig *entity.AgentConfigVersion
	OldAgentConfig *config.AgentConfig
}

func (s *Service) PrepareSession(ctx context.Context, role *entity.SessionRole, opt PrepareSessionOption) error {
	var count int64
	var err error
	if opt.NewAgentConfig != nil {
		count, err = s.dao.CountSessions(ctx, dal.CountSessionsOption{
			Status: []entity.SessionStatus{entity.SessionStatusPending, entity.SessionStatusPrepared},
			Role:   role,
		})
	} else {
		count, err = s.dao.CountSessions(ctx, dal.CountSessionsOption{
			Status: []entity.SessionStatus{entity.SessionStatusPending, entity.SessionStatusPrepared},
		})
	}

	if err != nil {
		return errors.WithMessage(err, "failed to count sessions")
	}
	_ = metrics.NSM.PrepareCubeSize.WithTags(&metrics.NextServerPrepareCubeTag{
		Role: int(pointer.Get(role)),
	}).Store(float64(count))

	// 这里可能 count 出来的值后续有更新会变，但是因为资源池数量不大，所以这一期先忽略，多一个少一个问题不大
	// 存在的数量少于资源池数量，扩充资源池
	// 只在 TCE 环境跑
	var poolSize int
	if opt.NewAgentConfig != nil {
		if opt.NewAgentConfig.RuntimeConfig.OrchestrationConfig != nil {
			poolSize = opt.NewAgentConfig.RuntimeConfig.OrchestrationConfig.PoolSize
		}
	} else {
		poolSize = s.tccConf.StratoCubeConfig.GetValue().PoolSize
	}
	if int(count) < poolSize {
		for i := 0; i < poolSize-int(count); i++ {
			var session *entity.Session
			if opt.NewAgentConfig != nil {
				session, err = s.dao.CreateSession(ctx, dal.CreateSessionOption{
					ID:      s.idGen.NewID(),
					Creator: "unknown",
					Status:  entity.SessionStatusPending,
					Context: entity.SessionContext{},
					RuntimeMetadata: entity.SessionRuntimeMetadata{
						AgentConfigID:        opt.NewAgentConfig.AgentConfigID,
						AgentConfigVersion:   opt.NewAgentConfig.Version,
						AgentConfigVersionID: opt.NewAgentConfig.ID,
					},
					Role:               role,
					CanResume:          true,
					CanNotResumeReason: entity.SessionCanNotResumeReasonNotAllowed,
				})
			} else {
				session, err = s.dao.CreateSession(ctx, dal.CreateSessionOption{
					ID:                 s.idGen.NewID(),
					Creator:            "unknown",
					Status:             entity.SessionStatusPending,
					Context:            entity.SessionContext{},
					RuntimeMetadata:    entity.SessionRuntimeMetadata{},
					CanResume:          true,
					CanNotResumeReason: entity.SessionCanNotResumeReasonNotAllowed,
				})
			}

			if err != nil {
				log.V1.CtxError(ctx, "failed to create pending session: %v", err)
				continue
			}
			err = s.prepareAgent(ctx, session, opt.OldAgentConfig)
			if err != nil {
				log.V1.CtxError(ctx, "failed to start agent for prepare container: %v", err)
				continue
			}
		}
	}
	return nil
}

func (s *Service) prepareAgent(ctx context.Context, session *entity.Session, oldAgentConfig *config.AgentConfig) error {
	var err error
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("PrepareAgent", err != nil, serverservice.ErrorToErrorReason(err), "")

		if err != nil {
			logs.V1.CtxError(ctx, "failed to prepare agent for session `%s`: %v, set session to error", session.ID, err)
			_, updateErr := s.dao.UpdateSession(ctx, dal.UpdateSessionOption{
				ID:     session.ID,
				Status: lo.ToPtr(entity.SessionStatusError),
				RuntimeMetadata: &entity.SessionRuntimeMetadata{
					Error:                err.Error(),
					AgentConfigID:        session.RuntimeMetaData.AgentConfigID,
					AgentConfigVersion:   session.RuntimeMetaData.AgentConfigVersion,
					AgentConfigVersionID: session.RuntimeMetaData.AgentConfigVersionID,
					LogID:                session.RuntimeMetaData.LogID,
				},
			})
			if updateErr != nil {
				logs.V1.CtxError(ctx, "session %s: failed to update session: %v", session.ID, updateErr)
				return
			}
		}
	}()

	// 1. create agent run
	if oldAgentConfig != nil {
		_, err = s.runtime.NextCreateAgentRunForPrepare(ctx, runtimeservice.NextCreateAgentRunForPrepare{
			Agent:     oldAgentConfig.Agent,
			Version:   oldAgentConfig.Version,
			SessionID: session.ID,
		})
	} else {
		_, err = s.runtime.NextCreateAgentRunForPrepare(ctx, runtimeservice.NextCreateAgentRunForPrepare{
			SessionID: session.ID,
		})
	}

	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to create agent run: %v", session.ID, err)
		return errors.WithMessage(err, "failed to create agent run")
	}

	// 2. send event
	if oldAgentConfig != nil {
		err = s.runtime.NextSchedule(ctx, runtimeservice.ScheduleOption{
			Agent:     oldAgentConfig.Agent,
			Version:   oldAgentConfig.Version,
			SessionID: session.ID,
		})
	} else {
		err = s.runtime.NextSchedule(ctx, runtimeservice.ScheduleOption{
			SessionID: session.ID,
		})
	}
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to schedule agent: %v", session.ID, err)
		return errors.WithMessage(err, "failed to schedule agent")
	}
	return nil
}

func (s *Service) startAgentWithPrepare(ctx context.Context, session *entity.Session, message *entity.Message, opt CreateMessageOption) error {
	var (
		err          error
		user         = opt.User
		agentVersion = opt.AgentVersion
		templateID   = opt.TemplateID
		eventOffset  = opt.EventOffset
	)
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("StartAgentWithPrepare", err != nil, serverservice.ErrorToErrorReason(err), user.Username)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to start agent with prepare for session `%s`: %v, set session to error", session.ID, err)
			_, updateErr := s.dao.UpdateSession(ctx, dal.UpdateSessionOption{
				ID:     session.ID,
				Status: lo.ToPtr(entity.SessionStatusError),
				RuntimeMetadata: &entity.SessionRuntimeMetadata{
					Error:                err.Error(),
					AgentConfigID:        session.RuntimeMetaData.AgentConfigID,
					AgentConfigVersion:   session.RuntimeMetaData.AgentConfigVersion,
					AgentConfigVersionID: session.RuntimeMetaData.AgentConfigVersionID,
					LogID:                session.RuntimeMetaData.LogID,
				},
			})
			if updateErr != nil {
				logs.V1.CtxError(ctx, "session %s: failed to update session: %v", session.ID, updateErr)
				return
			}
		}
	}()

	// 1. update agent
	var (
		agents []agententity.AgentMetadata
		params = make(map[agententity.RuntimeParameterKey]any)
	)
	if session.RuntimeMetaData.AgentConfigID == "" {
		agentConfig := serverservice.GetGeneralAgentByRole(session.Role, agentVersion)
		agents = []agententity.AgentMetadata{{Agent: agentConfig.Name, Version: agentConfig.Version}}
	}

	// get template experience and put to params
	if templateID != "" {
		expParameters, err := s.getExpRunParameters(ctx, templateID, user.Username, opt.TemplateVariables)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get exp run parameters: %v", err)
		}
		if expParameters != nil {
			params[agententity.RuntimeParametersRunWithExperience] = expParameters
		}
	}

	attachments := lo.Map(message.Attachments, func(a *entity.Attachment, _ int) agententity.AttachmentMeta {
		return agententity.AttachmentMeta{
			ArtifactID: a.ID,
			Filename:   a.FileName,
		}
	})
	for _, m := range message.Mentions { // 将 attachments mention 也作为 attachment 传给 agent
		if m.AttachmentMention != nil {
			attachments = append(attachments, agententity.AttachmentMeta{
				ArtifactID: m.AttachmentMention.ArtifactID,
				Filename:   m.AttachmentMention.Path,
			})
		}
	}

	if session.SourceSpaceID != "" {
		params[agententity.RuntimeParametersSpaceInfomation] = s.getSpaceInfo(ctx, session.SourceSpaceID)
	}

	run, err := s.runtime.NextUpdateAgentRunForPrepare(ctx, runtimeservice.NextUpdateAgentRunForPrepare{
		SessionID:   session.ID,
		MessageID:   message.ID,
		InitialMsg:  message.Content.Content,
		Attachments: attachments,
		Options: agententity.MessageOptions{
			Locale: message.Options.Locale,
		},
		User:                user,
		EnableInternalTools: session.Context.UseInternalTool,
		MCPs:                session.Context.MCPs,
		Agents:              agents,
		Params:              params,
		SpaceID:             opt.SpaceID,
		Mentions:            opt.Mentions,
	})
	if err != nil {
		log.V1.CtxError(ctx, "session %s: failed to start agent with prepare: %v", session.ID, err)
		return errors.WithMessage(err, "failed to start agent with prepare")
	}

	// 2. run agent
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "panic in run agent: %+v, stacktrace: %s, session id: %s, uri: %s", r, string(debug.Stack()), session.ID, run.RuntimeMetadata.URI)
			}
		}()
		newCtx := s.getNewCtxFromSession(ctx, session)
		operation := func() error {
			return s.runtime.NextRunAgent(newCtx, runtimeservice.NextRunAgentOption{
				SessionID: session.ID,
				URI:       run.RuntimeMetadata.URI,
			})
		}
		err = backoff.Retry(operation, backoff.WithContext(backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3), ctx))
		if err != nil {
			log.V1.CtxError(ctx, "failed to run agent: %+v, session id: %s, uri: %s, set session to error", err, session.ID, run.RuntimeMetadata.URI)
			session, err = s.UpdateSession(ctx, UpdateSessionOption{
				SessionID: session.ID,
				Status:    pointer.To(entity.SessionStatusError),
			})
			if err != nil {
				log.V1.CtxError(ctx, "failed to update session: %+v", err)
			}
		} else {
			session, err = s.UpdateSession(ctx, UpdateSessionOption{
				SessionID: session.ID,
				Status:    pointer.To(entity.SessionStatusRunning),
			})
			if err != nil {
				log.V1.CtxError(ctx, "failed to update session: %+v", err)
			}
		}
	}()

	// 3. create user message event.
	eventID := s.idGen.NewID()
	err = s.dao.BatchCreateEvent(ctx, []*entity.Event{{
		ID:        eventID,
		SessionID: session.ID,
		TaskID:    message.TaskID,
		EventName: nextagent.EventNameMessageCreate,
		EventData: entity.EventData{
			Event: nextagent.EventNameMessageCreate,
			Data: nextagent.MessageCreateEvent{
				EventID: eventID,
				Message: &nextagent.Message{
					MessageID: message.ID,
					SessionID: message.SessionID,
					TaskID:    message.TaskID,
					Role:      string(message.Role),
					Content:   message.Content.Content,
					Attachments: lo.Map(message.Attachments, func(a *entity.Attachment, _ int) *nextagent.Attachment {
						return &nextagent.Attachment{
							ID:            a.ID,
							FileName:      a.FileName,
							Path:          a.Path,
							Type:          a.Type,
							URL:           a.URL,
							ContentType:   a.ContentType,
							ContentLength: a.ContentLength,
							ParentStepIDs: a.ParentStepIDs,
						}
					}),
					Creator:   message.Creator,
					CreatedAt: message.CreatedAt.String(),
					UpdatedAt: message.UpdatedAt.String(),
					Mentions:  pack.ConvertMentionsToDTO(opt.Mentions),
				},
				Timestamp:   time.Now().Unix(),
				EventOffset: eventOffset, // 这里默认是从 0 开始，前端一般不用传
				EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, eventOffset),
			},
		},
		EventOffset: eventOffset,
		EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, eventOffset),
	}})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to create message event: %v", session.ID, err)
		return errors.WithMessage(err, "failed to create message event")
	}

	// 4. update session status to running

	return nil
}

func (s *Service) resumeAgent(ctx context.Context, session *entity.Session, message *entity.Message, opt CreateMessageOption) error {
	var (
		err         error
		user        = opt.User
		eventOffset = opt.EventOffset
	)
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("ResumeAgent", err != nil, serverservice.ErrorToErrorReason(err), user.Username)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to resume agent for session `%s`: %v, set session to error", session.ID, err)
			_, updateErr := s.dao.UpdateSession(ctx, dal.UpdateSessionOption{
				ID:     session.ID,
				Status: lo.ToPtr(entity.SessionStatusError),
				RuntimeMetadata: &entity.SessionRuntimeMetadata{
					Error:                err.Error(),
					AgentConfigID:        session.RuntimeMetaData.AgentConfigID,
					AgentConfigVersion:   session.RuntimeMetaData.AgentConfigVersion,
					AgentConfigVersionID: session.RuntimeMetaData.AgentConfigVersionID,
					LogID:                session.RuntimeMetaData.LogID,
				},
			})
			if updateErr != nil {
				logs.V1.CtxError(ctx, "session %s: failed to update session: %v", session.ID, updateErr)
				return
			}
		}
	}()

	// 2. start workspace
	err = s.runtime.NextRuntimeStartWorkspace(ctx, runtimeservice.StartWorkspaceOption{
		SessionID: session.ID,
		User:      user,
		FromType:  runtimestarter.FromTypeMessage,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to start worspace: %v", session.ID, err)
		return errors.WithMessage(err, "failed to start agent")
	}

	// 3. create user message event.
	eventID := s.idGen.NewID()
	err = s.dao.BatchCreateEvent(ctx, []*entity.Event{{
		ID:        eventID,
		SessionID: session.ID,
		TaskID:    message.TaskID,
		EventName: nextagent.EventNameMessageCreate,
		EventData: entity.EventData{
			Event: nextagent.EventNameMessageCreate,
			Data: nextagent.MessageCreateEvent{
				EventID: eventID,
				Message: &nextagent.Message{
					MessageID: message.ID,
					SessionID: message.SessionID,
					TaskID:    message.TaskID,
					Role:      string(message.Role),
					Content:   message.Content.Content,
					Attachments: lo.Map(message.Attachments, func(a *entity.Attachment, _ int) *nextagent.Attachment {
						return &nextagent.Attachment{
							ID:            a.ID,
							FileName:      a.FileName,
							Path:          a.Path,
							Type:          a.Type,
							URL:           a.URL,
							ContentType:   a.ContentType,
							ContentLength: a.ContentLength,
							ParentStepIDs: a.ParentStepIDs,
						}
					}),
					Creator:   message.Creator,
					CreatedAt: message.CreatedAt.String(),
					UpdatedAt: message.UpdatedAt.String(),
					Mentions:  pack.ConvertMentionsToDTO(opt.Mentions),
				},
				Timestamp:   time.Now().Unix(),
				EventOffset: eventOffset, // 这里默认是从 0 开始，前端一般不用传
				EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, eventOffset),
			},
		},
		EventOffset: eventOffset,
		EventKey:    entity.GetMessageCreateEventKey(session.ID, message.ID, eventOffset),
	}})
	if err != nil {
		logs.V1.CtxError(ctx, "session %s: failed to create message event: %v", session.ID, err)
		return errors.WithMessage(err, "failed to create message event")
	}

	return nil
}

// 上传的 logid 和 session 存储不一致时以 session 为准
// 短期兼容逻辑，后续前端会上传同一个 logid，理论上不会有这个问题
func (s *Service) getNewCtxFromSession(ctx context.Context, session *entity.Session) context.Context {
	logID, _ := ctxvalues.LogID(ctx)
	var newCtx context.Context
	if session.RuntimeMetaData.LogID != "" && logID != session.RuntimeMetaData.LogID {
		newCtx = ctxvalues.SetLogID(ctx, session.RuntimeMetaData.LogID)
	} else {
		newCtx = ctx
	}
	return newCtx
}

func (s *Service) GetRuntimeStatus(ctx context.Context, sessionID string) (bool, error) {
	return s.runtime.CheckCubeIsStopped(ctx, sessionID)
}

type ListSessionReplaysOption struct {
	SessionID string
	Limit     int
	Offset    int
}

func (s *Service) ListSessionReplays(ctx context.Context, opt ListSessionReplaysOption) ([]*entity.Replay, error) {
	return s.dao.ListReplays(ctx, dal.ListReplayOption{
		SessionID: opt.SessionID,
		Limit:     opt.Limit,
		Offset:    opt.Offset,
	})
}
