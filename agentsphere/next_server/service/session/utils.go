package session

import (
	"context"
	"encoding/xml"
	"fmt"
	"sort"
	"strings"

	"code.byted.org/lang/gg/gslice"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/logs/v2"
)

// rewriteMentionsInQuery rewrites the mention in the query `[resource](aime://resource_type/resource_id)` to the prompt string `@resource_description`.
func rewriteMentionsInQuery(content string, mentions []*agententity.Mention) string {
	lo.ForEach(mentions, func(mention *agententity.Mention, _ int) {
		if mention == nil {
			return
		}
		mentionLink := fmt.Sprintf("[resource](aime://mention/%s)", mention.ID)
		content = strings.ReplaceAll(content, mentionLink, mention.PromptString())
	})
	return content
}

type GenerateSessionTitleOption struct {
	Agent     string
	UserInput string
	Language  string
}

func (s *Service) GenerateSessionTitle(ctx context.Context, opt GenerateSessionTitleOption) (string, error) {
	language := "zh"
	if opt.Language != "" {
		language = opt.Language
	}
	sessionConfig := s.tccConf.NextSessionConfig.GetPointer()
	r, err := s.llmService.ChatCompletion(ctx, llm.ChatCompletionRequest{
		Model: sessionConfig.GenerateTitleModel,
		Messages: []llm.ChatCompletionMessage{
			{
				Role: "system",
				Content: fmt.Sprintf(`Summarize given conversational message into a concise "topic". The topic should capture the essence or main topic of the conversation effectively.

# Steps
- Analyze the core message and main points of the conversation.
- Identify key themes, subjects, or ideas discussed.
- Formulate a brief, descriptive topic that encapsulates these themes or topics.
- If the user's message is not clear or lacks context, output user's message as the topic.
- The topic's language should be same as the user's message. For example, if the user's message is in Chinese, the topic should also be in Chinese.

# Output Format
- You must output in language %s.
- Return only the topic as plain text, without any additional formatting or characters.
- Limit the topic to a maximum of 100 characters.
- DO NOT ASK QUESTIONS OR REQUEST ADDITIONAL INFORMATION FROM THE USER.

# Examples
- Message: "I need a 7-day Japan itinerary for April 15-23 from Seattle, with a $2500-5000 budget for my fiancée and me. We love historical sites, hidden gems, and Japanese culture (kendo, tea ceremonies, Zen meditation). We want to see Nara's deer and explore cities on foot. I plan to propose during this trip and need a special location recommendation. Please provide a detailed itinerary and a simple HTML travel handbook with maps, attraction descriptions, essential Japanese phrases, and travel tips we can reference throughout our journey."
  Topic: "7-Day Japan Itinerary with Proposal Ideas"
- Message: "123"
  Topic: "123"
- Message: "Here's last month's sales data from my Amazon store. Could you analyze it thoroughly with visualizations and recommend specific, data-driven strategies to boost next month's sales by 10%%?"
  Topic: "Sales Analysis and Strategies for Amazon Store
- Message: "帮我看看北京有什么好吃的"
  Topic: "北京美食推荐咨询"
- Message: "https://github.com/browser-use/browser-use 总结这个仓库"
  Topic: "browser-use/browser-use 仓库总结"`, language),
			},
			{
				Role:    "user",
				Content: fmt.Sprintf("Message to generate topic:\n---\n%s\n---\nplease only output the topic, without any other text, the output cannot exceed 100 characters.", opt.UserInput),
			},
		},
		Stream: false,
		SensitiveOpt: llm.SensitiveOpt{
			DisableAntiDirt:    lo.ToPtr(true),
			DisableLLMSecurity: lo.ToPtr(true),
		},
		Tag: "session_title_generation",
	})
	if err != nil {
		return "", err
	}

	res, err := r.Aggregation(ctx)
	if err != nil {
		return "", err
	}

	return res.GetFirstChoiceContent(), nil
}

type ScenarioDetectionResult struct {
	Analysis string `xml:"analysis"`
	Scenario string `xml:"scenario"`
	Risk     string `xml:"risk"`
	Decision string `xml:"decision"`
}

type DetectScenarioOption struct {
	SessionID            string
	Message              *entity.Message
	DetectionModelConfig config.NextAgentScenarioDetectionModelConfig
	DetectSystemPrompt   string
	Language             string
}

func (s *Service) DetectScenario(ctx context.Context, opt DetectScenarioOption) (*ScenarioDetectionResult, error) {
	// 获取历史对话消息, 并构建用户提示词
	messages, err := s.dao.ListMessages(ctx, dal.ListMessageOption{
		SessionID: opt.SessionID,
		Sync:      true, //读主库，否则主从延迟可能导致读不到最新的消息导致场景检测不准确
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list messages")
	}

	// 如果messages中不包含opt.Message，加入到最后
	if !lo.ContainsBy(messages, func(item *entity.Message) bool {
		return item.ID == opt.Message.ID
	}) {
		messages = append(messages, opt.Message)
	}
	sort.Slice(messages, func(i, j int) bool {
		return messages[i].CreatedAt.Unix() < messages[j].CreatedAt.Unix()
	})
	userMessage := strings.Builder{}
	lo.ForEach(messages, func(item *entity.Message, _ int) {
		switch item.Role {
		case entity.MessageRoleUser:
			userMessage.WriteString("user:\n")
			query := item.Content.Content
			if item.Content.ToolCalls != nil {
				for _, toolCall := range item.Content.ToolCalls {
					query += fmt.Sprintf("\n%s", toolCall.Content)
				}
			}
			userMessage.WriteString(rewriteMentionsInQuery(query, item.Mentions))
			userMessage.WriteString("\n\n")
		case entity.MessageRoleAssistant:
			userMessage.WriteString("response:\n")
			userMessage.WriteString(item.Content.Content)
			userMessage.WriteString("\n\n")
		}
	})

	// 构建包含所有场景的系统提示词
	systemPrompt := opt.DetectSystemPrompt
	r, err := s.llmService.ChatCompletion(ctx, llm.ChatCompletionRequest{
		Model: lo.Ternary(opt.DetectionModelConfig.Model != "", opt.DetectionModelConfig.Model, "doubao-1.5-pro-32k-intent"),
		Messages: []llm.ChatCompletionMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: fmt.Sprintf("用户查询：%s", userMessage.String()),
			},
		},
		Stream: false,
		SensitiveOpt: llm.SensitiveOpt{
			DisableAntiDirt:    lo.ToPtr(true),
			DisableLLMSecurity: lo.ToPtr(true),
		},
		FallbackModels: lo.Ternary(len(opt.DetectionModelConfig.FallbackModels) > 0, opt.DetectionModelConfig.FallbackModels, []string{"doubao-1.5-pro-32k-intent"}),
		Temperature:    lo.ToPtr(lo.Ternary(opt.DetectionModelConfig.Temperature > 0, opt.DetectionModelConfig.Temperature, 0.1)),
		MaxTokens:      lo.ToPtr(lo.Ternary(opt.DetectionModelConfig.MaxTokens > 0, opt.DetectionModelConfig.MaxTokens, 8192)),
		Tag:            lo.Ternary(opt.DetectionModelConfig.Tag != "", opt.DetectionModelConfig.Tag, "scenario_detection"),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call llm for scenario detection")
	}

	res, err := r.Aggregation(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to aggregate llm response")
	}

	content := res.GetFirstChoiceContent()

	var result ScenarioDetectionResult
	if err := xml.Unmarshal([]byte(content), &result); err != nil {
		logs.V1.CtxError(ctx, "failed to parse scenario detection results: %v, content: %s", err, content)
		return nil, errors.WithMessage(err, "failed to parse scenario detection results")
	}

	return &result, nil
}

func (s *Service) ProcessScenarioDetectionAndNotification(ctx context.Context, message *entity.Message, sessionID string) (err error) {
	// 场景检测 metrics
	reportMetricsFunc := metrics.NSM.ReportScenarioDetectionMetrics()
	var (
		errOperation string
		errReason    string
	)

	defer func() {
		reportMetricsFunc(errOperation, err != nil, errReason)
	}()

	if message == nil {
		return errors.New("message is nil")
	}

	scenarioConfig := s.tccConf.NextAgentScenarioDetectionConfig.GetPointer()
	if scenarioConfig == nil || !scenarioConfig.Enabled {
		return nil
	}

	// 场景检测
	detectionResult, err := s.DetectScenario(ctx, DetectScenarioOption{
		SessionID:            sessionID,
		Message:              message,
		DetectionModelConfig: scenarioConfig.DetectionModelConfig,
		DetectSystemPrompt:   scenarioConfig.ScenarioDetectPrompt,
	})
	if err != nil {
		errOperation, errReason = "detect", "detect_failed"
		return errors.Errorf("failed to detect scenario: %v", err)
	}
	logs.V1.CtxInfo(ctx, "sent scenario detection summary notification for scenarios %+v", detectionResult)

	targetChatID := scenarioConfig.LarkChatID
	query := message.Content.Content
	if message.Content.ToolCalls != nil {
		for _, toolCall := range message.Content.ToolCalls {
			query += fmt.Sprintf("\n%s", toolCall.Content)
		}
	}
	query = rewriteMentionsInQuery(query, message.Mentions)
	queryRunes := []rune(query)
	if len(queryRunes) >= 1024 {
		queryRunes = append(queryRunes[:1024], []rune("(已缩略...)")...)
	}
	if detectionResult.Decision == "pause" && targetChatID != "" {
		err := s.larkService.SendScenarioDetectionSummaryNotification(ctx,
			sessionID,
			targetChatID,
			&larkservice.ScenarioDetectionResult{
				Analysis: detectionResult.Analysis,
				Scenario: detectionResult.Scenario,
				Risk:     detectionResult.Risk,
				Decision: detectionResult.Decision,
			},
			string(queryRunes),
		)
		if err != nil {
			errOperation, errReason = "notify", "notify_failed"
			return err
		}
	}
	return nil
}

func (s *Service) GetSessionRuntimeMeta(ctx context.Context, sessionID string, sync bool) (result *agententity.AgentRun, err error) {
	return s.runtime.GetAgentRun(ctx, runtimeservice.GetAgentRunOption{SessionID: sessionID, Sync: sync})
}

func (s *Service) getExpRunParameters(ctx context.Context, templateID, username string, variables map[string]*entity.TemplateVariableValue) (
	*agententity.ExpRunParameters, error) {
	if templateID == "" || username == "" {
		return nil, fmt.Errorf("template id or username is empty")
	}
	template, err := s.dao.GetTemplateNormalVersionByTemplateID(ctx, templateID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get template version by template id")
	}
	variablePlaceholders := make(map[string]string)
	for k, v := range variables { // 用户填写的变量内容
		if v == nil {
			continue
		}
		if v.Content != nil {
			variablePlaceholders[k] = *v.Content
		}
		if len(v.Attachments) > 0 {
			variablePlaceholders[k] += strings.Join(lo.Map(v.Attachments, func(a *entity.Attachment, _ int) string {
				if a == nil {
					return ""
				}
				return a.FileName
			}), ",")
		}
	}
	return &agententity.ExpRunParameters{
		UserQueryPlaceholders: variablePlaceholders,
		UserQueryTemplate: &agententity.ExpUserQueryTemplate{
			Name:                  template.Name,
			UserQuery:             template.PromptContent,
			UserQueryPlaceholders: template.PromptVariables.ConvertToPlaceholder(),
		},
		ProgressPlan: &template.Plan,
		SOP:          lo.Ternary(template.Expired, nil, template.ExpSop), // 如果模板被修改过，就不使用模板的 SOP 经验
	}, nil
}

func (s *Service) shouldCheckGlobalSessionLimit(session *entity.Session) (bool, bool) {
	var (
		isNewSession                  bool
		shouldCheckGlobalSessionLimit bool
	)
	switch session.Status {
	case entity.SessionStatusCreated, entity.SessionStatusStopped, entity.SessionStatusError:
		// created认为是新session，且需要检查全局会话限制; stopped&error状态为唤醒session，也认为是新session
		isNewSession = true
		shouldCheckGlobalSessionLimit = true
	case entity.SessionStatusPrepared, entity.SessionStatusBound, entity.SessionStatusPending:
		// 预热状态的都认为是新session，且需要检查全局会话限制
		isNewSession = true
		shouldCheckGlobalSessionLimit = true
	case entity.SessionStatusIdle, entity.SessionStatusCanceled:
		// idle&canceled 状态下，认为不是新会话，也需要检查全局会话限制
		isNewSession = false
		shouldCheckGlobalSessionLimit = true
	case entity.SessionStatusWaiting, entity.SessionStatusRunning:
		// waiting&running 状态下，认为不是新会话，为补充或打断会话，不做检查
		isNewSession = false
		shouldCheckGlobalSessionLimit = false
	default:
		// 其他状态下，认为不是新会话，也不需要检查全局会话限制
	}

	return isNewSession, shouldCheckGlobalSessionLimit
}

// 本方法用于构造Space相关配置的变量，供Agent消费，构造过程发生错误只打错误日志，不返回错误和影响其他字段构造
func (s *Service) getSpaceInfo(ctx context.Context, spaceID string) *agententity.SpaceInfo {
	var isProjectSpace bool
	space, err := s.dao.GetSpace(ctx, spaceID, false)
	if err != nil {
		logs.V1.CtxError(ctx, "[getSpaceInfo] failed to get space for space_id :%s, err: %v", spaceID, err)
	} else if space != nil && space.Type == entity.SpaceTypeProject {
		isProjectSpace = true
	}

	info := agententity.NewSpaceInfo(spaceID, isProjectSpace)

	pc, err := s.dao.GetPlatformConfig(ctx, spaceID)
	if err != nil {
		logs.V1.CtxError(ctx, "[getSpaceInfo] failed to get space configs for space_id :%s, err: %v", spaceID, err)
	} else if pc == nil {
		logs.V1.CtxInfo(ctx, "[getSpaceInfo] does not found platform config for space_id :%s", spaceID)
	} else {
		info.AddMeegoList(gslice.Map(pc.MeegoSpaceList.Data(), func(ms *entity.MeegoSpace) string {
			return fmt.Sprintf("https://meego.larkoffice.com/%s", ms.SimpleName)
		}))
	}

	devOpt := dal.ListDevServiceOption{
		SpaceID: spaceID,
	}
	devSvcs, _, err := s.dao.ListDevService(ctx, devOpt)
	if err != nil {
		logs.V1.CtxError(ctx, "[getSpaceInfo] failed to get space service list for space_id :%s, err: %v", spaceID, err)
	}

	for _, svc := range devSvcs {
		spaceSvc := agententity.NewSpaceDevServiceInfo(svc.Type, svc.Name, svc.ControlPlane)
		info.AddDevService(spaceSvc)
	}

	repoOpt := dal.ListCodeRepoOption{
		SpaceID: spaceID,
	}
	repos, _, err := s.dao.ListCodeRepo(ctx, repoOpt)
	if err != nil {
		logs.V1.CtxError(ctx, "[getSpaceInfo] failed to get space repo list for space_id :%s, err: %v", spaceID, err)
	}

	for _, repo := range repos {
		spaceRepo := agententity.NewSpaceCodeRepoInfo(repo.RepoName, repo.RepoID)
		info.AddCodeRepo(spaceRepo)
	}
	logs.V1.CtxInfo(ctx, "[getSpaceInfo] construct space_infos space_id :%s, infors: %s", spaceID, info.ToString())

	return info
}
