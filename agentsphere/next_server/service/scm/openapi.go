package scm

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/cloudjwt"
	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/pkg/protocol"
	"github.com/cloudwego/hertz/pkg/app/client"
	"github.com/cloudwego/hertz/pkg/network/standard"
	"github.com/pkg/errors"
)

type ScmOpenapiServiceImpl struct {
	client    *byted.Client
	tccConfig *tcc.GenericConfig[config.NextAimeAccountConfig]
	scmConfig *config.AgentSphereScmConfig
	jwtClient *cloudjwt.CloudJWTClient
}

func NewScmOpenapiServiceImpl(conf *config.AgentSphereConfig, tccConfig *tcc.GenericConfig[config.NextAimeAccountConfig]) (*ScmOpenapiServiceImpl, error) {
	client, err := byted.NewClient(byted.WithAppClientOptions(
		client.WithDialTimeout(time.Duration(conf.SCMConfig.Timeout)*time.Second),
		client.WithDialer(standard.NewDialer())),
	)

	if err != nil {
		return nil, err
	}

	return &ScmOpenapiServiceImpl{
		client:    client,
		tccConfig: tccConfig,
		scmConfig: &conf.SCMConfig,
		jwtClient: cloudjwt.NewCloudJWTClient(tccConfig.GetPointer().AimeAccountSecretKey, tccConfig.GetPointer().CloudJWTHost),
	}, nil
}

type GetScmVersionResp struct {
	Count    int                  `json:"count"`
	Next     string               `json:"next"`
	Previous string               `json:"previous"`
	Results  []*entity.ScmVersion `json:"results"`
}

func (s *ScmOpenapiServiceImpl) GetScmVersionList(ctx context.Context, opt *serverservice.GetScmVersionListOption) ([]*entity.ScmVersion, error) {
	req := &protocol.Request{}
	res := &protocol.Response{}
	jwtToken, err := s.jwtClient.GenJWT(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get cloud jwt token failed")
	}
	req.SetRequestURI(s.scmConfig.Host + "/api/v2/versions/")
	req.SetMethod("GET")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Jwt-Token", jwtToken)
	query := "limit=50&repo_name=" + s.scmConfig.RepoName
	if opt.Branch != "" {
		query += "&branch=" + opt.Branch
	}
	if opt.TypeList != "" {
		query += "&type_list=" + opt.TypeList
	}
	if opt.Version != "" {
		query += "&version=" + opt.Version
	}
	if opt.Commit != "" {
		query += "&commit=" + opt.Commit
	}
	req.SetQueryString(query)

	err = s.client.Do(ctx, req, res)
	if err != nil {
		return nil, errors.Wrap(err, "get scm version list failed")
	}

	var data GetScmVersionResp
	err = json.Unmarshal(res.Body(), &data)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshal scm version list failed")
	}

	return data.Results, nil
}
