package deployreview

import (
	"context"
	"errors"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	mockdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/mock/dal"
	mockservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/mock/service"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/ab"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	rocketmqmock "code.byted.org/devgpt/kiwis/port/rocketmq/mock"
	"github.com/bytedance/mockey"
	"github.com/samber/lo"
	. "github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"
)

type MockIDGen struct {
}

func (m *MockIDGen) NewID() string {
	return "mock-id"
}

func TestDeployReviewServiceImpl_CreateDeploy(t *testing.T) {
	mockey.PatchConvey("Test CreateDeploy", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockDeployCanaryDAO := mockdal.NewMockDeployCanaryDAO(ctrl)
		mockBpm := mockservice.NewMockBpmOpenAPI(ctrl)
		mockAgentService := &agent.Service{}
		mockMQ := rocketmqmock.NewMockClient(ctrl)

		service := &DeployReviewServiceImpl{
			dao:                   &dal.DAO{},
			idGen:                 &MockIDGen{},
			bpm:                   mockBpm,
			deployCanaryDAO:       mockDeployCanaryDAO,
			agentService:          mockAgentService,
			deployReviewMQ:        mockMQ,
			tccDeployReviewConfig: &tcc.GenericConfig[config.NextDeployReviewConfig]{},
		}

		ctx := context.Background()
		agentConfigVersionID := "version-123"
		actor := "test-user"
		extra := &entity.DeployExtraInfo{
			Reviewer: "reviewer",
		}

		agentConfigVersion := &entity.AgentConfigVersion{
			ID:            agentConfigVersionID,
			AgentConfigID: "config-123",
			Enabled:       false,
		}

		agentConfig := &entity.AgentConfig{
			ID:      "config-123",
			AgentID: "agent-123",
			Type:    entity.AgentConfigTypeBase,
		}

		mockey.PatchConvey("Success", func() {
			// Mock agent service GetAndCheckAgentConfigVersion
			mockey.Mock((*agent.Service).GetAndCheckAgentConfigVersion).Return(agentConfigVersion, agentConfig, nil).Build()

			// Mock update agent config version
			mockey.Mock((*dal.DAO).UpdateAgentConfigVersion).Return(agentConfigVersion, nil).Build()

			// Mock get agent deploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, gorm.ErrRecordNotFound).Build()

			// Mock get agent config version
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(nil, gorm.ErrRecordNotFound).Build()

			mockey.Mock((*dal.DAO).GetAgent).Return(&entity.Agent{
				ID:   "agent-123",
				Name: "test-agent",
			}, nil).Build()

			// Mock create workflow record
			workflowRecord := &entity.WorkflowRecord{
				ID: 123,
			}
			mockBpm.EXPECT().CreateWorkflowRecord(gomock.Any(), gomock.Any()).DoAndReturn(
				func(ctx context.Context, opt *serverservice.CreateWorkflowRecordOptions) (*entity.WorkflowRecord, error) {
					So(opt.Creator, ShouldEqual, actor)
					So(opt.Reviewer, ShouldEqual, extra.Reviewer)
					So(opt.DeployID, ShouldEqual, "mock-id")
					So(opt.AgentConfigVersionID, ShouldEqual, agentConfigVersionID)
					So(opt.AgentConfigID, ShouldEqual, agentConfigVersion.AgentConfigID)
					return workflowRecord, nil
				},
			)

			// Mock create agent deploy
			mockey.Mock((*dal.DAO).CreateAgentDeployOnly).To(func(ctx context.Context, opt dal.CreateAgentDeployOption) error {
				So(opt.ID, ShouldEqual, "mock-id")
				So(opt.AgentConfigID, ShouldEqual, agentConfigVersion.AgentConfigID)
				So(opt.AgentConfigVersionID, ShouldEqual, agentConfigVersionID)
				So(opt.Actor, ShouldEqual, actor)
				So(opt.Status, ShouldEqual, entity.AgentDeployStatusCreated)
				So(opt.ExtraInfo, ShouldEqual, extra)
				So(opt.WorkflowID, ShouldEqual, workflowRecord.ID)
				return nil
			}).Build()

			// Mock update workflow record
			mockBpm.EXPECT().UpdateWorkflowRecord(gomock.Any(), gomock.Any()).DoAndReturn(
				func(ctx context.Context, opt *serverservice.UpdateWorkflowOptions) error {
					So(opt.WorkflowRecordID, ShouldEqual, workflowRecord.ID)
					So(opt.OpKey, ShouldEqual, entity.WorkflowNodeKeyStart)
					So(opt.Actor, ShouldEqual, actor)
					return nil
				},
			)

			deployID, workflowID, err := service.CreateDeploy(ctx, agentConfigVersionID, actor, extra)
			So(err, ShouldBeNil)
			So(deployID, ShouldEqual, "mock-id")
			So(workflowID, ShouldEqual, workflowRecord.ID)
		})

		mockey.PatchConvey("Failed - GetAndCheckAgentConfigVersion error", func() {
			expectedErr := errors.New("get agent config version error")
			mockey.Mock((*agent.Service).GetAndCheckAgentConfigVersion).Return(nil, nil, expectedErr).Build()

			deployID, workflowID, err := service.CreateDeploy(ctx, agentConfigVersionID, actor, extra)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(deployID, ShouldEqual, "")
			So(workflowID, ShouldEqual, 0)
		})

		mockey.PatchConvey("Failed - UpdateAgentConfigVersion error", func() {
			expectedErr := errors.New("update agent config version error")
			mockey.Mock((*agent.Service).GetAndCheckAgentConfigVersion).Return(agentConfigVersion, agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(nil, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, nil).Build()

			mockey.Mock((*dal.DAO).UpdateAgentConfigVersion).Return(nil, expectedErr).Build()

			deployID, workflowID, err := service.CreateDeploy(ctx, agentConfigVersionID, actor, extra)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(deployID, ShouldEqual, "")
			So(workflowID, ShouldEqual, 0)
		})

		mockey.PatchConvey("Failed - Agent config version in deploy status", func() {
			agentConfigVersion.Enabled = true
			mockey.Mock((*agent.Service).GetAndCheckAgentConfigVersion).Return(agentConfigVersion, agentConfig, nil).Build()

			// Mock get agent deploy
			mockey.Mock((*dal.DAO).UpdateAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(nil, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(&entity.AgentDeploy{}, nil).Build()

			deployID, workflowID, err := service.CreateDeploy(ctx, agentConfigVersionID, actor, extra)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, ErrAgentConfigVersionInDeployStatus.Error())
			So(deployID, ShouldEqual, "")
			So(workflowID, ShouldEqual, 0)
		})

		mockey.PatchConvey("Failed - CreateWorkflowRecord error", func() {
			expectedErr := errors.New("create workflow record error")
			mockey.Mock((*agent.Service).GetAndCheckAgentConfigVersion).Return(agentConfigVersion, agentConfig, nil).Build()

			// Mock update agent config version
			mockey.Mock((*dal.DAO).UpdateAgentConfigVersion).Return(agentConfigVersion, nil).Build()

			// Mock get agent deploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, gorm.ErrRecordNotFound).Build()

			// Mock get agent config version
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(nil, gorm.ErrRecordNotFound).Build()
			mockey.Mock((*dal.DAO).GetAgent).Return(&entity.Agent{
				ID:   "agent-123",
				Name: "test-agent",
			}, nil).Build()

			// Mock create workflow record
			mockBpm.EXPECT().CreateWorkflowRecord(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

			deployID, workflowID, err := service.CreateDeploy(ctx, agentConfigVersionID, actor, extra)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(deployID, ShouldEqual, "")
			So(workflowID, ShouldEqual, 0)
		})

		mockey.PatchConvey("Failed - CreateAgentDeployOnly error", func() {
			expectedErr := errors.New("create agent deploy error")
			mockey.Mock((*agent.Service).GetAndCheckAgentConfigVersion).Return(agentConfigVersion, agentConfig, nil).Build()

			// Mock update agent config version
			mockey.Mock((*dal.DAO).UpdateAgentConfigVersion).Return(agentConfigVersion, nil).Build()

			// Mock get agent deploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, gorm.ErrRecordNotFound).Build()

			// Mock get agent config version
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(nil, gorm.ErrRecordNotFound).Build()
			mockey.Mock((*dal.DAO).GetAgent).Return(&entity.Agent{
				ID:   "agent-123",
				Name: "test-agent",
			}, nil).Build()

			// Mock create workflow record
			workflowRecord := &entity.WorkflowRecord{
				ID: 123,
			}
			mockBpm.EXPECT().CreateWorkflowRecord(gomock.Any(), gomock.Any()).Return(workflowRecord, nil)

			// Mock create agent deploy
			mockey.Mock((*dal.DAO).CreateAgentDeployOnly).Return(expectedErr).Build()

			deployID, workflowID, err := service.CreateDeploy(ctx, agentConfigVersionID, actor, extra)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(deployID, ShouldEqual, "")
			So(workflowID, ShouldEqual, 0)
		})
	})
}

func TestDeployReviewServiceImpl_GetDeployProcessInfo(t *testing.T) {
	mockey.PatchConvey("Test GetDeployProcessInfo", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockDeployCanaryDAO := mockdal.NewMockDeployCanaryDAO(ctrl)
		mockBpm := mockservice.NewMockBpmOpenAPI(ctrl)
		mockAgentService := &agent.Service{}

		service := &DeployReviewServiceImpl{
			dao:             &dal.DAO{},
			idGen:           &MockIDGen{},
			bpm:             mockBpm,
			deployCanaryDAO: mockDeployCanaryDAO,
			agentService:    mockAgentService,
		}

		ctx := context.Background()
		deployID := "deploy-123"
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"
		agentID := "agent-123"

		deploy := &entity.AgentDeploy{
			ID:                   deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			Status:               entity.AgentDeployStatusCanary,
		}

		mockey.PatchConvey("Success - With canary tasks", func() {
			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock getCanaryRationConfig
			successCount := int64(80)
			failCount := int64(10)
			runningCount := int64(10)
			newVersion := &ab.RollingStatus{
				Succeed:       successCount,
				Failed:        failCount,
				Running:       runningCount,
				RunningWeight: 0.6,
			}
			oldVersion := &ab.RollingStatus{
				Succeed:       100,
				Failed:        1,
				Running:       0,
				RunningWeight: 0.6,
			}
			mockey.Mock((*DeployReviewServiceImpl).getCanaryRationConfig).Return(newVersion, oldVersion, nil).Build()

			// Mock GetAgentConfig
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(&entity.AgentConfig{
				AgentID: agentID,
			}, nil).Build()

			// Mock GetDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().GetDeployCanaryConfig(gomock.Any(), agentID).Return("version-123", 0.5, nil)

			// Mock CalcRollingProbability
			mockey.Mock(ab.CalcRollingProbabilityWithOpt).Return(0.8765).Build()

			info, err := service.GetDeployProcessInfo(ctx, deployID)
			So(err, ShouldBeNil)
			So(info, ShouldNotBeNil)
			So(info.SuccessCount, ShouldEqual, successCount)
			So(info.FailCount, ShouldEqual, failCount)
			So(info.RunningCount, ShouldEqual, runningCount)
			So(info.CanaryRatio, ShouldEqual, 0.5) // 受 ratioLimit 限制的值
			So(info.Status, ShouldEqual, entity.AgentDeployStatusCanary)
		})

		mockey.PatchConvey("Success - No canary tasks", func() {
			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock getCanaryRationConfig
			successCount := int64(0)
			failCount := int64(0)
			runningCount := int64(0)
			newVersion := &ab.RollingStatus{
				Succeed:       successCount,
				Failed:        failCount,
				Running:       runningCount,
				RunningWeight: 0.6,
			}
			oldVersion := &ab.RollingStatus{
				Succeed:       100,
				Failed:        1,
				Running:       0,
				RunningWeight: 0.6,
			}
			mockey.Mock((*DeployReviewServiceImpl).getCanaryRationConfig).Return(newVersion, oldVersion, nil).Build()

			// Mock GetAgentConfig
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(&entity.AgentConfig{
				AgentID: agentID,
			}, nil).Build()

			// Mock GetDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().GetDeployCanaryConfig(gomock.Any(), agentID).Return("version-123", 0.5, nil)

			info, err := service.GetDeployProcessInfo(ctx, deployID)
			So(err, ShouldBeNil)
			So(info, ShouldNotBeNil)
			So(info.SuccessCount, ShouldEqual, 0)
			So(info.FailCount, ShouldEqual, 0)
			So(info.RunningCount, ShouldEqual, 0)
			So(info.CanaryRatio, ShouldEqual, 0) // 当没有任务时，比例为0
			So(info.Status, ShouldEqual, entity.AgentDeployStatusCanary)
		})

		mockey.PatchConvey("Failed - GetAgentDeploy error", func() {
			expectedErr := errors.New("get agent deploy error")
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, expectedErr).Build()

			info, err := service.GetDeployProcessInfo(ctx, deployID)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(info, ShouldBeNil)
		})

		mockey.PatchConvey("Failed - getCanaryRationConfig error", func() {
			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(&entity.AgentConfig{
				AgentID: agentID,
			}, nil).Build()

			// Mock GetDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().GetDeployCanaryConfig(gomock.Any(), agentID).Return("version-123", 0.5, nil)

			// Mock getCanaryRationConfig error
			expectedErr := errors.New("get canary ration config error")
			mockey.Mock((*DeployReviewServiceImpl).getCanaryRationConfig).Return(nil, nil, expectedErr).Build()

			info, err := service.GetDeployProcessInfo(ctx, deployID)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(info, ShouldBeNil)
		})

		mockey.PatchConvey("Failed - GetAgentConfig error", func() {
			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock getCanaryRationConfig
			newVersion := &ab.RollingStatus{
				Succeed:       80,
				Failed:        10,
				Running:       10,
				RunningWeight: 0.6,
			}
			oldVersion := &ab.RollingStatus{
				Succeed:       100,
				Failed:        1,
				Running:       0,
				RunningWeight: 0.6,
			}
			mockey.Mock((*DeployReviewServiceImpl).getCanaryRationConfig).Return(newVersion, oldVersion, nil).Build()

			// Mock GetAgentConfig error
			expectedErr := errors.New("get agent config error")
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(nil, expectedErr).Build()

			info, err := service.GetDeployProcessInfo(ctx, deployID)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(info, ShouldBeNil)
		})

		mockey.PatchConvey("Failed - GetDeployCanaryConfig error", func() {
			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock getCanaryRationConfig
			newVersion := &ab.RollingStatus{
				Succeed:       80,
				Failed:        10,
				Running:       10,
				RunningWeight: 0.6,
			}
			oldVersion := &ab.RollingStatus{
				Succeed:       100,
				Failed:        1,
				Running:       0,
				RunningWeight: 0.6,
			}
			mockey.Mock((*DeployReviewServiceImpl).getCanaryRationConfig).Return(newVersion, oldVersion, nil).Build()

			// Mock GetAgentConfig
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(&entity.AgentConfig{
				AgentID: agentID,
			}, nil).Build()

			// Mock GetDeployCanaryConfig error
			expectedErr := errors.New("get deploy canary config error")
			mockDeployCanaryDAO.EXPECT().GetDeployCanaryConfig(gomock.Any(), agentID).Return("", 0.0, expectedErr)

			info, err := service.GetDeployProcessInfo(ctx, deployID)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(info, ShouldBeNil)
		})
	})
}

func TestDeployReviewServiceImpl_StartCanary(t *testing.T) {
	mockey.PatchConvey("Test StartCanary", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockDeployCanaryDAO := mockdal.NewMockDeployCanaryDAO(ctrl)
		mockBpm := mockservice.NewMockBpmOpenAPI(ctrl)
		mockAgentService := &agent.Service{}
		mockMQ := rocketmqmock.NewMockClient(ctrl)

		service := &DeployReviewServiceImpl{
			dao:                   &dal.DAO{},
			idGen:                 &MockIDGen{},
			bpm:                   mockBpm,
			deployCanaryDAO:       mockDeployCanaryDAO,
			agentService:          mockAgentService,
			deployReviewMQ:        mockMQ,
			tccDeployReviewConfig: &tcc.GenericConfig[config.NextDeployReviewConfig]{},
		}

		ctx := context.Background()
		deployID := "deploy-123"
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"
		workflowID := int64(123)

		opt := &serverservice.DeployCanaryOption{
			DeployID:             deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
			Creator:              "test-user",
			Status:               entity.WorkflowNodeKeyCanary1,
		}

		agentConfig := &entity.AgentConfig{
			ID:      agentConfigID,
			AgentID: "agent-123",
		}

		agentConfigVersion := &entity.AgentConfigVersion{
			ID:            agentConfigVersionID,
			AgentConfigID: agentConfigID,
		}

		deploy := &entity.AgentDeploy{
			ID:                   deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
			Actor:                "test-user",
		}

		mockey.PatchConvey("Success - Canary1", func() {
			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock CreateDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().CreateDeployCanaryConfig(gomock.Any(), gomock.Any()).DoAndReturn(
				func(ctx context.Context, opt dal.CreateDeployCanaryConfigOption) error {
					So(opt.AgentID, ShouldEqual, agentConfig.AgentID)
					So(opt.AgentConfigVersionID, ShouldEqual, agentConfigVersionID)
					So(opt.RatioLimit, ShouldEqual, 0.01)
					return nil
				},
			)

			// Mock UpdateAgentDeploy
			mockey.Mock((*dal.DAO).UpdateAgentDeploy).To(
				func(ctx context.Context, opt dal.UpdateAgentDeployOption) error {
					So(opt.ID, ShouldEqual, deployID)
					So(opt.Status, ShouldEqual, entity.AgentDeployStatusCanary)
					So(opt.AgentConfigVersionID, ShouldEqual, agentConfigVersionID)
					So(opt.AgentConfigVersionStatus, ShouldEqual, entity.AgentConfigStatusCanary)
					return nil
				},
			).Build()

			// Mock SendMessage
			mockMQ.EXPECT().SendDelayedMessage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mockey.MockGeneric((*tcc.GenericConfig[config.NextDeployReviewConfig]).GetPointer).Return(&config.NextDeployReviewConfig{
				InspectInterval: 5,
			}).Build()
			err := service.StartCanary(ctx, opt)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Success - Canary10", func() {
			opt.Status = entity.WorkflowNodeKeyCanary10

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock CreateDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().CreateDeployCanaryConfig(gomock.Any(), gomock.Any()).DoAndReturn(
				func(ctx context.Context, opt dal.CreateDeployCanaryConfigOption) error {
					So(opt.AgentID, ShouldEqual, agentConfig.AgentID)
					So(opt.AgentConfigVersionID, ShouldEqual, agentConfigVersionID)
					So(opt.RatioLimit, ShouldEqual, 0.1)
					return nil
				},
			)

			err := service.StartCanary(ctx, opt)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Failed - getAndCheckDeploy error", func() {
			expectedErr := errors.New("get agent config error")
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(nil, expectedErr).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			err := service.StartCanary(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - CreateDeployCanaryConfig error", func() {
			expectedErr := errors.New("create deploy canary config error")

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock CreateDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().CreateDeployCanaryConfig(gomock.Any(), gomock.Any()).Return(expectedErr)

			err := service.StartCanary(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - UpdateAgentDeploy error", func() {
			expectedErr := errors.New("update agent deploy error")

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock CreateDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().CreateDeployCanaryConfig(gomock.Any(), gomock.Any()).Return(nil)

			// Mock UpdateAgentDeploy
			mockey.Mock((*dal.DAO).UpdateAgentDeploy).Return(expectedErr).Build()

			err := service.StartCanary(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})
	})
}

func TestDeployReviewServiceImpl_CancelCanary(t *testing.T) {
	mockey.PatchConvey("Test CancelCanary", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockDeployCanaryDAO := mockdal.NewMockDeployCanaryDAO(ctrl)
		mockBpm := mockservice.NewMockBpmOpenAPI(ctrl)
		mockAgentService := &agent.Service{}

		service := &DeployReviewServiceImpl{
			dao:             &dal.DAO{},
			idGen:           &MockIDGen{},
			bpm:             mockBpm,
			deployCanaryDAO: mockDeployCanaryDAO,
			agentService:    mockAgentService,
		}

		ctx := context.Background()
		deployID := "deploy-123"
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"
		workflowID := int64(123)

		opt := &serverservice.DeployCanaryOption{
			DeployID:             deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
			Creator:              "test-user",
		}

		agentConfig := &entity.AgentConfig{
			ID:      agentConfigID,
			AgentID: "agent-123",
			Type:    entity.AgentConfigTypeBase,
		}

		agentConfigVersion := &entity.AgentConfigVersion{
			ID:            agentConfigVersionID,
			AgentConfigID: agentConfigID,
		}

		deploy := &entity.AgentDeploy{
			ID:                   deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
			Actor:                "test-user",
		}

		mockey.PatchConvey("Success", func() {
			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(nil)

			// Mock UpdateAgentDeploy
			mockey.Mock((*dal.DAO).UpdateAgentDeploy).To(func(ctx context.Context, opt dal.UpdateAgentDeployOption) error {
				So(opt.ID, ShouldEqual, deployID)
				So(opt.Status, ShouldEqual, entity.AgentDeployStatusCancelCanary)
				So(opt.AgentConfigVersionID, ShouldEqual, agentConfigVersionID)
				So(opt.AgentConfigVersionStatus, ShouldEqual, entity.AgentConfigStatusCreated)
				return nil
			}).Build()

			err := service.CancelCanary(ctx, opt)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Failed - getAndCheckDeploy error", func() {
			expectedErr := errors.New("get agent config error")
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(nil, expectedErr).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()
			err := service.CancelCanary(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - DelDeployCanaryConfig error", func() {
			expectedErr := errors.New("del deploy canary config error")

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(expectedErr)

			err := service.CancelCanary(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - UpdateAgentDeploy error", func() {
			expectedErr := errors.New("update agent deploy error")

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(nil)

			// Mock UpdateAgentDeploy
			mockey.Mock((*dal.DAO).UpdateAgentDeploy).Return(expectedErr).Build()

			err := service.CancelCanary(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})
	})
}

func TestDeployReviewServiceImpl_CloseWorkflowRecord(t *testing.T) {
	mockey.PatchConvey("Test CloseWorkflowRecord", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockDeployCanaryDAO := mockdal.NewMockDeployCanaryDAO(ctrl)
		mockBpm := mockservice.NewMockBpmOpenAPI(ctrl)
		mockAgentService := &agent.Service{}

		service := &DeployReviewServiceImpl{
			dao:             &dal.DAO{},
			idGen:           &MockIDGen{},
			bpm:             mockBpm,
			deployCanaryDAO: mockDeployCanaryDAO,
			agentService:    mockAgentService,
		}

		ctx := context.Background()
		deployID := "deploy-123"
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"
		workflowID := int64(123)

		agentConfig := &entity.AgentConfig{
			ID:      agentConfigID,
			AgentID: "agent-123",
			Type:    entity.AgentConfigTypeBase,
		}

		agentConfigVersion := &entity.AgentConfigVersion{
			ID:            agentConfigVersionID,
			AgentConfigID: agentConfigID,
		}

		deploy := &entity.AgentDeploy{
			ID:                   deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
			Actor:                "test-user",
			ExtraInfo:            &entity.DeployExtraInfo{},
		}

		mockey.PatchConvey("Success - Close", func() {
			opt := &serverservice.DeployCanaryOption{
				DeployID:             deployID,
				AgentConfigID:        agentConfigID,
				AgentConfigVersionID: agentConfigVersionID,
				WorkflowID:           workflowID,
				Creator:              "test-user",
				CloseReason:          "test close reason",
			}

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(nil)

			// Mock UpdateAgentDeploy
			mockey.Mock((*dal.DAO).UpdateAgentDeploy).To(func(ctx context.Context, opt dal.UpdateAgentDeployOption) error {
				So(opt.ID, ShouldEqual, deployID)
				So(opt.Status, ShouldEqual, entity.AgentDeployStatusClose)
				So(opt.AgentConfigVersionID, ShouldEqual, agentConfigVersionID)
				So(opt.AgentConfigVersionStatus, ShouldEqual, entity.AgentConfigStatusCreated)
				So(opt.ExtraInfo.CloseReason, ShouldEqual, "test close reason")
				return nil
			}).Build()

			err := service.CloseWorkflowRecord(ctx, opt)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Success - AuditReject", func() {
			opt := &serverservice.DeployCanaryOption{
				DeployID:             deployID,
				AgentConfigID:        agentConfigID,
				AgentConfigVersionID: agentConfigVersionID,
				WorkflowID:           workflowID,
				Creator:              "test-user",
				AuditRejectComment:   "test reject reason",
			}

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(nil)

			// Mock UpdateAgentDeploy
			mockey.Mock((*dal.DAO).UpdateAgentDeploy).To(func(ctx context.Context, opt dal.UpdateAgentDeployOption) error {
				So(opt.ID, ShouldEqual, deployID)
				So(opt.Status, ShouldEqual, entity.AgentDeployStatusAuditReject)
				So(opt.AgentConfigVersionID, ShouldEqual, agentConfigVersionID)
				So(opt.AgentConfigVersionStatus, ShouldEqual, entity.AgentConfigStatusCreated)
				So(opt.ExtraInfo.AuditRejectComment, ShouldEqual, "test reject reason")
				return nil
			}).Build()

			err := service.CloseWorkflowRecord(ctx, opt)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Failed - getAndCheckDeploy error", func() {
			opt := &serverservice.DeployCanaryOption{
				DeployID:             deployID,
				AgentConfigID:        agentConfigID,
				AgentConfigVersionID: agentConfigVersionID,
				WorkflowID:           workflowID,
				Creator:              "test-user",
			}

			expectedErr := errors.New("get agent config error")
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(nil, expectedErr).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(nil, expectedErr).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, expectedErr).Build()

			err := service.CloseWorkflowRecord(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - DelDeployCanaryConfig error", func() {
			opt := &serverservice.DeployCanaryOption{
				DeployID:             deployID,
				AgentConfigID:        agentConfigID,
				AgentConfigVersionID: agentConfigVersionID,
				WorkflowID:           workflowID,
				Creator:              "test-user",
			}

			expectedErr := errors.New("del deploy canary config error")

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(expectedErr)

			err := service.CloseWorkflowRecord(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - UpdateAgentDeploy error", func() {
			opt := &serverservice.DeployCanaryOption{
				DeployID:             deployID,
				AgentConfigID:        agentConfigID,
				AgentConfigVersionID: agentConfigVersionID,
				WorkflowID:           workflowID,
				Creator:              "test-user",
			}

			expectedErr := errors.New("update agent deploy error")

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(nil)

			// Mock UpdateAgentDeploy
			mockey.Mock((*dal.DAO).UpdateAgentDeploy).Return(expectedErr).Build()

			err := service.CloseWorkflowRecord(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})
	})
}

func TestDeployReviewServiceImpl_Online(t *testing.T) {
	mockey.PatchConvey("Test Online", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockDeployCanaryDAO := mockdal.NewMockDeployCanaryDAO(ctrl)
		mockBpm := mockservice.NewMockBpmOpenAPI(ctrl)
		mockAgentService := &agent.Service{}

		service := &DeployReviewServiceImpl{
			dao:             &dal.DAO{},
			idGen:           &MockIDGen{},
			bpm:             mockBpm,
			deployCanaryDAO: mockDeployCanaryDAO,
			agentService:    mockAgentService,
		}

		ctx := context.Background()
		deployID := "deploy-123"
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"
		workflowID := int64(123)

		opt := &serverservice.DeployCanaryOption{
			DeployID:             deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
			Creator:              "test-user",
			SkipCanaryComment:    "test skip canary",
		}

		agentConfig := &entity.AgentConfig{
			ID:      agentConfigID,
			AgentID: "agent-123",
			Type:    entity.AgentConfigTypeBase,
		}

		agentConfigVersion := &entity.AgentConfigVersion{
			ID:            agentConfigVersionID,
			AgentConfigID: agentConfigID,
		}

		deploy := &entity.AgentDeploy{
			ID:                   deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
			Actor:                "test-user",
			ExtraInfo:            &entity.DeployExtraInfo{},
		}
		mockey.Mock((*DeployReviewServiceImpl).deletePreparedSessions).Build()
		mockey.PatchConvey("Success", func() {
			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(nil)

			// Mock UpdateDeployAgentStatus
			mockey.Mock((*agent.Service).UpdateDeployAgentStatus).Return(nil).Build()

			err := service.Online(ctx, opt)
			So(err, ShouldBeNil)
			So(deploy.ExtraInfo.SkipCanaryComment, ShouldEqual, "test skip canary")
		})

		mockey.PatchConvey("Failed - getAndCheckDeploy error", func() {
			expectedErr := errors.New("get agent config error")

			mockey.Mock((*dal.DAO).GetAgentConfig).Return(nil, expectedErr).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(nil, expectedErr).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, expectedErr).Build()

			err := service.Online(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - DelDeployCanaryConfig error", func() {
			expectedErr := errors.New("del deploy canary config error")

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(expectedErr)

			err := service.Online(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - UpdateDeployAgentStatus error", func() {
			expectedErr := errors.New("update deploy agent status error")

			// Mock getAndCheckDeploy
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock DelDeployCanaryConfig
			mockDeployCanaryDAO.EXPECT().DelDeployCanaryConfig(gomock.Any(), agentConfig.AgentID).Return(nil)

			// Mock UpdateDeployAgentStatus
			mockey.Mock((*agent.Service).UpdateDeployAgentStatus).Return(expectedErr).Build()

			err := service.Online(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})
	})
}

func TestDeployReviewServiceImpl_HandleWorkflowProcess(t *testing.T) {
	mockey.PatchConvey("Test HandleWorkflowProcess", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockDeployCanaryDAO := mockdal.NewMockDeployCanaryDAO(ctrl)
		mockBpm := mockservice.NewMockBpmOpenAPI(ctrl)
		mockMQ := rocketmqmock.NewMockClient(ctrl)
		mockAgentService := &agent.Service{}

		// 创建 TCC 配置
		tccConfig := &config.NextDeployReviewConfig{
			FailLimit:       5,
			InspectInterval: 60,
			CanaryVersionRationConfig: map[string]config.NextAgentCanaryVersionRationConfig{
				"1": {
					OldVersion: config.NextCanaryVersionRationConfig{
						Succeed:       100,
						Failed:        1,
						Running:       0,
						RunningWeight: 0.6,
					},
					NewVersion: config.NextCanaryVersionRationConfig{
						Succeed:          0,
						Failed:           0,
						Running:          0,
						RunningWeight:    0.6,
						CanaryProbeCount: 5, // 添加故意放进的请求总数
					},
				},
			},
		}
		tccGenericConfig := &tcc.GenericConfig[config.NextDeployReviewConfig]{}
		mockey.MockGeneric((*tcc.GenericConfig[config.NextDeployReviewConfig]).GetPointer).Return(tccConfig).Build()

		service := &DeployReviewServiceImpl{
			dao:                   &dal.DAO{},
			deployCanaryDAO:       mockDeployCanaryDAO,
			bpm:                   mockBpm,
			deployReviewMQ:        mockMQ,
			tccDeployReviewConfig: tccGenericConfig,
			agentService:          mockAgentService,
		}

		ctx := context.Background()
		deployID := "deploy-123"
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"
		workflowID := int64(123)
		workflowRecordID := int64(456)
		actor := "test-user"

		event := &entity.DeployReviewEvent{
			AgentDeployID:        deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowRecordID:     workflowRecordID,
		}

		deploy := &entity.AgentDeploy{
			ID:                   deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
			Actor:                actor,
			Status:               entity.AgentDeployStatusCanary,
		}

		mockey.PatchConvey("Canary1 - Ratio not reached", func() {
			successCount := int64(90)
			failCount := int64(0)
			runningCount := int64(10)
			// 模拟灰度比例为 0.009，未达到目标比例 0.01

			workflowRecord := &entity.WorkflowRecord{
				ID:     workflowID,
				Status: string(entity.WorkflowNodeKeyCanary1),
			}
			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// 模拟 getCanaryRationConfig 方法
			role := entity.SessionRole(1) // 使用 int32 代替 agent.AgentRole
			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			// Mock GetCanaryTaskStatus
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)

			// Mock CalcRollingProbabilityWithOpt
			mockey.Mock(ab.CalcRollingProbabilityWithOpt).Return(0.009).Build()

			// Mock GetWorkflowRecord
			mockBpm.EXPECT().GetWorkflowRecord(gomock.Any(), workflowRecordID, actor).Return(workflowRecord, nil)

			mockMQ.EXPECT().SendDelayedMessage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			// 不应调用 UpdateWorkflowRecord，因为比例未达到
			// 但应发送延迟消息继续巡检
			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Canary1 - Ratio reached", func() {
			successCount := int64(990)
			failCount := int64(0)
			runningCount := int64(10)
			// 模拟灰度比例为 0.02，已达到目标比例 0.01

			workflowRecord := &entity.WorkflowRecord{
				ID:     workflowID,
				Status: string(entity.WorkflowNodeKeyCanary1),
			}

			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// 模拟 getCanaryRationConfig 方法
			role := entity.SessionRole(1) // 使用 int32 代替 agent.AgentRole

			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			// Mock GetCanaryTaskStatus
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)

			// Mock CalcRollingProbabilityWithOpt
			mockey.Mock(ab.CalcRollingProbabilityWithOpt).Return(0.02).Build()

			// Mock GetWorkflowRecord
			mockBpm.EXPECT().GetWorkflowRecord(gomock.Any(), workflowRecordID, actor).Return(workflowRecord, nil)
			mockMQ.EXPECT().SendDelayedMessage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			// 应调用 UpdateWorkflowRecord，因为比例已达到
			mockBpm.EXPECT().UpdateWorkflowRecord(gomock.Any(), gomock.Any()).DoAndReturn(
				func(ctx context.Context, opt *serverservice.UpdateWorkflowOptions) error {
					So(opt.WorkflowRecordID, ShouldEqual, workflowRecordID)
					So(opt.OpKey, ShouldEqual, entity.WorkflowNodeKeyCanary1)
					So(opt.OpData["canary_1_check"], ShouldEqual, true)
					So(opt.Actor, ShouldEqual, actor)
					return nil
				},
			)

			// 应发送延迟消息继续巡检
			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Canary1 - Fail limit exceeded", func() {
			successCount := int64(90)
			failCount := int64(10) // 超过失败限制 5
			runningCount := int64(0)

			workflowRecord := &entity.WorkflowRecord{
				ID:     workflowID,
				Status: string(entity.WorkflowNodeKeyCanary1),
			}

			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// 模拟 getCanaryRationConfig 方法
			role := entity.SessionRole(1) // 使用 int32 代替 agent.AgentRole
			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			// Mock GetCanaryTaskStatus
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)

			// Mock CalcRollingProbabilityWithOpt
			mockey.Mock(ab.CalcRollingProbabilityWithOpt).Return(0.9).Build()

			// Mock GetWorkflowRecord
			mockBpm.EXPECT().GetWorkflowRecord(gomock.Any(), workflowRecordID, actor).Return(workflowRecord, nil)

			// 应调用 UpdateWorkflowRecord，因为失败数超过限制
			mockBpm.EXPECT().UpdateWorkflowRecord(gomock.Any(), gomock.Any()).DoAndReturn(
				func(ctx context.Context, opt *serverservice.UpdateWorkflowOptions) error {
					So(opt.WorkflowRecordID, ShouldEqual, workflowRecordID)
					So(opt.OpKey, ShouldEqual, entity.WorkflowNodeKeyCanary1)
					So(opt.OpData["canary_1_check"], ShouldEqual, false)
					So(opt.Actor, ShouldEqual, actor)
					return nil
				},
			)

			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("GetAgentDeploy error", func() {
			expectedErr := errors.New("get agent deploy error")
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, expectedErr).Build()

			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("GetCanaryTaskStatus error", func() {
			expectedErr := errors.New("get canary task status error")
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(int64(0), int64(0), int64(0), expectedErr)

			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("GetRoleByAgentConfigVersion error", func() {
			expectedErr := errors.New("get role by agent config version error")
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(int64(90), int64(0), int64(10), nil)
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(entity.SessionRole(1)), expectedErr).Build()

			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "failed to get role by agent config version")
		})

		mockey.PatchConvey("GetWorkflowRecord error", func() {
			expectedErr := errors.New("get workflow record error")
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// 模拟 getCanaryRationConfig 方法
			role := entity.SessionRole(1) // 使用 int32 代替 agent.AgentRole

			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(int64(90), int64(0), int64(10), nil)
			mockey.Mock(ab.CalcRollingProbabilityWithOpt).Return(0.9).Build()
			mockBpm.EXPECT().GetWorkflowRecord(gomock.Any(), workflowRecordID, actor).Return(nil, expectedErr)

			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, expectedErr.Error())
		})

		mockey.PatchConvey("Workflow already finished", func() {
			// 模拟 getCanaryRationConfig 方法
			role := entity.SessionRole(1) // 使用 int32 代替 agent.AgentRole

			// 创建已完成的工作流记录
			finishedWorkflowRecord := &entity.WorkflowRecord{
				ID:       workflowID,
				Status:   string(entity.WorkflowNodeKeyFinish),
				Finished: 1, // 已完成
			}

			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			// Mock GetCanaryTaskStatus
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(int64(90), int64(0), int64(10), nil)

			// Mock CalcRollingProbabilityWithOpt
			mockey.Mock(ab.CalcRollingProbabilityWithOpt).Return(0.9).Build()

			// Mock GetWorkflowRecord - 返回已完成的工作流
			mockBpm.EXPECT().GetWorkflowRecord(gomock.Any(), workflowRecordID, actor).Return(finishedWorkflowRecord, nil)

			// 不应调用 UpdateWorkflowRecord 或 SendDelayedMessage，因为工作流已完成
			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("UpdateWorkflowRecord error", func() {
			successCount := int64(990)
			failCount := int64(0)
			runningCount := int64(10)

			workflowRecord := &entity.WorkflowRecord{
				ID:     workflowID,
				Status: string(entity.WorkflowNodeKeyCanary1),
			}

			expectedErr := errors.New("update workflow record error")

			// 模拟 getCanaryRationConfig 方法
			role := entity.SessionRole(1)

			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)
			mockey.Mock(ab.CalcRollingProbabilityWithOpt).Return(0.02).Build()
			mockBpm.EXPECT().GetWorkflowRecord(gomock.Any(), workflowRecordID, actor).Return(workflowRecord, nil)
			mockBpm.EXPECT().UpdateWorkflowRecord(gomock.Any(), gomock.Any()).Return(expectedErr)

			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("SendDelayedMessage error", func() {
			successCount := int64(90)
			failCount := int64(0)
			runningCount := int64(10)

			workflowRecord := &entity.WorkflowRecord{
				ID:     workflowID,
				Status: string(entity.WorkflowNodeKeyCanary1),
			}

			expectedErr := errors.New("send delayed message error")

			// 模拟 getCanaryRationConfig 方法

			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)
			mockey.Mock(ab.CalcRollingProbabilityWithOpt).Return(0.009).Build()
			mockBpm.EXPECT().GetWorkflowRecord(gomock.Any(), workflowRecordID, actor).Return(workflowRecord, nil)
			role := entity.SessionRole(1)
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			mockMQ.EXPECT().SendDelayedMessage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedErr)
			err := service.HandleWorkflowProcess(ctx, event)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})
	})
}

func TestDeployReviewServiceImpl_GetDeploy(t *testing.T) {
	mockey.PatchConvey("Test GetDeploy", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		service := &DeployReviewServiceImpl{
			dao: &dal.DAO{},
		}

		ctx := context.Background()
		deployID := "deploy-123"
		deploy := &entity.AgentDeploy{
			ID:     deployID,
			Status: entity.AgentDeployStatusCanary,
		}

		mockey.PatchConvey("Success", func() {
			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			result, err := service.GetDeploy(ctx, deployID)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(result.ID, ShouldEqual, deployID)
			So(result.Status, ShouldEqual, entity.AgentDeployStatusCanary)
		})

		mockey.PatchConvey("Failed - GetAgentDeploy error", func() {
			expectedErr := errors.New("get agent deploy error")
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, expectedErr).Build()

			result, err := service.GetDeploy(ctx, deployID)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(result, ShouldBeNil)
		})
	})
}

func TestDeployReviewServiceImpl_GetDeployReviewUser(t *testing.T) {
	mockey.PatchConvey("Test GetDeployReviewUser", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		tccConfig := &config.NextDeployReviewConfig{
			DeployUser: []string{"user1", "user2"},
		}
		tccGenericConfig := &tcc.GenericConfig[config.NextDeployReviewConfig]{}
		mockey.MockGeneric((*tcc.GenericConfig[config.NextDeployReviewConfig]).GetPointer).Return(tccConfig).Build()

		service := &DeployReviewServiceImpl{
			tccDeployReviewConfig: tccGenericConfig,
		}

		ctx := context.Background()

		mockey.PatchConvey("Success", func() {
			users, err := service.GetDeployReviewUser(ctx)
			So(err, ShouldBeNil)
			So(users, ShouldNotBeNil)
			So(users, ShouldResemble, []string{"user1", "user2"})
		})
	})
}

func TestDeployReviewServiceImpl_ListAgentDeploy(t *testing.T) {
	mockey.PatchConvey("Test ListAgentDeploy", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		service := &DeployReviewServiceImpl{
			dao: &dal.DAO{},
		}

		ctx := context.Background()
		agentConfigID := "config-123"
		status := entity.AgentDeployStatusCanary
		opt := &serverservice.GetAgentDeployListOption{
			AgentConfigID: agentConfigID,
			Status:        lo.ToPtr(string(status)),
			PageNum:       1,
			PageSize:      10,
		}

		deployList := []*entity.AgentDeploy{
			{
				ID:            "deploy-1",
				AgentConfigID: agentConfigID,
				Status:        entity.AgentDeployStatusCanary,
			},
			{
				ID:            "deploy-2",
				AgentConfigID: agentConfigID,
				Status:        entity.AgentDeployStatusCanary,
			},
		}
		agentConfigVersion := &entity.AgentConfigVersion{
			ID: agentConfigID,
		}
		total := int64(2)

		mockey.PatchConvey("Success", func() {
			// Mock GetAgentDeployList
			mockey.Mock((*dal.DAO).GetAgentDeployList).To(func(ctx context.Context, opt dal.GetAgentDeployListOption) ([]*entity.AgentDeploy, int64, error) {
				So(opt.AgentConfigID, ShouldEqual, agentConfigID)
				So(*opt.Status, ShouldEqual, string(status))
				So(opt.Offset, ShouldEqual, 0)
				So(opt.Limit, ShouldEqual, 10)
				return deployList, total, nil
			}).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()

			result, count, err := service.ListAgentDeploy(ctx, opt)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(len(result), ShouldEqual, 2)
			So(count, ShouldEqual, total)
		})

		mockey.PatchConvey("Failed - GetAgentDeployList error", func() {
			expectedErr := errors.New("get agent deploy list error")
			mockey.Mock((*dal.DAO).GetAgentDeployList).Return(nil, int64(0), expectedErr).Build()

			result, count, err := service.ListAgentDeploy(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(result, ShouldBeNil)
			So(count, ShouldEqual, 0)
		})
	})
}

func TestDeployReviewServiceImpl_getAndCheckDeploy(t *testing.T) {
	mockey.PatchConvey("Test getAndCheckDeploy", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		service := &DeployReviewServiceImpl{
			dao: &dal.DAO{},
		}

		ctx := context.Background()
		deployID := "deploy-123"
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"
		workflowID := int64(123)

		opt := &serverservice.DeployCanaryOption{
			DeployID:             deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
		}

		agentConfig := &entity.AgentConfig{
			ID:      agentConfigID,
			AgentID: "agent-123",
		}

		agentConfigVersion := &entity.AgentConfigVersion{
			ID:            agentConfigVersionID,
			AgentConfigID: agentConfigID,
		}

		deploy := &entity.AgentDeploy{
			ID:                   deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowID:           workflowID,
		}

		mockey.PatchConvey("Success", func() {
			// Mock GetAgentConfig
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			// Mock GetAgentConfigVersion
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			// Mock GetAgentDeploy
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			resultConfig, resultVersion, resultDeploy, err := service.getAndCheckDeploy(ctx, opt)
			So(err, ShouldBeNil)
			So(resultConfig, ShouldNotBeNil)
			So(resultConfig.ID, ShouldEqual, agentConfigID)
			So(resultVersion, ShouldNotBeNil)
			So(resultVersion.ID, ShouldEqual, agentConfigVersionID)
			So(resultDeploy, ShouldNotBeNil)
			So(resultDeploy.ID, ShouldEqual, deployID)
		})

		mockey.PatchConvey("Failed - GetAgentConfig error", func() {
			expectedErr := errors.New("get agent config error")
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(nil, expectedErr).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			_, _, _, err := service.getAndCheckDeploy(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - GetAgentConfigVersion error", func() {
			expectedErr := errors.New("get agent config version error")
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(nil, expectedErr).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deploy, nil).Build()

			_, _, _, err := service.getAndCheckDeploy(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - GetAgentDeploy error", func() {
			expectedErr := errors.New("get agent deploy error")
			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(nil, expectedErr).Build()

			_, _, _, err := service.getAndCheckDeploy(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})

		mockey.PatchConvey("Failed - WorkflowID not match", func() {
			deployWithDifferentWorkflowID := &entity.AgentDeploy{
				ID:                   deployID,
				AgentConfigID:        agentConfigID,
				AgentConfigVersionID: agentConfigVersionID,
				WorkflowID:           workflowID + 1, // 不匹配的 WorkflowID
			}

			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deployWithDifferentWorkflowID, nil).Build()

			_, _, _, err := service.getAndCheckDeploy(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "workflow id not match")
		})

		mockey.PatchConvey("Failed - AgentConfigID not match", func() {
			deployWithDifferentAgentConfigID := &entity.AgentDeploy{
				ID:                   deployID,
				AgentConfigID:        agentConfigID + "_different", // 不匹配的 AgentConfigID
				AgentConfigVersionID: agentConfigVersionID,
				WorkflowID:           workflowID,
			}

			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deployWithDifferentAgentConfigID, nil).Build()

			_, _, _, err := service.getAndCheckDeploy(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "agent config id not match")
		})

		mockey.PatchConvey("Failed - AgentConfigVersionID not match", func() {
			deployWithDifferentAgentConfigVersionID := &entity.AgentDeploy{
				ID:                   deployID,
				AgentConfigID:        agentConfigID,
				AgentConfigVersionID: agentConfigVersionID + "_different", // 不匹配的 AgentConfigVersionID
				WorkflowID:           workflowID,
			}

			mockey.Mock((*dal.DAO).GetAgentConfig).Return(agentConfig, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentConfigVersion).Return(agentConfigVersion, nil).Build()
			mockey.Mock((*dal.DAO).GetAgentDeploy).Return(deployWithDifferentAgentConfigVersionID, nil).Build()

			_, _, _, err := service.getAndCheckDeploy(ctx, opt)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "agent config version id not match")
		})
	})
}

func TestDeployReviewServiceImpl_updateWorkflowIfRatioReached(t *testing.T) {
	mockey.PatchConvey("Test updateWorkflowIfRatioReached", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockBpm := mockservice.NewMockBpmOpenAPI(ctrl)

		service := &DeployReviewServiceImpl{
			bpm: mockBpm,
		}

		ctx := context.Background()
		deployID := "deploy-123"
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"
		workflowRecordID := int64(456)
		actor := "test-user"

		event := &entity.DeployReviewEvent{
			AgentDeployID:        deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			WorkflowRecordID:     workflowRecordID,
		}

		deploy := &entity.AgentDeploy{
			ID:                   deployID,
			AgentConfigID:        agentConfigID,
			AgentConfigVersionID: agentConfigVersionID,
			Actor:                actor,
		}

		mockey.PatchConvey("Ratio reached - Update workflow", func() {
			ratio := 0.02       // 当前比例
			targetRatio := 0.01 // 目标比例
			opKey := entity.WorkflowNodeKeyCanary1
			checkKey := "canary_1_check"

			// Mock UpdateWorkflowRecord
			mockBpm.EXPECT().UpdateWorkflowRecord(gomock.Any(), gomock.Any()).DoAndReturn(
				func(ctx context.Context, opt *serverservice.UpdateWorkflowOptions) error {
					So(opt.WorkflowRecordID, ShouldEqual, workflowRecordID)
					So(opt.OpKey, ShouldEqual, opKey)
					So(opt.OpData[checkKey], ShouldEqual, true)
					So(opt.Actor, ShouldEqual, actor)
					return nil
				},
			)

			err := service.updateWorkflowIfRatioReached(ctx, event, deploy, ratio, targetRatio, opKey, checkKey)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Ratio not reached - No update", func() {
			ratio := 0.005      // 当前比例
			targetRatio := 0.01 // 目标比例
			opKey := entity.WorkflowNodeKeyCanary1
			checkKey := "canary_1_check"

			// 不应调用 UpdateWorkflowRecord
			mockBpm.EXPECT().UpdateWorkflowRecord(gomock.Any(), gomock.Any()).Times(0)

			err := service.updateWorkflowIfRatioReached(ctx, event, deploy, ratio, targetRatio, opKey, checkKey)
			So(err, ShouldBeNil)
		})

		mockey.PatchConvey("Ratio reached - UpdateWorkflowRecord error", func() {
			ratio := 0.02       // 当前比例
			targetRatio := 0.01 // 目标比例
			opKey := entity.WorkflowNodeKeyCanary1
			checkKey := "canary_1_check"
			expectedErr := errors.New("update workflow record error")

			// Mock UpdateWorkflowRecord with error
			mockBpm.EXPECT().UpdateWorkflowRecord(gomock.Any(), gomock.Any()).Return(expectedErr)

			err := service.updateWorkflowIfRatioReached(ctx, event, deploy, ratio, targetRatio, opKey, checkKey)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
		})
	})
}

func TestDeployReviewServiceImpl_getCanaryRationConfig(t *testing.T) {
	mockey.PatchConvey("Test getCanaryRationConfig", t, func() {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockDeployCanaryDAO := mockdal.NewMockDeployCanaryDAO(ctrl)

		// 创建测试配置
		tccConfig := &tcc.GenericConfig[config.NextDeployReviewConfig]{}

		// 使用mockey模拟GetPointer方法
		mockConfig := &config.NextDeployReviewConfig{
			CanaryVersionRationConfig: map[string]config.NextAgentCanaryVersionRationConfig{
				"1": {
					NewVersion: config.NextCanaryVersionRationConfig{
						RunningWeight: 0.6,
					},
					OldVersion: config.NextCanaryVersionRationConfig{
						Succeed:       100,
						Failed:        1,
						Running:       0,
						RunningWeight: 0.6,
					},
				},
			},
		}
		mockey.MockGeneric((*tcc.GenericConfig[config.NextDeployReviewConfig]).GetPointer).Return(mockConfig).Build()

		service := &DeployReviewServiceImpl{
			deployCanaryDAO:       mockDeployCanaryDAO,
			agentService:          &agent.Service{},
			tccDeployReviewConfig: tccConfig,
		}

		ctx := context.Background()
		agentConfigID := "config-123"
		agentConfigVersionID := "version-123"

		mockey.PatchConvey("Success - Role exists and in config", func() {
			successCount := int64(90)
			failCount := int64(0)
			runningCount := int64(10)
			role := entity.SessionRole(1)

			// Mock GetCanaryTaskStatus
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)

			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			newVersion, oldVersion, err := service.getCanaryRationConfig(ctx, agentConfigID, agentConfigVersionID)
			So(err, ShouldBeNil)
			So(newVersion, ShouldNotBeNil)
			So(oldVersion, ShouldNotBeNil)

			// 验证 newVersion 数据
			So(newVersion.Succeed, ShouldEqual, successCount)
			So(newVersion.Failed, ShouldEqual, failCount)
			So(newVersion.Running, ShouldEqual, runningCount)
			So(newVersion.RunningWeight, ShouldEqual, 0.6)

			// 验证 oldVersion 数据
			So(oldVersion.Succeed, ShouldEqual, 100)
			So(oldVersion.Failed, ShouldEqual, 1)
			So(oldVersion.Running, ShouldEqual, 0)
			So(oldVersion.RunningWeight, ShouldEqual, 0.6)
		})

		mockey.PatchConvey("Success - Role exists but not in config", func() {
			successCount := int64(90)
			failCount := int64(0)
			runningCount := int64(10)
			role := entity.SessionRole(2) // 不在配置中的角色

			// Mock GetCanaryTaskStatus
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)

			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(lo.ToPtr(role), nil).Build()

			newVersion, oldVersion, err := service.getCanaryRationConfig(ctx, agentConfigID, agentConfigVersionID)
			So(err, ShouldBeNil)
			So(newVersion, ShouldNotBeNil)
			So(oldVersion, ShouldBeNil) // 不在配置中，oldVersion 应为 nil

			// 验证 newVersion 数据
			So(newVersion.Succeed, ShouldEqual, successCount)
			So(newVersion.Failed, ShouldEqual, failCount)
			So(newVersion.Running, ShouldEqual, runningCount)
			So(newVersion.RunningWeight, ShouldEqual, 0) // 默认值
		})

		mockey.PatchConvey("Success - Role is nil", func() {
			successCount := int64(90)
			failCount := int64(0)
			runningCount := int64(10)

			// Mock GetCanaryTaskStatus
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)

			// Mock GetRoleByAgentConfigVersion
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(nil, nil).Build()

			newVersion, oldVersion, err := service.getCanaryRationConfig(ctx, agentConfigID, agentConfigVersionID)
			So(err, ShouldBeNil)
			So(newVersion, ShouldNotBeNil)
			So(oldVersion, ShouldBeNil) // role 为 nil，oldVersion 应为 nil

			// 验证 newVersion 数据
			So(newVersion.Succeed, ShouldEqual, successCount)
			So(newVersion.Failed, ShouldEqual, failCount)
			So(newVersion.Running, ShouldEqual, runningCount)
			So(newVersion.RunningWeight, ShouldEqual, 0) // 默认值
		})

		mockey.PatchConvey("Failed - GetCanaryTaskStatus error", func() {
			expectedErr := errors.New("get canary task status error")

			// Mock GetCanaryTaskStatus with error
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(int64(0), int64(0), int64(0), expectedErr)

			newVersion, oldVersion, err := service.getCanaryRationConfig(ctx, agentConfigID, agentConfigVersionID)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(newVersion, ShouldBeNil)
			So(oldVersion, ShouldBeNil)
		})

		mockey.PatchConvey("Failed - GetRoleByAgentConfigVersion error", func() {
			successCount := int64(90)
			failCount := int64(0)
			runningCount := int64(10)
			expectedErr := errors.New("get role by agent config version error")

			// Mock GetCanaryTaskStatus
			mockDeployCanaryDAO.EXPECT().GetCanaryTaskStatus(gomock.Any(), agentConfigVersionID).Return(successCount, failCount, runningCount, nil)

			// Mock GetRoleByAgentConfigVersion with error
			mockey.Mock((*agent.Service).GetRoleByAgentConfigVersion).Return(nil, expectedErr).Build()

			newVersion, oldVersion, err := service.getCanaryRationConfig(ctx, agentConfigID, agentConfigVersionID)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "failed to get role by agent config version")
			So(newVersion, ShouldBeNil)
			So(oldVersion, ShouldBeNil)
		})
	})
}
