package deployreview

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/ab"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/pool"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

var (
	ErrAgentConfigVersionInDeployStatus = errors.New("has other agent config version is in deploy status")
)

var _ serverservice.DeployReviewService = &DeployReviewServiceImpl{}

type DeployReviewServiceImpl struct {
	dao                   *dal.DAO
	idGen                 uuid.Generator
	bpm                   serverservice.BpmOpenAPI
	deployCanaryDAO       dal.DeployCanaryDAO
	agentService          *agent.Service
	deployReviewMQ        rocketmq.Client `name:"deploy_review_mq"`
	tccDeployReviewConfig *tcc.GenericConfig[config.NextDeployReviewConfig]
	sessionService        *sessionservice.Service
}

type CreateDeployReviewServiceOption struct {
	fx.In
	Dao                   *dal.DAO
	DeployCanaryDAO       dal.DeployCanaryDAO
	AgentService          *agent.Service
	DeployReviewMQ        rocketmq.Client `name:"deploy_review_mq"`
	Bpm                   serverservice.BpmOpenAPI
	TccDeployReviewConfig *tcc.GenericConfig[config.NextDeployReviewConfig]
	SessionService        *sessionservice.Service
}

func NewService(opt CreateDeployReviewServiceOption) *DeployReviewServiceImpl {
	return &DeployReviewServiceImpl{
		dao:                   opt.Dao,
		idGen:                 uuid.GetDefaultGenerator(nil),
		bpm:                   opt.Bpm,
		deployCanaryDAO:       opt.DeployCanaryDAO,
		agentService:          opt.AgentService,
		deployReviewMQ:        opt.DeployReviewMQ,
		tccDeployReviewConfig: opt.TccDeployReviewConfig,
		sessionService:        opt.SessionService,
	}
}

// checkDeployStatus 并发校验是否有在部署的工单和处于 canary 状态的 agent config version
func (s *DeployReviewServiceImpl) checkDeployStatus(ctx context.Context, agentConfigID string) error {
	p := pool.New().WithErrors().WithFirstError().WithContext(ctx)

	// 校验是否有在部署的工单
	p.Go(func(ctx context.Context) error {
		deploy, err := s.dao.GetAgentDeploy(ctx, dal.GetAgentDeployOption{
			AgentConfigID: agentConfigID,
			Status:        []string{string(entity.AgentDeployStatusCreated), string(entity.AgentDeployStatusCanary)},
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.Wrap(err, "get agent deploy failed")
		}

		if deploy != nil {
			return ErrAgentConfigVersionInDeployStatus
		}
		return nil
	})

	// 校验是否有处于 canary 状态的 agent config version
	p.Go(func(ctx context.Context) error {
		canaryVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
			AgentConfigID: &agentConfigID,
			Status:        lo.ToPtr(entity.AgentConfigStatusCanary),
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.Wrap(err, "get agent config version failed")
		}

		if canaryVersion != nil {
			return ErrAgentConfigVersionInDeployStatus
		}
		return nil
	})

	// 等待所有并发任务完成
	return p.Wait()
}

// ensureVersionEnabled 确保 agent config version 已启用
func (s *DeployReviewServiceImpl) ensureVersionEnabled(ctx context.Context, version *entity.AgentConfigVersion) error {
	if !version.Enabled {
		_, err := s.dao.UpdateAgentConfigVersion(ctx, dal.UpdateAgentConfigVersionOption{
			ID:      version.ID,
			Enabled: lo.ToPtr(true),
		})
		if err != nil {
			return errors.WithMessage(err, "failed update agent config version")
		}
	}
	return nil
}

// handleAbTestDirectOnline 处理 AB Test 自动上线
func (s *DeployReviewServiceImpl) handleAbTestDirectOnline(ctx context.Context, workflowID int64, agentConfigVersionID string, agentConfig *entity.AgentConfig, actor string) {
	asyncPool := pool.New().WithContext(ctx)
	asyncPool.Go(func(ctx context.Context) error {
		isAbTest := false
		if agentConfig.Type == entity.AgentConfigTypeAbTest {
			isAbTest = true
			log.V1.CtxInfo(ctx, "ab test config direct online, workflow id: %d, agent config version id: %s", workflowID, agentConfigVersionID)
		}

		err := s.bpm.UpdateWorkflowRecord(ctx, &serverservice.UpdateWorkflowOptions{
			WorkflowRecordID: workflowID,
			OpKey:            entity.WorkflowNodeKeyStart,
			OpData:           map[string]any{"abtest_direct_online": isAbTest},
			Actor:            actor,
		})
		if err != nil {
			log.V1.CtxError(ctx, "update workflow ab test direct online failed, err: %v", err)
			return errors.Wrap(err, "update workflow ab test direct online failed")
		}
		return nil
	})
}

func (s *DeployReviewServiceImpl) CreateDeploy(ctx context.Context, agentConfigVersionID, actor string, extra *entity.DeployExtraInfo) (string, int64, error) {
	// 校验并获取 agent config version
	agentConfigVersion, agentConfig, err := s.agentService.GetAndCheckAgentConfigVersion(ctx, agentConfigVersionID)
	if err != nil {
		return "", 0, err
	}

	// 并发校验部署状态
	if err := s.checkDeployStatus(ctx, agentConfigVersion.AgentConfigID); err != nil {
		return "", 0, err
	}

	// 确保版本已启用
	if err := s.ensureVersionEnabled(ctx, agentConfigVersion); err != nil {
		return "", 0, err
	}

	// 并发获取 Agent 信息和线上的 agent config version id
	var (
		agentName string
		onlineID  string
	)

	p := pool.New().WithErrors().WithContext(ctx)

	// 获取 Agent 信息
	p.Go(func(ctx context.Context) error {
		var e error
		agent, e := s.dao.GetAgent(ctx, dal.GetAgentOption{
			ID: agentConfig.AgentID,
		})
		if e != nil {
			return errors.Wrap(e, "get agent failed")
		}
		if agent != nil {
			agentName = agent.Name
		}
		return nil
	})

	// 获取线上的 agent config version id
	p.Go(func(ctx context.Context) error {
		var e error
		onlineAgentConfigVersion, e := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
			AgentConfigID: &agentConfigVersion.AgentConfigID,
			Status:        lo.ToPtr(entity.AgentConfigStatusOnline),
		})
		if e != nil && !errors.Is(e, gorm.ErrRecordNotFound) {
			return errors.Wrap(e, "get agent config version failed")
		}

		if onlineAgentConfigVersion != nil {
			onlineID = onlineAgentConfigVersion.ID
		}
		return nil
	})

	// 等待所有并发任务完成
	if err := p.Wait(); err != nil {
		return "", 0, err
	}

	// 生成部署ID
	deployID := s.idGen.NewID()
	// 创建 BPM Workflow 工单
	workflow, err := s.bpm.CreateWorkflowRecord(ctx, &serverservice.CreateWorkflowRecordOptions{
		Creator:                       actor,
		Reviewer:                      extra.Reviewer,
		DeployID:                      deployID,
		AgentConfigVersionID:          agentConfigVersionID,
		AgentConfigID:                 agentConfigVersion.AgentConfigID,
		AgentConfigVersion:            agentConfigVersion.Version,
		AgentConfigVersionDescription: agentConfigVersion.Description,
		AgentConfigType:               string(agentConfig.Type),
		AgentName:                     agentName,
	})
	if err != nil {
		return "", 0, err
	}

	// 创建 deploy 记录
	err = s.dao.CreateAgentDeployOnly(ctx, dal.CreateAgentDeployOption{
		ID:                         deployID,
		AgentConfigID:              agentConfigVersion.AgentConfigID,
		AgentConfigVersionID:       agentConfigVersionID,
		Actor:                      actor,
		SourceStatus:               agentConfigVersion.Status,
		TargetStatus:               entity.AgentConfigStatusOnline,
		Status:                     entity.AgentDeployStatusCreated,
		ExtraInfo:                  extra,
		WorkflowID:                 workflow.ID,
		AgentConfigVersionOnlineID: &onlineID,
	})
	if err != nil {
		return "", 0, errors.Wrap(err, "create agent deploy failed")
	}

	// 处理 AB Test 自动上线
	s.handleAbTestDirectOnline(ctx, workflow.ID, agentConfigVersionID, agentConfig, actor)

	return deployID, workflow.ID, nil
}

func (s *DeployReviewServiceImpl) GetDeployProcessInfo(ctx context.Context, deployID string) (*entity.DeployProcessInfo, error) {
	// 查询 deploy 记录
	deploy, err := s.dao.GetAgentDeploy(ctx, dal.GetAgentDeployOption{
		ID: deployID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get agent deploy")
	}
	var newVersion, oldVersion *ab.RollingStatus
	var ratioLimit float64
	p := pool.New().WithErrors().WithContext(ctx)
	p.Go(func(ctx context.Context) error {
		var err error
		newVersion, oldVersion, err = s.getCanaryRationConfig(ctx, deploy.AgentConfigID, deploy.AgentConfigVersionID)
		if err != nil {
			return err
		}

		return nil
	})

	p.Go(func(ctx context.Context) error {
		var err error
		agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
			ID: &deploy.AgentConfigID,
		})
		if err != nil {
			return errors.Wrap(err, "failed to get agent config")
		}

		_, ratioLimit, err = s.deployCanaryDAO.GetDeployCanaryConfig(ctx, agentConfig.AgentID)
		if err != nil {
			return errors.Wrap(err, "failed to get canary task status")
		}
		return nil
	})
	// 等待所有并发任务完成
	if err := p.Wait(); err != nil {
		return nil, err
	}

	ratio := ab.CalcRollingProbabilityWithOpt(ab.CalcRollingProbabilityOption{
		OldVersion: oldVersion,
		NewVersion: *newVersion,
	})
	log.V1.CtxInfo(ctx, "get deploy process info, deploy id: %s, success count: %d, fail count: %d, running count: %d, ratio: %f", deployID, newVersion.Succeed, newVersion.Failed, newVersion.Running, ratio)

	if newVersion.Succeed == 0 && newVersion.Failed == 0 && newVersion.Running == 0 {
		ratio = 0
	}
	minRatio := ratio
	if ratioLimit < ratio {
		minRatio = ratioLimit
	}

	return &entity.DeployProcessInfo{
		SuccessCount: newVersion.Succeed,
		FailCount:    newVersion.Failed,
		RunningCount: newVersion.Running,
		CanaryRatio:  minRatio,
		Status:       deploy.Status,
	}, nil
}

func (s *DeployReviewServiceImpl) StartCanary(ctx context.Context, opt *serverservice.DeployCanaryOption) error {
	agentConfig, _, _, err := s.getAndCheckDeploy(ctx, opt)
	if err != nil {
		return err
	}

	rationLimit := 0.01
	if opt.Status == entity.WorkflowNodeKeyCanary10 {
		rationLimit = 0.1
	}
	if opt.Status == entity.WorkflowNodeKeyCanary50 {
		rationLimit = 0.5
	}

	// 启动灰度过程
	err = s.deployCanaryDAO.CreateDeployCanaryConfig(ctx, dal.CreateDeployCanaryConfigOption{
		AgentID:              agentConfig.AgentID,
		AgentConfigVersionID: opt.AgentConfigVersionID,
		RatioLimit:           rationLimit,
	})

	if err != nil {
		log.V1.CtxError(ctx, "CreateDeployCanaryConfig failed, err: %v", err)
		return errors.Wrap(err, "CreateDeployCanaryConfig failed")
	}

	// 只需要 canary_1 节点更改下面的状态
	if opt.Status != entity.WorkflowNodeKeyCanary1 {
		return nil
	}

	// 更新 deploy 状态为 canary
	err = s.dao.UpdateAgentDeploy(ctx, dal.UpdateAgentDeployOption{
		ID:                       opt.DeployID,
		Status:                   entity.AgentDeployStatusCanary,
		AgentConfigVersionID:     opt.AgentConfigVersionID,
		AgentConfigVersionStatus: entity.AgentConfigStatusCanary,
	})

	if err != nil {
		return errors.Wrap(err, "update agent deploy status failed")
	}
	// 开始灰度进程巡检
	log.V1.CtxInfo(ctx, "start canary inspect, deploy id: %s, workflow id: %d, agent config id: %s, agent config version id: %s", opt.DeployID, opt.WorkflowID, opt.AgentConfigID, opt.AgentConfigVersionID)
	event, err := json.Marshal(entity.DeployReviewEvent{
		AgentDeployID:        opt.DeployID,
		WorkflowRecordID:     opt.WorkflowID,
		AgentConfigID:        opt.AgentConfigID,
		AgentConfigVersionID: opt.AgentConfigVersionID,
	})
	if err != nil {
		return errors.Wrap(err, "marshal deploy review event failed")
	}

	delay := s.tccDeployReviewConfig.GetPointer().InspectInterval
	err = s.deployReviewMQ.SendDelayedMessage(ctx, event, time.Duration(delay)*time.Minute, agententity.DeployReviewTag)
	if err != nil {
		return errors.Wrap(err, "send deploy review message failed")
	}
	return nil

}

func (s *DeployReviewServiceImpl) CancelCanary(ctx context.Context, opt *serverservice.DeployCanaryOption) error {
	agentConfig, agentConfigVersion, _, err := s.getAndCheckDeploy(ctx, opt)
	if err != nil {
		return err
	}
	// 移除灰度标识
	if agentConfig.Type == entity.AgentConfigTypeBase {
		err = s.deployCanaryDAO.DelDeployCanaryConfig(ctx, agentConfig.AgentID)
		if err != nil {
			log.V1.CtxError(ctx, "DelDeployCanaryConfig failed, err: %v", err)
			return errors.Wrap(err, "DelDeployCanaryConfig failed")
		}
	}

	// 更新deploy 状态为 cancel
	err = s.dao.UpdateAgentDeploy(ctx, dal.UpdateAgentDeployOption{
		ID:                       opt.DeployID,
		Status:                   entity.AgentDeployStatusCancelCanary,
		AgentConfigVersionStatus: entity.AgentConfigStatusCreated,
		AgentConfigVersionID:     agentConfigVersion.ID,
		AgentConfigVersionEnable: lo.ToPtr(false),
	})

	if err != nil {
		return errors.Wrap(err, "update agent deploy status failed")
	}

	return nil
}

func (s *DeployReviewServiceImpl) CloseWorkflowRecord(ctx context.Context, opt *serverservice.DeployCanaryOption) error {
	agentConfig, agentConfigVersion, deploy, err := s.getAndCheckDeploy(ctx, opt)
	if err != nil {
		return err
	}
	// 移除灰度标识
	if agentConfig.Type == entity.AgentConfigTypeBase {
		err = s.deployCanaryDAO.DelDeployCanaryConfig(ctx, agentConfig.AgentID)
		if err != nil {
			log.V1.CtxError(ctx, "DelDeployCanaryConfig failed, err: %v", err)
			return errors.Wrap(err, "DelDeployCanaryConfig failed")
		}
	}

	var status entity.AgentDeployStatus
	if opt.AuditRejectComment != "" {
		status = entity.AgentDeployStatusAuditReject
	} else {
		status = entity.AgentDeployStatusClose
	}

	if deploy.ExtraInfo != nil {
		deploy.ExtraInfo.CloseReason = opt.CloseReason
		deploy.ExtraInfo.AuditRejectComment = opt.AuditRejectComment
	} else {
		deploy.ExtraInfo = &entity.DeployExtraInfo{
			CloseReason:        opt.CloseReason,
			AuditRejectComment: opt.AuditRejectComment,
		}
	}

	// 更新 deploy 状态为 close
	err = s.dao.UpdateAgentDeploy(ctx, dal.UpdateAgentDeployOption{
		ID:                       opt.DeployID,
		Status:                   status,
		ExtraInfo:                deploy.ExtraInfo,
		AgentConfigVersionID:     agentConfigVersion.ID,
		AgentConfigVersionStatus: entity.AgentConfigStatusCreated,
		AgentConfigVersionEnable: lo.ToPtr(false),
	})
	if err != nil {
		return errors.Wrap(err, "update agent deploy status failed")
	}

	return nil
}

// 上线后清理预热容器
func (s *DeployReviewServiceImpl) deletePreparedSessions(ctx context.Context, agentConfigVersion *entity.AgentConfigVersion) {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.V1.CtxError(ctx, "recover from panic: %v", err)
			}
		}()
		newCtx := ctxvalues.SetLogID(context.Background(), ctxvalues.LogIDDefault(ctx))

		// 获取部署 Agent 对应的 Role
		role, err := s.agentService.GetRoleByAgentConfigVersion(newCtx, *agentConfigVersion)
		if err != nil {
			log.V1.CtxError(newCtx, "get role error: %v", err)
			return
		}
		if role == nil {
			log.V1.CtxError(newCtx, "role not found")
			return
		}

		// 删除预热容器
		_, failedSessions, err := s.sessionService.DeleteAllPreparedSessions(newCtx, role)
		if err != nil {
			log.V1.CtxError(newCtx, "delete prepares session error: %v", err)
			return
		}
		if len(failedSessions) > 0 {
			log.V1.CtxWarn(newCtx, "delete prepares sessions partial failure, failed sessions: %v", failedSessions)
			return
		}
	}()
}

func (s *DeployReviewServiceImpl) Online(ctx context.Context, opt *serverservice.DeployCanaryOption) error {
	agentConfig, agentConfigVersion, deploy, err := s.getAndCheckDeploy(ctx, opt)
	if err != nil {
		return err
	}
	//  移除灰度标识，只有 base 类型需要操作
	if agentConfig.Type == entity.AgentConfigTypeBase {
		err = s.deployCanaryDAO.DelDeployCanaryConfig(ctx, agentConfig.AgentID)
		if err != nil {
			log.V1.CtxError(ctx, "DelDeployCanaryConfig failed, err: %v", err)
			return errors.Wrap(err, "DelDeployCanaryConfig failed")
		}
	}

	// 更新 agent config version 状态
	if deploy.ExtraInfo != nil {
		deploy.ExtraInfo.SkipCanaryComment = opt.SkipCanaryComment
	}
	err = s.agentService.UpdateDeployAgentStatus(ctx, agentConfigVersion, agentConfig, agent.DeployAgentOption{
		DeployID:  deploy.ID,
		ExtraInfo: deploy.ExtraInfo,
		Actor:     deploy.Actor,
	})

	if err != nil {
		log.V1.CtxError(ctx, "agent deploy online config version failed, err: %v", err)
		return errors.Wrap(err, "agent deploy online config version failed")
	}

	s.deletePreparedSessions(ctx, agentConfigVersion)
	return nil
}

// 获取灰度阶段配置
func (s *DeployReviewServiceImpl) getCanaryStageConfig(workflowStatus string) (needContinue bool, targetRatio float64, checkKey string, opKey entity.WorkflowNodeKey) {
	switch entity.ParseWorkflowNodeKey(workflowStatus) {
	case entity.WorkflowNodeKeyCanary1:
		return true, 0.01, "canary_1_check", entity.WorkflowNodeKeyCanary1
	case entity.WorkflowNodeKeyCanary10:
		return true, 0.1, "canary_10_check", entity.WorkflowNodeKeyCanary10
	case entity.WorkflowNodeKeyCanary50:
		// 检查并更新最终灰度阶段
		return true, 0.5, "canary_50_check", entity.WorkflowNodeKeyCanary50
	case entity.WorkflowNodeKeyFinish:
		return false, 0.0, "", ""
	default:
		return true, 0.0, "", ""
	}
}

// 继续灰度巡检
func (s *DeployReviewServiceImpl) continueCanaryInspection(ctx context.Context, event *entity.DeployReviewEvent, ratio, targetRatio float64) error {
	log.V1.CtxInfo(ctx, "(%s)canary continue inspect, current ratio: %f, targetRatio: %f", event.AgentConfigVersionID, ratio, targetRatio)
	msg, err := json.Marshal(event)
	if err != nil {
		return errors.Wrap(err, "marshal deploy review event failed")
	}

	delay := s.tccDeployReviewConfig.GetPointer().InspectInterval
	err = s.deployReviewMQ.SendDelayedMessage(ctx, msg, time.Duration(delay)*time.Minute, agententity.DeployReviewTag)
	if err != nil {
		return errors.Wrap(err, "send deploy review message failed")
	}
	return nil
}

// updateWorkflowIfRatioReached 根据灰度比例更新工作流状态
func (s *DeployReviewServiceImpl) updateWorkflowIfRatioReached(ctx context.Context, event *entity.DeployReviewEvent, deploy *entity.AgentDeploy, ratio float64, targetRatio float64, opKey entity.WorkflowNodeKey, checkKey string) error {
	if ratio >= targetRatio {
		// 当前灰度比例已达到目标比例，更新工作流状态
		return s.bpm.UpdateWorkflowRecord(ctx, &serverservice.UpdateWorkflowOptions{
			WorkflowRecordID: event.WorkflowRecordID,
			OpKey:            opKey,
			OpData:           map[string]any{checkKey: true},
			Actor:            deploy.Actor,
		})
	}
	return nil
}

// 处理工作流程 MQ，检查失败任务数是否超过限制，超过则终止流程
func (s *DeployReviewServiceImpl) HandleWorkflowProcess(ctx context.Context, event *entity.DeployReviewEvent) error {
	// 获取部署信息
	deploy, err := s.dao.GetAgentDeploy(ctx, dal.GetAgentDeployOption{
		ID: event.AgentDeployID,
	})
	if err != nil {
		return errors.Wrap(err, "failed to get agent deploy")
	}

	// 获取灰度任务状态
	newVersion, oldVersion, err := s.getCanaryRationConfig(ctx, deploy.AgentConfigID, deploy.AgentConfigVersionID)
	if err != nil {
		return err
	}
	ratio := ab.CalcRollingProbabilityWithOpt(ab.CalcRollingProbabilityOption{
		OldVersion: oldVersion,
		NewVersion: *newVersion,
	})
	log.V1.CtxInfo(ctx, "(%s)canary task status, successCount: %d, failCount: %d, runningCount: %d, ratio: %f",
		event.AgentConfigVersionID, newVersion.Succeed, newVersion.Failed, newVersion.Running, ratio)

	// 获取工作流记录
	workflowRecord, err := s.bpm.GetWorkflowRecord(ctx, event.WorkflowRecordID, deploy.Actor)
	if err != nil {
		return err
	}

	if workflowRecord.Finished >= 1 {
		log.V1.CtxInfo(ctx, "(%s) canary workflow record(%d) finished", event.AgentConfigVersionID, event.WorkflowRecordID)
		return nil
	}
	log.V1.CtxInfo(ctx, "(%s)canary workflow record(%d) status: %s, finish: %d", event.AgentConfigVersionID, event.WorkflowRecordID, workflowRecord.Status, workflowRecord.Finished)

	// 获取当前灰度阶段配置
	needContinue, targetRatio, checkKey, opKey := s.getCanaryStageConfig(workflowRecord.Status)
	//TODO: 检查失败任务数是否超过限制，终止流程
	if checkKey != "" {
		if newVersion.Failed > s.tccDeployReviewConfig.GetPointer().FailLimit {
			return s.bpm.UpdateWorkflowRecord(ctx, &serverservice.UpdateWorkflowOptions{
				WorkflowRecordID: event.WorkflowRecordID,
				OpKey:            opKey,
				OpData:           map[string]any{checkKey: false},
				Actor:            deploy.Actor,
			})
		}

		// 检查并更新灰度比例
		if err := s.updateWorkflowIfRatioReached(ctx, event, deploy, ratio, targetRatio, opKey, checkKey); err != nil {
			log.V1.CtxError(ctx, "handle bpm workflow process failed, err: %v", err)
			return errors.Wrap(err, "handle bpm workflow process failed")
		}
	}

	// 继续巡检
	if needContinue {
		return s.continueCanaryInspection(ctx, event, ratio, targetRatio)
	}

	return nil
}

func (s *DeployReviewServiceImpl) GetDeploy(ctx context.Context, deployID string) (*entity.AgentDeploy, error) {
	deploy, err := s.dao.GetAgentDeploy(ctx, dal.GetAgentDeployOption{
		ID: deployID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get agent deploy")
	}
	return deploy, nil
}

func (s *DeployReviewServiceImpl) GetDeployReviewUser(ctx context.Context) ([]string, error) {
	return s.tccDeployReviewConfig.GetPointer().DeployUser, nil
}

func (s *DeployReviewServiceImpl) ListAgentDeploy(ctx context.Context, opt *serverservice.GetAgentDeployListOption) ([]*entity.AgentDeploy, int64, error) {
	if opt.PageSize > 20 {
		opt.PageSize = 20
	}
	deployList, total, err := s.dao.GetAgentDeployList(ctx, dal.GetAgentDeployListOption{
		AgentConfigID: opt.AgentConfigID,
		Status:        opt.Status,
		Offset:        int((opt.PageNum - 1) * opt.PageSize),
		Limit:         int(opt.PageSize),
	})
	if err != nil {
		return nil, 0, errors.Wrap(err, "failed to get agent deploy list")
	}

	p := pool.New().WithErrors().WithContext(ctx)
	for _, deploy := range deployList {
		d := deploy
		p.Go(func(ctx context.Context) error {
			agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{ID: &d.AgentConfigVersionID})
			if err != nil {
				return errors.Wrap(err, "failed to get agent config version")
			}
			d.Version = agentConfigVersion.Version
			d.Description = agentConfigVersion.Description

			// 如果存在在线版本，获取在线版本信息
			if d.AgentConfigVersionOnlineID != "" {
				onlineVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{ID: &d.AgentConfigVersionOnlineID})
				if err != nil {
					return errors.Wrap(err, "failed to get agent config version")
				}
				d.OnlineVersion = onlineVersion.Version
				deploy.OnlineDescription = onlineVersion.Description
			}
			return nil
		})
	}
	if err := p.Wait(); err != nil {
		return nil, 0, err
	}

	return deployList, total, nil
}

// getAndCheckDeploy 并发获取并校验部署相关数据
func (s *DeployReviewServiceImpl) getAndCheckDeploy(ctx context.Context, opt *serverservice.DeployCanaryOption) (*entity.AgentConfig, *entity.AgentConfigVersion, *entity.AgentDeploy, error) {
	var (
		agentConfig        *entity.AgentConfig
		agentConfigVersion *entity.AgentConfigVersion
		deploy             *entity.AgentDeploy
	)

	// 并发获取所有需要的数据
	p := pool.New().WithErrors().WithContext(ctx)

	// 获取 agent config
	p.Go(func(ctx context.Context) error {
		var err error
		agentConfig, err = s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{ID: &opt.AgentConfigID})
		if err != nil {
			return errors.Wrap(err, "get agent config failed")
		}
		return nil
	})

	// 获取 agent config version
	p.Go(func(ctx context.Context) error {
		var err error
		agentConfigVersion, err = s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{ID: &opt.AgentConfigVersionID})
		if err != nil {
			return errors.Wrap(err, "get agent config version failed")
		}
		return nil
	})

	// 获取 deploy 记录
	p.Go(func(ctx context.Context) error {
		var err error
		deploy, err = s.dao.GetAgentDeploy(ctx, dal.GetAgentDeployOption{ID: opt.DeployID})
		if err != nil {
			return errors.Wrap(err, "get deploy record failed")
		}
		return nil
	})

	// 等待所有并发任务完成
	if err := p.Wait(); err != nil {
		return nil, nil, nil, err
	}

	// 校验数据完整性
	if err := s.validateDeployData(opt, agentConfig, agentConfigVersion, deploy); err != nil {
		return nil, nil, nil, err
	}

	return agentConfig, agentConfigVersion, deploy, nil
}

// validateDeployData 校验部署数据的一致性
func (s *DeployReviewServiceImpl) validateDeployData(opt *serverservice.DeployCanaryOption, agentConfig *entity.AgentConfig, agentConfigVersion *entity.AgentConfigVersion, deploy *entity.AgentDeploy) error {
	// 确保所有数据都已成功获取
	if agentConfig == nil || agentConfigVersion == nil || deploy == nil {
		return errors.New("failed to get agent config data")
	}

	// 校验数据一致性
	if deploy.WorkflowID != opt.WorkflowID {
		return errors.New("workflow id not match")
	}

	if deploy.AgentConfigID != opt.AgentConfigID {
		return errors.New("agent config id not match")
	}

	if deploy.AgentConfigVersionID != opt.AgentConfigVersionID {
		return errors.New("agent config version id not match")
	}

	return nil
}

func (s *DeployReviewServiceImpl) getCanaryRationConfig(ctx context.Context, agentConfigID string, agentConfigVersionID string) (*ab.RollingStatus, *ab.RollingStatus, error) {
	successCount, failCount, runningCount, err := s.deployCanaryDAO.GetCanaryTaskStatus(ctx, agentConfigVersionID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get canary task status")
	}

	newVersion := &ab.RollingStatus{
		Succeed: successCount,
		Failed:  failCount,
		Running: runningCount,
	}

	role, err := s.agentService.GetRoleByAgentConfigVersion(ctx, entity.AgentConfigVersion{
		AgentConfigID: agentConfigID,
	})
	if err != nil {
		return nil, nil, errors.New("failed to get role by agent config version")
	}
	if role == nil {
		return newVersion, nil, nil
	}

	// 从 tcc 配置中获取 agent 对应的灰度配置
	var oldVersion *ab.RollingStatus
	rationConfig := s.tccDeployReviewConfig.GetPointer().CanaryVersionRationConfig
	if r, ok := rationConfig[strconv.Itoa(int(*role))]; ok {
		newVersion.RunningWeight = r.NewVersion.RunningWeight
		oldVersion = &ab.RollingStatus{
			Succeed:       r.OldVersion.Succeed,
			Failed:        r.OldVersion.Failed,
			Running:       r.OldVersion.Running,
			RunningWeight: r.OldVersion.RunningWeight,
		}
	}
	return newVersion, oldVersion, nil
}
