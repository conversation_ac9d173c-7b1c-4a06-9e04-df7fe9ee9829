package lark

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"strconv"
	"strings"
	"time"
	"unicode"

	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lock"
	"code.byted.org/devgpt/kiwis/lib/metrics"

	"github.com/cenkalti/backoff/v4"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher/callback"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/gopkg/env"
)

type Service struct {
	tccConf    *config.AgentSphereTCCConfig
	config     *config.AgentSphereConfig
	dao        *dal.DAO
	larkClient lark.Client
	lock       lock.Lock
	cache      redis.Client
}

type CreateServiceOption struct {
	fx.In
	TCCConf    *config.AgentSphereTCCConfig
	Config     *config.AgentSphereConfig
	DAO        *dal.DAO
	LarkClient lark.Client
	Lock       lock.Lock
	Cache      redis.Client
}

const (
	LockKeyTransferOwner = "lark_lock_transfer_owner" // 转移文档所有权需要串行调用，否则会概率性出现所有权转移但是文件没有移动的情况
)

var (
	ErrRefreshTokenExpired    = errors.New("refresh token expired")
	ErrLarkUserRecordNotFound = errors.New("lark_user record not found")
	ErrLarkDocAuthFailed      = errors.New("lark doc auth failed")
)

func NewService(opt CreateServiceOption) (*Service, error) {
	return &Service{
		tccConf:    opt.TCCConf,
		dao:        opt.DAO,
		larkClient: opt.LarkClient,
		lock:       opt.Lock,
		cache:      opt.Cache,
		config:     opt.Config,
	}, nil
}

func (s *Service) LarkAuth(ctx context.Context, username, email, code string, authorizationDenied bool) (authorization bool, err error) {
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleLark, "LarkAuth")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()

	var (
		larkUser entity.LarkUser
	)
	if authorizationDenied {
		larkUser = entity.LarkUser{
			Username:            username,
			Email:               email,
			AccessToken:         "",
			RefreshToken:        "",
			AccessExpireAt:      time.Time{},
			RefreshExpireAt:     time.Time{},
			AuthorizationDenied: true,
		}
		authorization = false
	} else {
		userToken, err := s.larkClient.GetUserAccessToken(ctx, code)
		if err != nil {
			return false, err
		}

		userInfo, err := s.larkClient.GetAuthenUserInfo(ctx, userToken.AccessToken)
		if err != nil {
			return false, err
		}
		if userInfo.Email != email {
			return false, errors.WithMessagef(serverservice.ErrLarkAuthAuthenNotMatch, "email not match, %s!=%s", email, userInfo.Email)
		}

		larkUser = entity.LarkUser{
			Username:            username,
			Email:               email,
			AccessToken:         userToken.AccessToken,
			RefreshToken:        userToken.RefreshToken,
			AccessExpireAt:      userToken.AccessExpireAt,
			RefreshExpireAt:     userToken.RefreshExpireAt,
			Scope:               userToken.Scope,
			AuthorizationDenied: false,
		}
		authorization = true
	}

	err = s.dao.UpsertLarkUserToken(ctx, &larkUser)
	if err != nil {
		return false, err
	}

	if authorization {
		// 同意授权后，get user openId
		_, err := s.GetAndSaveLarkUserOpenID(ctx, username, email)
		if err != nil {
			return false, err
		}
	}
	return authorization, nil
}

func (s *Service) GetLarkUser(ctx context.Context, username string) (*entity.LarkUser, error) {
	return s.dao.GetLarkUser(ctx, username, false)
}

func (s *Service) CheckLarkAuth(ctx context.Context, username string, mail string) (authorizationDenied bool, authorization bool, err error) {
	larkUser, err := s.dao.GetLarkUser(ctx, username, true)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return false, false, nil
		}
		return false, false, errors.WithMessagef(err, "failed to get lark user: %s", username)
	}
	if larkUser.AuthorizationDenied {
		return true, false, nil
	}
	// scope 校验
	if !lo.Every(strings.Split(larkUser.Scope, " "), strings.Split(s.tccConf.NeumaLarkAppConfig.GetValue().Scope, " ")) {
		return false, false, nil
	}

	now := time.Now()
	if larkUser.AccessExpireAt.Add(-10 * time.Minute).Before(now) {
		if larkUser.RefreshExpireAt.Before(now) { // refresh token expired
			return false, false, nil
		} else {
			userToken, err := s.larkClient.RefreshToken(ctx, larkUser.RefreshToken)
			if err != nil {
				return false, false, errors.WithMessagef(err, "failed to refresh lark user: %s", username)
			}
			// ticket, err := s.larkClient.GetJSApiTicket(ctx, userToken.AccessToken)
			// if err != nil {
			// 	return false, false, err
			// }

			larkUser.AccessToken = userToken.AccessToken
			larkUser.RefreshToken = userToken.RefreshToken
			larkUser.AccessExpireAt = userToken.AccessExpireAt
			larkUser.RefreshExpireAt = userToken.RefreshExpireAt
			larkUser.Scope = userToken.Scope
			larkUser.AuthorizationDenied = false
			err = s.dao.UpsertLarkUserToken(ctx, larkUser)
			if err != nil {
				log.V1.CtxError(ctx, "failed to upsert lark user token: %v, username: %s", err, username)
				return false, false, err
			}
		}
	}

	if larkUser.Email != mail && mail != "" { //可能用户的邮箱发生了变化，需要更新
		err = s.dao.UpdateLarkUserEmail(ctx, username, mail)
		if err != nil {
			log.V1.CtxError(ctx, "failed to update lark user email: %v, username: %s", err, username)
			// 不需要返回错误，等下次更新
		}
	}

	return false, true, nil
}

func (s *Service) GetAndSaveLarkUserOpenID(ctx context.Context, username string, email string) (string, error) {
	if username == "" {
		return "", errors.New("username or email is empty")
	}

	// get user openId
	userEMail := lo.Ternary(email != "", email, username+"@bytedance.com")
	openIds, err := s.larkClient.MGetUserIDByEmail(ctx, []string{userEMail}, lark.UserIDTypeOpenID, true)
	if err != nil {
		// log.V1.CtxWarn(ctx, "failed to get user openId: %v, username: %s", err, userEMail)
		return "", errors.WithMessagef(err, "failed to get user openId, user: %s", userEMail)
	}
	openID, ok := openIds[userEMail]
	if !ok || len(openID) == 0 {
		// log.V1.CtxWarn(ctx, "failed to get user openId: %v, username: %s", err, userEMail)
		return "", errors.Errorf("failed to get user openId, no openID for user: %s", userEMail)
	}

	err = s.dao.UpdateUserOpenID(ctx, dal.UpdateLarkUserOpenIDOption{
		Username: username,
		OpenID:   openID,
	})
	if err != nil {
		// log.V1.CtxWarn(ctx, "failed to update user openId: %v, username: %s", err, username)
		return "", errors.WithMessagef(err, "failed to update user openId, user: %s", userEMail)
	}

	return openID, nil
}

type TicketResult struct {
	JSApiTicket   lark.JSApiTicket
	OpenID        string
	AppID         string
	Authorization bool
}

func (s *Service) GetLarkTicket(ctx context.Context, username string, email string) (result *TicketResult, err error) {
	reportMetricsFunc := metrics.NSM.ReportModuleMetrics(metrics.NextServerModuleLark, "GetLarkTicket")
	defer func() {
		reportMetricsFunc(err != nil, serverservice.ErrorToErrorReason(err))
	}()
	appID := s.tccConf.NeumaLarkAppConfig.GetValue().AppID
	larkUser, err := s.dao.GetLarkUser(ctx, username, true)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get lark user: %v, username: %s", err, username)
		if db.IsRecordNotFoundError(err) {
			return &TicketResult{
				JSApiTicket:   lark.JSApiTicket{},
				OpenID:        "",
				AppID:         appID,
				Authorization: false,
			}, nil
		}
		return nil, err
	}

	// 取openID
	openID := larkUser.OpenID
	if openID == "" {
		// get user openId
		id, err := s.GetAndSaveLarkUserOpenID(ctx, username, email)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to get user openId: %v, username: %s", err, username)
			return nil, err
		}
		openID = id
	}

	if larkUser.JSApiTicket != "" && larkUser.JSApiTicketExpireAt.Add(-10*time.Minute).After(time.Now()) {
		return &TicketResult{
			JSApiTicket: lark.JSApiTicket{
				Ticket:   larkUser.JSApiTicket,
				ExpireIn: int32(time.Until(larkUser.JSApiTicketExpireAt).Seconds()),
			},
			OpenID:        openID,
			AppID:         appID,
			Authorization: true,
		}, nil
	}
	// 如果不存在或者过期了，获取userAccessToken，调用lark的api获取新的ticket
	var userToken string
	err = backoff.Retry(func() error {
		userToken, err = s.GetUserAccessToken(ctx, username) // 如果accessToken过期了，调用GetUserAccessToken会自动刷新
		if err != nil {
			if errors.Is(err, lark.ErrRefreshTokenAlreadyUsed) || errors.Is(err, lark.ErrRefreshTokenRevoked) { // 重试
				return err
			}
			return backoff.Permanent(err) // 其他 error 不需要重试
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(backoff.WithInitialInterval(time.Millisecond*500), backoff.WithMaxInterval(time.Second)), 3))
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get user access token: %v, username: %s", err, username)
		if errors.Is(err, ErrRefreshTokenExpired) || errors.Is(err, lark.ErrRefreshTokenRevoked) || errors.Is(err, lark.ErrRefreshTokenAlreadyUsed) {
			// 不返回错误，返回未授权
			return &TicketResult{
				JSApiTicket:   lark.JSApiTicket{},
				OpenID:        "",
				AppID:         appID,
				Authorization: false,
			}, nil
		}
		return nil, err
	}

	now := time.Now()
	ticket, err := s.larkClient.GetJSApiTicket(ctx, userToken)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get jsapi ticket: %v, username: %s", err, username)
		return nil, err
	}

	// save ticket into db
	err = s.dao.UpdateUserJSTicket(ctx, dal.UpdateLarkUserTicketOption{
		Username: username,
		Ticket:   ticket.Ticket,
		ExpireAt: now.Add(time.Second * time.Duration(ticket.ExpireIn)),
	})
	if err != nil {
		// 记录错误，不需要返回错误
		log.V1.CtxWarn(ctx, "failed to update user jsapi ticket: %v", err)
	}

	result = &TicketResult{
		JSApiTicket:   ticket,
		OpenID:        openID,
		AppID:         appID,
		Authorization: true,
	}

	return result, nil

}

func (s *Service) GetUserAccessToken(ctx context.Context, username string) (accessToken string, err error) {
	err = backoff.Retry(func() error {
		larkUser, err := s.dao.GetLarkUser(ctx, username, true)
		if err != nil {
			return backoff.Permanent(errors.WithMessagef(err, "failed to get lark user: %v", username))
		}
		if larkUser.AuthorizationDenied {
			return backoff.Permanent(errors.New("authorization denied"))
		}
		now := time.Now()
		if larkUser.AccessExpireAt.Add(-60 * time.Minute).After(now) { // 过期之前 60 分钟保证刷新
			accessToken = larkUser.AccessToken
			return nil
		} else {
			if larkUser.RefreshExpireAt.Before(now) {
				return backoff.Permanent(ErrRefreshTokenExpired)
			} else {
				userToken, err := s.larkClient.RefreshToken(ctx, larkUser.RefreshToken)
				if err != nil {
					if errors.Is(err, lark.ErrRefreshTokenAlreadyUsed) {
						return err
					}
					return backoff.Permanent(errors.WithMessage(err, "failed to refresh token"))
				}
				larkUser.AccessToken = userToken.AccessToken
				larkUser.RefreshToken = userToken.RefreshToken
				larkUser.AccessExpireAt = userToken.AccessExpireAt
				larkUser.RefreshExpireAt = userToken.RefreshExpireAt
				util.DoASync(func() {
					err = s.dao.UpsertLarkUserToken(ctx, larkUser)
					if err != nil {
						log.V1.CtxError(ctx, "failed to upsert lark user token: %v, username: %s", err, username)
						return
					}
				})
				accessToken = userToken.AccessToken
				return nil
			}
		}
	}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Millisecond*300), uint64(3)))
	return accessToken, err
}

func (s *Service) SendTaskFinishNotification(ctx context.Context, sessionID, username, title string) (err error) {
	stepNum, err := s.dao.CountSteps(ctx, sessionID, entity.StepStatusSuccess)
	if err != nil {
		log.V1.CtxError(ctx, "failed to count steps: %v", err)
	}
	if stepNum == 0 {
		log.V1.CtxError(ctx, "count step is zero")
		return
	}
	u := fmt.Sprintf("%s/chat/%s", s.config.BaseConfig.Domain, sessionID)
	contents, err := genStandardCard(fmt.Sprintf("**%v**任务已完成，共 **%v** 步，点击下方按钮查看详情 🎉", strings.TrimSpace(title), stepNum), "🔔 你交给 Aime 的任务已完成，快去看看吧~", u, "查看详情")
	if err != nil {
		return err
	}

	email := s.getUserEmailWithFallback(ctx, username)
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeEmail, email, larkim.MsgTypeInteractive, contents)
	return err
}

func (s *Service) SendNotificationMessage(ctx context.Context, username, link, title, content, linkName string) (err error) {
	contents, err := genStandardCard(fmt.Sprintf("%s", strings.TrimSpace(content)), fmt.Sprintf("🔔 %s", title), link, linkName)
	if err != nil {
		return err
	}

	email := s.getUserEmailWithFallback(ctx, username)
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeEmail, email, larkim.MsgTypeInteractive, contents)
	return err
}

func (s *Service) getUserEmailWithFallback(ctx context.Context, username string) string {
	email, err := s.getUserEmail(ctx, username)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get user email: %v, username: %s", err, username)
		return username + "@bytedance.com"
	}
	return lo.Ternary(email != "", email, username+"@bytedance.com")
}

func (s *Service) getUserEmail(ctx context.Context, username string) (string, error) {
	larkUser, err := s.dao.GetLarkUser(ctx, username, false)
	if err != nil {
		return "", err
	}
	if larkUser == nil || larkUser.Email == "" {
		return "", errors.New("lark user email not found")
	}
	return larkUser.Email, nil
}

func genStandardCard(content, header, buttonUrl, buttonTag string) (string, error) {
	contentMap := map[string]interface{}{
		"schema": "2.0",
		"config": map[string]interface{}{
			"update_multi": true,
			"style": map[string]interface{}{
				"text_size": map[string]interface{}{
					"normal_v2": map[string]interface{}{
						"default": "normal",
						"pc":      "normal",
						"mobile":  "heading",
					},
				},
			},
		},
		"body": map[string]interface{}{
			"direction": "vertical",
			"padding":   "12px 12px 12px 12px",
			"elements": []interface{}{
				map[string]interface{}{
					"tag":        "markdown",
					"content":    content,
					"text_align": "left",
					"text_size":  "normal_v2",
					"margin":     "0px 0px 0px 0px",
				},
				map[string]interface{}{
					"tag":    "hr",
					"margin": "0px 0px 0px 0px",
				},
				map[string]interface{}{
					"tag": "button",
					"text": map[string]string{
						"tag":     "plain_text",
						"content": buttonTag,
					},
					"type":  "primary_filled",
					"width": "default",
					"size":  "large",
					"behaviors": []interface{}{
						map[string]interface{}{
							"type":        "open_url",
							"default_url": buttonUrl,
							"pc_url":      "",
							"ios_url":     "",
							"android_url": "",
						},
					},
					"margin": "0px 0px 0px 0px",
				},
			},
		},
		"header": map[string]interface{}{
			"title": map[string]string{
				"tag":     "plain_text",
				"content": header,
			},
			"subtitle": map[string]string{
				"tag":     "plain_text",
				"content": "",
			},
			"template": "blue",
			"padding":  "12px 12px 12px 12px",
		},
	}
	contentBytes, err := json.Marshal(contentMap)
	return string(contentBytes), err
}

func (s *Service) SendTaskFinishNotificationFeedbackCard(ctx context.Context, replayID, email, title string) (openMessageID string, err error) {
	if email == "" {
		return "", errors.New("username is empty")
	}
	chatURL := fmt.Sprintf("%s/share/%s", s.config.BaseConfig.Domain, replayID)
	userIDMap, err := s.larkClient.MGetUserIDByEmail(ctx, []string{email}, lark.UserIDTypeUserID, true)
	if err != nil {
		return "", err
	}
	userID, ok := userIDMap[email]
	if !ok {
		return "", errors.New("user not found")
	}
	contents, err := getNotificationFeedbackCard(userID, title, chatURL)
	if err != nil {
		return "", err
	}
	resp, err := s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeEmail, email, larkim.MsgTypeInteractive, contents)
	if err != nil {
		return "", err
	}
	return lo.FromPtr(resp.MessageId), err
}

func (s *Service) SendTaskNeedHumanNotification(ctx context.Context, sessionID, username, title string) (err error) {
	u := fmt.Sprintf("%s/chat/%s", s.config.BaseConfig.Domain, sessionID)
	contents, err := genStandardCard(fmt.Sprintf("**%v**需要补充更多信息完成任务，点击下方按钮查看详情✉️", strings.TrimSpace(title)), "🔔 你交给 Aime 的任务需要补充更多信息，期待你的回复～", u, "查看详情")
	if err != nil {
		return err
	}
	email := s.getUserEmailWithFallback(ctx, username)
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeEmail, email, larkim.MsgTypeInteractive, contents)
	return err
}

func (s *Service) SendTaskNeedTakeBrowserNotification(ctx context.Context, sessionID, username, title string) (err error) {
	u := fmt.Sprintf("%s/chat/%s", s.config.BaseConfig.Domain, sessionID)
	contents, err := genStandardCard(fmt.Sprintf("**任务名称**：%v\n**请于 60 分钟内接管浏览器，如未操作，将自动跳过。️**", strings.TrimSpace(title)), "🔔 Aime 需要您接管浏览器以完成任务", u, "查看详情")
	if err != nil {
		return err
	}
	email := s.getUserEmailWithFallback(ctx, username)
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeEmail, email, larkim.MsgTypeInteractive, contents)
	return err
}

// SendQrcodeLoginNotification 发送二维码登录通知
func (s *Service) SendQrcodeLoginNotification(ctx context.Context, sessionID, username, title string) (err error) {
	u := fmt.Sprintf("%s/chat/%s", s.config.BaseConfig.Domain, sessionID)
	contents, err := genStandardCard(fmt.Sprintf("【**%v**】：请扫描二维码以获取完整的任务详情和支持。️", strings.TrimSpace(title)), "🔔 你交给 Aime 的任务需要扫描二维码登录获取更多信息", u, "查看详情")
	if err != nil {
		return err
	}
	email := s.getUserEmailWithFallback(ctx, username)
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeEmail, email, larkim.MsgTypeInteractive, contents)
	return err
}

func (s *Service) SendLarkReplayLinkMessage(ctx context.Context, username, replayLink, taskName, toType string) (err error) {
	email := s.getUserEmailWithFallback(ctx, username)
	userIDMap, err := s.larkClient.MGetUserIDByEmail(ctx, []string{email}, lark.UserIDTypeUserID, true)
	if err != nil {
		return err
	}
	userID, ok := userIDMap[email]
	if !ok {
		return errors.New("user not found")
	}
	return s.InnerSendLarkReplayLinkMessage(ctx, userID, email, taskName, replayLink, toType)
}

func (s *Service) InnerSendLarkReplayLinkMessage(ctx context.Context, userID string, email string, taskName string, replayLink string, toType string) (err error) {
	if toType == LarkMessageToTypeGroup {
		contentMap := map[string]string{
			"text": fmt.Sprintf(LarkSendShareLinkToGroupTextContent, userID, taskName, replayLink),
		}
		contentByte, err := json.Marshal(contentMap)
		if err != nil {
			return err
		}
		_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeChatID, s.tccConf.NextAgentSendLarkMessageConfig.GetValue().GroupID, larkim.MsgTypeText, string(contentByte))
		return err
	} else if toType == LarkMessageToTypeUser {
		contentMap := map[string]string{
			"text": fmt.Sprintf("你的任务「%s」已完成，请查收~\n %s", taskName, replayLink),
		}
		contentByte, err := json.Marshal(contentMap)
		if err != nil {
			return err
		}
		_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeEmail, email, larkim.MsgTypeText, string(contentByte))
		return err
	} else {
		return errors.New("to type not found")
	}
}

func (s *Service) SendNoAutoSendNotificationCard(ctx context.Context, email string, taskName string, replayLink string, templateTitle, templateContent string) (err error) {
	contents, err := getNoAutoSendNotificationCard(templateTitle, strings.ReplaceAll(templateContent, entity.NotificationTemplateTaskName, fmt.Sprintf("[%s](%s)", taskName, replayLink)))
	if err != nil {
		return err
	}
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeEmail, email, larkim.MsgTypeInteractive, contents)
	return err
}

func (s *Service) SendDeployNotification(ctx context.Context, agentName, username, version, desc, link, configType string) (err error) {
	email := s.getUserEmailWithFallback(ctx, username)
	userIDMap, err := s.larkClient.MGetUserIDByEmail(ctx, []string{email}, lark.UserIDTypeUserID, true)
	if err != nil {
		return err
	}
	userID, ok := userIDMap[email]
	if !ok {
		return errors.New("user not found")
	}
	contentMap := map[string]string{
		"text": fmt.Sprintf(LarkSendDeployToGroupTextContent, userID, env.GetCurrentVRegion(), agentName, configType, version, s.config.BaseConfig.Domain+link, desc),
	}
	contentByte, err := json.Marshal(contentMap)
	if err != nil {
		return err
	}
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeChatID, s.tccConf.NextAgentSendLarkMessageConfig.GetValue().DeployNotificationGroupID, larkim.MsgTypeText, string(contentByte))
	return err
}

func (s *Service) SendSessionStayTooLongNotification(ctx context.Context, sessionID string, stayMin int, notifySupplyInfo string) (err error) {
	sessionLink := s.config.BaseConfig.Domain + "/chat/" + sessionID

	notifyText := fmt.Sprintf(LarkSendSessionStayTooLongToGroupTextContent, sessionID, sessionLink, stayMin)
	if notifySupplyInfo != "" {
		notifyText += notifySupplyInfo
	}

	contentMap := map[string]string{
		"text": notifyText,
	}
	contentByte, err := json.Marshal(contentMap)
	if err != nil {
		return err
	}
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeChatID, s.tccConf.NextAgentSendLarkMessageConfig.GetValue().SessionMonitorGroupID, larkim.MsgTypeText, string(contentByte))
	return err
}

func (s *Service) SendSessionWaitFirstMessageTooLongNotification(ctx context.Context, sessionID string, stayMin int, notifySupplyInfo string) (err error) {
	sessionLink := s.config.BaseConfig.Domain + "/chat/" + sessionID

	notifyText := fmt.Sprintf(LarkSendSessionFirstMessageWaitTooLongContent, sessionID, sessionLink, stayMin)
	if notifySupplyInfo != "" {
		notifyText += notifySupplyInfo
	}

	contentMap := map[string]string{
		"text": notifyText,
	}
	contentByte, err := json.Marshal(contentMap)
	if err != nil {
		return err
	}
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeChatID, s.tccConf.NextAgentSendLarkMessageConfig.GetValue().SessionMonitorGroupID, larkim.MsgTypeText, string(contentByte))
	return err
}

func (s *Service) SendSessionErrorNotification(ctx context.Context, sessionID string, notifySupplyInfo string) (err error) {
	sessionLink := s.config.BaseConfig.Domain + "/chat/" + sessionID

	notifyText := fmt.Sprintf(LarkSendSessionErrorToGroupTextContent, sessionID, sessionLink)
	if notifySupplyInfo != "" {
		notifyText += notifySupplyInfo
	}

	contentMap := map[string]string{
		"text": notifyText,
	}
	contentByte, err := json.Marshal(contentMap)
	if err != nil {
		return err
	}
	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeChatID, s.tccConf.NextAgentSendLarkMessageConfig.GetValue().SessionMonitorGroupID, larkim.MsgTypeText, string(contentByte))
	return err
}

type ScenarioDetectionResult struct {
	Analysis string `json:"analysis"`
	Scenario string `json:"scenario"`
	Risk     string `json:"risk"`
	Decision string `json:"decision"`
}

func (s *Service) SendScenarioDetectionSummaryNotification(ctx context.Context, sessionID string, chatID string, scenarios *ScenarioDetectionResult, query string) (err error) {
	if scenarios == nil {
		return nil
	}

	content := fmt.Sprintf(
		"**场景**: %s\n**分析**: %s\n**风险**: %s\n**决策**: %s\n\n",
		scenarios.Scenario, scenarios.Analysis, scenarios.Risk, scenarios.Decision)

	scenarioCardTemplateID := s.tccConf.NextAgentScenarioDetectionConfig.GetValue().ScenarioCardTemplateID
	u := fmt.Sprintf("%s/lab/trace/session?session_id=%s", s.config.BaseConfig.Domain, sessionID)
	card := &callback.Card{
		Type: "template",
		Data: &callback.TemplateCard{
			TemplateID: scenarioCardTemplateID,
			TemplateVariable: map[string]any{
				"session_id":  sessionID,
				"query":       query,
				"create_time": time.Now().Format("2006-01-02 15:04:05"),
				"scenarios":   content,
				"session_url": map[string]any{
					"url": u,
				},
			},
		},
	}
	contentByte, err := json.Marshal(card)
	if err != nil {
		return err
	}

	_, err = s.larkClient.SendLarkApplicationMessage(ctx, lark.ReceiveIDTypeChatID, chatID, larkim.MsgTypeInteractive, string(contentByte))
	if err != nil {
		return errors.WithMessage(err, "failed to send scenario detection summary notification")
	}

	return nil
}

func (s *Service) SendOncallCardNotification(ctx context.Context, replyID string, oncallUrl string) (err error) {
	oncallCardID := s.tccConf.NextOncallConfig.GetValue().OncallCardID
	card := &callback.Card{
		Type: "template",
		Data: &callback.TemplateCard{
			TemplateID: oncallCardID,
			TemplateVariable: map[string]any{
				"oncall_applink": map[string]any{
					"url": oncallUrl,
				},
			},
		},
	}
	contentByte, err := json.Marshal(card)
	if err != nil {
		return err
	}

	_, err = s.larkClient.SendLarkApplicationReplyMessage(ctx, replyID, larkim.MsgTypeInteractive, string(contentByte))
	return err
}

// GetUserLarkURL 将文档移动到用户的个人空间，获取用户的飞书链接（函数名叫Get，为了对应前端功能，实际上是转移文档Owner）
func (s *Service) GetUserLarkURL(ctx context.Context, link string, username string, email string) (string, error) {
	// 从url中提取出DocmentID，如https://bytedance.larkoffice.com/wiki/UhhVw4mLGiMbcSkecQVc7u4vnbb，提取出UhhVw4mLGiMbcSkecQVc7u4vnbb
	parsedURL, err := url.Parse(link)
	if err != nil {
		return "", errors.New("invalid lark_url format, link: " + link)
	}

	parts := strings.Split(parsedURL.Path, "/")
	if len(parts) < 2 {
		return "", errors.New("invalid lark_url format")
	}

	docID := parts[len(parts)-1]
	if docID == "" {
		return "", errors.New("invalid lark_url format: empty documentID")
	}

	docType := parts[len(parts)-2]
	docType, ok := GetLarkDocType(docType)
	if !ok {
		return "", errors.New("invalid lark_url format: invalid document type")
	}

	// 根据机器人的OpenID，判断机器人是否有权限转移该文档的权限
	reqDoc := &lark.RequestDoc{
		DocType:  docType,
		DocToken: docID,
	}
	meta, err := s.larkClient.GetFilesMeta(ctx, []*lark.RequestDoc{reqDoc}, "")
	if err != nil || meta == nil || len(meta.Metas) == 0 {
		return "", errors.Errorf("failed to get doc meta: %v", err)
	}

	docMeta := meta.Metas[0]
	conf := s.tccConf.NeumaLarkAppConfig.GetValue()
	if *docMeta.OwnerId != conf.OpenID {
		return "", errors.New("failed to transfer owner: bot is not owner")
	}

	// 获取openID
	userEMail := email
	openIds, err := s.larkClient.MGetUserIDByEmail(ctx, []string{userEMail}, lark.UserIDTypeOpenID, true)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get user openId: %v, username: %s", err, username)
		return "", err
	}
	openID, ok := openIds[userEMail]
	if !ok {
		// log.V1.CtxWarn(ctx, "failed to get user openId: not exist, username: %s", err, username)
		return "", errors.New("user open_id not found")
	}
	// 拉取协作者列表，判断是否是协作者
	memberReq := larkdrive.NewListPermissionMemberReqBuilder().
		Token(docID).
		Type(docType).
		Fields(`*`).
		PermType(`container`).
		Build()
	larkResp, err := s.larkClient.ListPermissionMember(ctx, memberReq, "")
	if err != nil || larkResp == nil {
		log.V1.CtxError(ctx, "failed to list permission member: %v", err)
		return "", err
	}

	// 根据openID判断是否在协作者里
	isMember := lo.ContainsBy(larkResp.Items, func(member *larkdrive.Member) bool {
		return *member.MemberId == openID
	})

	if !isMember {
		return "", errors.New("user is not a member of the document")
	}

	locker, err := s.lock.Acquire(ctx, LockKeyTransferOwner)
	if err != nil {
		return "", err
	}
	defer locker.Release(ctx)
	transReq := larkdrive.NewTransferOwnerPermissionMemberReqBuilder().
		Token(docID).
		Type(docType).
		NeedNotification(false).
		RemoveOldOwner(false).
		Owner(larkdrive.NewOwnerBuilder().
			MemberType("email").
			MemberId(userEMail).
			Build()).
		Build()
	resp, err := s.larkClient.TransferOwnerPermissionMember(ctx, transReq, "")

	if err != nil {
		log.V1.CtxError(ctx, "failed to transfer owner permission: %v", err)
		return "", err
	}

	log.V1.CtxInfo(ctx, "[GetUserLarkURL] transfer_owner: link: %s, email: %s, request_id: %s", link, userEMail, resp.RequestId())

	return link, nil
}

func GetLarkDocType(docType string) (string, bool) {
	var validDocTypes = map[string]string{
		"docx":   "docx",
		"docs":   "doc",
		"sheets": "sheet",
		"wiki":   "wiki",
		"file":   "file",
		"base":   "bitable",
	}

	newDocType, ok := validDocTypes[docType]
	return newDocType, ok
}

// GetLarkDocxBlocks 获取aime产物中指定飞书文档的全部块
func (s *Service) GetLarkDocxBlocks(ctx context.Context, documentId string) ([]*larkdocx.Block, error) {
	blocks, err := s.larkClient.GetLarkDocxBlocks(ctx, documentId, "")
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lark docx blocks: %v", err)
		return nil, err
	}
	return blocks, nil
}

// AddRowToLarkSheet 向飞书文档中添加一行
// @param folderToken 飞书文档所在的文件夹token
// @param messageID 飞书消息ID
// @param field 行数据
// @param imageKeys 飞书消息中的图片key列表
func (s *Service) AddFeedbackToLarkSheet(ctx context.Context, folderToken string, messageID string, field []interface{}, imageKeys []string) error {
	// todaySheet := time.Now().Format("2006-01-02")
	thisMonthSheet := time.Now().Format("2006-01")

	type cacheStruct struct {
		Token string `json:"token"`
		Range string `json:"range"`
	}
	// 对单个sheet的追加数据只能串行调用，参考使用限制https://open.larkoffice.com/document/server-docs/docs/sheets-v3/overview
	locker, err := s.lock.Acquire(ctx, thisMonthSheet)
	if err != nil {
		return err // TODO: backoff
	}
	defer func() {
		locker.Release(ctx)
	}()

	// 优先查缓存
	if value, err := s.cache.Get(ctx, thisMonthSheet); err == nil {
		v := cacheStruct{}
		if err := json.Unmarshal([]byte(value), &v); err != nil {
			log.V1.CtxWarn(ctx, "failed to unmarshal cache: %v", err)
		} else {
			return s.addFeedbackToLarkSheet(ctx, v.Token, v.Range, messageID, field, imageKeys)
		}
	}

	// 未中缓存，查找sheet
	resp, err := s.larkClient.ListFiles(ctx, folderToken, "", 10, "CreatedTime", "DESC")
	if err != nil {
		return err
	}

	spreadSheet, exist := lo.Find(resp.Files, func(item *larkdrive.File) bool {
		return lo.FromPtr(item.Name) == thisMonthSheet
	})

	var sheetToken, sheetID string
	if !exist {
		// create sheet
		resp, err := s.larkClient.CreateSheet(ctx, thisMonthSheet, folderToken)
		if err != nil {
			return err
		}
		sheetToken = lo.FromPtr(resp.Spreadsheet.SpreadsheetToken)
	} else {
		sheetToken = lo.FromPtr(spreadSheet.Token)
	}

	sheetData, err := s.larkClient.QuerySheet(ctx, sheetToken, "")
	if err != nil {
		return err
	}
	sheetID = lo.FromPtr(sheetData.Sheets[0].SheetId)

	value, _ := json.Marshal(cacheStruct{
		Token: sheetToken,
		Range: sheetID,
	})
	// 缓存token和range
	s.cache.Set(ctx, thisMonthSheet, value, 7*24*time.Hour)

	err = s.addFeedbackToLarkSheet(ctx, sheetToken, sheetID, messageID, field, imageKeys)
	return err
}

func (s *Service) addFeedbackToLarkSheet(ctx context.Context, sheetToken string, sheetID string, messageID string, field []interface{}, imageKeys []string) error {
	rows := [][]interface{}{field}
	sheetRow, err := s.larkClient.AddRowForLarkSheet(ctx, sheetToken, sheetID, rows)
	if err != nil {
		return err
	}

	rowNum, err := s.getLastRowFromSheetRange(sheetRow.Data.TableRange)
	if err != nil {
		return err
	}
	// 依次获取图片key对应的二进制数据，插入到该行后续的cell中
	for i, imageKey := range imageKeys {
		colNum := 'A' + i + len(field) //最多支持到Z列
		if colNum > 'Z' {
			break
		}

		resp, err := s.larkClient.GetMessageResource(ctx, messageID, imageKey, "image")
		if err != nil || resp.CodeError.Code != 0 {
			log.V1.CtxError(ctx, "failed to get message resource: %v, messageID %s, imageKey %s", err, messageID, imageKey)
			continue
		}

		imageByte, err := io.ReadAll(resp.File)
		if err != nil {
			log.V1.CtxError(ctx, "failed to read message resource: %v, messageID %s, imageKey %s", err, messageID, imageKey)
			continue
		}

		cellRange := fmt.Sprintf("%s!%c%d:%c%d", sheetID, colNum, rowNum, colNum, rowNum)
		_, err = s.larkClient.AddImageCellForLarkSheet(ctx, sheetToken, cellRange, imageByte, imageKey+".png")
		if err != nil {
			log.V1.CtxError(ctx, "failed to add image cell for lark sheet: %v, messageID %s, imageKey %s", err, messageID, imageKey)
			continue
		}
	}

	return nil
}

// getLastRowFromSheetRange 根据sheetRange获取最后一行，如6e5ed3!A1000:C1001，返回1001
func (s *Service) getLastRowFromSheetRange(sheetRange string) (int, error) {
	splitRange := strings.Split(sheetRange, "!")
	if len(splitRange) != 2 {
		return 0, fmt.Errorf("invalid sheet range")
	}
	rowRange := splitRange[1]
	rangeParts := strings.Split(rowRange, ":")
	if len(rangeParts) != 2 {
		return 0, fmt.Errorf("invalid sheet range")
	}

	digitIndex := -1
	endCell := rangeParts[1]
	for i, char := range endCell {
		if unicode.IsDigit(char) {
			digitIndex = i
			break
		}
	}
	if digitIndex == -1 {
		return 0, fmt.Errorf("invalid sheet range: no row number")
	}
	row, err := strconv.Atoi(endCell[digitIndex:])
	if err != nil {
		return 0, fmt.Errorf("invalid sheet range: %v", err)
	}
	return row, nil
}
