package lark

const (
	LarkSendShareLinkToGroupTextContent = `<at user_id="%s"></at> 你的任务「%s」已完成，请查收~
%s`
	LarkSendDeployToGroupTextContent = `<at user_id="%s"></at> 正在发布 %s 环境「%s」Agent %s [V%s](%s) 版本
版本描述: %s`
	LarkSendSessionStayTooLongToGroupTextContent  = `任务 [%s](%s) 已停留超过 %d 分钟未响应，请及时关注~`
	LarkSendSessionErrorToGroupTextContent        = `任务 [%s](%s) 运行出错，请及时关注~`
	LarkSendSessionFirstMessageWaitTooLongContent = `任务 [%s](%s) 超过 %d 分钟未回复第一条消息，请及时关注~`
)

const (
	LarkMessageToTypeGroup = "group"
	LarkMessageToTypeUser  = "user"
)
