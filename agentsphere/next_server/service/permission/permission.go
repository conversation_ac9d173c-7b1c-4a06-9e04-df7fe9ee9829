package permission

import (
	"context"
	"fmt"
	"sync"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/panics"
	"go.uber.org/fx"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/uuid"

	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
)

var (
	ErrResourceNotFound                    = errors.New("resource not found")
	ErrPermissionNotFound                  = errors.New("permission not found")
	ErrInvalidParam                        = errors.New("invalid param")
	ErrNotAllowedRemoveOwner               = errors.New("not allowed remove owner")
	ErrNotAllowedAddSpacePermissionInSpace = errors.New("not allowed add space permission")
)

type Service struct {
	dao         *dal.DAO
	idGen       uuid.Generator
	tccConf     *config.AgentSphereTCCConfig
	userService *userservice.Service
}

type CreateServiceOption struct {
	fx.In
	DAO         *dal.DAO
	TccConf     *config.AgentSphereTCCConfig
	UserService *userservice.Service
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		idGen:       uuid.GetDefaultGenerator(nil),
		dao:         opt.DAO,
		tccConf:     opt.TccConf,
		userService: opt.UserService,
	}
	return s, nil
}

type CheckPermissionOption struct {
	Account            authentity.Account
	ResourceID         *string // ID 不存在时，使用 ExternalID + Type 的组合查询资源
	ResourceExternalID *string
	ResourceType       *entity.ResourceType
	Action             entity.PermissionAction
	GroupID            *string
}

type CheckPermissionResult struct {
	Allowed  bool
	Resource *entity.Resource
}

func (s *Service) CheckPermission(ctx context.Context, opt CheckPermissionOption) (*CheckPermissionResult, error) {
	if opt.Action == entity.PermissionActionUnknown || opt.Action == "" {
		return nil, ErrInvalidParam
	}

	// 1. 特殊逻辑，创建空间的权限检查
	if opt.Action == entity.PermissionActionSpaceCreate {
		if s.userService.IsDeveloper(&opt.Account) {
			return &CheckPermissionResult{Allowed: true}, nil
		}
		return &CheckPermissionResult{Allowed: false}, nil
	}

	// 2. 除空间外的资产创建权限，需要 GroupID 不为空
	if lo.SomeBy([]entity.PermissionAction{entity.PermissionActionSessionCreate, entity.PermissionActionMCPCreate,
		entity.PermissionActionKnowledgebaseCreate, entity.PermissionActionTemplateCreate}, func(item entity.PermissionAction) bool {
		return opt.Action == item
	}) {
		if opt.GroupID == nil {
			return nil, ErrInvalidParam
		}
		// 获取该用户对该空间的权限
		res, err := s.GetUserResourcePermission(ctx, GetUserResourcePermissionOption{
			Account:            opt.Account,
			ResourceExternalID: opt.GroupID,
			ResourceType:       lo.ToPtr(entity.ResourceTypeSpace),
			PermissionActions:  []entity.PermissionAction{opt.Action},
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get user resource permission")
		}
		if res != nil && len(res.Permissions) != 0 {
			return &CheckPermissionResult{Allowed: true}, nil
		}
		return &CheckPermissionResult{
			Allowed: false,
		}, nil
	}

	// 3. 正常权限判断
	res, err := s.GetUserResourcePermission(ctx, GetUserResourcePermissionOption{
		Account:            opt.Account,
		ResourceExternalID: opt.ResourceExternalID,
		ResourceType:       opt.ResourceType,
		ResourceID:         opt.ResourceID,
		PermissionActions:  []entity.PermissionAction{opt.Action},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get user resource permission")
	}
	if res != nil && len(res.Permissions) != 0 {
		return &CheckPermissionResult{Allowed: true, Resource: res}, nil
	}

	return &CheckPermissionResult{
		Allowed: false,
	}, nil
}

type CreateResourceOption struct {
	Owner         string
	Type          entity.ResourceType
	ExternalID    string
	Status        *entity.ResourceStatus
	GroupID       *string
	IsSpacePublic *bool
}

func (s *Service) CreateResource(ctx context.Context, opt CreateResourceOption) (resource *entity.Resource, err error) {
	// group id 为空，但是要求 space 内公开，则返回错误
	if opt.GroupID == nil && pointer.Get(opt.IsSpacePublic) {
		return nil, ErrInvalidParam
	}

	// type 为 group, 不允许嵌套 group
	if opt.Type == entity.ResourceTypeSpace && opt.GroupID != nil {
		return nil, ErrInvalidParam
	}

	status := opt.Type.DefaultStatus()
	if opt.Status != nil {
		status = *opt.Status
	}

	resource, err = s.dao.CreateResource(ctx, dal.CreateResourceOption{
		ID:         s.idGen.NewID(),
		Type:       opt.Type,
		ExternalID: opt.ExternalID,
		Owner:      opt.Owner,
		Status:     status,
		GroupID:    opt.GroupID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create resource")
	}

	// 创建失败的话，清理 resource 数据
	defer func() {
		if err != nil {
			nerr := s.dao.DeleteResource(ctx, dal.DeleteResourceOption{
				ResourceID: resource.ID,
			})
			if nerr != nil {
				log.V1.CtxError(ctx, "failed to delete dirty resource id: %s, err: %v", resource.ID, nerr)
			}
			resource = nil
		}
	}()

	// 授予 Owner 权限
	ownerPermission, err := s.dao.CreatePermission(ctx, dal.CreatePermissionOption{
		ID:           s.idGen.NewID(),
		ResourceID:   resource.ID,
		ResourceType: resource.Type,
		Type:         entity.PermissionTypeUser,
		ExternalID:   opt.Owner,
		Role:         entity.PermissionRoleAdmin,
	})
	if err != nil {
		return resource, errors.WithMessage(err, "failed to add admin permission to resource")
	}
	resource.Permissions = []*entity.Permission{ownerPermission}

	if opt.GroupID != nil && pointer.Get(opt.IsSpacePublic) {
		// 授予空间权限
		// 1. 查询空间资源
		spaceResource, err := s.dao.GetResource(ctx, dal.GetResourceOption{
			ExternalID: opt.GroupID,
			Type:       lo.ToPtr(entity.ResourceTypeSpace),
			Sync:       true,
		})
		if err != nil {
			return resource, errors.WithMessage(err, "failed to get space resource")
		}

		// 2. 授予新建 Resource 该空间组的只读访客权限
		permission, err := s.dao.CreatePermission(ctx, dal.CreatePermissionOption{
			ID:           s.idGen.NewID(),
			ResourceID:   resource.ID,
			ResourceType: resource.Type,
			Type:         entity.PermissionTypeSpace,
			ExternalID:   spaceResource.ExternalID,
			Role:         entity.PermissionRoleVisitor,
		})
		if err != nil {
			return resource, errors.WithMessage(err, "failed to add space permission to resource")
		}
		resource.Permissions = append(resource.Permissions, permission)
	}

	return resource, nil
}

type DeleteResourceOption struct {
	ResourceID         *string // ID 不存在时，使用 ExternalID + Type 的组合查询资源
	ResourceExternalID *string
	ResourceType       *entity.ResourceType
}

func (s *Service) DeleteResource(ctx context.Context, opt DeleteResourceOption) error {
	resource, err := s.resolveResource(ctx, resolveResourceOption{
		ResourceID:         opt.ResourceID,
		ResourceExternalID: opt.ResourceExternalID,
		ResourceType:       opt.ResourceType,
	})
	if err != nil {
		return err
	}
	err = s.dao.DeleteResource(ctx, dal.DeleteResourceOption{
		ResourceID: resource.ID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to delete resource")
	}
	return nil
}

type AddResourcePermissionOption struct {
	ResourceID         *string // ID 不存在时，使用 ExternalID + Type 的组合查询资源
	ResourceExternalID *string
	ResourceType       *entity.ResourceType
	PermissionMetas    []entity.PermissionMeta
}

func (s *Service) AddResourcePermission(ctx context.Context, opt AddResourcePermissionOption) (*entity.Resource, error) {
	resource, err := s.resolveResource(ctx, resolveResourceOption{
		ResourceID:         opt.ResourceID,
		ResourceExternalID: opt.ResourceExternalID,
		ResourceType:       opt.ResourceType,
	})
	if err != nil {
		return nil, err
	}

	// 不支持给 space type 加 space 权限
	if resource.Type == entity.ResourceTypeSpace {
		for _, meta := range opt.PermissionMetas {
			if meta.Type == entity.PermissionTypeSpace {
				return nil, ErrNotAllowedAddSpacePermissionInSpace
			}
		}
	}

	permissions, err := s.dao.BatchCreatePermission(ctx, dal.BatchCreatePermissionOption{
		IDs: lo.Map(opt.PermissionMetas, func(item entity.PermissionMeta, index int) string {
			return s.idGen.NewID()
		}),
		ResourceID:      resource.ID,
		ResourceType:    resource.Type,
		PermissionMetas: opt.PermissionMetas,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create permission")
	}
	resource.Permissions = permissions

	return resource, nil
}

type RemoveResourcePermissionOption struct {
	ResourceID         *string // ID 不存在时，使用 ExternalID + Type 的组合查询资源
	ResourceExternalID *string
	ResourceType       *entity.ResourceType
	PermissionMetas    []entity.PermissionMeta
}

func (s *Service) RemoveResourcePermission(ctx context.Context, opt RemoveResourcePermissionOption) (*entity.Resource, error) {
	resource, err := s.resolveResource(ctx, resolveResourceOption{
		ResourceID:         opt.ResourceID,
		ResourceExternalID: opt.ResourceExternalID,
		ResourceType:       opt.ResourceType,
	})
	if err != nil {
		return nil, err
	}

	for _, meta := range opt.PermissionMetas {
		// 如果是 owner，不允许 owner 移除自己的权限，通过权限转移方式移交权限
		if meta.Type == entity.PermissionTypeUser && meta.ExternalID == resource.Owner && meta.Role == entity.PermissionRoleAdmin {
			return nil, ErrNotAllowedRemoveOwner
		}
	}

	permissions, err := s.dao.FindPermissions(ctx, dal.FindPermissionOption{
		ResourceID:      resource.ID,
		PermissionMetas: opt.PermissionMetas,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrPermissionNotFound
		}
		return nil, errors.WithMessage(err, "failed to find permission")
	}

	err = s.dao.BatchDeletePermissions(ctx, dal.BatchDeletePermissionsOption{
		IDs: lo.Map(permissions, func(item *entity.Permission, index int) string {
			return item.ID
		}),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to batch delete permission")
	}

	return resource, nil
}

type GetResourceOption struct {
	ResourceID         *string // ID 不存在时，使用 ExternalID + Type 的组合查询资源
	ResourceExternalID *string
	ResourceType       *entity.ResourceType
	NeedPermission     bool
	NeedGroup          bool
	Sync               bool
}

func (s *Service) GetResource(ctx context.Context, opt GetResourceOption) (*entity.Resource, error) {
	resource, err := s.resolveResource(ctx, resolveResourceOption{
		ResourceID:         opt.ResourceID,
		ResourceExternalID: opt.ResourceExternalID,
		ResourceType:       opt.ResourceType,
	})
	if err != nil {
		return nil, err
	}

	if opt.NeedPermission {
		permissions, err := s.dao.ListPermission(ctx, dal.ListPermissionOption{
			ResourceIDs: []string{resource.ID},
			Sync:        opt.Sync,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list permission")
		}
		uniqPermissions := s.uniqResourcePermissionsByRole(permissions)
		resource.Permissions = uniqPermissions
	}

	if opt.NeedGroup {
		groupResourceRelation, err := s.dao.ListGroupResourceRelation(ctx, dal.ListGroupResourceRelationOption{
			ResourceID: lo.ToPtr(resource.ID),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list group resource relation")
		}
		resource.GroupRelation = groupResourceRelation
	}

	return resource, nil
}

type UpdateResourceOptions struct {
	ResourceID         *string // ID 不存在时，使用 ExternalID + Type 的组合查询资源
	ResourceExternalID *string
	ResourceType       *entity.ResourceType
	Owner              *string                // 转移 Owner
	Status             *entity.ResourceStatus // 修改状态
	IsSpacePublic      *bool                  // 项目内是否公开
}

func (s *Service) UpdateResource(ctx context.Context, opt UpdateResourceOptions) (*entity.Resource, error) {
	resource, err := s.resolveResource(ctx, resolveResourceOption{
		ResourceID:         opt.ResourceID,
		ResourceExternalID: opt.ResourceExternalID,
		ResourceType:       opt.ResourceType,
	})
	if err != nil {
		return nil, err
	}

	resourcePermissions, err := s.GetResource(ctx, GetResourceOption{ResourceID: lo.ToPtr(resource.ID), NeedPermission: true, NeedGroup: true})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list permission")
	}

	// 转移 Owner
	if opt.Owner != nil && *opt.Owner != resource.Owner {
		// 添加新的管理员权限
		per, err := s.dao.CreatePermission(ctx, dal.CreatePermissionOption{
			ID:           s.idGen.NewID(),
			ResourceID:   resource.ID,
			ResourceType: resource.Type,
			Type:         entity.PermissionTypeUser,
			ExternalID:   *opt.Owner,
			Role:         entity.PermissionRoleAdmin,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create permission")
		}

		oldPermission, ok := lo.Find(resourcePermissions.Permissions, func(item *entity.Permission) bool {
			return item.Role == entity.PermissionRoleAdmin && item.Type == entity.PermissionTypeUser && item.ExternalID == resource.Owner
		})
		// 删除老的管理员权限
		if ok {
			err = s.dao.DeletePermission(ctx, dal.DeletePermissionOption{
				ID: oldPermission.ID,
			})
		}
		resourcePermissions.Permissions = lo.Filter(resourcePermissions.Permissions, func(item *entity.Permission, index int) bool {
			return oldPermission.ID != item.ID
		})
		resourcePermissions.Permissions = append(resourcePermissions.Permissions, per)
	}

	// 项目内公开
	if opt.IsSpacePublic != nil {
		// 获取关联的 Group
		relations, err := s.dao.ListGroupResourceRelation(ctx, dal.ListGroupResourceRelationOption{
			ResourceID: lo.ToPtr(resource.ID),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list group resource relation")
		}

		if *opt.IsSpacePublic {
			// 需要更改为 true，新增授权
			pers, err := s.dao.BatchCreatePermission(ctx, dal.BatchCreatePermissionOption{
				IDs: lo.Map(relations, func(item *entity.GroupResourceRelation, index int) string {
					return s.idGen.NewID()
				}),
				ResourceID:   resource.ID,
				ResourceType: resource.Type,
				PermissionMetas: lo.Map(relations, func(item *entity.GroupResourceRelation, index int) entity.PermissionMeta {
					return entity.PermissionMeta{
						Type:       entity.PermissionTypeSpace,
						ExternalID: item.GroupID,
						Role:       entity.PermissionRoleVisitor,
					}
				}),
			})
			if err != nil && !errors.Is(err, gorm.ErrDuplicatedKey) {
				return nil, errors.WithMessage(err, "failed to batch create permission")
			}
			if len(pers) > 0 {
				resourcePermissions.Permissions = append(resourcePermissions.Permissions, pers...)
			}
		} else {
			// 更改为 false, 移除授权
			oldPermissions := lo.Filter(resourcePermissions.Permissions, func(item *entity.Permission, index int) bool {
				return item.Type == entity.PermissionTypeSpace && lo.ContainsBy(relations, func(relation *entity.GroupResourceRelation) bool {
					return relation.GroupID == item.ExternalID
				})
			})
			if len(oldPermissions) > 0 {
				err = s.dao.BatchDeletePermissions(ctx, dal.BatchDeletePermissionsOption{
					IDs: lo.Map(oldPermissions, func(item *entity.Permission, index int) string {
						return item.ID
					}),
				})
				if err != nil {
					return nil, errors.WithMessage(err, "failed to batch delete permission")
				}
			}
			resourcePermissions.Permissions = lo.Filter(resourcePermissions.Permissions, func(item *entity.Permission, index int) bool {
				return lo.ContainsBy(oldPermissions, func(oldPer *entity.Permission) bool {
					return item.ID != oldPer.ID
				})
			})
		}
	}

	resource, err = s.dao.UpdateResource(ctx, dal.UpdateResourceOption{
		ID:     resource.ID,
		Owner:  opt.Owner,
		Status: opt.Status,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update resource")
	}
	resource.Permissions = resourcePermissions.Permissions

	return resource, nil
}

type GetUserResourcePermissionOption struct {
	Account            authentity.Account
	ResourceID         *string // ID 不存在时，使用 ExternalID + Type 的组合查询资源
	ResourceExternalID *string
	ResourceType       *entity.ResourceType
	// 支持指定角色 or 权限点
	PermissionActions []entity.PermissionAction
	PermissionRoles   []entity.PermissionRole
}

func (s *Service) GetUserResourcePermission(ctx context.Context, opt GetUserResourcePermissionOption) (*entity.Resource, error) {
	// 1. 检查用户是否有特殊权限
	roles := s.getUserSpecialRole(opt.Account)

	resource, err := s.resolveResource(ctx, resolveResourceOption{
		ResourceID:         opt.ResourceID,
		ResourceExternalID: opt.ResourceExternalID,
		ResourceType:       opt.ResourceType,
	})
	if err != nil {
		return nil, err
	}

	actionRoles := s.translateActionToRoles(opt.PermissionActions)
	actionRoles = append(actionRoles, opt.PermissionRoles...)
	permissions, err := s.getUserPermissions(ctx, getUserPermissionOption{
		Account:      opt.Account,
		ResourceIDs:  []string{resource.ID},
		ResourceType: lo.ToPtr(resource.Type),
		Roles:        actionRoles,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get user permission")
	}

	if len(roles) > 0 {
		actionRolesMap := lo.SliceToMap(actionRoles, func(item entity.PermissionRole) (entity.PermissionRole, bool) {
			return item, true
		})
		for _, role := range roles {
			// 如果指定了角色，并且内置角色不在这个范围内，则忽略
			if len(actionRolesMap) > 0 && !actionRolesMap[role] {
				continue
			}
			permissions = append(permissions, &entity.Permission{
				ResourceID:   resource.ID,
				ResourceType: resource.Type,
				Type:         entity.PermissionTypeUser,
				ExternalID:   opt.Account.Username,
				Role:         role,
			})
		}
	}
	if !opt.Account.IsServiceAccount() && resource.Status == entity.ResourceStatusPublic { // public不对服务账号生效
		permissions = append(permissions, &entity.Permission{
			ResourceID:   resource.ID,
			ResourceType: resource.Type,
			Type:         entity.PermissionTypeUser,
			ExternalID:   opt.Account.Username,
			Role:         entity.PermissionRoleVisitor,
		})
	}

	uniqPermissions := s.uniqResourcePermissionsByRole(permissions)
	// 5. Merge 权限 & 权限点信息
	for _, per := range uniqPermissions {
		actions := s.PackPermissionAction(ctx, PackPermissionActionOption{
			PermissionRole: per.Role,
			ResourceType:   resource.Type,
		})
		per.PermissionActions = actions
	}

	groupResourceRelation, err := s.dao.ListGroupResourceRelation(ctx, dal.ListGroupResourceRelationOption{
		ResourceID: lo.ToPtr(resource.ID),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list group resource relation")
	}

	resource.Permissions = uniqPermissions
	resource.GroupRelation = groupResourceRelation
	return resource, nil
}

type GetUserPermissionsOption struct {
	Account           authentity.Account
	Roles             []entity.PermissionRole // 不填，查所有角色，填了查具体某个角色
	Actions           []entity.PermissionAction
	ResourceType      entity.ResourceType // 必须指定 Type, 减少查询压力
	GroupID           *string
	NeedGroupRelation bool
}

// GetUserPermissions 获取用户权限列表场景，忽略特殊的权限逻辑，只返回用户真实有权限的资源
func (s *Service) GetUserPermissions(ctx context.Context, opt GetUserPermissionsOption) ([]*entity.Resource, error) {
	// 先缩小范围
	_, groupResources, err := s.dao.ListResource(ctx, dal.ListResourceOption{
		Type:    lo.ToPtr(opt.ResourceType),
		GroupID: opt.GroupID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list group resources")
	}
	groupResourcesMap := make(map[string]*entity.Resource)
	groupResourcesID := make([]string, 0, len(groupResources))
	for _, res := range groupResources {
		groupResourcesMap[res.ID] = res
		groupResourcesID = append(groupResourcesID, res.ID)
	}

	// 1. 获取权限信息
	actionRoles := s.translateActionToRoles(opt.Actions)
	actionRoles = append(actionRoles, opt.Roles...)
	permissions, err := s.getUserPermissions(ctx, getUserPermissionOption{
		Account:      opt.Account,
		ResourceIDs:  groupResourcesID,
		ResourceType: lo.ToPtr(opt.ResourceType),
		Roles:        actionRoles,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get user permission")
	}

	var publicResources []*entity.Resource
	// 2. 获取 Public 的资源信息
	if len(opt.Roles) == 0 || lo.Contains(opt.Roles, entity.PermissionRoleVisitor) {
		_, publicResources, err = s.dao.ListResource(ctx, dal.ListResourceOption{
			Type:   lo.ToPtr(opt.ResourceType),
			Status: lo.ToPtr(entity.ResourceStatusPublic),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list public resources")
		}
	}

	// 3. merge 权限信息
	var result []*entity.Resource
	existResource := make(map[string]*entity.Resource)
	// 公开资源，默认授予一个访客权限
	for _, res := range publicResources {
		if _, ok := existResource[res.ID]; !ok {
			res.Permissions = append(res.Permissions, &entity.Permission{
				ResourceID:   res.ID,
				ResourceType: res.Type,
				Type:         entity.PermissionTypeUser,
				ExternalID:   opt.Account.Username,
				Role:         entity.PermissionRoleVisitor,
				PermissionActions: s.PackPermissionAction(ctx, PackPermissionActionOption{
					PermissionRole: entity.PermissionRoleVisitor,
					ResourceType:   res.Type,
				}),
			})
			result = append(result, res)
			existResource[res.ID] = res
		}
	}

	for _, per := range permissions {
		per.PermissionActions = s.PackPermissionAction(ctx, PackPermissionActionOption{
			PermissionRole: per.Role,
			ResourceType:   per.ResourceType,
		})

		// 理论上必须存在，因为是用 in ids 的方式查出来的
		res, ok := groupResourcesMap[per.ResourceID]
		if ok {
			if existRes, ok := existResource[per.ResourceID]; !ok {
				res.Permissions = append(res.Permissions, per)
				result = append(result, res)
				existResource[per.ResourceID] = res
			} else {
				existRes.Permissions = append(res.Permissions, per)
			}
		} else {
			// continue
			log.V1.CtxWarn(ctx, "failed to find group resource with id %s", per.ResourceID)
		}
	}

	if opt.NeedGroupRelation {
		groupResourceRelationsMap, err := s.getGroupRelationMap(ctx, opt.GroupID, result)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get group resource relations")
		}
		for _, res := range result {
			res.GroupRelation = groupResourceRelationsMap[res.ID]
		}
	}

	return result, nil
}

type ListResourceOption struct {
	ResourceType      entity.ResourceType
	Owner             *string
	Status            *entity.ResourceStatus
	GroupID           *string
	Offset            int
	Limit             int  // <= 0 表示不限制
	NeedGroupRelation bool // List 查询数据较多，默认不返回 Relation 信息
}

func (s *Service) ListResources(ctx context.Context, opt ListResourceOption) (int64, []*entity.Resource, error) {
	// 必须指定类型
	if opt.ResourceType == "" || opt.ResourceType == entity.ResourceTypeUnknown {
		return 0, nil, ErrInvalidParam
	}

	total, resources, err := s.dao.ListResource(ctx, dal.ListResourceOption{
		Owner:   opt.Owner,
		Type:    lo.ToPtr(opt.ResourceType),
		Status:  opt.Status,
		GroupID: opt.GroupID,
		Offset:  opt.Offset,
		Limit:   opt.Limit,
	})
	if err != nil {
		return 0, nil, errors.WithMessage(err, "failed to list resources")
	}

	if opt.NeedGroupRelation {
		groupResourceRelationsMap, err := s.getGroupRelationMap(ctx, opt.GroupID, resources)
		if err != nil {
			return 0, nil, errors.WithMessage(err, "failed to list group resource relations")
		}
		for _, res := range resources {
			res.GroupRelation = groupResourceRelationsMap[res.ID]
		}
	}

	return total, resources, nil
}

type resolveResourceOption struct {
	ResourceID         *string
	ResourceExternalID *string
	ResourceType       *entity.ResourceType
}

func (s *Service) resolveResource(ctx context.Context, opt resolveResourceOption) (*entity.Resource, error) {
	var resource *entity.Resource
	var err error
	if opt.ResourceID != nil {
		resource, err = s.dao.GetResource(ctx, dal.GetResourceOption{
			ID: opt.ResourceID,
		})
	} else if opt.ResourceExternalID != nil && *opt.ResourceType != "" {
		resource, err = s.dao.GetResource(ctx, dal.GetResourceOption{
			ExternalID: opt.ResourceExternalID,
			Type:       opt.ResourceType,
		})
	} else {
		return nil, ErrResourceNotFound
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrResourceNotFound
		}
		return nil, errors.WithMessage(err, "failed to get resource")
	}

	return resource, nil
}

type PackPermissionActionOption struct {
	PermissionRole entity.PermissionRole
	ResourceType   entity.ResourceType
}

func (s *Service) PackPermissionAction(ctx context.Context, opt PackPermissionActionOption) []entity.PermissionAction {
	var actions []entity.PermissionAction
	configActions, ok := s.tccConf.PermissionActionConfig.GetValue().Roles[opt.PermissionRole.String()]
	if !ok {
		log.V1.CtxWarn(ctx, "role %s not exist", opt.PermissionRole.String())
		return actions
	}
	actions = append(actions, lo.Filter(lo.Map(configActions, func(item string, index int) entity.PermissionAction {
		return entity.PermissionAction(item)
	}), func(item entity.PermissionAction, index int) bool {
		return item.ResourceType() == opt.ResourceType
	})...)

	return lo.Uniq(actions)
}

func (s *Service) getUserSpecialRole(account authentity.Account) []entity.PermissionRole {
	var roles []entity.PermissionRole
	isDeveloper := s.userService.IsDeveloper(&account)
	if isDeveloper {
		roles = append(roles, entity.PermissionRoleDeveloper)
	}
	isOperator := s.userService.IsGroupOperator(&account)
	if isOperator {
		roles = append(roles, entity.PermissionRoleOperator)
	}
	isMCPPartner := s.userService.IsMCPPartner(&account)
	if isMCPPartner {
		roles = append(roles, entity.PermissionRoleMCPPartner)
	}

	return roles
}

type getUserPermissionOption struct {
	Account      authentity.Account
	ResourceIDs  []string
	ResourceType *entity.ResourceType
	Roles        []entity.PermissionRole
}

func (s *Service) getUserPermissions(ctx context.Context, opt getUserPermissionOption) ([]*entity.Permission, error) {
	wg := sync.WaitGroup{}
	var permissionsUser, permissionServiceAccount, permissionsDepartment, permissionsSpace []*entity.Permission
	var permissionsUserErr, permissionServiceAccountErr, permissionsDepartmentErr, permissionsSpaceErr error

	// 1. 检查用户真实权限
	// 注意：某些服务账号（评测账号testai）也会以普通用户的类型执行操作，服务账号也需要执行PermissionTypeUser检查
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := panics.Try(func() {
			permissionsUser, permissionsUserErr = s.dao.ListPermission(ctx, dal.ListPermissionOption{
				ResourceIDs:  opt.ResourceIDs,
				ResourceType: opt.ResourceType,
				Type:         lo.ToPtr(entity.PermissionTypeUser),
				ExternalID:   lo.ToPtr(opt.Account.Username),
				Roles:        opt.Roles,
			})
			if permissionsUserErr != nil {
				logs.V1.CtxError(ctx, "failed to list permission user: %v", permissionsUserErr)
				return
			}
		}).AsError(); err != nil {
			logs.V1.CtxError(ctx, "list permission users panic: %v", err)
			permissionsUserErr = err
		}
	}()

	// 1.1 检查服务账户权限
	wg.Add(1)
	go func() {
		defer wg.Done()
		if !opt.Account.IsServiceAccount() {
			return
		}
		if err := panics.Try(func() {
			permissionServiceAccount, permissionServiceAccountErr = s.dao.ListPermission(ctx, dal.ListPermissionOption{
				ResourceIDs:  opt.ResourceIDs,
				ResourceType: opt.ResourceType,
				Type:         lo.ToPtr(entity.PermissionTypeServiceAccount),
				ExternalID:   lo.ToPtr(opt.Account.Username),
			})
			if permissionServiceAccountErr != nil {
				logs.V1.CtxError(ctx, "failed to list permission user: %v", permissionsUserErr)
				return
			}
		}).AsError(); err != nil {
			logs.V1.CtxError(ctx, "list permission service_account panic: %v", err)
			permissionServiceAccountErr = err
		}
	}()

	// 2. 检查用户所在部门权限(服务账号Department为空)
	if opt.Account.Department != "" {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := panics.Try(func() {
				permissionsDepartment, permissionsDepartmentErr = s.dao.ListPermission(ctx, dal.ListPermissionOption{
					ResourceIDs:  opt.ResourceIDs,
					ResourceType: opt.ResourceType,
					Type:         lo.ToPtr(entity.PermissionTypeDepartment),
					ExternalID:   lo.ToPtr(opt.Account.Department),
					Roles:        opt.Roles,
				})
				if permissionsDepartmentErr != nil {
					logs.V1.CtxError(ctx, "failed to list permission department: %v", permissionsDepartmentErr)
					return
				}
			}).AsError(); err != nil {
				logs.V1.CtxError(ctx, "list permission department panic: %v", err)
				permissionsDepartmentErr = err
			}
		}()
	}

	// 3. 检查用户所在空间权限, 空间类型不检查
	if opt.ResourceType != nil && *opt.ResourceType != entity.ResourceTypeSpace {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := panics.Try(func() {
				resources, nerr := s.getUserGroupResources(ctx, getUserGroupResourcesOption{
					Account:      opt.Account,
					ResourceIDs:  opt.ResourceIDs,
					ResourceType: opt.ResourceType,
					Roles:        opt.Roles,
				})
				if nerr != nil {
					permissionsSpaceErr = nerr
					return
				}

				for _, res := range resources {
					permissionsSpace = append(permissionsSpace, res.Permissions...)
				}
			}).AsError(); err != nil {
				logs.V1.CtxError(ctx, "list permission space panic: %v", err)
				permissionsSpaceErr = err
			}
		}()
	}

	wg.Wait()
	if permissionsUserErr != nil || permissionsDepartmentErr != nil || permissionsSpaceErr != nil || permissionServiceAccountErr != nil {
		return nil, errors.New(fmt.Sprintf("failed to list permissions err, user: %v, department: %v, space: %v, service_account: %v", permissionsUserErr, permissionsDepartmentErr, permissionsSpaceErr, permissionServiceAccountErr))
	}
	permissions := permissionsUser
	permissions = append(permissions, permissionServiceAccount...)
	permissions = append(permissions, permissionsDepartment...)
	permissions = append(permissions, permissionsSpace...)

	return s.uniqResourcePermissionsByRole(permissions), nil
}

type getUserGroupResourcesOption struct {
	Account      authentity.Account
	ResourceIDs  []string
	ResourceType *entity.ResourceType
	Roles        []entity.PermissionRole
}

func (s *Service) getUserGroupResources(ctx context.Context, opt getUserGroupResourcesOption) ([]*entity.Resource, error) {
	// 获取用户所在的 Group
	userSpacePermissions, err := s.dao.ListPermission(ctx, dal.ListPermissionOption{
		ResourceType: lo.ToPtr(entity.ResourceTypeSpace),
		Type:         lo.ToPtr(entity.PermissionTypeUser),
		ExternalID:   lo.ToPtr(opt.Account.Username),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to list user group: %v", err)
		return nil, err
	}
	// 获取用户所在部门的 Group
	departmentSpacePermissions, err := s.dao.ListPermission(ctx, dal.ListPermissionOption{
		ResourceType: lo.ToPtr(entity.ResourceTypeSpace),
		Type:         lo.ToPtr(entity.PermissionTypeDepartment),
		ExternalID:   lo.ToPtr(opt.Account.Department),
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to list department group: %v", err)
		return nil, err
	}
	spacePermissions := userSpacePermissions
	spacePermissions = append(spacePermissions, departmentSpacePermissions...)
	permissions := s.uniqResourcePermissionsByRole(spacePermissions)

	resources, err := s.dao.BatchGetResources(ctx, dal.BatchGetResourceOption{IDs: lo.Map(permissions, func(item *entity.Permission, index int) string {
		return item.ResourceID
	})})
	if err != nil {
		log.V1.CtxError(ctx, "failed to list resources: %v", err)
		return nil, err
	}

	nwg := sync.WaitGroup{}
	var permissionSpaceErr error
	for _, res := range resources {
		res := res
		nwg.Add(1)
		go func() {
			defer nwg.Done()
			if err := panics.Try(func() {
				permissionsSpace, err := s.dao.ListPermission(ctx, dal.ListPermissionOption{
					ResourceIDs:  opt.ResourceIDs,
					ResourceType: opt.ResourceType,
					Type:         lo.ToPtr(entity.PermissionTypeSpace),
					ExternalID:   lo.ToPtr(res.ExternalID),
					Roles:        opt.Roles,
				})
				if err != nil {
					logs.V1.CtxError(ctx, "failed to list permission space: %v", err)
					permissionSpaceErr = err
					return
				}
				res.Permissions = permissionsSpace
			}).AsError(); err != nil {
				log.V1.CtxError(ctx, "list permission spaces panic: %v", resources)
				permissionSpaceErr = err
			}
		}()
	}
	nwg.Wait()

	if permissionSpaceErr != nil {
		log.V1.CtxError(ctx, "failed to list permission spaces: %v", permissionSpaceErr)
		return nil, errors.WithMessage(permissionSpaceErr, "failed to list permission space")
	}

	return resources, nil
}

func (s *Service) uniqResourcePermissionsByRole(oldPermissions []*entity.Permission) []*entity.Permission {
	permissions := make([]*entity.Permission, 0, len(oldPermissions))
	existResourcePermissions := map[string]bool{}
	for _, permission := range oldPermissions {
		uniqKey := fmt.Sprintf("%s-%s-%s-%d", permission.ResourceID, permission.Type, permission.ExternalID, permission.Role)
		if !existResourcePermissions[uniqKey] {
			permissions = append(permissions, permission)
			existResourcePermissions[uniqKey] = true
		}
	}

	return permissions
}

func (s *Service) translateActionToRoles(permissionActions []entity.PermissionAction) []entity.PermissionRole {
	if len(permissionActions) == 0 {
		return []entity.PermissionRole{}
	}

	actionPermissionRoleMap := make(map[entity.PermissionAction][]entity.PermissionRole)
	for role, actions := range s.tccConf.PermissionActionConfig.GetValue().Roles {
		for _, action := range actions {
			if roles, ok := actionPermissionRoleMap[entity.PermissionAction(action)]; ok {
				roles = append(roles, entity.ParseStringToPermissionRole(role))
				actionPermissionRoleMap[entity.PermissionAction(action)] = roles
			} else {
				actionPermissionRoleMap[entity.PermissionAction(action)] = []entity.PermissionRole{entity.ParseStringToPermissionRole(role)}
			}
		}
	}

	var result []entity.PermissionRole
	for _, action := range permissionActions {
		result = append(result, actionPermissionRoleMap[action]...)
	}
	return lo.Uniq(result)
}

func (s *Service) getGroupRelationMap(ctx context.Context, groupID *string, resources []*entity.Resource) (map[string][]*entity.GroupResourceRelation, error) {
	var groupResourceRelations []*entity.GroupResourceRelation
	var err error
	// 如果指定了 Group 则直接填数据
	if groupID != nil {
		groupResourceRelations = lo.Map(resources, func(item *entity.Resource, index int) *entity.GroupResourceRelation {
			return &entity.GroupResourceRelation{
				ResourceID: item.ID,
				GroupID:    *groupID,
			}
		})
	} else {
		groupResourceRelations, err = s.dao.BatchGetGroupResourceRelations(ctx, dal.BatchGetGroupResourceRelationsOption{
			ResourceIDs: lo.Map(resources, func(item *entity.Resource, index int) string {
				return item.ID
			}),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list group resource relations")
		}
	}

	var groupResourceRelationsMap = make(map[string][]*entity.GroupResourceRelation)
	for _, res := range groupResourceRelations {
		if relations, ok := groupResourceRelationsMap[res.ResourceID]; ok {
			relations = append(relations, res)
			groupResourceRelationsMap[res.ResourceID] = relations
		} else {
			relations = []*entity.GroupResourceRelation{res}
			groupResourceRelationsMap[res.ResourceID] = relations
		}
	}

	return groupResourceRelationsMap, nil
}
