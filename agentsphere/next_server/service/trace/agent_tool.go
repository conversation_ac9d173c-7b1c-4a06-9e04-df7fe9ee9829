package trace

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/devai"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/gopkg/logs/v2"
)

type ProcessLarkMarkdownFileArgs struct {
	Title        string            `json:"title" description:"文档标题"`
	Content      string            `json:"content" description:"Markdown 文件内容"`
	RelatedFiles map[string][]byte `json:"related_files,omitempty" description:"相关文件映射，键为相对路径，值为文件内容（支持二进制文件）"`
	FolderToken  string            `json:"folder_token,omitempty" description:"文件夹 token"`
}

func (s *Service) mockAgentConvertLarkMarkdownFile(ctx context.Context, args ProcessLarkMarkdownFileArgs) (string, error) {
	tempDir, mainFilePath, err := createTemporaryDirectory(args.Content, args.RelatedFiles)
	if err != nil {
		return "", fmt.Errorf("failed to create temporary directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	environ := iris.NewRunEnviron()
	environ.Set(entity.RuntimeTCEHostEnv, "boe")
	agentRunCtx := iris.NewRunContext(ctx, nil, nil, &mockLogger{}, &iris.AgentRunState{
		Store: map[string]any{},
	}, nil, environ, &config.AgentRunConfig{}, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	documentID, _, err := lark.ProcessLarkMarkdownFile(agentRunCtx, mainFilePath, args.Title, &lark.ProcessLarkMarkdownFileOptions{
		FolderToken:     args.FolderToken,
		SkipFixMermaid:  true,
		SkipFixPlantUml: true,
	})
	err = devai.SaveLarkDocument(ctx, documentID, "mockAgentConvertLarkMarkdownFile")
	if err != nil {
		logs.CtxError(ctx, "failed to save lark document: %v", err)
	}
	if err != nil {
		return "", fmt.Errorf("failed to process lark markdown file: %v", err)
	}

	return documentID, nil
}

func createTemporaryDirectory(mainContent string, relatedFiles map[string][]byte) (string, string, error) {
	tempDir, err := os.MkdirTemp("", "lark_markdown_*")
	if err != nil {
		return "", "", fmt.Errorf("failed to create temp directory: %v", err)
	}

	mainFilePath := filepath.Join(tempDir, "document.lark.md")
	if err := os.WriteFile(mainFilePath, []byte(mainContent), 0600); err != nil {
		os.RemoveAll(tempDir)
		return "", "", fmt.Errorf("failed to write main file: %v", err)
	}

	for originalPath, content := range relatedFiles {
		var targetPath string
		cleanPath := filepath.Clean(originalPath)
		// /workspace/iris_xxx/path/to/file.txt -> path/to/file.txt
		if filepath.IsAbs(cleanPath) {
			parts := strings.Split(cleanPath, "/")
			if len(parts) >= 4 && parts[1] == "workspace" && strings.HasPrefix(parts[2], "iris_") {
				relativePart := strings.Join(parts[3:], "/")
				targetPath = filepath.Join(tempDir, relativePart)
			} else {
				fileName := filepath.Base(cleanPath)
				targetPath = filepath.Join(tempDir, fileName)
			}
		} else {
			targetPath = filepath.Join(tempDir, cleanPath)
		}

		if err := os.MkdirAll(filepath.Dir(targetPath), 0750); err != nil {
			continue
		}

		os.WriteFile(targetPath, content, 0600)
	}

	return tempDir, mainFilePath, nil
}

type mockLogger struct{}

func (m *mockLogger) Trace(args ...any)                 {}
func (m *mockLogger) Debug(args ...any)                 {}
func (m *mockLogger) Info(args ...any)                  {}
func (m *mockLogger) Warn(args ...any)                  {}
func (m *mockLogger) Error(args ...any)                 {}
func (m *mockLogger) Fatal(args ...any)                 {}
func (m *mockLogger) Tracef(format string, args ...any) {}
func (m *mockLogger) Debugf(format string, args ...any) {}
func (m *mockLogger) Infof(format string, args ...any)  {}
func (m *mockLogger) Warnf(format string, args ...any)  {}
func (m *mockLogger) Errorf(format string, args ...any) {}
func (m *mockLogger) Fatalf(format string, args ...any) {}
