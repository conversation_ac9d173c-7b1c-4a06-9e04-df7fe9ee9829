package knowledgebase

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	bytetrace "code.byted.org/bytedtrace/interface-go"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/gopkg/logs/v2"
	"github.com/hashicorp/go-multierror"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type DocumentLoader interface {
	Load(ctx context.Context, opt LoadOption) (string, error)
	LoadMeta(ctx context.Context, operator, userToken string, opt MetaOption) (*DocumentMeta, error)
	BatchLoadMeta(ctx context.Context, userToken string, getOwner bool, opts []MetaOption) ([]*DocumentMeta, error)
}

type LarkLoader struct {
	larkCli    lark.Client
	larkParser *LarkParser
}

func NewLarkLoader(larkCli lark.Client) *LarkLoader {
	return &LarkLoader{
		larkCli:    larkCli,
		larkParser: NewLarkParser(larkCli, WithEnableUserOpenID(true), WithEnableImage(true)),
	}
}

type LoadOption struct {
	DocToken    string                     // 文档来源标识 doc-token、tos-key、link等
	Operator    string                     // 操作人
	ContentType entity.DocumentContentType // 文档内容类型 doc/docx 等
	UserToken   string
}

func (l *LarkLoader) Load(ctx context.Context, opt LoadOption) (string, error) {
	span, ctx := bytetrace.StartCustomSpan(ctx, "LarkLoader", "Load")
	defer span.Finish()
	var (
		content string
		err     error
	)
	switch opt.ContentType {
	case entity.DocumentContentTypeDoc:
		content, err = l.getMarkdownFromLarkDoc(ctx, opt.DocToken, opt.Operator, []lark.Option{{UserAccessToken: opt.UserToken}})
	case entity.DocumentContentTypeDocx:
		content, err = l.getMarkdownFromLarkDocx(ctx, opt.DocToken, opt.Operator, []lark.Option{{UserAccessToken: opt.UserToken}})
	case entity.DocumentContentTypeSheet:
		content, err = l.getMarkdownFromLarkSheetV2(ctx, opt.DocToken, opt.UserToken)
	case entity.DocumentContentTypeBitable:
		content, err = l.getMarkdownFromLarkBitable(ctx, opt.DocToken, opt.UserToken)
	default:
		return "", fmt.Errorf("unsupport content type: %s", opt.ContentType)
	}
	if err != nil {
		return "", err
	}
	return content, nil
}

type MetaOption struct {
	SourceType entity.DocumentSourceType // 数据源类型 lark_wiki 等
	SourceUid  string                    // 文档来源标识 doc-token、tos-key、link等
}

type DocumentMeta struct {
	Title       string
	SourceUid   string
	ContentType entity.DocumentContentType
	Owner       string
	CreateTime  time.Time
	UpdateTime  time.Time
	SourceType  entity.DocumentSourceType
	DocToken    string
}

func (l *LarkLoader) LoadMeta(ctx context.Context, operator, userToken string, opt MetaOption) (*DocumentMeta, error) {
	span, ctx := bytetrace.StartCustomSpan(ctx, "LarkLoader", "LoadMeta")
	defer span.Finish()
	reqDocs := []*lark.RequestDoc{
		{
			DocToken: opt.SourceUid,
			DocType:  opt.SourceType.GetDocType(),
		}}
	//存在 sourceUid跟返回的DocToken对不齐的情况，因此不能批量请求, 这里使用单个请求
	datas, err := l.larkCli.GetDocMeta(ctx, reqDocs, operator, lark.Option{UserAccessToken: userToken})
	if err != nil {
		logs.V1.Error("get document meta failed, err:%v, opt:%+v", err, opt)
		return nil, err
	}
	if len(datas.FailedList) > 0 {
		switch lo.FromPtr(datas.FailedList[0].Code) {
		case lark.GetDocMetaCodeNoPermission:
			return nil, lark.ErrPermissionDenied
		case lark.GetDocMetaCodeUnsupportedDocType:
			return nil, errors.Errorf("unsupported doc type")
		case lark.GetDocMetaCodeResourceNotExist:
			return nil, lark.ErrResourceNotFound
		default:
			return nil, errors.Errorf("get document meta failed, err: %v, opt:%+v", datas.FailedList[0], opt)
		}

	}
	if len(datas.Metas) == 0 {
		return nil, lark.ErrResourceNotFound
	}
	meta := &DocumentMeta{
		Title:       lo.FromPtr(datas.Metas[0].Title),
		SourceUid:   opt.SourceUid,
		DocToken:    lo.FromPtr(datas.Metas[0].DocToken), //与opt.SourceUid不同，获取文档内容需要用DocToken获取
		ContentType: entity.DocumentContentType(lo.FromPtr(datas.Metas[0].DocType)),
		SourceType:  opt.SourceType,
	}
	meta.setCreateTime(lo.FromPtr(datas.Metas[0].CreateTime))
	meta.setUpdateTime(lo.FromPtr(datas.Metas[0].LatestModifyTime))
	users, err := l.larkCli.ListLarkUsers(ctx, []string{lo.FromPtr(datas.Metas[0].OwnerId)}, "")
	if err != nil {
		return nil, err
	}
	for _, user := range users {
		r := strings.Split(lo.FromPtr(user.Email), "@")
		if len(r) == 0 || r[0] == "" {
			continue
		}
		if lo.FromPtr(user.OpenId) != lo.FromPtr(datas.Metas[0].OwnerId) {
			continue
		}
		meta.Owner = r[0]
	}
	return meta, nil
}

func (l *LarkLoader) BatchLoadMeta(ctx context.Context, userToken string, getOwner bool, opts []MetaOption) ([]*DocumentMeta, error) {
	if len(opts) == 0 {
		return nil, nil
	}
	span, ctx := bytetrace.StartCustomSpan(ctx, "LarkLoader", "BatchLoadMeta")
	defer span.Finish()
	reqDocs := make([]*lark.RequestDoc, 0, len(opts))
	for _, opt := range opts {
		reqDocs = append(reqDocs, &lark.RequestDoc{
			DocToken: opt.SourceUid,
			DocType:  opt.SourceType.GetDocType(),
		})
	}

	datas, err := l.larkCli.GetFilesMeta(ctx, reqDocs, userToken)
	if err != nil {
		logs.V1.Error("get document meta failed, err:%v, opts:%+v", err, opts)
		return nil, err
	}
	if len(datas.Metas) == 0 {
		return nil, lark.ErrResourceNotFound
	}
	var errs = &multierror.Error{}
	failedTokenMap := make(map[string]bool)
	for _, failed := range datas.FailedList {
		failedTokenMap[lo.FromPtr(failed.Token)] = true
		errs = multierror.Append(errs, errors.Errorf("failed to set content type, errCode:%v, sourceUid:%v", lo.FromPtr(failed.Code), lo.FromPtr(failed.Token)))
	}
	if errs.ErrorOrNil() != nil {
		logs.V1.Error("get document meta failed, err:%v, opts:%+v", errs, opts)
	}
	failedAccumulativeCount := 0
	ret := make([]*DocumentMeta, 0, len(datas.Metas))
	ownerIDs := make([]string, 0, len(datas.Metas))
	for idx, opt := range opts {
		if failedTokenMap[opt.SourceUid] {
			failedAccumulativeCount++
			continue
		}
		if idx-failedAccumulativeCount >= len(datas.Metas) {
			logs.V1.CtxError(ctx, "failed to get meta, idx:%v, failedAccumulativeCount:%v, meta length:%v", idx, failedAccumulativeCount, len(datas.Metas))
			break
		}
		meta := datas.Metas[idx-failedAccumulativeCount]
		docMeta := &DocumentMeta{
			Title:       lo.FromPtr(meta.Title),
			SourceUid:   opt.SourceUid,
			DocToken:    lo.FromPtr(meta.DocToken), //与opt.SourceUid不同，获取文档内容需要用DocToken获取
			ContentType: entity.DocumentContentType(lo.FromPtr(meta.DocType)),
			SourceType:  opt.SourceType,
		}
		docMeta.setCreateTime(lo.FromPtr(meta.CreateTime))
		docMeta.setUpdateTime(lo.FromPtr(meta.LatestModifyTime))
		docMeta.Owner = lo.FromPtr(meta.OwnerId)
		ret = append(ret, docMeta)
		ownerIDs = append(ownerIDs, lo.FromPtr(meta.OwnerId))
	}
	if len(ret) == 0 {
		return nil, errs.ErrorOrNil()
	}
	if !getOwner {
		return ret, nil
	}
	users, err := l.larkCli.ListLarkUsers(ctx, lo.Uniq(ownerIDs), "")
	if err != nil {
		return nil, err
	}
	userMap := make(map[string]string)
	for _, user := range users {
		r := strings.Split(lo.FromPtr(user.Email), "@")
		if len(r) == 0 || r[0] == "" {
			continue
		}
		userMap[lo.FromPtr(user.OpenId)] = r[0]
	}
	for _, meta := range ret {
		meta.Owner = userMap[meta.Owner]
	}
	return ret, nil
}

func (l *LarkLoader) getMarkdownFromLarkDoc(ctx context.Context, sourceKey, operator string, options []lark.Option) (string, error) {
	blocks, err := l.larkCli.GetLarkDocBlock(ctx, sourceKey, operator, options...)
	if err != nil {
		return "", err
	}
	markdown := l.larkParser.ParseDocContent(ctx, blocks)

	return markdown, nil
}

func (l *LarkLoader) getMarkdownFromLarkDocx(ctx context.Context, sourceKey, operator string, options []lark.Option) (string, error) {
	blocks, err := l.larkCli.GetLarkDocxBlock(ctx, sourceKey, operator, options...)
	if err != nil {
		return "", err
	}

	markdown := l.larkParser.ParseDocxContent(ctx, sourceKey, blocks)
	return markdown, nil
}

func (l *LarkLoader) getMarkdownFromLarkBitable(ctx context.Context, sourceKey, userToken string) (string, error) {
	listBitableResp, err := l.larkCli.ListBitableAppTable(ctx, sourceKey, "", 20, userToken)
	if err != nil {
		return "", errors.WithMessage(err, "failed to list bitable app table")
	}
	if lo.FromPtr(listBitableResp.HasMore) {
		logs.V1.Error("has more bitable app table")
		// Ignore more bitable to limit the dataset size.
	}
	buf := new(strings.Builder)
	for _, table := range listBitableResp.Items {
		buf.WriteString(fmt.Sprintf("## %s\n", lo.FromPtr(table.Name)))
		fields, err := l.getFullBitableFields(ctx, sourceKey, lo.FromPtr(table.TableId), userToken)
		if err != nil {
			return "", errors.WithMessage(err, "failed to get bitable app table fields")
		}
		var pageToken string
		for i := 0; i < 100; i++ {
			searchBitableRecordResp, err := l.larkCli.SearchBitableRecord(ctx, sourceKey, lo.FromPtr(table.TableId), "", pageToken, 500, userToken)
			if err != nil {
				logs.V1.Error("failed to search bitable record")
				return "", errors.WithMessage(err, "failed to search bitable record")
			}
			for _, item := range searchBitableRecordResp.Items {
				row, err := l.larkParser.formatBitableRow(fields, item.Fields)
				if err != nil {
					logs.V1.Error("failed to format bitable row")
					continue
				}
				buf.WriteString(row)
				buf.WriteString("\n")
			}
			if !lo.FromPtr(searchBitableRecordResp.HasMore) {
				break
			}
			if lo.FromPtr(searchBitableRecordResp.PageToken) == "" {
				break
			}
			pageToken = lo.FromPtr(searchBitableRecordResp.PageToken)
		}

	}
	return buf.String(), nil
}

func (l *LarkLoader) getFullBitableFields(ctx context.Context, sourceKey, tableID, userToken string) ([]*larkbitable.AppTableFieldForList, error) {
	var pageToken string
	result := make([]*larkbitable.AppTableFieldForList, 0)
	for i := 0; i < 20; i++ {
		listAppTableFieldRespData, err := l.larkCli.ListAppTableField(ctx, sourceKey, tableID, pageToken, 200, userToken)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list bitable app table field")
		}
		result = append(result, listAppTableFieldRespData.Items...)
		if !lo.FromPtr(listAppTableFieldRespData.HasMore) {
			break
		}
		if lo.FromPtr(listAppTableFieldRespData.PageToken) == "" {
			break
		}
		pageToken = lo.FromPtr(listAppTableFieldRespData.PageToken)
	}
	return result, nil
}

func (l *LarkLoader) getMarkdownFromLarkSheet(ctx context.Context, sourceKey string, userToken string) (string, error) {
	sheetMeta, err := l.larkCli.QuerySheet(ctx, sourceKey, userToken)
	if err != nil {
		return "", errors.WithMessage(err, "query sheet failed")
	}
	if len(sheetMeta.Sheets) == 0 {
		return "", nil
	}
	var sheetRange strings.Builder
	for idx, sheet := range sheetMeta.Sheets {
		sheetID := lo.FromPtr(sheet.SheetId)
		rowCount := lo.FromPtr(lo.FromPtr(sheet.GridProperties).RowCount)
		columnCount := lo.FromPtr(lo.FromPtr(sheet.GridProperties).ColumnCount)
		excelColumn := numberToExcelColumn(columnCount)
		rangeTmp := fmt.Sprintf("%s!A1:%s%d", sheetID, excelColumn, rowCount)
		sheetRange.WriteString(rangeTmp)
		if idx != len(sheetMeta.Sheets)-1 {
			sheetRange.WriteString(",")
		}
	}
	logs.V1.CtxInfo(ctx, "Get Markdown from Lark Sheet: %s", sheetRange.String())
	sheetData, err := l.larkCli.ReadBatchLarkSheet(ctx, sourceKey, sheetRange.String(), userToken)
	if err != nil {
		return "", errors.WithMessage(err, "read sheet failed")
	}
	content, err := l.larkParser.ParseSheet(ctx, sheetMeta, sheetData)
	if err != nil {
		return "", errors.WithMessage(err, "parse sheet failed")
	}
	return content, nil
}

func (l *LarkLoader) getMarkdownFromLarkSheetV2(ctx context.Context, sourceKey string, userToken string) (string, error) {
	sheetMeta, err := l.larkCli.QuerySheet(ctx, sourceKey, userToken)
	if err != nil {
		return "", errors.WithMessage(err, "query sheet failed")
	}
	if len(sheetMeta.Sheets) == 0 {
		return "", nil
	}
	contentBuilder := strings.Builder{}
	for _, sheet := range sheetMeta.Sheets {
		sheetID := lo.FromPtr(sheet.SheetId)
		rowCount := lo.FromPtr(lo.FromPtr(sheet.GridProperties).RowCount)
		columnCount := lo.FromPtr(lo.FromPtr(sheet.GridProperties).ColumnCount)
		excelColumn := numberToExcelColumn(columnCount)
		rangeTmp := fmt.Sprintf("%s!A1:%s%d", sheetID, excelColumn, rowCount)
		ranges := []string{rangeTmp}
		sheetData, err := l.larkCli.GetSheetMarkdownContent(ctx, sourceKey, sheetID, ranges, userToken)
		if err != nil {
			return "", errors.WithMessage(err, "read sheet failed")
		}
		content, err := l.larkParser.ParseSheetV2(ctx, sheet, sheetData)
		if err != nil {
			return "", errors.WithMessage(err, "parse sheet failed")
		}
		contentBuilder.WriteString(content)
	}
	return contentBuilder.String(), nil
}

func (d *DocumentMeta) setCreateTime(createTime string) {
	if val, err := strconv.ParseInt(createTime, 10, 64); err != nil {
		d.CreateTime = time.Now()
	} else {
		d.CreateTime = time.Unix(val, 0)
	}
}

func (d *DocumentMeta) setUpdateTime(updateTime string) {
	if val, err := strconv.ParseInt(updateTime, 10, 64); err != nil {
		d.UpdateTime = time.Now()
	} else {
		d.UpdateTime = time.Unix(val, 0)
	}
}

func numberToExcelColumn(n int) string {
	if n <= 0 {
		return ""
	}
	result := ""
	for n > 0 {
		remainder := (n - 1) % 26
		result = string(rune('A'+remainder)) + result
		n = (n - 1) / 26
	}
	return result
}
