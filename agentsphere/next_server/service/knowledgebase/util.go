package knowledgebase

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"time"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/devai"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type NerRequest struct {
	Query string `json:"query"`
}

type NerData struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

type NerResponse struct {
	Code int       `json:"code"`
	Msg  string    `json:"msg"`
	Data []NerData `json:"data"`
}

const (
	NerTypePropn = "PROPN"
	NerTypeNoun  = "NOUN"
)

func doNerReq(query string) ([]NerData, error) {
	url := "https://bits-ai-online-embedding.byted.org/ner"
	cli := http.Client{}
	reqBody := NerRequest{
		Query: query,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}
	httpReq, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")
	resp, err := cli.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	var nerResp NerResponse
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to read body")
	}
	if err := json.Unmarshal(respBody, &nerResp); err != nil {
		return nil, errors.WithMessage(err, "failed to unmarshal response")
	}
	if nerResp.Code != 0 {
		return nil, errors.Errorf("embedding failed, code: %d, msg: %s", nerResp.Code, nerResp.Msg)
	}
	return nerResp.Data, nil
}

func (s *Service) embeddings(ctx context.Context, jwtToken string, queries []string) ([][]float64, error) {
	req := &devai.CalculateEmbeddingsRequest{
		Contents: queries,
		// HardCode here. Make it as a configure when necessary.
		Model: lo.ToPtr("m3e"),
	}
	result := &devai.CalculateEembeddingsResponse{}
	err := backoff.Retry(func() error {
		_, err := s.embeddingClient.DoJSONReq(ctx, http.MethodPost, "/openapi/knowledge/v1/embeddings", hertz.ReqOption{
			ExpectedCode:    http.StatusOK,
			Body:            req,
			Result:          result,
			Notes:           "embedding",
			Headers:         map[string]string{"x-jwt-token": jwtToken},
			Timeout:         time.Second * 5,
			SetTTEnvHeaders: true,
		})
		return err
	}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Millisecond*100), 3))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result.Embeddings, nil
}

func cosineSimilarity(vecA, vecB []float64) float64 {
	var dotProduct float64
	for i, a := range vecA {
		dotProduct += a * vecB[i]
	}
	var normA float64
	for _, a := range vecA {
		normA += a * a
	}
	normA = math.Sqrt(normA)
	var normB float64
	for _, b := range vecB {
		normB += b * b
	}
	normB = math.Sqrt(normB)
	return dotProduct / (normA * normB)
}

func mergeAlternately[T any](a, b []T) []T {
	merged := make([]T, 0, len(a)+len(b))
	maxLen := len(a)
	if len(b) > maxLen {
		maxLen = len(b)
	}

	for i := 0; i < maxLen; i++ {
		if i < len(a) {
			merged = append(merged, a[i])
		}
		if i < len(b) {
			merged = append(merged, b[i])
		}
	}
	return merged
}

func genWikiSpaceKey(spaceID string) string {
	return fmt.Sprintf("wiki_tree_space_id:%s", spaceID)
}

func genLarkWikiUrl(sourceUid string) string {
	return fmt.Sprintf("https://bytedance.feishu.cn/wiki/%s", sourceUid)
}

func ToTime(createTime string) time.Time {
	if val, err := strconv.ParseInt(createTime, 10, 64); err != nil {
		return time.Now()
	} else {
		return time.Unix(val, 0)
	}
}
