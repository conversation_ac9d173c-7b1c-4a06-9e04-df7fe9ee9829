package knowledgebase

import (
	"context"
	"testing"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/port/lark"
	mocklark "code.byted.org/devgpt/kiwis/port/lark/mock"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestService_SearchDocsFromLark(t *testing.T) {
	dao := dal.NewMockDAO(t)
	err := dao.UpsertLarkUserToken(context.Background(), &entity.LarkUser{
		Username:       "xiazihao",
		AccessExpireAt: time.Now().Add(time.Hour * 24),
	})
	assert.Nil(t, err)
	t.Run("search_url", func(t *testing.T) {
		client := mocklark.NewMockClient(gomock.NewController(t))
		client.EXPECT().GetFilesMeta(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&larkdrive.BatchQueryMetaRespData{
			Metas: []*larkdrive.Meta{{
				Title:        lo.ToPtr("Title"),
				Url:          lo.ToPtr("https://bytedance.larkoffice.com/wiki/MLwDwIQ6MinweRkl6AkcdikQn4c"),
				SecLabelName: nil,
				DocType:      lo.ToPtr("doc"),
				OwnerId:      lo.ToPtr("1111"),
			}},
			FailedList: nil,
		}, nil)
		client.EXPECT().ListLarkUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*larkcontact.User{
			{
				OpenId: lo.ToPtr("1111"),
				Email:  lo.ToPtr("<EMAIL>"),
			},
		}, nil)
		service, err := larkservice.NewService(larkservice.CreateServiceOption{
			DAO:        dao,
			LarkClient: client,
		})
		assert.Nil(t, err)
		s := &Service{larkCli: client, dao: dao, larkService: service, loader: NewLarkLoader(client)}
		docs, _, err := s.SearchDocsFromLark(context.Background(), "xiazihao", "  https://bytedance.larkoffice.com/wiki/MLwDwIQ6MinweRkl6AkcdikQn4c  ", "111", entity.ImportTypeSingle)
		assert.Nil(t, err)
		assert.Equal(t, 1, len(docs))
	})
	t.Run("search", func(t *testing.T) {
		client := mocklark.NewMockClient(gomock.NewController(t))
		client.EXPECT().SearchLarkObject(gomock.Any(), gomock.Any(), gomock.Any()).Return(&lark.SearchLarkObjectRespData{}, nil)
		client.EXPECT().SearchWikiNode(gomock.Any(), gomock.Any(), gomock.Any()).Return(&lark.SearchWikiNodeData{}, nil)
		client.EXPECT().SearchLarkData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&lark.SearchMeta{
			Data: struct {
				Passages []lark.SearchPassage `json:"passages"`
			}{
				Passages: []lark.SearchPassage{{
					Content: "Content",
					Title:   "title",
					URL:     "https://bytedance.larkoffice.com/wiki/MLwDwIQ6MinweRkl6AkcdikQn4c",
					ID:      "",
					Extra:   "{\"obj_type\":\"22\"}",
				}},
			},
		}, nil)
		service, err := larkservice.NewService(larkservice.CreateServiceOption{
			DAO:        dao,
			LarkClient: client,
		})
		s := &Service{larkCli: client, dao: dao, larkService: service, loader: NewLarkLoader(client)}
		docs, _, err := s.SearchDocsFromLark(context.Background(), "xiazihao", " Aime", "11", entity.ImportTypeSingle)
		assert.Nil(t, err)
		assert.Equal(t, 1, len(docs))
	})
}
