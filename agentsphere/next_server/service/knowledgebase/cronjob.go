package knowledgebase

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"sync"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logid"
	"code.byted.org/gopkg/logs/v2"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/robfig/cron/v3"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

func (s *Service) StartCron() error {
	c := cron.New(cron.WithSeconds(),
		cron.WithChain(cron.SkipIfStillRunning(cron.DefaultLogger)))
	_, err := c.AddFunc("0 0 22 * * *", func() {
		ctx := ctxvalues.SetLogID(context.Background(), logid.GenLogID())
		if err := s.FullUpdateDocument(ctx, s.nextAgentKnowledgeConfig.GetValue().ForceUpdate); err != nil {
			logs.V1.CtxError(ctx, "Failed to handle cron job update document", "err", err)
		}
	})
	if err != nil {
		return err
	}
	_, err = c.AddFunc("0 0 19 * * *", func() { //0 0 19 * * *
		ctx := ctxvalues.SetLogID(context.Background(), logid.GenLogID())
		if err := s.PatrolIndexer(ctx); err != nil {
			logs.V1.CtxError(ctx, "Failed to handle cron job patrol indexer", "err", err)
		}
	})
	if err != nil {
		return err
	}
	_, err = c.AddFunc("0 0 18 * * *", func() { //0 0 18 * * *
		ctx := ctxvalues.SetLogID(context.Background(), logid.GenLogID())
		if err := s.PruneIndexer(ctx); err != nil {
			logs.V1.CtxError(ctx, "Failed to handle cron job prune indexer", "err", err)
		}
	})
	if err != nil {
		return err
	}
	c.Start()
	return nil
}

func (s *Service) FullUpdateDocument(ctx context.Context, forceUpdate bool) error {
	logs.V1.CtxInfo(ctx, "[FullUpdateDocument] start to full update document")
	lockKey := "knowledgebase_full_update_document"
	r := rand.New(rand.NewSource(time.Now().UnixNano())) // 使用当前时间作为种子
	lockValue := r.Intn(math.MaxInt)
	ok, err := s.redis.SetNX(ctx, lockKey, lockValue, time.Hour*12)
	if err != nil {
		return errors.WithMessage(err, "failed to lock")
	}
	if !ok {
		return errors.New("failed to get lock")
	}
	defer s.redis.Cad(ctx, lockKey, lockValue)
	datasetList, err := s.dao.ListDatasets(ctx)
	if err != nil {
		logs.V1.CtxError(ctx, "[FullUpdateDocument] failed to list datasets: %v", err)
		return err
	}
	for _, dataset := range datasetList {
		docs, err := s.dao.ListDocumentsByDatasetID(ctx, dataset.ID)
		if err != nil {
			logs.V1.CtxError(ctx, "[FullUpdateDocument] failed to list documents: %v", err)
			continue
		}
		if len(docs) == 0 {
			logs.V1.CtxInfo(ctx, "[FullUpdateDocument] no documents found for dataset: %s", dataset.ID)
			continue
		}
		for _, doc := range docs {
			ctx = ctxvalues.SetLogID(context.Background(), logid.GenLogID())
			err := s.dao.UpdateDocument(ctx, doc.ID, &dal.UpdateDocumentOption{
				ProcessStatus: lo.ToPtr(entity.DocumentProcessStatusProcessing),
			})
			if err != nil {
				logs.V1.CtxError(ctx, "[FullUpdateDocument] failed to update document: %v, document id:%v", err, doc.ID)
				continue
			}
			err = s.SendBatchUpsertContentMessage(ctx, []*entity.Document{doc}, forceUpdate)
			if err != nil {
				logs.V1.CtxError(ctx, "[FullUpdateDocument] failed to send batch upsert content: %v, document id:%v", err, doc.ID)
			}
		}

	}
	return nil
}

func (s *Service) PatrolIndexer(ctx context.Context) error {
	logs.V1.CtxInfo(ctx, "[PatrolIndexer] start to patrol indexer")
	lockKey := "knowledgebase_patrol_indexer"
	r := rand.New(rand.NewSource(time.Now().UnixNano())) // 使用当前时间作为种子
	lockValue := r.Intn(math.MaxInt)
	ok, err := s.redis.SetNX(ctx, lockKey, lockValue, time.Hour*6)
	if err != nil {
		return errors.WithMessage(err, "failed to lock")
	}
	if !ok {
		return errors.New("failed to get lock")
	}
	defer s.redis.Cad(ctx, lockKey, lockValue)
	errLock := sync.Mutex{}
	mulErr := &multierror.Error{}
	g := errgroup.Group{}
	indexerTypes := s.getIndexerTypes()
	for _, indexerType := range indexerTypes {
		indexerType := indexerType
		g.Go(func() error {
			err := s.patrolIndexerSegment(ctx, indexerType, time.Hour*6)
			if err == nil {
				return nil
			}
			logs.V1.CtxError(ctx, "[PatrolIndexer] failed to patrol indexer segment: %v", err)
			errLock.Lock()
			mulErr = multierror.Append(mulErr, err)
			errLock.Unlock()
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		return errors.WithMessage(err, "failed to wait patrol indexer")
	}
	return mulErr.ErrorOrNil()
}

func (s *Service) patrolIndexerSegment(ctx context.Context, indexerType IndexerType, maxTTL time.Duration) error {
	start := time.Now()
	limitNum := 1000000
	rowNumberStart := 0
	for i := 0; i < 1000; i++ {
		sqlStr := fmt.Sprintf(`
select segment_id, row_number from %s
where date='%s' and indexer_type='%s' and row_number > %d
order by row_number asc
limit %d
`, s.patrolTable, indexerType.patrolTime(), indexerType.string(), rowNumberStart, limitNum)
		logs.V1.CtxInfo(ctx, "[PatrolIndexer] sqlStr: %s", sqlStr)
		result, err := s.tqsCli.FetchQueryResult(ctx, sqlStr, map[string]interface{}{})
		if err != nil {
			return errors.WithMessagef(err, "failed to query from tqs")
		}
		if len(result) == 0 {
			logs.V1.CtxInfo(ctx, "[PatrolIndexer] no segment found for indexer type: %s", indexerType.string())
			break
		}
		result = result[1:] // Ignore the header.
		if len(result) == 0 {
			logs.V1.CtxInfo(ctx, "[PatrolIndexer] no segment found for indexer type: %s", indexerType.string())
			break
		}
		allSegmentIDs := lo.Map(result, func(row []string, _ int) int64 {
			parseUint := conv.IntDefault(row[1], 0)
			if parseUint > rowNumberStart {
				rowNumberStart = parseUint
			}
			return conv.Int64Default(row[0], 0)
		})
		const bufferSize = 20
		chunkedSegmentIDs := lo.Chunk(allSegmentIDs, bufferSize)
		for _, segmentUniqueIDs := range chunkedSegmentIDs {
			if time.Since(start) > maxTTL {
				logs.V1.CtxInfo(ctx, "patrol indexer segment reached maxTTL %v", maxTTL)
				return nil
			}
			segments, err := s.dao.MGetSegments(ctx, segmentUniqueIDs)
			if err != nil {
				logs.V1.CtxError(ctx, "[PatrolIndexer] failed to fetch segments: %v", err)
				return err
			}
			saveSegmentsFunc := s.getSaveSegmentFunc(indexerType)
			if saveSegmentsFunc == nil {
				logs.V1.CtxError(ctx, "[PatrolIndexer] failed to get save segments func")
				return errors.New("failed to get save segments func")
			}
			err = saveSegmentsFunc(ctx, segments)
			if err != nil {
				logs.V1.CtxError(ctx, "[PatrolIndexer] failed to save segments: %v", err)
				return err
			}
		}
		if len(result) < limitNum {
			break
		}
	}
	return nil
}

type saveSegmentFunc func(ctx context.Context, segments []*entity.Segment) error

func (s *Service) getSaveSegmentFunc(indexerType IndexerType) saveSegmentFunc {
	switch indexerType {
	case IndexerTypeViking:
		return s.saveSegmentsToViking
	case IndexerTypeElasticsearch:
		return s.saveSegmentsToES
	default:
		return nil
	}
}

type deleteSegmentsFunc func(ctx context.Context, segmentIDs []int64) error

func (s *Service) getDeleteSegmentsFunc(indexerType IndexerType) deleteSegmentsFunc {
	switch indexerType {
	case IndexerTypeViking:
		return s.deleteSegmentsToViking
	case IndexerTypeElasticsearch:
		return s.deleteSegmentsToES
	default:
		return nil
	}
}

func (s *Service) PruneIndexer(ctx context.Context) error {
	logs.V1.CtxInfo(ctx, "[PruneIndexer] start to prune indexer")
	lockKey := "knowledgebase_prune_indexer"
	r := rand.New(rand.NewSource(time.Now().UnixNano())) // 使用当前时间作为种子
	lockValue := r.Intn(math.MaxInt)
	ok, err := s.redis.SetNX(ctx, lockKey, lockValue, time.Hour*6)
	if err != nil {
		return errors.WithMessage(err, "failed to lock")
	}
	if !ok {
		return errors.New("failed to get lock")
	}
	defer s.redis.Cad(ctx, lockKey, lockValue)
	errLock := sync.Mutex{}
	mulErr := &multierror.Error{}
	g := errgroup.Group{}
	indexerTypes := s.getIndexerTypes()
	for _, indexerType := range indexerTypes {
		indexerType := indexerType
		g.Go(func() error {
			err := s.pruneIndexerSegment(ctx, indexerType, time.Hour*6)
			if err == nil {
				return nil
			}
			logs.V1.CtxError(ctx, "[PruneIndexer] failed to prune indexer segment: %v", err)
			errLock.Lock()
			mulErr = multierror.Append(mulErr, err)
			errLock.Unlock()
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		return errors.WithMessage(err, "failed to wait patrol indexer")
	}
	return mulErr.ErrorOrNil()
}

func (s *Service) pruneIndexerSegment(ctx context.Context, indexerType IndexerType, maxTTL time.Duration) error {
	start := time.Now()
	limitNum := 1000000
	rowNumberStart := 0
	for i := 0; i < 1000; i++ {
		sqlStr := fmt.Sprintf(`
select segment_id, row_number from %s
where date='%s' and indexer_type='%s' and row_number > %d
order by row_number asc
limit %d
`, s.pruneTable, indexerType.patrolTime(), indexerType.string(), rowNumberStart, limitNum)
		logs.V1.CtxInfo(ctx, "[PruneIndexer] sqlStr: %s", sqlStr)
		result, err := s.tqsCli.FetchQueryResult(ctx, sqlStr, map[string]interface{}{})
		if err != nil {
			return errors.WithMessagef(err, "failed to query from tqs")
		}
		if len(result) == 0 {
			logs.V1.CtxInfo(ctx, "[PatrolIndexer] no segment found for indexer type: %s", indexerType.string())
			break
		}
		result = result[1:] // Ignore the header.
		if len(result) == 0 {
			logs.V1.CtxInfo(ctx, "[PatrolIndexer] no segment found for indexer type: %s", indexerType.string())
			break
		}
		allSegmentIDs := lo.Map(result, func(row []string, _ int) int64 {
			parseUint := conv.IntDefault(row[1], 0)
			if parseUint > rowNumberStart {
				rowNumberStart = parseUint
			}
			return conv.Int64Default(row[0], 0)
		})
		const bufferSize = 20
		chunkedSegmentIDs := lo.Chunk(allSegmentIDs, bufferSize)
		for _, segmentUniqueIDs := range chunkedSegmentIDs {
			if time.Since(start) > maxTTL {
				logs.V1.CtxInfo(ctx, "patrol indexer segment reached maxTTL %v", maxTTL)
				return nil
			}
			segments, err := s.dao.MGetSegments(ctx, segmentUniqueIDs)
			if err != nil {
				logs.V1.CtxError(ctx, "[PatrolIndexer] failed to fetch segments: %v", err)
				return err
			}
			segmentUniqueIDs = removeIDsFromA(segmentUniqueIDs, segments)
			if len(segmentUniqueIDs) == 0 {
				continue
			}
			delSegmentsFunc := s.getDeleteSegmentsFunc(indexerType)
			if delSegmentsFunc == nil {
				logs.V1.CtxError(ctx, "[PatrolIndexer] failed to get del segments func")
				return errors.New("failed to get del segments func")
			}
			err = delSegmentsFunc(ctx, segmentUniqueIDs)
			if err != nil {
				logs.V1.CtxError(ctx, "[PatrolIndexer] failed to del segments: %v", err)
				return err
			}
		}
		if len(result) < limitNum {
			break
		}
	}
	return nil
}

type IndexerType string

const (
	IndexerTypeViking        IndexerType = "viking"
	IndexerTypeElasticsearch IndexerType = "elasticsearch"
)

func (s *Service) getIndexerTypes() []IndexerType {
	return []IndexerType{IndexerTypeViking, IndexerTypeElasticsearch}
}

func (i IndexerType) string() string {
	return string(i)
}

func (i IndexerType) patrolTime() string {
	now := time.Now()
	if now.Hour() < 6 { // 0-6 点, 前一天的数据可能未产出，往前推一天
		now = now.AddDate(0, 0, -1)
	}
	switch i {
	case IndexerTypeViking:
		now = now.AddDate(0, 0, -3) // viking数据是T+3, 往前推3天
		return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Format("20060102")
	case IndexerTypeElasticsearch:
		now = now.AddDate(0, 0, -1) // elasticsearch数据是T+1, 往前推1天
		return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Format("20060102")
	default:
		return ""
	}
}

func removeIDsFromA(segmentIDs []int64, B []*entity.Segment) []int64 {
	if len(B) == 0 {
		return segmentIDs
	}
	idMap := make(map[int64]bool)
	for _, item := range B {
		idMap[item.ID] = true
	}

	var result []int64
	for _, id := range segmentIDs {
		if !idMap[id] {
			result = append(result, id)
		}
	}

	return result
}
