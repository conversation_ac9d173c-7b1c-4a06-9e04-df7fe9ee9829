package knowledgebase

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/toutiao/elastic/v7"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

// esSegmentDoc represents the structure of a segment document in Elasticsearch.
type esSegmentDoc struct {
	DatasetID  string `json:"dataset_id"`
	DocumentID string `json:"document_id"`
	Content    string `json:"content"`
}

func (s *Service) saveSegmentsToES(ctx context.Context, segments []*entity.Segment) error {
	bulkRequest := s.esCli.Bulk()
	for _, segment := range segments {
		doc := esSegmentDoc{
			DatasetID:  segment.DatasetID,
			DocumentID: segment.DocumentID,
			Content:    segment.Content.Text,
		}
		req := elastic.NewBulkIndexRequest().Index(s.esIndex).Id(strconv.FormatInt(segment.ID, 10)).Doc(doc)
		bulkRequest = bulkRequest.Add(req)
	}
	resp, err := bulkRequest.Do(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to save segments to elasticsearch")
	}
	if resp.Errors {
		var errs = &multierror.Error{}
		for _, item := range resp.Items {
			for kk, responseItem := range item {
				if responseItem.Error != nil {
					errMsg := errors.Errorf("error indexing segment %s: %+v", kk, responseItem.Error)
					errs = multierror.Append(errs, errMsg)
				}
			}
		}
		return errs.ErrorOrNil()
	}
	return nil
}

func (s *Service) recallSegmentsFromES(ctx context.Context, datasetID string, query string, topK int) ([]int64, error) {
	queryDSL := elastic.NewBoolQuery().
		Must(elastic.NewMatchQuery("content", query)).
		Filter(elastic.NewTermQuery("dataset_id", datasetID))

	searchResult, err := s.esCli.Search().
		Index(s.esIndex).
		Query(queryDSL).
		Size(topK).
		Do(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to search segments from elasticsearch")
	}

	var segmentIDs []int64
	for _, hit := range searchResult.Hits.Hits {
		var doc esSegmentDoc
		if err := json.Unmarshal(hit.Source, &doc); err != nil {
			logs.V1.CtxError(ctx, "failed to unmarshal segment doc: %v", err)
			continue
		}
		id, err := strconv.ParseInt(hit.Id, 10, 64)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to parse segment id: %v", err)
			continue
		}
		segmentIDs = append(segmentIDs, id)
	}
	return segmentIDs, nil
}

func (s *Service) deleteSegmentsToES(ctx context.Context, segmentIDs []int64) error {
	if len(segmentIDs) == 0 {
		return nil
	}
	bulkRequest := s.esCli.Bulk()
	for _, segmentID := range segmentIDs {
		req := elastic.NewBulkDeleteRequest().Index(s.esIndex).Id(strconv.FormatInt(segmentID, 10))
		bulkRequest = bulkRequest.Add(req)
	}
	resp, err := bulkRequest.Do(ctx)
	if err != nil {
		elaErr, ok := err.(*elastic.Error)
		if ok {
			if elaErr.Status == http.StatusNotFound {
				return ErrNotFound
			}
		}
		return errors.WithMessage(err, "failed to delete segments from elasticsearch")
	}
	if resp.Errors {
		var errs = &multierror.Error{}
		for _, item := range resp.Items {
			for kk, responseItem := range item {
				if responseItem.Error != nil {
					errMsg := errors.Errorf("error deleting segment %s: %+v", kk, responseItem.Error)
					errs = multierror.Append(errs, errMsg)
				}
			}
		}
		return errs.ErrorOrNil()
	}
	return nil
}

func (s *Service) existSegmentIDsFromES(ctx context.Context, segmentIDs []int64) ([]int64, error) {
	mget := s.esCli.Mget()
	for _, segmentID := range segmentIDs {
		mget = mget.Add(elastic.NewMultiGetItem().Index(s.esIndex).Id(strconv.FormatInt(segmentID, 10)))
	}
	resp, err := mget.Do(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to fetch segments from elasticsearch")
	}
	ids := make([]int64, 0)
	for _, item := range resp.Docs {
		if item.Error != nil {
			logs.V1.CtxError(ctx, "failed to fetch segments from elasticsearch: %v, id:%v", item.Error, item.Id)
			continue
		}
		ids = append(ids, conv.Int64Default(item.Id, 0))
	}
	return ids, nil
}

type esDocumentDoc struct {
	DatasetID         string  `json:"dataset_id"`
	Title             string  `json:"title"`
	Owner             string  `json:"owner"`
	CreatedAt         int64   `json:"created_at"`
	Creator           string  `json:"creator"`
	LastUpdatedAt     int64   `json:"last_updated_at"`
	HitCount          int64   `json:"hit_count"`
	ID                *string `json:"id,omitempty"`
	UpdatedAt         int64   `json:"updated_at"`
	SourceType        string  `json:"source_type"`
	ContentType       string  `json:"content_type"`
	DocumentCreatedAt int64   `json:"document_created_at"`
	ProcessStatus     *string `json:"process_status"`
}

const (
	datasetIDField         = "dataset_id"
	titleNgramField        = "title.ngram"
	titleField             = "title"
	ownerField             = "owner"
	creatorField           = "creator"
	lastUpdatedAtField     = "last_updated_at"
	hitCountField          = "hit_count"
	updatedAtField         = "updated_at"
	documentCreatedAtField = "document_created_at"
	processStatusField     = "process_status"
)

func (s *Service) saveDocumentsToES(ctx context.Context, documents []*entity.Document) error {
	bulkRequest := s.esCli.Bulk()
	for _, document := range documents {
		doc := esDocumentDoc{
			DatasetID:         document.DatasetID,
			Title:             document.Title,
			Owner:             document.Owner,
			CreatedAt:         time.Now().Unix(),
			LastUpdatedAt:     document.LastUpdatedAt.Unix(),
			Creator:           document.Creator,
			HitCount:          0, // 初始化为0
			UpdatedAt:         time.Now().Unix(),
			SourceType:        string(document.SourceType),
			ContentType:       string(document.ContentType),
			DocumentCreatedAt: document.DocumentCreatedAt.Unix(),
			ProcessStatus:     lo.ToPtr(string(document.ProcessStatus)),
		}
		req := elastic.NewBulkIndexRequest().Index(s.nextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex).Id(document.ID).Doc(doc)
		bulkRequest = bulkRequest.Add(req)
	}
	resp, err := bulkRequest.Refresh("true").Do(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to save document to elasticsearch")
	}
	if resp.Errors {
		var errs = &multierror.Error{}
		for _, item := range resp.Items {
			for kk, responseItem := range item {
				if responseItem.Error != nil {
					errMsg := errors.Errorf("error indexing document %s: %+v", kk, responseItem.Error)
					errs = multierror.Append(errs, errMsg)
				}
			}
		}
		return errs.ErrorOrNil()
	}
	return nil
}

func (s *Service) add1HitCountToES(ctx context.Context, documentID string) error {
	req := s.esCli.Update().Index(s.nextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex).Id(documentID).
		Script(elastic.NewScript("ctx._source.hit_count += params.count").Params(map[string]interface{}{"count": 1}))
	_, err := req.Refresh("true").Do(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to upsert document to elasticsearch")
	}
	return nil
}

type updateDocumentToESOption struct {
	DocumentID        string
	Title             *string
	Owner             *string
	LastUpdatedAt     *time.Time
	UpdatedAt         *time.Time
	DocumentCreatedAt *time.Time
	ProcessStatus     *string
}

func (s *Service) updateDocumentToES(ctx context.Context, opt *updateDocumentToESOption) error {
	updateMap := map[string]any{}
	if opt.Title != nil {
		updateMap[titleField] = *opt.Title
	}
	if opt.Owner != nil {
		updateMap[ownerField] = *opt.Owner
	}
	if opt.LastUpdatedAt != nil {
		updateMap[lastUpdatedAtField] = opt.LastUpdatedAt.Unix()
	}
	if opt.UpdatedAt != nil {
		updateMap[updatedAtField] = opt.UpdatedAt.Unix()
	}
	if opt.DocumentCreatedAt != nil {
		updateMap[documentCreatedAtField] = opt.DocumentCreatedAt.Unix()
	}
	if opt.ProcessStatus != nil {
		updateMap[processStatusField] = *opt.ProcessStatus
	}
	updateMap[updatedAtField] = time.Now().Unix()
	req := s.esCli.Update().Index(s.nextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex).Id(opt.DocumentID).Doc(updateMap)
	_, err := req.Refresh("true").Do(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to update document to elasticsearch")
	}
	return nil
}

func (s *Service) batchUpdateDocumentToES(ctx context.Context, opts []*updateDocumentToESOption) error {
	if len(opts) == 0 {
		return nil
	}
	bulkRequest := s.esCli.Bulk()
	for _, opt := range opts {
		updateMap := map[string]any{}
		if opt.Title != nil {
			updateMap[titleField] = *opt.Title
		}
		if opt.Owner != nil {
			updateMap[ownerField] = *opt.Owner
		}
		if opt.LastUpdatedAt != nil {
			updateMap[lastUpdatedAtField] = opt.LastUpdatedAt.Unix()
		}
		if opt.UpdatedAt != nil {
			updateMap[updatedAtField] = opt.UpdatedAt.Unix()
		}
		if opt.ProcessStatus != nil {
			updateMap[processStatusField] = *opt.ProcessStatus
		}
		updateMap[updatedAtField] = time.Now().Unix()

		req := elastic.NewBulkUpdateRequest().Index(s.nextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex).Id(opt.DocumentID).Doc(updateMap)
		bulkRequest = bulkRequest.Add(req)
	}
	resp, err := bulkRequest.Do(ctx)
	if err != nil {
		elaErr, ok := err.(*elastic.Error)
		if ok {
			if elaErr.Status == http.StatusNotFound {
				return ErrNotFound
			}
		}
		return errors.WithMessage(err, "failed to update documents from elasticsearch")
	}
	if resp.Errors {
		var errs = &multierror.Error{}
		for _, item := range resp.Items {
			for kk, responseItem := range item {
				if responseItem.Error != nil {
					errMsg := errors.Errorf("error update documentID %s: %+v", kk, responseItem.Error)
					errs = multierror.Append(errs, errMsg)
				}
			}
		}
		return errs.ErrorOrNil()
	}

	return nil
}

func (s *Service) deleteDocumentToES(ctx context.Context, documentID string) error {
	_, err := s.esCli.Delete().Index(s.nextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex).Id(documentID).Refresh("true").Do(ctx)
	if err != nil {
		elaErr, ok := err.(*elastic.Error)
		if ok {
			if elaErr.Status == http.StatusNotFound {
				return ErrNotFound
			}
		}
		return errors.WithMessage(err, "failed to delete document to elasticsearch")
	}
	return nil
}

func (s *Service) batchDeleteDocumentToES(ctx context.Context, documentIDs []string) error {
	if len(documentIDs) == 0 {
		return nil
	}
	bulkRequest := s.esCli.Bulk()
	for _, documentID := range documentIDs {
		req := elastic.NewBulkDeleteRequest().Index(s.nextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex).Id(documentID)
		bulkRequest = bulkRequest.Add(req)
	}
	resp, err := bulkRequest.Do(ctx)
	if err != nil {
		elaErr, ok := err.(*elastic.Error)
		if ok {
			if elaErr.Status == http.StatusNotFound {
				return ErrNotFound
			}
		}
		return errors.WithMessage(err, "failed to delete documents from elasticsearch")
	}
	if resp.Errors {
		var errs = &multierror.Error{}
		for _, item := range resp.Items {
			for kk, responseItem := range item {
				if responseItem.Error != nil {
					errMsg := errors.Errorf("error deleting documentID %s: %+v", kk, responseItem.Error)
					errs = multierror.Append(errs, errMsg)
				}
			}
		}
		return errs.ErrorOrNil()
	}
	return nil
}

type searchDocumentsOption struct {
	Query         *string
	Creator       []string
	DescOrderBy   *string
	ProcessStatus []string
	Offset        int
	Limit         int
}

const (
	DescOrderByLastUpdatedAt = "last_updated_at"
	DescOrderByHeat          = "heat"
	DestOrderByUpdatedAt     = "updated_at"
)

func (s *Service) searchDocumentsFromES(ctx context.Context, datasetID string, opt *searchDocumentsOption) (documents []*esDocumentDoc, totalCount int64, err error) {
	query := elastic.NewBoolQuery().Filter(elastic.NewTermsQuery(datasetIDField, datasetID))
	var searchService *elastic.SearchService
	if opt == nil {
		searchService = s.esCli.Search().Index(s.nextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex).Query(query)
	} else {
		if opt.Query != nil && len(*opt.Query) > 0 {
			query = query.Must(elastic.NewMultiMatchQuery(*opt.Query, titleNgramField, titleField))
		}
		if len(opt.Creator) > 0 {
			query = query.Filter(elastic.NewTermsQuery(creatorField, lo.ToAnySlice(opt.Creator)...))
		}
		if len(opt.ProcessStatus) > 0 {
			query = query.Filter(elastic.NewTermsQuery(processStatusField, lo.ToAnySlice(opt.ProcessStatus)...))
		}
		var sorter []elastic.Sorter
		switch lo.FromPtr(opt.DescOrderBy) {
		case DescOrderByLastUpdatedAt:
			sorter = []elastic.Sorter{
				elastic.NewFieldSort(lastUpdatedAtField).Desc(),
			}
		case DescOrderByHeat:
			sorter = []elastic.Sorter{
				elastic.NewFieldSort(hitCountField).Desc(),
			}
		case DestOrderByUpdatedAt:
			sorter = []elastic.Sorter{
				elastic.NewFieldSort(updatedAtField).Desc(),
			}
		}
		searchService = s.esCli.Search().Index(s.nextAgentKnowledgeConfig.GetValue().ElasticSearchDocumentIndex).Query(query)
		if opt.Limit != 0 {
			searchService = searchService.From(opt.Offset).Size(opt.Limit)
		}
		if len(sorter) > 0 {
			searchService.SortBy(sorter...)
		}
	}
	resp, err := searchService.Do(ctx)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to search documents from elasticsearch")
	}
	if resp.Hits.TotalHits != nil {
		totalCount = resp.Hits.TotalHits.Value
	}
	for _, hit := range resp.Hits.Hits {
		tmp := &esDocumentDoc{}
		if err := json.Unmarshal(hit.Source, tmp); err != nil {
			logs.V1.CtxError(ctx, "failed to unmarshal document: %v", err)
			continue
		}
		tmp.ID = &hit.Id
		documents = append(documents, tmp)
	}
	return documents, totalCount, nil
}
