package code_repo

import (
	"context"
	"strings"
	"time"

	"code.byted.org/codebase/sdk/v2"
	"code.byted.org/codebase/sdk/v2/types/repository"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/port/nextcode"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/v2/operatorx"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kite/kitex/client/callopt"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bits_ai_graph/kitex_gen/bits/ai/graph"
	"code.byted.org/overpass/bits_ai_graph/rpc/bits_ai_graph"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"
	"golang.org/x/sync/errgroup"
)

type Service struct {
	dao            *dal.DAO
	nextCodeClient nextcode.Client
}

type CreateServiceOption struct {
	fx.In

	DAO            *dal.DAO
	NextCodeClient nextcode.Client
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		dao:            opt.DAO,
		nextCodeClient: opt.NextCodeClient,
	}

	return s, nil
}

func (s *Service) SearchCodeRepo(ctx context.Context, req *nextagent.SearchCodeRepoRequest, jwt string) ([]*nextagent.CodeRepo, int64, error) {
	repositories, err := s.nextCodeClient.ListRepositories(ctx, repository.ListRepositoriesRequest{
		Query:      lo.ToPtr(req.GetQuery()),
		PageNumber: lo.ToPtr(int32(req.GetPageNum())),
		PageSize:   lo.ToPtr(int32(req.GetPageSize())),
	}, []sdk.CallOption{sdk.WithAppAuth(0, ""), sdk.WithUserJWT(jwt)}...)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed list repository")
	}

	uploadedCodeRepos, _, err := s.dao.ListCodeRepo(ctx, dal.ListCodeRepoOption{
		SpaceID: req.GetSpaceID(),
	})
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed list code repo")
	}
	uploadedCodeRepoM := gslice.ToMap(uploadedCodeRepos, func(item *po.NextCodeRepoPO) (string, struct{}) {
		return item.RepoID, struct{}{}
	})

	return gslice.Map(repositories.GetRepositories(), func(item *repository.Repository) *nextagent.CodeRepo {
		_, isUploaded := uploadedCodeRepoM[item.GetId()]
		return &nextagent.CodeRepo{
			RepoID:     item.GetId(),
			RepoName:   item.GetPath(),
			IsUploaded: isUploaded,
			Desc:       lo.ToPtr(item.GetDescription()),
			AvatarUrl:  lo.ToPtr(item.GetAvatarURL()),
			Url:        item.GetURL(),
		}
	}), int64(repositories.GetTotalCount()), nil
}

func (s *Service) CreateCodeRepos(ctx context.Context, repos []*nextagent.CodeRepo, spaceID, username string) error {
	existRepos, _, err := s.dao.ListCodeRepo(ctx, dal.ListCodeRepoOption{
		SpaceID: spaceID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list code repos")
	}

	existM := gslice.ToMap(existRepos, func(r *po.NextCodeRepoPO) (string, struct{}) {
		return r.RepoID, struct{}{}
	})

	err = s.dao.CreateCodeRepos(ctx, gslice.FilterMap(repos, func(r *nextagent.CodeRepo) (*po.NextCodeRepoPO, bool) {
		if _, ok := existM[r.GetRepoID()]; ok {
			return nil, false
		}

		return &po.NextCodeRepoPO{
			SpaceID:  spaceID,
			RepoID:   r.GetRepoID(),
			RepoName: r.GetRepoName(),
			Creator:  username,
		}, true
	}))
	if err != nil {
		return errors.WithMessage(err, "failed to create code repos")
	}

	return nil
}

func (s *Service) ListCodeRepo(ctx context.Context, req *nextagent.ListCodeRepoRequest) ([]*nextagent.CodeRepo, int64, error) {
	codeRepos, _, err := s.dao.ListCodeRepo(ctx, dal.ListCodeRepoOption{
		SpaceID: req.GetSpaceID(),
	})
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to list code repo from db")
	}

	if req.GetQuery() != "" {
		codeRepos = gslice.Filter(codeRepos, func(item *po.NextCodeRepoPO) bool {
			return strings.Contains(strings.ToLower(item.RepoName), strings.ToLower(req.GetQuery()))
		})
	}

	if len(req.GetCreators()) > 0 {
		codeRepos = gslice.Filter(codeRepos, func(item *po.NextCodeRepoPO) bool {
			return gslice.Contains(req.GetCreators(), item.Creator)
		})
	}

	repoStatusM := GetRepoStatusM(ctx, codeRepos, req.GetSpaceID())
	if len(req.GetProcessStatus()) > 0 {
		codeRepos = gslice.Filter(codeRepos, func(item *po.NextCodeRepoPO) bool {
			return gslice.Contains(req.GetProcessStatus(), repoStatusM[item.RepoName])
		})
	}

	total := len(codeRepos)
	start := operatorx.IfThen(req.GetPageNum() > 0, int((req.GetPageNum()-1)*req.GetPageSize()), 0)
	end := operatorx.IfThen(req.GetPageSize() > 0, start+int(req.GetPageSize()), total)
	if start >= total {
		return []*nextagent.CodeRepo{}, 0, nil
	}
	if end > total {
		end = total
	}

	res := make([]*nextagent.CodeRepo, end-start)
	eg := errgroup.Group{}
	eg.SetLimit(10)

	for i, cr := range codeRepos[start:end] {
		repo := cr
		index := i
		eg.Go(func() error {
			defer func() {
				if err := recover(); err != nil {
					log.V1.CtxError(ctx, "recover from panic: %v", err)
				}
			}()

			repoInfo, err := s.nextCodeClient.GetRepository(ctx, repository.GetRepositoryRequest{
				Id: lo.ToPtr(repo.RepoID),
			})
			if err != nil {
				logs.V1.CtxError(ctx, "failed to get repository: %v", err)
			}

			codeRepo := &nextagent.CodeRepo{
				RepoID:     repo.RepoID,
				RepoName:   repo.RepoName,
				IsUploaded: true,
			}

			if repoInfo != nil {
				codeRepo.Desc = lo.ToPtr(repoInfo.GetRepository().GetDescription())
				codeRepo.AvatarUrl = lo.ToPtr(repoInfo.GetRepository().GetAvatarURL())
				codeRepo.Url = repoInfo.GetRepository().GetURL()
			}
			res[index] = codeRepo

			return nil
		})
	}

	if err = eg.Wait(); err != nil {
		return nil, 0, err
	}

	for _, r := range res {
		r.ProcessStatus = repoStatusM[r.GetRepoName()]
	}

	return res, int64(total), nil
}

func GetRepoStatusM(ctx context.Context, dbCodeRepos []*po.NextCodeRepoPO, spaceID string) map[string]string {
	graphResp, err := bits_ai_graph.RawCall.ListResourceStatus(ctx, &graph.ListResourceStatusRequest{
		AimeSpaceId: spaceID,
		Scene:       graph.SceneType_Code,
	}, callopt.WithRPCTimeout(15*time.Second), callopt.WithVRegion(env.VREGION_CHINANORTH))
	if err != nil {
		log.V1.CtxError(ctx, "list resource status failed: %v", err)
	}
	log.V1.CtxInfo(ctx, "list resource status: %+v", graphResp.GetResources())

	repoResource := gslice.Filter(graphResp.GetResources(), func(item *graph.ResourceWithStatus) bool {
		return item.GetResourceType() == graph.ResourceType_Code
	})

	repoStatusM := gslice.ToMap(repoResource, func(item *graph.ResourceWithStatus) (string, string) {
		return item.GetCode().GetRepoName(), item.GetStatus()
	})

	for _, r := range dbCodeRepos {
		if _, ok := repoStatusM[r.RepoName]; !ok {
			if time.Since(r.UpdatedAt) > 2*time.Hour {
				repoStatusM[r.RepoName] = nextagent.ProcessStatusFailed
				continue
			}

			repoStatusM[r.RepoName] = nextagent.ProcessStatusProcessing
		}
	}

	return repoStatusM
}

func (s *Service) DeleteCodeRepo(ctx context.Context, req *nextagent.DeleteCodeRepoRequest) error {
	err := s.dao.DeleteCodeRepo(ctx, req.GetSpaceID(), gslice.Map(req.GetCodeRepos(), func(item *nextagent.CodeRepo) string {
		return item.GetRepoID()
	}))
	if err != nil {
		return errors.WithMessage(err, "failed to delete code repo")
	}

	return nil
}

func (s *Service) UploadCodeRepo(ctx context.Context, req *nextagent.UploadCodeRepoRequest, username string) error {
	err := s.dao.CreateCodeRepos(ctx, gslice.Map(req.GetCodeRepos(), func(item *nextagent.CodeRepo) *po.NextCodeRepoPO {
		return &po.NextCodeRepoPO{
			SpaceID:  req.GetSpaceID(),
			Creator:  username,
			RepoName: item.GetRepoName(),
			RepoID:   item.GetRepoID(),
		}
	}))
	if err != nil {
		return errors.WithMessage(err, "failed to create code repo")
	}

	return nil
}
