package platform_config

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/lang/gg/gslice"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"
	"gorm.io/datatypes"
)

type Service struct {
	dao *dal.DAO
}

type CreateServiceOption struct {
	fx.In

	DAO *dal.DAO
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		dao: opt.DAO,
	}

	return s, nil
}

func (s *Service) CreatePlatformConfig(ctx context.Context, req *nextagent.InitSpaceRequest, username string) error {
	config, err := s.dao.GetPlatformConfig(ctx, req.GetSpaceID())
	if err != nil {
		return errors.WithMessage(err, "failed to get platform config")
	}

	if config == nil {
		err = s.dao.CreatePlatformConfig(ctx, &po.NextPlatformConfigPO{
			SpaceID: req.GetSpaceID(),
			Creator: username,
			MeegoSpaceList: lo.ToPtr(datatypes.NewJSONType(gslice.Map(req.GetPlatformConfig().GetMeegoSpaceList(), func(ms *nextagent.MeegoSpace) *entity.MeegoSpace {
				return &entity.MeegoSpace{
					SpaceID:    ms.GetSpaceID(),
					SpaceName:  ms.GetSpaceName(),
					SimpleName: ms.GetSimpleName(),
				}
			}))),
		})
		if err != nil {
			return errors.WithMessage(err, "failed to create platform config")
		}
	}

	return nil
}

func (s *Service) GetPlatformConfig(ctx context.Context, spaceID string) (*nextagent.PlatformConfig, error) {
	dbPlatformConfig, err := s.dao.GetPlatformConfig(ctx, spaceID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get platform config")
	}

	if dbPlatformConfig == nil {
		return &nextagent.PlatformConfig{}, nil
	}

	if dbPlatformConfig.MeegoSpaceList == nil {
		return &nextagent.PlatformConfig{
			MeegoSpaceList: []*nextagent.MeegoSpace{},
		}, nil
	}

	return &nextagent.PlatformConfig{
		MeegoSpaceList: gslice.Map(dbPlatformConfig.MeegoSpaceList.Data(), func(ms *entity.MeegoSpace) *nextagent.MeegoSpace {
			return &nextagent.MeegoSpace{
				SpaceName:  ms.SpaceName,
				SimpleName: ms.SimpleName,
				Url:        genMeegoSpaceUrl(ms.SimpleName),
				IsUploaded: false,
			}
		}),
	}, nil
}

func (s *Service) UpdatePlatformConfig(ctx context.Context, req *nextagent.UpdatePlatformConfigRequest, username string) error {
	meegoSpaceList, err := s.getMeegoSpaceList(ctx, req.GetSpaceID(), req.GetOperateType(), req.GetMeegoSpaces())
	if err != nil {
		return err
	}

	err = s.dao.CreateOrUpdatePlatformConfig(ctx, req.GetSpaceID(), meegoSpaceList, username)
	if err != nil {
		return errors.WithMessage(err, "failed to update platform config")
	}

	return nil
}

func (s *Service) getMeegoSpaceList(ctx context.Context, spaceID string, operateType nextagent.OperateType, meegoSpaceList []*nextagent.MeegoSpace) ([]*entity.MeegoSpace, error) {
	oldPlatformConfig, err := s.dao.GetPlatformConfig(ctx, spaceID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get platform config")
	}

	var res []*entity.MeegoSpace
	entityMeegoSpaceList := gslice.Map(meegoSpaceList, func(ms *nextagent.MeegoSpace) *entity.MeegoSpace {
		return &entity.MeegoSpace{
			SpaceID:    ms.GetSpaceID(),
			SpaceName:  ms.GetSpaceName(),
			SimpleName: ms.GetSimpleName(),
		}
	})

	switch operateType {
	case nextagent.OperateTypeAdd:
		if oldPlatformConfig != nil {
			res = append(res, oldPlatformConfig.MeegoSpaceList.Data()...)
			res = append(res, entityMeegoSpaceList...)

			return gslice.Uniq(res), nil
		}

		return gslice.Uniq(entityMeegoSpaceList), nil
	case nextagent.OperateTypeDelete:
		if oldPlatformConfig != nil {
			existM := gslice.ToMap(entityMeegoSpaceList, func(ms *entity.MeegoSpace) (string, bool) {
				return ms.SimpleName, true
			})

			res = gslice.Filter(oldPlatformConfig.MeegoSpaceList.Data(), func(ms *entity.MeegoSpace) bool {
				if existM[ms.SimpleName] {
					return false
				}

				return true
			})

			return res, nil
		}

		return nil, errors.New("delete empty platform config")
	default:
		return nil, errors.New("invalid operate type")
	}
}
