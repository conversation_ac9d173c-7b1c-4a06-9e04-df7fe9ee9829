package platform_config

import (
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kite/kitex/client/callopt"
	"code.byted.org/overpass/bytedance_bits_feature/kitex_gen/bytedance/bits/feature"
	"code.byted.org/overpass/bytedance_bits_feature/rpc/bytedance_bits_feature"
)

func (s *Service) SearchMeegoSpace(ctx context.Context, req *nextagent.SearchMeegoSpaceRequest, username string) ([]*nextagent.MeegoSpace, error) {
	var res []*nextagent.MeegoSpace
	resp, err := bytedance_bits_feature.RawCall.GetFeatureMeegoProjects(ctx, &feature.GetFeatureMeegoProjectsQuery{
		UserEmail: genUserEmail(username),
	}, callopt.WithRPCTimeout(10*time.Second), callopt.WithVRegion(env.VREGION_CHINANORTH))
	if err != nil {
		log.V1.CtxError(ctx, "[SearchMeegoSpace] failed to search meego space: %v", err)
		return nil, err
	}

	existMeegoSpaceM := make(map[string]bool)
	platformConfig, err := s.dao.GetPlatformConfig(ctx, req.SpaceID)
	if err != nil {
		return nil, err
	}
	if platformConfig != nil && len(platformConfig.MeegoSpaceList.Data()) > 0 {
		for _, meegoSpace := range platformConfig.MeegoSpaceList.Data() {
			existMeegoSpaceM[meegoSpace.SimpleName] = true
		}
	}

	simpleName, ok := extractMeegoSpaceSimpleName(req.GetQuery())
	for _, project := range resp.GetList() {
		if !ok && strings.Contains(strings.ToLower(project.Title), strings.ToLower(req.GetQuery())) {
			res = append(res, &nextagent.MeegoSpace{
				SpaceID:    project.GetProjectID(),
				SpaceName:  project.GetTitle(),
				SimpleName: project.GetName(),
				Url:        genMeegoSpaceUrl(project.GetName()),
				IsUploaded: existMeegoSpaceM[project.GetName()],
			})
		} else if ok && project.GetName() == simpleName {
			res = append(res, &nextagent.MeegoSpace{
				SpaceID:    project.GetProjectID(),
				SpaceName:  project.GetTitle(),
				SimpleName: simpleName,
				Url:        genMeegoSpaceUrl(project.GetName()),
				IsUploaded: existMeegoSpaceM[project.GetName()],
			})
		}
	}

	return res, nil
}

func extractMeegoSpaceSimpleName(url string) (string, bool) {
	after, ok := strings.CutPrefix(url, "https://meego.larkoffice.com/")
	if !ok {
		return "", false
	}

	return strings.Split(after, "/")[0], true
}

func genMeegoSpaceUrl(simpleName string) string {
	return fmt.Sprintf("https://meego.larkoffice.com/%s", simpleName)
}

func genUserEmail(username string) string {
	return fmt.Sprintf("%<EMAIL>", username)
}
