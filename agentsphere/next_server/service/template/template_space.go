package template

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
)

type ListSpaceTemplatesOptions struct {
	SpaceID  string
	Category *string
	Search   *string
	Source   entity.TemplateSource
	User     *authentity.Account
	Label    *string
	Limit    int64
	StartID  *int64
}

// ListSpaceTemplates 获取指定空间下的模板列表
func (s *Service) ListSpaceTemplates(ctx context.Context, opt ListSpaceTemplatesOptions) ([]*entity.TemplateVersion, int64, error) {
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.User.Username)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to get space id")
	}
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: spaceID, Sync: true})
	if err != nil {
		return nil, 0, err
	}
	// 参数校验
	var limit = defaultPageSize
	if opt.Limit > 0 {
		limit = int(opt.Limit)
	}

	// 构建查询条件
	var (
		options                                     = dal.ListTemplateVersionsOptions{}
		starredTemplateIDsMap, sharedTemplateIDsMap map[string]bool
		username                                    = opt.User.Username
	)
	// 支持按模板source过滤
	switch opt.Source {
	case entity.TemplateSourceMy: // 我创建的模板
		options.Condition = fmt.Sprintf("(creator = '%s' AND source_space_id = '%s')", username, spaceID)
		starredTemplateIDs, err := s.dao.ListStarTemplateIDsByUsernameAndSpaceID(ctx, username, spaceID)
		if err != nil {
			log.V1.CtxError(ctx, "[ListSpaceTemplates] failed to list starred template ids: %v", err)
		}
		starredTemplateIDsMap = make(map[string]bool)
		for _, id := range starredTemplateIDs {
			starredTemplateIDsMap[id] = true
		}
	case entity.TemplateSourceStar: // 我收藏的模板
		starredTemplateIDs, sharedTemplateIDs := s.getStarredAndSharedTemplateIDsWithSpace(ctx, opt.User, space)
		if len(starredTemplateIDs) == 0 {
			return nil, 0, nil
		}
		starredTemplateIDsMap = make(map[string]bool)
		for _, id := range starredTemplateIDs {
			starredTemplateIDsMap[id] = true
		}
		if len(sharedTemplateIDs) > 0 {
			sharedTemplateIDsMap = make(map[string]bool)
			for _, id := range sharedTemplateIDs {
				sharedTemplateIDsMap[id] = true
			}
		}
		templateIDsStr := strings.Join(lo.Map(starredTemplateIDs, func(id string, _ int) string {
			return "'" + id + "'"
		}), ",")
		options.Condition = fmt.Sprintf("template_id IN (%s)", templateIDsStr)
	case entity.TemplateSourceProject:
		if !space.Type.IsProject() {
			return nil, 0, nil // 非项目空间，返回0
		}
		options.Condition = fmt.Sprintf("(creator = '%s' OR scope = '%s') AND source_space_id = '%s'", username, entity.TemplateScopeProjectPublic, spaceID)
		starredTemplateIDs, err := s.dao.ListStarTemplateIDsByUsernameAndSpaceID(ctx, username, spaceID)
		if err != nil {
			log.V1.CtxError(ctx, "[ListSpaceTemplates] failed to list starred template ids: %v", err)
		}
		starredTemplateIDsMap = make(map[string]bool)
		for _, id := range starredTemplateIDs {
			starredTemplateIDsMap[id] = true
		}
	default: // 全部模板，包括公共模板、官方模板、自己在当前空间下创建的模板、分享给自己的模板和自己收藏的模板
		var withoutPublic bool
		if opt.Source == entity.TemplateSourceProject { // 项目内模板
			withoutPublic = true
		}
		starredTemplateIDs, sharedTemplateIDs := s.getStarredAndSharedTemplateIDsWithSpace(ctx, opt.User, space)
		var starredAndSharedTemplateIDs []string
		if len(starredTemplateIDs) > 0 {
			starredAndSharedTemplateIDs = append(starredAndSharedTemplateIDs, starredTemplateIDs...)
			starredTemplateIDsMap = make(map[string]bool)
			for _, id := range starredTemplateIDs {
				starredTemplateIDsMap[id] = true
			}
		}
		if len(sharedTemplateIDs) > 0 {
			starredAndSharedTemplateIDs = append(starredAndSharedTemplateIDs, sharedTemplateIDs...)
			sharedTemplateIDsMap = make(map[string]bool)
			for _, id := range sharedTemplateIDs {
				sharedTemplateIDsMap[id] = true
			}
		}
		if len(starredAndSharedTemplateIDs) > 0 {
			templateIDsStr := strings.Join(lo.Map(lo.Uniq(starredAndSharedTemplateIDs), func(id string, _ int) string {
				return "'" + id + "'"
			}), ",")
			if !withoutPublic { // 包含公共模板和官方模板
				options.Condition = fmt.Sprintf("(scope = '%s' OR scope = '%s' OR (creator = '%s' AND source_space_id = '%s') OR template_id IN (%s))",
					entity.TemplateScopePublic, entity.TemplateScopeOfficial, username, spaceID, templateIDsStr)
			} else {
				options.Condition = fmt.Sprintf("((creator = '%s' AND source_space_id = '%s') OR template_id IN (%s))", username, spaceID, templateIDsStr)
			}
		} else {
			if !withoutPublic { // 包含公共模板和官方模板
				options.Condition = fmt.Sprintf("(scope = '%s' OR scope = '%s' OR (creator = '%s' AND source_space_id = '%s'))",
					entity.TemplateScopePublic, entity.TemplateScopeOfficial, username, spaceID)
			} else {
				options.Condition = fmt.Sprintf("(creator = '%s' AND source_space_id = '%s')", username, spaceID)
			}
		}
	}
	if opt.Category != nil {
		options.Category = *opt.Category
	}
	if opt.Search != nil {
		options.Search = *opt.Search
	}
	if opt.Label != nil {
		options.Label = *opt.Label
	}
	options.Statuses = []string{entity.TemplateStatusGenerating, entity.TemplateStatusAvailable}

	// 查询模板列表部分字段
	partials, err := s.dao.ListPartialTemplateVersions(ctx, options)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to list partial template version")
	}
	// 对模板列表排序和分页
	var (
		templates []*entity.TemplateVersion
		nextID    int64
	)
	partialTemplateIDs := partials.GetTemplateIDsByOffsetLimit(0, limit, lo.FromPtr(opt.StartID))
	if len(partialTemplateIDs) > 0 {
		// 查询 templates 详情并按照 ids 排序
		templates, err = s.dao.ListTemplateVersions(ctx, dal.ListTemplateVersionsOptions{TemplateIDs: partialTemplateIDs})
		if err != nil {
			return nil, 0, errors.WithMessage(err, "failed to list template versions")
		}
		// 在内存中对模板排序
		templateMap := make(map[string]*entity.TemplateVersion)
		for _, template := range templates {
			templateMap[template.TemplateID] = template
		}
		var orderedTemplates []*entity.TemplateVersion
		for _, id := range partialTemplateIDs {
			if template, ok := templateMap[id]; ok {
				orderedTemplates = append(orderedTemplates, template)
			}
		}
		templates = orderedTemplates
		if len(templates) > 0 && len(templates) == limit {
			nextID = templates[len(templates)-1].ID
		}
	}

	return lo.Map(templates, func(item *entity.TemplateVersion, _ int) *entity.TemplateVersion {
		if _, ok := starredTemplateIDsMap[item.TemplateID]; ok {
			item.Starred = lo.ToPtr(true)
		}
		if _, ok := sharedTemplateIDsMap[item.TemplateID]; ok && item.Scope == entity.TemplateScopePrivate && space.Type.IsPersonal() {
			item.Scope = entity.TemplateScopeShared
		}
		return item
	}), nextID, nil
}

type CountSpaceTemplatesOptions struct {
	Source  entity.TemplateSource
	User    *authentity.Account
	SpaceID string
}

func (s *Service) CountSpaceTemplates(ctx context.Context, opt CountSpaceTemplatesOptions) (int64, error) {
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.User.Username)
	if err != nil {
		return 0, errors.WithMessage(err, "failed to get space id")
	}
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: spaceID})
	if err != nil {
		return 0, err
	}
	// 构建查询条件
	var (
		options  = dal.ListTemplateVersionsOptions{}
		username = opt.User.Username
	)
	// 支持按模板source过滤
	switch opt.Source {
	case entity.TemplateSourceMy: // 我创建的模板
		options.Condition = fmt.Sprintf("(creator = '%s' AND source_space_id = '%s')", username, spaceID)
	case entity.TemplateSourceStar: // 收藏模板
		return s.dao.CountTemplateStarsByUsernameAndSpaceID(ctx, username, spaceID)
	case entity.TemplateSourceProject:
		if !space.Type.IsProject() {
			return 0, nil // 非项目空间，返回0
		}
		options.Condition = fmt.Sprintf("(creator = '%s' OR scope = '%s') AND source_space_id = '%s'", username, entity.TemplateScopeProjectPublic, spaceID)
	default: // 全部模板，包括公共模板、官方模板、自己在当前空间下创建的模板、分享给自己的模板和自己收藏的模板
		var withoutPublic bool
		if opt.Source == entity.TemplateSourceProject { // 项目内模板
			withoutPublic = true
		}
		starredTemplateIDs, sharedTemplateIDs := s.getStarredAndSharedTemplateIDsWithSpace(ctx, opt.User, space)
		var starredAndSharedTemplateIDs []string
		if len(starredTemplateIDs) > 0 {
			starredAndSharedTemplateIDs = append(starredAndSharedTemplateIDs, starredTemplateIDs...)
		}
		if len(sharedTemplateIDs) > 0 {
			starredAndSharedTemplateIDs = append(starredAndSharedTemplateIDs, sharedTemplateIDs...)
		}
		if len(starredAndSharedTemplateIDs) > 0 {
			templateIDsStr := strings.Join(lo.Map(lo.Uniq(starredAndSharedTemplateIDs), func(id string, _ int) string {
				return "'" + id + "'"
			}), ",")
			if !withoutPublic { // 包含公共模板和官方模板
				options.Condition = fmt.Sprintf("(scope = '%s' OR scope = '%s' OR (creator = '%s' AND source_space_id = '%s') OR template_id IN (%s))",
					entity.TemplateScopePublic, entity.TemplateScopeOfficial, username, spaceID, templateIDsStr)
			} else {
				options.Condition = fmt.Sprintf("((creator = '%s' AND source_space_id = '%s') OR template_id IN (%s))", username, spaceID, templateIDsStr)
			}
		} else {
			if !withoutPublic { // 包含公共模板和官方模板
				options.Condition = fmt.Sprintf("(scope = '%s' OR scope = '%s' OR (creator = '%s' AND source_space_id = '%s'))",
					entity.TemplateScopePublic, entity.TemplateScopeOfficial, username, spaceID)
			} else {
				options.Condition = fmt.Sprintf("(creator = '%s' AND source_space_id = '%s')", username, spaceID)
			}
		}
	}
	options.Statuses = []string{entity.TemplateStatusGenerating, entity.TemplateStatusAvailable}
	// 查询总数
	total, err := s.dao.CountTemplateVersions(ctx, options)
	if err != nil {
		return 0, errors.WithMessage(err, "count template versions failed")
	}
	return total, nil
}

func (s *Service) getStarredAndSharedTemplateIDsWithSpace(ctx context.Context, user *authentity.Account, space *entity.Space) ([]string, []string) {
	if space == nil {
		return nil, nil
	}
	// 获取自己收藏的模板ID列表
	starredTemplateIDs, err := s.dao.ListStarTemplateIDsByUsernameAndSpaceID(ctx, user.Username, space.ID)
	if err != nil {
		log.V1.CtxError(ctx, "[getStarredAndSharedTemplateIDsWithSpace] failed to list starred template ids: %v", err)
		// 不返回错误，继续执行，忽略收藏模板
	}
	// 个人空间内，直接从分享表中获取分享给用户的模板ID列表
	if space.Type.IsPersonal() {
		// 获取分享给用户的模板ID列表
		sharedTemplateIDs, err := s.dao.ListSharedTemplateIDsByTargetUsername(ctx, user.Username)
		if err != nil {
			log.V1.CtxError(ctx, "[getStarredAndSharedTemplateIDsWithSpace] failed to list shared template ids: %v", err)
			// 不返回错误，继续执行，忽略分享模板
		}
		return starredTemplateIDs, sharedTemplateIDs
	}
	// 项目空间内，从权限表中获取该用户有权限访问的模板ID列表
	resources, err := s.permissionService.GetUserPermissions(ctx, permissionservice.GetUserPermissionsOption{
		Account:      lo.FromPtr(user),
		Roles:        nil,
		Actions:      nil,
		ResourceType: entity.ResourceTypeTemplate,
		GroupID:      &space.ID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[getStarredAndSharedTemplateIDsWithSpace] failed to get user permission resources: %v", err)
		// 不返回错误，继续执行，忽略分享模板
	}
	var sharedTemplateIDs []string // 过滤出不是自己创建的模板
	for _, resource := range resources {
		if resource.Owner == user.Username {
			continue
		}
		sharedTemplateIDs = append(sharedTemplateIDs, resource.ExternalID)
	}
	return starredTemplateIDs, sharedTemplateIDs
}
