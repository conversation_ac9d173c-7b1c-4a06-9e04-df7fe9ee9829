package testing

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"github.com/pkg/errors"
)

type RecycleContainerEvent struct {
	SessionID   string `json:"session_id"`
	ContainerID string `json:"container_id"`
}

func (s *Service) sendRecycleContainerEvent(ctx context.Context, sessionID string, containerID string) error {
	marshal, err := json.Marshal(RecycleContainerEvent{
		SessionID:   sessionID,
		ContainerID: containerID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to marshal event")
	}
	return s.mq.SendDelayedMessage(ctx, marshal, time.Hour*24*2, entity.TestingMonitorTag)
}

func (s *Service) HandleTestingMonitorEvent(ctx context.Context, e *RecycleContainerEvent) error {
	return s.DeleteRuntime(ctx, e.SessionID)
}
