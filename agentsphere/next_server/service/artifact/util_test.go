package artifact

import (
	"io"
	"strings"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"gotest.tools/v3/assert"
)

func TestService_ReplaceCiteTagsByLine(t *testing.T) {
	text := `### 1. 2025年五一假期基本情况
*   **放假时间：** 2025年5月1日（星期四）至5月5日（星期一），共计5天::cite[1]::cite[2]::cite[3]::cite[4]::cite[5]::cite[6]::cite[7]::cite[8]::cite[9]::cite[10]::cite[11]::cite[12]。
*   **调休安排：** 4月27日（星期日）需上班::cite[1]::cite[2]::cite[4]::cite[13]::cite[7]::cite[8]::cite[9]::cite[10]::cite[11]::cite[12]::cite[14]。
*   **法定假日增加：** 自2025年起，劳动节法定假日由1天增至2天（5月1日、5月2日）::cite[1]::cite[4]::cite[13]::cite[15]::cite[16]::cite[7]::cite[9]。
*   **高速免费通行：** 5月1日0时至5月5日24时，7座及以下小型客车享受全国收费公路免费通行政策::cite[1]::cite[6]::cite[17]::cite[18]::cite[19]::cite[11]::cite[20]::cite[21]::cite[22]::cite[23]::cite[24]。`
	referenceMap := make(map[int32]string)
	for i := 1; i <= 30; i++ {
		referenceMap[int32(i)] = "https://test.url"
	}
	s := &Service{}
	r, _, err := s.ReplaceCiteTagsByLine(strings.NewReader(text), &entity.FileMeta{
		Size: int64(len(text)),
	}, referenceMap)
	assert.NilError(t, err)
	res, err := io.ReadAll(r)
	assert.NilError(t, err)
	t.Log(string(res))
	assert.Assert(t, !strings.Contains(string(res), "::cite"))
	assert.Assert(t, strings.Contains(string(res), "https://test.url"))
}
