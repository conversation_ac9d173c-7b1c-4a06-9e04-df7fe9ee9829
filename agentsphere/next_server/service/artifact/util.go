package artifact

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"io"
	"net/url"
	"path/filepath"
	"regexp"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/util"
	"github.com/go-enry/go-enry/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

var documentExts = []string{"md", "txt", "docx", "csv", "xls", "xlsx", "pdf", "zip", "tar.gz", "tar.bz2", "bz2", "gz", "rar", "tar", "prof", "ppt", "pptx"}

// IsDocumentFile 区分文件是否是文档类型，二进制 或者 上面后缀的都放入文档编辑器
func IsDocumentFile(filePath string, content []byte) bool {
	ext := strings.TrimPrefix(filepath.Ext(filePath), ".")
	return lo.Contains(documentExts, ext) || enry.IsBinary(content)
}

func (s *Service) GenerateLinkArtifactKey(source entity.LinkArtifactKeySource, sourceID string) string {
	if source == entity.LinkArtifactKeySourceDeployment || source == entity.LinkArtifactKeySourceLarkDoc ||
		source == entity.LinkArtifactKeySourceLarkSheet || source == entity.LinkArtifactKeySourceURL {
		return fmt.Sprintf("%s-%s", source.String(), sourceID)
	}
	return s.idGen.NewID()
}

// GetReferenceMap 根据 reference 事件生成 reference map
func (s *Service) GetReferenceMap(ctx context.Context, sessionID string) (map[int32]string, error) {
	events, err := s.dao.GetEventsBySession(ctx, sessionID, dal.GetEventsBySessionOption{
		EventName: nextagent.EventNameReference,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get events")
	}
	referenceMap := make(map[int32]string)
	for _, event := range events {
		var (
			d    = conv.DefaultAny[map[string]any](event.EventData.Data)
			e, _ = conv.MapToStructByJSONTag[*nextagent.ReferenceEvent](d)
		)
		if e == nil {
			continue
		}
		for _, item := range e.References {
			if item == nil {
				continue
			}
			if _, ok := referenceMap[item.ID]; ok {
				continue
			}
			referenceMap[item.ID] = item.URI
		}
	}
	return referenceMap, nil
}

const (
	MaxCiteReplaceBufSize = 10 * 1024 * 1024 // 10MB

	citePattern       = `::cite\[(\d+)\]`
	maxScannerBufSize = 10 * 1024 * 1024 // 10MB
)

// ReplaceCiteTagsByLine 将 markdown 文档里的 ::cite[23] 替换为 [23](http://xxx) 的通用形式，并忽略报错
func (s *Service) ReplaceCiteTagsByLine(r io.Reader, meta *entity.FileMeta, referenceMap map[int32]string) (reader io.Reader, size int, err error) {
	if len(referenceMap) == 0 {
		return nil, 0, errors.New("no reference")
	}
	if meta != nil && meta.Size > MaxCiteReplaceBufSize {
		return nil, 0, errors.New("file size exceeded")
	}
	var (
		buf        = bytes.NewBuffer(make([]byte, 0, MaxCiteReplaceBufSize))
		scanner    = bufio.NewScanner(r)
		scannerBuf = make([]byte, 0, maxScannerBufSize)
	)
	scanner.Buffer(scannerBuf, maxScannerBufSize) // 设置一个较大的scannerBuf，避免一些超长的line导致scanner.Scan失效

	defer func() {
		if reader == nil {
			return
		}
		// 关闭旧的 reader
		if closer, ok := r.(io.Closer); ok {
			closer.Close()
		}
	}()
	re, err := regexp.Compile(citePattern)
	if err != nil {
		return nil, 0, errors.Wrap(err, "failed to compile regex")
	}
	replace := func(line string) string {
		return re.ReplaceAllStringFunc(line, func(match string) string {
			// 提取序号
			subMatches := re.FindStringSubmatch(match)
			if len(subMatches) < 2 {
				return match
			}
			num := subMatches[1]
			if uri, ok := referenceMap[int32(util.FromString(num))]; ok {
				return fmt.Sprintf("[[%s]](%s)", num, uri)
			}
			return match
		})
	}
	for scanner.Scan() {
		line := scanner.Text()
		if strings.TrimSpace(line) != "" {
			line = replace(line)
		}
		if buf.Len()+len(line)+1 > MaxCiteReplaceBufSize {
			// 超过缓冲区大小
			return nil, 0, errors.New("buffer size exceeded")
		}
		_, err = buf.WriteString(line)
		if err != nil {
			return nil, 0, errors.Wrap(err, "failed to write to buffer")
		}
		err = buf.WriteByte('\n')
		if err != nil {
			return nil, 0, errors.Wrap(err, "failed to write to buffer")
		}
	}
	if err = scanner.Err(); err != nil {
		return nil, 0, errors.Wrap(err, "failed to scan line")
	}

	reader = buf
	size = buf.Len()
	return
}

func GetArtifactTypeAndSubType(filePath string, content []byte, linkSource entity.LinkArtifactKeySource) (entity.ArtifactType, string) {
	if linkSource != "" { // 如果是链接类型的，直接将 link source 作为 sub type 返回
		return entity.ArtifactTypeLink, linkSource.String()
	}
	var (
		artifactType entity.ArtifactType
		subType      string
	)
	// 判断是不是图片
	var (
		ext      = strings.ToLower(strings.TrimPrefix(filepath.Ext(filePath), "."))
		language = enry.GetLanguage(filePath, content)
	)
	if lo.Contains(imageExts, ext) { //TODO: 后续可以根据图片文件头增加判断准确性
		subType = ext
		artifactType = entity.ArtifactTypeImage
	} else if IsDocumentFile(filePath, content) {
		subType = ext
		artifactType = entity.ArtifactTypeFile
	} else if language != "" {
		subType = language
		artifactType = entity.ArtifactTypeCode
	} else { // 默认用 Other 类型
		subType = ext
		artifactType = entity.ArtifactTypeOther
	}
	return artifactType, subType
}

// isHTTPURL 函数用于判断输入的字符串是否为HTTP链接
func isHTTPURL(str string) bool {
	u, err := url.Parse(str)
	if err != nil {
		return false
	}
	return (u.Scheme == "http" || u.Scheme == "https") && u.Host != ""
}

type ReaderWithCancel struct {
	io.Reader
	cancel context.CancelFunc
}

func NewReaderWithCancel(reader io.Reader, cancel context.CancelFunc) *ReaderWithCancel {
	return &ReaderWithCancel{
		Reader: reader,
		cancel: cancel,
	}
}

func (r *ReaderWithCancel) Close() error {
	if closer, ok := r.Reader.(io.Closer); ok {
		closer.Close()
	}
	r.cancel()
	return nil
}
