package eval

import (
	"regexp"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

var larkCitationReg = regexp.MustCompile(`\[(\d+)\]`)

func CheckLarkDocument(t runtime.Context, session *runtime.Session) {
	artifacts, err := session.GetArtifacts()
	require.Nil(t, err, "获取产物失败")
	_, b := lo.Find(artifacts, func(artifact *nextagent.Artifact) bool {
		return artifact.Type == nextagent.ArtifactTypeLink && (strings.HasPrefix(artifact.FileMetas[0].LinkContent.URL, "https://bytedance.feishu.cn/docx/") || strings.HasPrefix(artifact.FileMetas[0].LinkContent.URL, "https://bytedance.larkoffice.com/docx/"))
	})
	require.True(t, b, "在产物中，未找到飞书文档")
}

func CheckLarkCitation(t runtime.Context, session *runtime.Session) {
	artifacts, err := session.GetArtifacts()
	assert.Nil(t, err)
	artifact, b := lo.Find(artifacts, func(artifact *nextagent.Artifact) bool {
		return artifact.Type == nextagent.ArtifactTypeLink && (strings.HasPrefix(artifact.FileMetas[0].LinkContent.URL, "https://bytedance.feishu.cn/docx/") || strings.HasPrefix(artifact.FileMetas[0].LinkContent.URL, "https://bytedance.larkoffice.com/docx/"))
	})
	require.True(t, b, "在产物中，未找到飞书文档")
	blocks := t.ReadLarkDocs(t, artifact.FileMetas[0].LinkContent.URL)
	textBlocks := lo.Filter(blocks, func(item *larkdocx.Block, index int) bool {
		return item.Text != nil
	})
	var result [][2]string
	for _, block := range textBlocks {
		for _, textElement := range block.Text.Elements {
			if larkCitationReg.MatchString(lo.FromPtr(textElement.TextRun.Content)) && textElement.TextRun.TextElementStyle.Link != nil {
				result = append(result, [2]string{
					lo.FromPtr(textElement.TextRun.Content),
					lo.FromPtr(textElement.TextRun.TextElementStyle.Link.Url),
				})
			}
		}
	}
	assert.True(t, len(result) > 0, "在飞书文档中，未找到引用")
}
