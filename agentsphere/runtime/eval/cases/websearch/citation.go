package websearch

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/eval"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

func init() {
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name:      "citation",
		Message:   `帮我收集一下2025年3.15央视晚会曝光商品,要求有商品名称,商品问题`,
		Role:      nextagent.SessionRole_IndustryVeteran,
		Assertion: eval.CheckLarkCitation})
}
