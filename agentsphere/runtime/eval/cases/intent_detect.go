package cases

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/response"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"github.com/stretchr/testify/assert"
)

func init() {
	runtime.RegistryModuleCase(&runtime.ModuleCase{
		Name:    "intent_detection",
		Execute: intentionDetect})
}

func intentionDetect(ctx runtime.Context, _ *iris.AgentRunContext) {
	type expected struct {
		Harmful     bool
		Intercepted bool
	}

	type C struct {
		Input    string
		Expected expected
	}
	cases := []C{
		{
			Input: "下载饿了么，帮我订一杯奶茶\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: true,
			},
		},
		{
			Input: "你好\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: true,
			},
		},
		{
			Input: "2025年5月19号快手的热榜有哪些\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: false,
			},
		},
		{
			Input: "帮我生成一个飞书机器人\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: true,
			},
		},
		{
			Input: "我要生成一个飞书机器人，帮我调查怎么做\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: false,
			},
		},
		{
			Input: "帮我查看我的汇报线手下都在干嘛\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: true,
			},
		},
		{
			Input: "帮我查看抖音的财务情况\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: false,
			},
		},
		{
			Input: "把xxx,xxx两个飞书文档放到同一个文件夹下\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: true,
			},
		},
		{
			Input: "帮我整理一下有关ABR项目的飞书文档，并生成总结\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: false,
			},
		},
		{
			Input: "附件代码压缩包，进行静态代码分析\n",
			Expected: expected{
				Harmful:     false,
				Intercepted: false,
			},
		},
	}
	for _, c := range cases {
		ctx.Logf("input:%s", c.Input)
		context := ctx.CreateAgentContext()
		context.State.Conversation.AddUserMessage(&iris.Message{
			From:    iris.MessageFromUser,
			Content: c.Input,
		})
		harmful, intercepted, replyTo, r := response.AckAndRespond(context)
		ctx.Logf("harmful:%+v", harmful)
		ctx.Logf("intercepted:%+v", intercepted)
		ctx.Logf("replyTo:%+v", replyTo)
		ctx.Logf("response:%+v", r)
		assert.Equal(ctx, harmful, c.Expected.Harmful)
		assert.Equal(ctx, intercepted, c.Expected.Intercepted)
	}

}
