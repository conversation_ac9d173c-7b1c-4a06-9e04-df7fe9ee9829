package cases

import (
	"fmt"
	"regexp"
	"strconv"

	_ "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/dynamic"
	websearchactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	_ "code.byted.org/devgpt/kiwis/agentsphere/runtime/eval/cases/lark"
	_ "code.byted.org/devgpt/kiwis/agentsphere/runtime/eval/cases/websearch"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func init() {
	runtime.RegistryModuleCase(&runtime.ModuleCase{
		Name: "panic",
		Execute: func(ctx runtime.Context, runContext *iris.AgentRunContext) {
			panic("panic")
		}})
	runtime.RegistryModuleCase(&runtime.ModuleCase{
		Name: "fail",
		Execute: func(ctx runtime.Context, runContext *iris.AgentRunContext) {
			require.Fail(ctx, "fail here")
			ctx.Log("should not reach here")
		}})
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name:    "simple_websearch_e2e_test",
		Message: "搜索一下美国在哪个大洲",
		Role:    nextagent.SessionRole_IndustryVeteran,
		Assertion: func(ctx runtime.Context, session *runtime.Session) {
			err := session.WaitCompleted()
			require.Nil(ctx, err)
			artifacts, err := session.GetArtifacts()
			assert.Nil(ctx, err)
			ctx.Logf("%+v", artifacts)
			artifact, find := lo.Find(artifacts, func(artifact *nextagent.Artifact) bool {
				_, find := lo.Find(artifact.FileMetas, func(fileMeta *nextagent.FileMeta) bool {
					return fileMeta.Type == "file" && fileMeta.SubType == "md"
				})
				return find
			})
			assert.True(ctx, find)
			files, err := session.API.RetrieveArtifactFiles(ctx, &agentsphere.RetrieveArtifactFilesRequest{
				ID:    artifact.ID,
				Files: []string{artifact.FileMetas[0].Name},
			})
			assert.Nil(ctx, err)
			fileContent := files.Files[0].Content
			ctx.Log(fileContent)
			check := ctx.GroundTruthCheck(fileContent, `1. 美国核心领土位于北美洲`)
			ctx.Logf("%+v", check)
			assert.True(ctx, check.Score >= 8)
			return
		}})
	runtime.RegistryModuleCase(&runtime.ModuleCase{
		Name: "quick_research_tool",
		Execute: func(ctx runtime.Context, agentContext *iris.AgentRunContext) {
			ctx.Log("run quick research tool")
			agent := websearchactor.New(websearchactor.CreateOption{
				EnableBytedSearch: true,
			})
			researchResp, err := agent.InternalSimpleAIResearch(agentContext, "对ChatGPT进行全面深入的调研，包括其发展历史、技术架构、市场影响、应用场景、局限性和未来发展趋势，确保信息全面、准确、客观，并收集最新的相关数据和研究成果。",
				[]string{"ChatGPT的发展历史、版本迭代和核心功能特性", "ChatGPT的技术架构、模型规模、训练方法和关键技术突破", "ChatGPT的用户规模、商业模式、市场份额和对AI行业的影响", "ChatGPT在各行业的应用场景和成功案例分析", "ChatGPT的技术局限、伦理问题、安全隐患和未来发展趋势"})
			require.Nil(ctx, err)
			ctx.Log(fmt.Sprintf("test case response: %+v", researchResp))
			// Check citation
			citations := getAllCitation(ctx, researchResp.FinalReport)
			// All citations should be valid
			assert.True(ctx, len(citations) > 0)
		},
	})
}

func getAllCitation(ctx runtime.Context, output string) [][]int {
	re := regexp.MustCompile(`::cite\[(\d+)\]`)
	matches := re.FindAllStringSubmatchIndex(output, -1)
	var result [][]int
	var current []int
	var lastEnd int

	for _, match := range matches {
		numStr := output[match[2]:match[3]]
		num, err := strconv.Atoi(numStr)
		assert.Nil(ctx, err)
		if len(current) == 0 || match[0] == lastEnd {
			current = append(current, num)
		} else {
			result = append(result, current)
			current = []int{num}
		}
		lastEnd = match[1]
	}
	if len(current) > 0 {
		result = append(result, current)
	}
	return result
}
