import argparse
import os
import subprocess
import sys
import time
import uuid
import webbrowser

import requests

root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))


def run_cmd(cmd, env=None, check=True):
    print(f"执行命令: {cmd}")
    result = subprocess.run(cmd, shell=True, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    print(result.stdout)
    if result.returncode != 0:
        print(result.stderr)
        if check:
            sys.exit(result.returncode)
    return result


image_id = '00000000-0000-0000-0000-000000000000'


def cleanup_old_container(container_id):
    # 检查容器是否存在
    result = subprocess.run(
        f'docker ps -a --format "{{{{.ID}}}}" | grep {container_id}',
        shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
    )
    if result.returncode == 0:
        print(f"发现旧容器 {container_id}，正在停止并删除...")
        run_cmd(f'docker stop {container_id}', check=False)
        run_cmd(f'docker rm {container_id}', check=False)
        print("旧容器已清理。")


def wait_for_server(server_url, max_retries=30, retry_interval=2):
    """等待服务器启动"""
    print(f"等待服务器启动: {server_url}")
    for i in range(max_retries):
        try:
            response = requests.get(server_url, timeout=5)
            if response.status_code == 200:
                print(f"✅ 服务器已启动 (尝试 {i + 1}/{max_retries})")
                return True
        except requests.exceptions.RequestException:
            pass
        print(f"⏳ 等待服务器启动... (尝试 {i + 1}/{max_retries})")
        time.sleep(retry_interval)

    print(f"❌ 服务器启动超时，已尝试 {max_retries} 次")
    return False


def test_api_endpoint(url: str):
    """测试API端点"""
    print("🧪 测试API端点...")
    try:
        response = requests.post(
            url,
            headers={'Content-Type': 'application/json'},
            data='',
            timeout=10
        )
        print(f"✅ API测试成功，状态码: {response.status_code}")
        if response.text:
            print(f"响应内容: {response.text[:200]}...")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ API测试失败: {e}")
        return False


def open_test_platform():
    """打开测试平台页面"""
    print("🌐 打开测试平台页面...")
    try:
        webbrowser.open('http://127.0.0.1:6789/test/00000000-0000-0000-0000-000000000000')
        print("✅ 测试平台页面已打开")
        return True
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        return False


def get_runtime_doas_token(env):
    """获取 flow.agentsphere.runtime 的 doas token"""
    print("🔑 获取 flow.agentsphere.runtime 的 doas token...")
    runtime_token_result = run_cmd(f'doas --psm flow.agentsphere.runtime env', env)
    runtime_token = ""
    if runtime_token_result.returncode == 0:
        # 从输出中提取 token
        for line in runtime_token_result.stdout.split('\n'):
            if 'SEC_TOKEN_STRING=' in line:
                runtime_token = line.split('SEC_TOKEN_STRING=')[1].strip()
                env['RUNTIME_DOAS_TOKEN'] = runtime_token
                print(f"✅ 已获取 runtime doas token: {runtime_token[:10]}...")
                return runtime_token
        else:
            raise Exception("⚠️  未能从 doas 输出中提取到 DOAS_TOKEN")
    else:
        raise Exception("❌ 获取 runtime doas token 失败")


def build_and_start_server(debug_agent: bool = False, debug_server: bool = False, only_server: bool = False):
    print("🚀 构建和启动服务端...")
    cleanup_old_container(image_id)
    env = os.environ.copy()
    env['CONSUL_HTTP_HOST'] = '************'
    env['BYTED_HOST_IPV6'] = '::1'
    env['MY_HOST_IPV6'] = '::1'
    env['SEC_TOKEN_PATH'] = f"{os.getcwd()}/output/.token"
    unique_id = hex(uuid.getnode())[2:]
    env['SERVICE_ENV'] = f'boe_local_{unique_id}'
    env['SERVICE_CLUSTER'] = 'mq_consumer'
    env['PSM'] = 'flow.agentsphere.nextserver'
    env['RUNTIME_IDC_NAME'] = 'boe'

    # 获取 flow.agentsphere.runtime 的 doas token
    env["RUNTIME_DOAS_TOKEN"] = get_runtime_doas_token(env)

    # 编译
    run_cmd(
        f'go build -gcflags "all=-N -l" -o {root_dir}/output/agentsphere_nextserver code.byted.org/devgpt/kiwis/agentsphere/cmd/nextserver',
        env)
    # 生成 token
    run_cmd(f'doas --write {root_dir}/output/.token --psm flow.agentsphere.nextserver --print-env', env)
    # 启动服务
    print("🚀 服务端启动中...")
    try:
        # 使用后台进程启动服务器
        if debug_server:
            server_bootstrap = 'dlv --listen=:2346 --headless=true --api-version=2 --accept-multiclient exec --continue output/agentsphere_nextserver -- -conf-dir conf -log-dir log'
        else:
            server_bootstrap = 'output/agentsphere_nextserver -conf-dir conf -log-dir log'
        server_process = subprocess.Popen(
            server_bootstrap,
            shell=True, env=env
        )
        if not only_server:
            url = 'http://127.0.0.1:6789/api/agents/v2/testing/'
            if debug_agent:
                url = 'http://127.0.0.1:6789/api/agents/v2/testing/?debug=true'
            # 等待服务器启动
            if wait_for_server('http://127.0.0.1:6789/test/00000000-0000-0000-0000-000000000000'):
                # 测试API端点
                time.sleep(3)
                if test_api_endpoint(url):
                    if wait_for_server(
                            'http://127.0.0.1:6789/api/agents/v2/testing/cases/executions/?session_id=00000000-0000-0000-0000-000000000000'):
                        # 打开测试平台页面
                        open_test_platform()

        print("服务端运行中，按 Ctrl+C 退出...")
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("正在停止服务端...")
            server_process.terminate()
            server_process.wait()
            print("服务端已退出。")

    except KeyboardInterrupt:
        print("服务端已退出。")
    finally:
        run_cmd(f'docker stop {image_id}', check=False)
        run_cmd(f'docker rm {image_id}', check=False)
        print("清理完成。")


def build_client():
    run_cmd(
        f'GOOS=linux go build -gcflags "all=-N -l" -o {root_dir}/output/agentsphere_runtime code.byted.org/devgpt/kiwis/agentsphere/cmd/runtime')


def check_docker_and_image():
    # TODO: 函数有点问题，没有检查是否是最新的，并让用户确认。而是，直接获取最新的了。
    # 检查 docker 是否可用
    result = run_cmd('docker info', check=False)
    if result.returncode != 0:
        print("Docker 不可用，请先安装并启动 Docker。")
        sys.exit(1)
    image = 'hub.byted.org/base/iris_runtime_general:latest'
    # 获取本地镜像 ID
    local_id = subprocess.getoutput(
        f'docker images --format "{{{{.Repository}}}}:{{{{.Tag}}}} {{{{.ID}}}}" | grep "{image}" | awk \'{{print $2}}\'')
    # 拉取最新镜像
    print("正在检查镜像更新...")
    pull_result = run_cmd(f'docker pull {image}', check=False)
    # 获取最新镜像 ID
    latest_id = subprocess.getoutput(
        f'docker images --format "{{{{.Repository}}}}:{{{{.Tag}}}} {{{{.ID}}}}" | grep "{image}" | awk \'{{print $2}}\'')
    if local_id and latest_id and local_id != latest_id:
        ans = input(f"镜像 {image} 有更新，是否要更新本地镜像？(y/n): ")
        if ans.lower() == 'y':
            print("已更新到最新镜像。")
        else:
            print("未更新镜像。")
    else:
        print("镜像已是最新。")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Agentsphere Bootstrap")
    parser.add_argument("--debug", action="store_true", help="以 debug 模式启动, 支持对 Runtime 进行单步 Debug")
    parser.add_argument("--debug-server", action="store_true", help="以 debug 模式启动, 支持对 Server 进行单步 Debug")
    parser.add_argument("--only-server", action="store_true", help="仅启动 Server")
    args = parser.parse_args()

    build_client()
    build_and_start_server(debug_agent=args.debug, debug_server=args.debug_server, only_server=args.only_server)
    # check_docker_and_image()
