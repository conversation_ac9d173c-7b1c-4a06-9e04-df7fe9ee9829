package runtimeservice

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cenkalti/backoff/v4"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/eventbus"
	remotebus "code.byted.org/devgpt/kiwis/agentsphere/runtime/eventbus/remote"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/port/redis"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
)

const AgentDisplayName = "Generic Agent"

func (s *Service) checkRuntimeCapacity(ctx context.Context, Agent string, AgentVersion string, MaxConcurrency int) bool {
	if MaxConcurrency > 0 {
		return s.AcquireRuntimeSemaphore(ctx, AcqRelSemaphoreOption{
			Agent:   Agent,
			Version: AgentVersion,
		})
	}

	return true
}

type ScheduleOption struct {
	Agent     string
	Version   string
	SessionID string
	Immediate bool
	Restart   bool
	EventTime *time.Time
}

func (s *Service) Schedule(ctx context.Context, opt ScheduleOption) error {
	config := s.GetAgentConfig(opt.Agent, opt.Version)
	if config == nil {
		return errors.Errorf("unknown agent %s@%s", opt.Agent, opt.Version)
	}

	available := s.checkRuntimeCapacity(ctx, opt.Agent, opt.Version, config.OrchestrationConfig.MaxConcurrency)
	if !available {
		log.V1.CtxInfo(ctx, "unable to acquire semaphore for agent %s@%s", config.Agent, config.Version)
		err := s.scheduleLater(ctx, ScheduleOption{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version})
		return errors.Wrap(err, "failed to schedule runtime")
	}

	err := s.orchestratorMQClient.SendMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		CreateWorkspaceEvent: lo.ToPtr(entity.CreateWorkspaceEvent{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version}),
	}), entity.RuntimeOrchestrationTag)
	return errors.Wrap(err, "failed to schedule runtime")
}

// RuntimeScheduleEvent will send a message to general agent session queue
func (s *Service) scheduleLater(ctx context.Context, opt ScheduleOption) error {
	delay := 5*time.Second + time.Duration(rand.Intn(5000))*time.Millisecond
	return s.orchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		RuntimeScheduleEvent: lo.ToPtr(entity.RuntimeScheduleEvent{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version}),
	}), delay, entity.RuntimeOrchestrationTag)
}

// Create workspace after scheduled
func (s *Service) createWorkspace(ctx context.Context, opt CreateWorkspaceOption) error {
	config := s.GetAgentConfig(opt.Agent, opt.Version)
	if config == nil {
		return errors.Errorf("unknown agent %s@%s", opt.Agent, opt.Version)
	}
	resp, err := s.CreateRuntimeForSession(ctx, CreateRuntimeForSessionOption{
		SessionID:   opt.SessionID,
		Config:      *config,
		APIBaseURL:  fmt.Sprintf("%s%s", s.runtimeAPIConfig.APIBaseURL, s.runtimeAPIConfig.APIPrefix),
		SessionType: agentsphere.ArtifactSessionTypeGeneral,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to create runtime")
	}

	// _, err = s.dao.UpdateSession(ctx, sessiondal.UpdateSessionOption{
	// 	ID: opt.SessionID,
	// 	RuntimeMetadata: &sessionentity.RuntimeMetadata{
	// 		WorkspaceID:  instance.ID,
	// 		ProviderType: entity.RuntimeProviderType(config.RuntimeConfig.Type),
	// 	},
	// })
	// if err != nil {
	// 	return errors.WithMessage(err, "failed to update session")
	// }
	_, err = s.UpdateAgentRunBySession(ctx, UpdateAgentRuntimeStateOption{
		SessionID:       opt.SessionID,
		ContainerID:     resp.Inst.ID,
		Status:          entity.AgentRunStatusRunning,
		RuntimeProvider: entity.RuntimeProviderType(config.RuntimeConfig.Type),
		TenantKey:       resp.TenantKey,
	})

	if err != nil {
		return errors.WithMessage(err, "failed to update agent run")
	}

	err = s.ReclaimWorkspace(ctx, ReclaimWorkspaceOption{
		WorkspaceID: resp.Inst.ID,
		Agent:       opt.Agent,
		Version:     opt.Version,
		Provider:    entity.RuntimeProviderType(config.RuntimeConfig.Type),
		Timeout:     config.OrchestrationConfig.Timeout,
		SessionID:   opt.SessionID,
	})

	log.V1.CtxInfo(ctx, "create runtime success for session %s, runtime ID: %s", opt.SessionID, resp.Inst.ID)
	return errors.WithMessage(err, "failed to reclaim workspace")
}

type ReclaimWorkspaceOption struct {
	Agent       string
	Version     string
	WorkspaceID string
	Timeout     string
	Provider    entity.RuntimeProviderType
	SessionID   string
}

func (s *Service) ReclaimWorkspace(ctx context.Context, opt ReclaimWorkspaceOption) error {
	timeout, err := time.ParseDuration(opt.Timeout)
	if err != nil {
		timeout = 2 * time.Hour
		log.V1.CtxInfo(ctx, "no timeout is configured or in bad format: %s, using default timeout: %s", opt.Timeout, timeout.String())
	}
	return s.orchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		ReclaimWorkspaceEvent: lo.ToPtr(entity.ReclaimWorkspaceEvent{
			SessionID:   opt.SessionID,
			WorkspaceID: opt.WorkspaceID,
			Agent:       opt.Agent,
			Version:     opt.Version,
			Provider:    string(opt.Provider),
		}),
	}), timeout, entity.RuntimeOrchestrationTag)

}

type StopAgentRunOption struct {
	SessionID string
}

func (s *Service) StopAgentRun(ctx context.Context, opt StopAgentRunOption) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return err
	}
	if run.Status.IsStopped() {
		return nil
	}
	metadata := util.First(run.AgentMetadata.Agents)
	agent := metadata.Agent
	version := metadata.Version
	log.V1.CtxInfo(ctx, "stop workspace: %s@%s", run.RuntimeMetadata.RuntimeProvider, agent)

	err = s.stopWorkspace(ctx, StopWorkspaceOption{
		RuntimeProvider: run.RuntimeMetadata.RuntimeProvider,
		WorksapceID:     run.RuntimeMetadata.ContainerID,
		TenantKey:       run.RuntimeMetadata.TenantKey,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to stop workspace")
	}

	s.UpdateAgentRunBySession(ctx, UpdateAgentRuntimeStateOption{
		SessionID: opt.SessionID,
		Status:    entity.AgentRunStatusCompleted,
	})

	// release semaphore on agent completed
	err = s.ReleaseRuntimeSemaphore(ctx, AcqRelSemaphoreOption{
		Agent:   agent,
		Version: version,
	})

	return errors.WithMessage(err, "failed to stop workspace")
}

type StopWorkspaceOption struct {
	RuntimeProvider entity.RuntimeProviderType
	WorksapceID     string
	TenantKey       string
}

func (s *Service) stopWorkspace(ctx context.Context, opt StopWorkspaceOption) error {
	provider := s.runtimeProviderRegistry.GetProvider(opt.RuntimeProvider)
	if provider == nil {
		return errors.New("runtime provider not found")
	}

	status, err := provider.GetContainerStatus(ctx, opt.TenantKey, opt.WorksapceID)
	if err != nil {
		return errors.WithMessage(err, "failed to get container status")
	}

	if status != runtimedal.ContainerStatusRunning {
		log.V1.CtxInfo(ctx, "workspace is not running, status: %s", status)
		return nil
	}

	if err := provider.StopContainer(ctx, opt.TenantKey, opt.WorksapceID); err != nil {
		return errors.WithMessage(err, "failed to stop workspace")
	}
	return nil
}

type UpdateAgentRuntimeStateOption struct {
	SessionID       string
	Status          entity.AgentRunStatus
	ContainerID     string
	RuntimeProvider entity.RuntimeProviderType
	URI             string
	PID             int
	InitCost        int64
	Message         string
	Sync            bool
	ContainerHost   string
	TenantKey       string
}

func (s *Service) UpdateAgentRunBySession(ctx context.Context, opt UpdateAgentRuntimeStateOption) (*entity.AgentRun, error) {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
		Sync:      opt.Sync,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}
	return s.dao.UpdateAgentRunFunc(ctx, run.ID, func(run *entity.AgentRun) {
		if opt.Status != "" {
			run.Status = opt.Status
		}
		if opt.ContainerID != "" {
			run.RuntimeMetadata.ContainerID = opt.ContainerID
		}
		if opt.RuntimeProvider != "" {
			run.RuntimeMetadata.RuntimeProvider = opt.RuntimeProvider
		}
		if opt.URI != "" {
			run.RuntimeMetadata.URI = opt.URI
		}
		if opt.PID != 0 {
			run.RuntimeMetadata.PID = opt.PID
		}
		if opt.InitCost != 0 {
			run.RuntimeMetadata.InitTimeCost = opt.InitCost
		}
		if opt.Message != "" {
			run.RuntimeMetadata.LastReportMessage = opt.Message
		}
		if opt.ContainerHost != "" {
			run.RuntimeMetadata.ContainerHost = opt.ContainerHost
		}
		if opt.TenantKey != "" {
			run.RuntimeMetadata.TenantKey = opt.TenantKey
		}
	})
}

type RunAgentOption struct {
	SessionID   string
	WorkspaceID string
	URI         string
}

func (s *Service) GetEventOffset(ctx context.Context, runID string) int64 {
	return s.getAgentRunEventOffset(ctx, runID)
}

type GetAgentRunStreamOption struct {
	SessionID   string
	EventOffset int64
	Reconnect   bool
	IsOffline   bool
}

func (s *Service) MustGetAgentRunEvents(ctx context.Context, opt GetAgentRunStreamOption) (*stream.RecvChannel[entity.SessionDataStreamEvent], error) {
	send, recv := stream.NewChannel[entity.SessionDataStreamEvent](20)
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}
	if run == nil {
		return nil, ErrAgentRunNotFound
	}
	log.V1.CtxInfo(ctx, "agent run. session id: %s, run id: %s, status: %s", opt.SessionID, run.ID, run.Status)
	if run.Status.IsStopped() {
		if run.Detail.FailureReason != nil {
			return nil, errors.WithMessagef(ErrAgentStopped, "agent exited with error: %s", *run.Detail.FailureReason)
		}
		return nil, ErrAgentStopped
	}
	if run.Status != entity.AgentRunStatusReady {
		send.Publish(entity.SessionDataStreamEvent{
			ProgressNotice: entity.NewSessionProgressNotice(entity.SessionProgressNoticeNameCreatingWorkspace),
		})
		run, err = s.waitUtilAgentRunning(ctx, waitAgentOption{
			RunID:  run.ID,
			Status: []entity.AgentRunStatus{entity.AgentRunStatusReady, entity.AgentRunStatusCompleted, entity.AgentRunStatusFailed, entity.AgentRunStatusCanceled},
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to wait agent run")
		}
		if run.Status.IsStopped() {
			if run.Detail.FailureReason != nil {
				return nil, errors.WithMessagef(ErrAgentStopped, "agent exited with error: %s", *run.Detail.FailureReason)
			}
			return nil, ErrAgentStopped
		}
	}
	send.Publish(entity.SessionDataStreamEvent{
		ProgressNotice: entity.NewSessionProgressNotice(entity.SessionProgressNoticeNameConnectingWorkspace),
	})
	ch, err := s.retrieveAgentRunEvents(ctx, GetAgentEventOption{
		AgentRun:    run,
		EventOffset: opt.EventOffset,
		Reconnect:   opt.Reconnect,
		IsOffline:   opt.IsOffline,
		SessionID:   opt.SessionID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to retrieve agent run events")
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "agent run stream panic: %v, session id: %s", r, opt.SessionID)
			}
			if !send.Stopped() {
				send.Close()
			}
		}()
		_ = stream.Forward(ctx, ch, send, func(event entity.SessionDataStreamEvent) entity.SessionDataStreamEvent {
			if event.NextAgentRunCompleted != nil {
				log.V1.CtxInfo(ctx, "fetch offline next_agent_run_completed session id: %s, offset: %d", opt.SessionID, opt.EventOffset)
			}
			return event
		})
		return
	}()
	return recv, nil
}

func (s *Service) GetAgentRunEvents(ctx context.Context, opt GetAgentRunStreamOption) (*stream.RecvChannel[entity.SessionDataStreamEvent], error) {
	send, recv := stream.NewChannel[entity.SessionDataStreamEvent](20)
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}
	if run == nil {
		return nil, ErrAgentRunNotFound
	}
	log.V1.CtxInfo(ctx, "agent run. session id: %s, run id: %s, status: %s", opt.SessionID, run.ID, run.Status)
	if run.Status.IsStopped() {
		if run.Detail.FailureReason != nil {
			return nil, errors.WithMessagef(ErrAgentStopped, "agent exited with error: %s", *run.Detail.FailureReason)
		}
		return nil, ErrAgentStopped
	}
	session, err := s.GetNextSession(ctx, GetNextSession{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get next session")
	}
	if session.Status.IsStopped() && !s.checkSessionRestartMark(ctx, opt.SessionID) {
		return nil, ErrAgentStopped
	}
	if session.Status.IsFinal() {
		return nil, errors.New("session terminated error")
	}
	if run.Status != entity.AgentRunStatusReady {
		send.Publish(entity.SessionDataStreamEvent{
			ProgressNotice: entity.NewSessionProgressNotice(entity.SessionProgressNoticeNameCreatingWorkspace),
		})
	}

	go func() (err error) {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "agent run stream panic: %v", r)
			}
			if err != nil {
				log.V1.CtxError(ctx, "agent run stream error: %v", err)
				send.PublishError(err, false)
			}
			if !send.Stopped() {
				send.Close()
			}
		}()
		if run.Status != entity.AgentRunStatusReady {
			run, err = s.waitUtilAgentRunning(ctx, waitAgentOption{
				RunID:  run.ID,
				Status: []entity.AgentRunStatus{entity.AgentRunStatusReady, entity.AgentRunStatusCompleted, entity.AgentRunStatusFailed, entity.AgentRunStatusCanceled},
			})
			if err != nil {
				return
			}
			if run.Status.IsStopped() {
				if run.Detail.FailureReason != nil {
					return errors.WithMessagef(ErrAgentStopped, "agent exited with error: %s", *run.Detail.FailureReason)
				}
				return ErrAgentStopped
			}
		}
		send.Publish(entity.SessionDataStreamEvent{
			ProgressNotice: entity.NewSessionProgressNotice(entity.SessionProgressNoticeNameConnectingWorkspace),
		})
		ch, err := s.retrieveAgentRunEvents(ctx, GetAgentEventOption{
			AgentRun:    run,
			EventOffset: opt.EventOffset,
			Reconnect:   opt.Reconnect,
			IsOffline:   opt.IsOffline,
			SessionID:   opt.SessionID,
		})
		if err != nil {
			return
		}
		_ = stream.Forward(ctx, ch, send, func(event entity.SessionDataStreamEvent) entity.SessionDataStreamEvent {
			return event
		})
		return
	}()

	return recv, nil
}

type GetAgentEventOption struct {
	AgentRun    *entity.AgentRun
	EventOffset int64
	SessionID   string
	Reconnect   bool
	IsOffline   bool
}

func (s *Service) retrieveAgentRunEvents(ctx context.Context, opt GetAgentEventOption) (*stream.RecvChannel[entity.SessionDataStreamEvent], error) {
	log.V1.CtxInfo(ctx, "start retrieve agent run events. session id: %s, is offline: %v", opt.SessionID, opt.IsOffline)
	cli, err := s.connect(ctx, connectOption{RuntimeMetadata: opt.AgentRun.RuntimeMetadata, Relay: s.relayConfig.GetValue().RelayURL})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to connect to runtime process")
	}
	defer cli.Close()

	var busURI string
	newCtx := s.InjectCtxDestInfo(ctx, opt.AgentRun.RuntimeMetadata.URI, s.GetDestPSM(ctx, opt.AgentRun))
	operation := func() error {
		busURI, err = cli.CtxCreateAgentEventStream(newCtx, opt.AgentRun.ID, opt.Reconnect)
		if err != nil {
			return errors.WithMessage(err, "failed to create agent event stream")
		}
		return err
	}
	err = backoff.Retry(operation, backoff.WithContext(backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 10), ctx))
	if err != nil {
		return nil, err
	}

	uri, host := s.runtimeProviderRegistry.ResolveURI(ctx, opt.AgentRun.RuntimeMetadata.RuntimeProvider, opt.AgentRun.RuntimeMetadata.TenantKey, opt.AgentRun.RuntimeMetadata.ContainerHost, opt.AgentRun.RuntimeMetadata.ContainerID, busURI)
	if host != "" {
		_, err = s.dao.UpdateAgentRunFunc(ctx, opt.AgentRun.ID, func(run *entity.AgentRun) {
			run.RuntimeMetadata.ContainerHost = host
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to update agent run metadata: %v", err)
		}
	}

	bus, err := remotebus.Connect(uri, remotebus.WithRelay(s.relayConfig.GetValue().RelayURL), remotebus.WithName("apiserver-next"))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent event stream")
	}
	bus.HandleDefaultRoutes() // register keepalive handler

	send, recv := stream.NewChannel[entity.SessionDataStreamEvent](20)
	events := eventbus.NewAgentEventStream(bus, opt.AgentRun.ID, opt.EventOffset)
	events.OnLog(func(level logrus.Level, content string) {
		log.V1.CtxInfo(ctx, "agent event stream log, level: %s, content: %s", level, content)
	})
	var stopOnce sync.Once
	events.OnKeepAlive(func(data entity.KeepAliveRequest) error {
		if !opt.IsOffline {
			return nil
		}
		run, err := s.GetAgentRun(ctx, GetAgentRunOption{
			SessionID: opt.SessionID,
			Sync:      true,
		})
		if err != nil {
			return err
		}
		if run.Status == entity.AgentRunStatusAborting {
			var stopErr error
			stopOnce.Do(func() {
				stopErr = s.SendStopRuntime(ctx, ScheduleOption{
					SessionID: opt.SessionID,
					Immediate: true,
				})
			})
			return stopErr
		}
		return nil
	})
	graphSteps := make(map[string]*iris.EventAgentStepGraph)
	originalSteps := make(map[string]*iris.EventAgentStep)
	thinkOfStep := make(map[string]*strings.Builder)
	pub := func(event *entity.SessionDataStreamEvent) {
		if event != nil && event.NextAgentRunCompleted != nil {
			log.V1.CtxInfo(ctx, "fetch next_agent_run_completed event, session id: %s, offline %v, event offset %d, opt offset %d", opt.SessionID, opt.IsOffline, event.EventOffset, opt.EventOffset)
		}
		e := lo.FromPtr(event)
		if e.EventOffset < opt.EventOffset {
			return
		}
		send.Publish(e)
	}

	events.OnEvent(ctx, func(event *iris.AgentRunEvent[any]) {
		if send.Stopped() || recv.Stopped() {
			log.V1.CtxInfo(ctx, "send or recv channel is stopped, close events")
			events.Close()
		}

		var eventIsError bool

		switch t := event.Data.(type) {
		// case *iris.EventAgentThink:
		case *iris.EventAgentThinkDelta:
			if step := originalSteps[t.StepID]; step != nil {
				// if the step is not created by plan, we should ignore it
				if step.ParentID != "" {
					return
				}
				msg := &entity.Message{
					Creator: entity.NewUser(entity.UserTypeAgent, AgentDisplayName),
					ID:      t.StepID,
					Content: entity.MessageContent{
						Content: t.Delta,
					},
					Type: entity.MessageTypeNormal,
				}
				sb, ok := thinkOfStep[t.StepID]
				if !ok {
					sb = new(strings.Builder)
					thinkOfStep[t.StepID] = sb
				}
				sb.WriteString(t.Delta)
				pub(&entity.SessionDataStreamEvent{
					MessageCreated: lo.Ternary(ok, nil, msg),
					MessageDelta:   lo.Ternary(ok, msg, nil),
					EventOffset:    event.Offset,
				})
			} else {
				log.V1.CtxError(ctx, "step %s not found", t.StepID)
			}
		case *iris.EventAgentThink:
			if step := originalSteps[t.StepID]; step != nil {
				if step.ParentID != "" {
					return
				}
				rationale := t.Thought.Rationale
				sb, ok := thinkOfStep[t.StepID]
				if !ok {
					sb = new(strings.Builder)
					thinkOfStep[t.StepID] = sb
				}
				// update rationale
				if rationale != sb.String() {
					sb.Reset()
					sb.WriteString(rationale)
				}
				// // add plan
				// if plan := t.Thought.Data["plan"].(string); plan != "" {
				// 	sb.WriteString("```" + plan + "```")
				// }
				pub(&entity.SessionDataStreamEvent{
					MessageUpdated: &entity.Message{
						ID:      t.StepID,
						Content: entity.MessageContent{Content: sb.String()},
						Creator: entity.NewUser(entity.UserTypeAgent, AgentDisplayName),
					},
					EventOffset: event.Offset,
				})
			} else {
				log.V1.CtxError(ctx, "step %s not found", t.StepID)
			}
		case *iris.EventAgentStep:
			originalSteps[t.StepID] = t
			if t.Status == iris.AgentRunStepStatusFailed || t.Status == iris.AgentRunStepStatusSuccess {
				if sb, ok := thinkOfStep[t.StepID]; ok {
					pub(&entity.SessionDataStreamEvent{
						MessageCompleted: &entity.Message{
							ID: t.StepID,
							Content: entity.MessageContent{
								Content: sb.String(),
							},
						},
						EventOffset: event.Offset,
					})
				}
			}
			var (
				attachments []entity.AttachmentMeta
				content     string
			)
			if t.Message != nil {
				attachments = lo.Map(t.Message.Attachments, func(a iris.Attachment, _ int) entity.AttachmentMeta {
					return entity.AttachmentMeta{
						ArtifactID: a.ArtifactID,
						Filename:   a.Path,
					}
				})
				content = t.Message.Content
			}
			pub(&entity.SessionDataStreamEvent{
				NextAgentStepUpdated: &entity.NextAgentStepUpdated{
					StepID:   t.StepID,
					ParentID: t.ParentID,
					Executor: t.Executor,
					Status:   entity.AgentRunStepStatus(t.Status),
					Action:   t.Action,
					Inputs:   t.Inputs,
					Outputs:  t.Outputs,
					Message: &entity.Message{
						ID:             "",
						ConversationID: "",
						Number:         0,
						Type:           "",
						Creator:        entity.User{},
						ParentID:       "",
						Content:        entity.MessageContent{Content: content},
						Attachments:    attachments,
						CreatedAt:      time.Time{},
						UpdatedAt:      time.Time{},
					},
					Error: t.Error,
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
		case *iris.EventAgentStepGraph:
			graphSteps[t.StepID] = t
			stepGraph := &entity.StepGraph{
				Steps: make([]entity.Step, 0, len(graphSteps)),
			}
			for _, step := range graphSteps {
				stepGraph.Steps = append(stepGraph.Steps, entity.Step{
					ID:          step.StepID,
					ParentID:    step.ParentID,
					Description: step.Content,
					Timestamp:   step.TimeStamp,
				})
			}
			pub(&entity.SessionDataStreamEvent{
				StepGraph: stepGraph,
				Step: &entity.Step{
					ID:          t.StepID,
					ParentID:    t.ParentID,
					Description: t.Content,
					Timestamp:   t.TimeStamp,
				},
				EventOffset: event.Offset,
			})
		case *iris.EventAgentStepArtifact:
			pub(&entity.SessionDataStreamEvent{
				StepArtifact: &entity.StepArtifact{
					StepID:     t.StepID,
					ArtifactID: t.ArtifactID,
					Type:       t.Type,
				},
				EventOffset: event.Offset,
			})
		case *iris.EventAgentRunCompleted:
			pub(&entity.SessionDataStreamEvent{
				NextAgentRunCompleted: &entity.NextAgentRunCompleted{
					RunID:  t.RunID,
					Error:  t.Error,
					Status: "",
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
			if event.Event == iris.AgentRunEventDynamicUICompleted {
				if len(t.Error) != 0 {
					send.PublishError(errors.Errorf("agent exited with error: %s", t.Error), false)
				}
				if !send.Stopped() {
					send.Close()
				}
			}
			if t.Error != "" {
				eventIsError = true
			}
		case *iris.EventAgentRestart:
			pub(&entity.SessionDataStreamEvent{
				NextAgentRestart: &entity.NextAgentRestart{
					RunID: t.RunID,
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
		/**  Deal with next server agent events. **/
		case *iris.EventAgentMessage:
			pub(&entity.SessionDataStreamEvent{
				NextMessageCreated: &entity.NextMessageCreated{
					ID:       t.ID,
					ParentID: t.ParentID,
					Content:  t.Content,
					Attachments: lo.Map(t.Attachments, func(a iris.Attachment, _ int) entity.Attachment {
						return entity.Attachment{
							ArtifactID: a.ArtifactID,
							Path:       a.Path,
							Type:       entity.AttachmentType(a.Type),
							URL:        a.URL,
						}
					}),
					Timestamp:      event.Timestamp,
					ReplyMessageID: t.ReplyTo,
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
		case *iris.EventPlanUpdated:
			pub(&entity.SessionDataStreamEvent{
				NextPlanUpdated: &entity.NextPlanUpdated{
					ID:      t.ID,
					Status:  t.Status,
					Content: t.Content,
					PlanSteps: lo.Map(t.PlanSteps, func(s iris.PlanStep, _ int) entity.PlanStep {
						return entity.PlanStep{
							ID:          s.ID,
							Description: s.Description,
							Actor:       s.Actor,
							Input:       s.Input,
							Status:      s.Status,
						}
					}),
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
		case *iris.EventPlanStepUpdated:
			pub(&entity.SessionDataStreamEvent{
				NextPlanStepUpdated: &entity.NextPlanStepUpdated{
					ID:          t.ID,
					PlanID:      t.PlanID,
					StepID:      t.StepID,
					Description: t.Description,
					Status:      t.Status,
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
		case *iris.EventAgentToolCall:
			pub(&entity.SessionDataStreamEvent{
				NextToolCall: &entity.NextToolCall{
					ID:          t.ID,
					StepID:      t.StepID,
					Tool:        t.Tool,
					Inputs:      t.Inputs,
					Outputs:     t.Outputs,
					Error:       t.Error,
					Status:      entity.ToolCallStatus(t.Status),
					Description: t.Description,
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
			if t.Error != "" {
				eventIsError = true
			}
		case *iris.EventAgentToolCallRequired:
			pub(&entity.SessionDataStreamEvent{
				NextToolCallRequired: &entity.NextToolCallRequired{
					ID:         t.ID,
					StepID:     t.StepID,
					Tool:       t.Tool,
					Parameters: t.Parameters,
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
		case *iris.EventAgentIdle:
			pub(&entity.SessionDataStreamEvent{
				NextEventAgentIdle: &entity.NextEventAgentIdle{},
				EventOffset:        event.Offset,
				Timestamp:          event.Timestamp,
			})
		case *iris.Reference:
			refs := lo.Map(*t, func(r iris.ReferenceItem, _ int) entity.ReferenceItem {
				return entity.ReferenceItem{
					ID:       r.ID,
					Title:    r.Title,
					URI:      r.URI,
					MetaData: r.MetaData,
				}
			})
			pub(&entity.SessionDataStreamEvent{
				NextEventReference: &entity.NextEventReference{References: refs},
				EventOffset:        event.Offset,
				Timestamp:          event.Timestamp,
			})
		case *iris.EventToolCallConfirmed:
			var action entity.ToolCallAction
			switch t.Action {
			case iris.ToolCallActionConfirm:
				action = entity.ToolCallActionConfirm
			case iris.ToolCallActionReject:
				action = entity.ToolCallActionReject
			case iris.ToolCallActionTimeout:
				action = entity.ToolCallActionTimeout
			}
			pub(&entity.SessionDataStreamEvent{
				NextToolCallConfirmed: &entity.NextToolCallConfirmed{
					ID:         t.ID,
					StepID:     t.StepID,
					ToolCallID: t.ToolCallID,
					Action:     action,
					Reason:     t.Reason,
					Parameters: t.Parameters,
				},
				EventOffset: event.Offset,
				Timestamp:   event.Timestamp,
			})
		}

		// agent event metrics
		tags := &metrics.NextServerAgentEventTag{
			Event:     event.Event.String(),
			IsError:   eventIsError,
			IsOffline: opt.IsOffline,
		}
		_ = metrics.NSM.AgentEventRate.WithTags(tags).Add(1)

		if event.Offset >= 150000 { // offset 大于15w，视为异常做下报警。阈值可动态调整
			if event.Offset%100 == 0 { // 取模100，防止日志过多
				log.V1.CtxWarn(ctx, "session %s event offset abnormal, offset %d, offline: %v", opt.SessionID, event.Offset, opt.IsOffline)
			}
			abnormalTags := &metrics.NextServerAgentEventOffsetAbnormalTag{
				SessionID: opt.SessionID,
				Event:     event.Event.String(),
				IsOffline: opt.IsOffline,
			}
			_ = metrics.NSM.AgentEventOffsetAbnormal.WithTags(abnormalTags).Add(1)
		}
	})
	// handlers are mount, ready to handle events
	events.Ready()

	if err = cli.RetrieveEvents(events); err != nil {
		send.Close()
		events.Close()
		return nil, errors.WithMessage(err, "failed to retrieve events")
	}

	return recv, nil
}

func (s *Service) getAgentRunEventOffset(ctx context.Context, runID string) int64 {
	offset, err := s.redis.Get(ctx, s.agentRunEventOffsetKey(runID))
	if err != nil && !redis.IsNil(err) {
		log.V1.CtxWarn(ctx, "failed to get run events offset: %v, use 0 as default", err)
	}
	var offsetInt int64
	if offset != "" {
		offsetInt, err = strconv.ParseInt(offset, 10, 64)
		if err != nil {
			log.V1.CtxError(ctx, "failed to parse run events offset: %v", err)
		}
	}
	return offsetInt
}

func (s *Service) OnScheduleEvent(ctx context.Context, event *entity.RuntimeScheduleEvent) error {
	err := s.Schedule(ctx, ScheduleOption{
		Agent:     event.Agent,
		Version:   event.Version,
		SessionID: event.SessionID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to schedule runtime: %v", err)
		// TODO: notify session status
		return err
	}
	return nil
}

type CreateWorkspaceOption struct {
	SessionID string
	Agent     string
	Version   string
}

func (s *Service) OnCreateEvent(ctx context.Context, event *entity.CreateWorkspaceEvent) error {
	return s.createWorkspace(ctx, CreateWorkspaceOption{
		SessionID: event.SessionID,
		Agent:     event.Agent,
		Version:   event.Version,
	})
}

func (s *Service) OnReclaimEvent(ctx context.Context, event *entity.ReclaimWorkspaceEvent) error {
	return s.StopAgentRun(ctx, StopAgentRunOption{
		SessionID: event.SessionID,
	})
}

func (s *Service) GetStep(ctx context.Context, stepID string) (*entity.AgentRunStep, error) {
	return s.dao.GetAgentRunStepByID(ctx, stepID, true)
}

func (s *Service) GetStepGraph(ctx context.Context, sessionID string) ([]*entity.AgentRunStep, error) {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: sessionID,
	})
	if err != nil {
		return nil, err
	}

	return s.dao.ListAgentRunSteps(ctx, runtimedal.ListAgentRunStepsOption{
		RunID: &run.ID,
	})
}

func (s *Service) GetPlanStepIDByAgentStepID(ctx context.Context, stepID string) (string, error) {
	var (
		planStepID    string
		currentStepID = stepID
	)
	for { // 按照 parent id 向上查找，直到找到 plan step
		agentRunStep, err := s.GetStep(ctx, currentStepID) // 这里的 step id 是指 agent run step，不是 plan step
		if err != nil {
			return "", errors.WithMessagef(err, "failed to get agent step by id `%s`", currentStepID)
		}
		if agentRunStep != nil {
			if agentRunStep.Metadata.ParentID == "" {
				return "", errors.Errorf("failed to get plan step by agent step id `%s`", currentStepID)
			}
			currentStepID = agentRunStep.Metadata.ParentID
			step, err := s.nextserverDao.GetStepByAgentStepID(ctx, currentStepID, true)
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to get step by agent step id: %v", err)
			}
			if step != nil {
				planStepID = step.ID
				break
			}
		}
	}
	return planStepID, nil
}

func (s *Service) GetLatestAgentRunStepByRunID(ctx context.Context, runID string) (*entity.PartialAgentRunStep, error) {
	return s.dao.GetLatestAgentRunStepByRunID(ctx, runID)
}
