package nexthandler

import "code.byted.org/middleware/hertz/pkg/app/server"

func (h *Handler) Register(hertz *server.Hertz) {
	v2 := hertz.Group("/api/agents/v2")

	// Internal APIs, not exposed to user.
	runtime := v2.Group("/runtime")
	{
		runtime.POST("/sessions/:session_id/state", h.UpdateRuntimeWorkspaceState)
		runtime.POST("/sessions/:session_id/connect", h.ConnectNextEvents)
		runtime.POST("/sessions/:session_id/connect_and_save_events", h.ConnectAndSaveNextEvents)

		// show agent logs
		runtime.GET("/log/html", h.GetLogHTML)
		runtime.GET("/log/events", h.GetLog)
		runtime.GET("/log/session/:session_id", h.RedirectLogURL)

		runtime.POST("/token/refresh", h.RefreshToken)

		// ingest traces
		runtime.POST("/traces/ingest", h.IngestTraces)
		runtime.POST("/bind", h.BindingAgent)
		runtime.GET("/run_agent_context", h.AuthM.RequireAccount(), h.GetRunTestCasesContext)
	}

	// LLM Proxy.
	// Azure compatible.
	azureLLMProxy := v2.Group("/runtime/openai/deployments")
	{
		azureLLMProxy.POST("/:model/chat/completions", h.ChatStream)
	}
	// OpenAI compatible.
	openAILLMProxy := v2.Group("/llmproxy")
	{
		openAILLMProxy.POST("/:model/chat/completions", h.ChatStream)
		openAILLMProxy.POST("/chat/completions", h.ChatStream)
		// Aime may write scripts to call LLM API to do tagging, generating, summarizing, etc.
		// We need to separate the LLM API from the internal agent LLM API.
		// FIXME(cyx): add auth and model limiting here.
		openAILLMProxy.POST("/user/chat/completions", h.ChatStream)
	}

	// AMAP LBS Proxy
	lbsProxy := v2.Group("/lbsproxy")
	{
		lbsProxy.Any("/_AMapService", h.ProxyToAMapLBS)
		lbsProxy.Any("/_AMapService/*path", h.ProxyToAMapLBS)
	}
}
