package nexthandler

import (
	"context"
	"embed"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"runtime/debug"
	"strings"
	"text/template"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	testingservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/testing"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/handler"
	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/service"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/apierror"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	customopenai "code.byted.org/devgpt/kiwis/port/openai"
	"code.byted.org/devgpt/kiwis/port/rocketmq"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz/pkg/common/utils"
	"code.byted.org/middleware/hertz/pkg/network/standard"
	"code.byted.org/middleware/hertz/pkg/protocol"
	"code.byted.org/middleware/hertz_ext/v2/reverseProxy"
	"github.com/AlekSi/pointer"
	"github.com/cenkalti/backoff/v4"
	hertzconfig "github.com/cloudwego/hertz/pkg/common/config"
	"github.com/hertz-contrib/sse"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type ChatRequest struct {
	customopenai.ChatCompletionsRequest
}

type Handler struct {
	Service            *runtimeservice.Service
	Conf               *config.AgentSphereConfig
	NextOrchestratorMQ rocketmq.Client `name:"next_runtime_orchestrator_mq"`
	AuthM              *handler.AuthMiddleware
	AuthService        service.Service
	LLMService         llm.Service
	AMapConfig         *tcc.GenericConfig[config.AMapConfig]
	TestingService     *testingservice.Service
	RuntimeService     *runtimeservice.Service
	SessionService     *sessionservice.Service
}

// 以 openai 兼容的形式返回错误信息，因为客户端用了 openai go sdk（虽然是第三方的）。
func openaiStyleJSONMessage(c *app.RequestContext, status int, message string) {
	c.JSON(status, utils.H{
		"error": customopenai.OpenAIErrorResponseDetail{
			Code:    nil,
			Message: message,
			Type:    "invalid_request_error",
			Param:   nil,
		},
	})
}

type LLMChatExtraOptions struct {
	SessionID      string   `json:"session_id" mapstructure:"session_id"`
	FallbackModels []string `json:"fallback_models" mapstructure:"fallback_models"`
	Tag            string   `json:"tag" mapstructure:"tag"`

	PromptCacheSessionKey *string `json:"prompt_cache_session_key,omitempty" mapstructure:"prompt_cache_session_key"`
}

// ChatStream is compatible with openai chat/completion.
func (h *Handler) ChatStream(ctx context.Context, c *app.RequestContext) {
	var (
		req       ChatRequest
		sessionID = string(c.GetHeader(entity.SessionIDHeader))
		tag       = string(c.GetHeader(framework.LLMTagKey))
		traceID   = string(c.GetHeader(framework.LLMTraceIDKey))
	)
	if err := c.Bind(&req); err != nil {
		log.V1.CtxWarn(ctx, "failed to parse chat request: %v", err)
		openaiStyleJSONMessage(c, http.StatusBadRequest, err.Error())
		return
	}
	llmServiceReq := llm.GetChatCompletionsRequestFromCustomOpenAI(req.ChatCompletionsRequest)
	llmServiceReq.Tag = tag
	// log.V1.CtxInfo(ctx, "[ChatStream] received chat request: %+v", req.ChatCompletionsRequest)

	if req.ExtraOptions != nil {
		var extraOptions LLMChatExtraOptions
		decodeErr := conv.DecodeMapstructureWithTimeStringEnabled(req.ExtraOptions, &extraOptions)
		if decodeErr != nil {
			log.V1.CtxError(ctx, "failed to decode extra options: %v, options: %+v", decodeErr, req.ExtraOptions)
		} else {
			llmServiceReq.SessionID = extraOptions.SessionID
			llmServiceReq.FallbackModels = extraOptions.FallbackModels
			llmServiceReq.Tag = extraOptions.Tag

			sessionID = extraOptions.SessionID
			tag = extraOptions.Tag
			// GPT 平台的接口底层有多个账号，但是 prompt cache 命中需要是同一个账号，因此需要根据 session key 来路由到同一个底层账号上。
			// https://bytedance.larkoffice.com/wiki/ODGmwD8MliLaaMkQUgJcb1W1nIi#share-HOO2d6ncCo00ajxI9VPcOK4Fngc
			if extraOptions.PromptCacheSessionKey != nil {
				llmServiceReq.Variables = lo.Assign(llmServiceReq.Variables, map[string]any{
					"extra_http_header": map[string]string{
						"extra": conv.JSONString(map[string]string{
							"session_id": *extraOptions.PromptCacheSessionKey,
						}),
					},
				})
			}
		}
	}

	ctx = logs.CtxAddKVs(ctx, "llm_trace_id", traceID, "llm_tag", tag, "llm_session_id", sessionID)

	// 拦截一下不明来源的 LLM 请求
	// FIXME(cyx): 添加 jwt 认证与鉴权
	if len(sessionID) == 0 && len(tag) == 0 {
		log.V1.CtxWarn(ctx, "path: %s, session id and tag are both empty: %s", string(c.Request.RequestURI()), req.Model)
		// openaiStyleJSONMessage(c, http.StatusBadRequest, "session id and tag are both empty")
		// return
	}

	if recordParams, err := h.Service.GetRecordParams(ctx, sessionID); err == nil {
		llmServiceReq.Record = recordParams.Record
		llmServiceReq.SessionID = recordParams.SessionID
		llmServiceReq.Account = recordParams.Account
		// use trace id as app id for agent
		llmServiceReq.AppID = traceID
	} else {
		log.V1.CtxInfo(ctx, "skip recording due to no agent run found for this llm request nor no session id provided: %v", err)
	}
	res, err := h.LLMService.ChatCompletion(ctx, llmServiceReq)
	if err != nil {
		log.V1.CtxError(ctx, "failed to do chat for model %s: %v", req.Model, err)
		openaiStyleJSONMessage(c, http.StatusBadRequest, "failed to init chat: "+err.Error())
		return
	}
	defer res.Close(ctx)

	if !req.Stream {
		aggRes, err := res.Aggregation(ctx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to aggregate response: %v", err)
			openaiStyleJSONMessage(c, http.StatusBadRequest, "failed to aggregate response: "+err.Error())
			return
		}
		c.JSON(http.StatusOK, llm.GetCustomOpenAIChatCompletionsResponse(*aggRes))
		return
	}

	c.SetContentType("text/event-stream")
	s := sse.NewStream(c)
	defer s.Publish(&sse.Event{Data: []byte("[DONE]")})
	for {
		chunk, err := res.NextChunk(ctx)
		if err != nil {
			if errors.Is(err, io.EOF) {
				return
			}
			log.V1.CtxWarn(ctx, "failed to get next chunk: %v", err)
			s.Publish(&sse.Event{Data: conv.JSONBytes(utils.H{"error": customopenai.OpenAIErrorResponseDetail{
				Code:    nil,
				Message: "failed to get next chunk: " + err.Error(),
				Type:    "internal_error",
				Param:   nil,
			}})})
			return
		}
		data := conv.JSONBytes(llm.GetCustomOpenAIChatCompletionsStreamResponse(*chunk))

		if err := s.Publish(&sse.Event{
			// Add space. openai process stream requires "data: {\"id":...}", while hertz-contrib/sse send "data:{\"id":...}" with no space.
			Data: append([]byte(" "), data...),
		}); err != nil { // Stop if client is disconnected, or it will get OOM.
			log.V1.CtxWarn(ctx, "failed to publish chunk: %v", err)
			return
		}
	}
}

//go:embed log.html
var logHTML embed.FS

func (h *Handler) GetLogHTML(ctx context.Context, c *app.RequestContext) {
	c.Response.Header.Set("Content-Type", "text/html; charset=utf-8")

	// for debug log html without restart apiserver
	htmlPath := "agentsphere/runtime/nexthandler/log_dev.html"
	_, err := os.Stat(htmlPath)
	if err == nil {
		htmlContent, err := os.ReadFile(htmlPath)
		if err == nil {
			c.Data(http.StatusOK, "text/html; charset=utf-8", htmlContent)
			return
		}
		log.V1.CtxError(ctx, "failed to read log.html from file system: %+v", err)
	}
	tmpl := template.Must(template.ParseFS(logHTML, "log.html"))
	err = tmpl.Execute(c, map[string]any{})
	if err != nil {
		log.V1.CtxError(ctx, "failed to execute log template: %+v", err)
		return
	}
}

type LogEvent struct {
	Timestamp string `json:"timestamp"`
	Event     string `json:"event"`
	Data      any    `json:"data"`
	Offset    int64  `json:"offset"`
}

func (h *Handler) GetLog(ctx context.Context, c *app.RequestContext) {
	runID := c.Query("run_id")
	containerID := c.Query("container_id")
	uri := c.Query("uri")
	provider := c.Query("provider")
	if runID == "" || containerID == "" || uri == "" {
		c.JSON(http.StatusBadRequest, "run_id, container_id and uri are required")
		return
	}

	var account *authentity.Account

	// 仅在 tce 当中的时候才鉴权，方便本地调试
	if env.InTCE() {
		// 手动调用中间件
		h.AuthM.RequireAccount()(ctx, c)
		if c.IsAborted() {
			return
		}
		var ok bool
		account, ok = h.AuthM.GetAccount(ctx, c)
		if !ok {
			c.JSON(http.StatusUnauthorized, "unauthorized")
			return
		}

		if !h.Service.IsDeveloper(account) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	s := sse.NewStream(c)
	pub := func(data any) error {
		return s.Publish(&sse.Event{
			Event: "json-data",
			Data:  conv.JSONBytes(data),
		})
	}
	defer pub(LogEvent{
		Event: agentsphere.DSEventDone,
		Data:  agentsphere.DSDone{},
	})
	done := make(chan struct{}, 1)
	defer close(done)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "failed to send ping: %+v", r)
			}
		}()
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				pub(LogEvent{
					Event: "ping",
					Data:  "ping",
				})
			case <-done:
				return
			}
		}
	}()
	recv, err := h.Service.GetLog(ctx, &entity.RuntimeMetadata{
		ContainerID:     containerID,
		URI:             uri,
		RuntimeProvider: lo.Ternary(provider != "", entity.RuntimeProviderType(provider), entity.RuntimeProviderTypeDocker),
	}, runID, account)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get log: %+v", err)
		return
	}
	for data := range recv.DataChannel {
		// Too many think_delta, skip it.
		if lo.Contains(
			[]iris.AgentRunEventType{
				iris.AgentRunEventThinkDelta,
				iris.AgentRunEventShellStdio,
			}, data.Event) {
			continue
		}
		pubErr := pub(LogEvent{
			Event:     string(data.Event),
			Data:      data.Data,
			Offset:    data.Offset,
			Timestamp: data.Timestamp.Format(time.RFC3339),
		})
		if pubErr != nil {
			log.V1.CtxError(ctx, "failed to publish agent run stream events to sse: %v", pubErr)
			recv.Close()
			break
		}
	}
}

func (h *Handler) UpdateRuntimeWorkspaceState(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[agentsphere.UpdateRuntimeStateRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "update runtime workspace state request: %+v", req)
	if h.TestingService.IsTestingRequest(ctx, req.SessionID) {
		h.TestingService.SaveDebugURI(ctx, req.SessionID, req.URI)
		c.JSON(http.StatusOK, agentsphere.UpdateRuntimeStateResponse{
			Message: "update debugging session",
		})
		return
	}
	workspaceStatus := (entity.SessionStatus)(req.Status)
	_, err := h.Service.UpdateAgentRunBySession(ctx, runtimeservice.UpdateAgentRuntimeStateOption{
		SessionID: req.SessionID,
		Status:    lo.Ternary(workspaceStatus.IsReady(), entity.AgentRunStatusReady, entity.AgentRunStatusFailed),
		URI:       req.URI,
		PID:       int(req.PID),
		InitCost:  time.Duration(req.InitTimeCost).Milliseconds(),
		Message:   req.Message,
		Sync:      true,
	})

	if err != nil {
		log.V1.CtxError(ctx, "failed to update session: %+v", err)
		c.JSON(http.StatusInternalServerError, agentsphere.UpdateRuntimeStateResponse{
			Message: err.Error(),
		})
		return
	}
	if req.Status == agentsphere.SessionStatusRunning {
		// container is ready
		session, err := h.Service.GetNextSession(ctx, runtimeservice.GetNextSession{
			SessionID: req.SessionID,
			Sync:      true,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to run agent by get next session error: %+v", err)
			c.JSON(http.StatusInternalServerError, agentsphere.UpdateRuntimeStateResponse{
				Message: err.Error(),
			})
			return
		}
		if session.Status == nextentity.SessionStatusPending {
			_ = metrics.NSM.PrepareCubeTimer.WithTags(&metrics.NextServerPrepareCubeTag{
				Role: int(pointer.Get(session.Role)),
			}).Observe(float64(time.Since(session.CreatedAt).Milliseconds()))
			session, err = h.Service.UpdateSession(ctx, runtimeservice.UpdateNextSession{
				SessionID: req.SessionID,
				Status:    pointer.To(nextentity.SessionStatusPrepared),
			})
			if err != nil {
				log.V1.CtxError(ctx, "failed to update session: %+v", err)
				c.JSON(http.StatusInternalServerError, agentsphere.UpdateRuntimeStateResponse{
					Message: err.Error(),
				})
				return
			}
		}

		// 只有 created 状态才执行 Run Agent
		if session.Status == nextentity.SessionStatusCreated {
			go func(ctx context.Context) {
				defer func() {
					if r := recover(); r != nil {
						log.V1.CtxError(ctx, "panic in run agent: %+v, stacktrace: %s, session id: %s, uri: %s", r, string(debug.Stack()), req.SessionID, req.URI)
					}
				}()
				logID, _ := ctxvalues.LogID(ctx)
				var newCtx context.Context
				if session.RuntimeMetaData.LogID != "" && session.RuntimeMetaData.LogID != logID {
					newCtx = ctxvalues.SetLogID(ctx, session.RuntimeMetaData.LogID)
				} else {
					newCtx = ctx
				}
				operation := func() error {
					return h.Service.NextRunAgent(newCtx, runtimeservice.NextRunAgentOption{
						SessionID: req.SessionID,
						URI:       req.URI,
					})
				}
				err = backoff.Retry(operation, backoff.WithContext(backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3), ctx))
				if err != nil {
					log.V1.CtxError(ctx, "failed to run agent: %+v, session id: %s, uri: %s, set session to error", err, req.SessionID, req.URI)
					session, err = h.Service.UpdateSession(ctx, runtimeservice.UpdateNextSession{
						SessionID: req.SessionID,
						Status:    pointer.To(nextentity.SessionStatusError),
					})
					if err != nil {
						log.V1.CtxError(ctx, "failed to update session: %+v", err)
					}
				} else {
					// Run Agent 后修改 Session 状态为 Running
					_, err = h.Service.UpdateSession(ctx, runtimeservice.UpdateNextSession{
						SessionID: session.ID,
						Status:    lo.ToPtr(nextentity.SessionStatusRunning),
					})
					if err != nil {
						logs.V1.CtxError(ctx, "session %s: failed to update session status: %v", session.ID, err)
					}
				}
			}(context.WithoutCancel(ctx))
		} else {
			log.V1.CtxInfo(ctx, "session %s is not status `created`, skip run agent", req.SessionID)
		}

		if session.Status == nextentity.SessionStatusStopped {
			_, err = h.Service.UpdateSession(ctx, runtimeservice.UpdateNextSession{
				SessionID: session.ID,
				Status:    lo.ToPtr(nextentity.SessionStatusRunning),
			})
			if err != nil {
				logs.V1.CtxError(ctx, "session %s: failed to update session status: %v", session.ID, err)
			}
		}
	}
	if req.Status == agentsphere.SessionStatusStopped {
		log.V1.CtxInfo(ctx, "container encountered an error and exited")
	}

	c.JSON(200, agentsphere.UpdateRuntimeStateResponse{
		Message: "success",
	})
}

func (h *Handler) ConnectNextEvents(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[entity.RequireConnectionRequest](ctx, c)
	if req == nil {
		return
	}
	ctx = logs.CtxAddKVs(ctx, "_session_id", req.SessionID)
	log.V1.CtxInfo(ctx, "connect next events request: %+v", req)

	err := h.Service.ProcessNextAgentRunEvents(ctx, runtimeservice.ProcessEventsForAgentRunOption{
		SessionID: req.SessionID,
		RunID:     req.RunID,
		Reconnect: true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect runtime: %+v", err)
		c.JSON(http.StatusInternalServerError, entity.RequireConnectionResponse{
			Message: err.Error(),
		})
		return
	}

	c.JSON(200, entity.RequireConnectionResponse{
		Message: "success",
	})
}

// ConnectAndSaveNextEvents 发起一次runtime连接并保存事件，在处理完事件之后释放连接.
func (h *Handler) ConnectAndSaveNextEvents(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[entity.RequireConnectionRequest](ctx, c)
	if req == nil {
		return
	}
	ctx = logs.CtxAddKVs(ctx, "_session_id", req.SessionID)
	log.V1.CtxInfo(ctx, "connect and save next events request: %+v", req)

	err := h.Service.ProcessNextAgentRunEvents(ctx, runtimeservice.ProcessEventsForAgentRunOption{
		SessionID: req.SessionID,
		RunID:     req.RunID,
		Reconnect: false,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect runtime: %+v", err)
		c.JSON(http.StatusInternalServerError, entity.RequireConnectionResponse{
			Message: err.Error(),
		})
		return
	}

	c.JSON(200, entity.RequireConnectionResponse{
		Message: "success",
	})
}

func (h *Handler) RefreshToken(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[entity.RefreshTokenRequest](ctx, c)
	if req == nil {
		return
	}

	if req.Token == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "token is empty")
		return
	}

	var origin service.AuthOrigin
	switch req.TokenType {
	case entity.TokenTypeCodebase:
		origin = service.AuthOriginCodebase
	case entity.TokenTypeNextCode:
		origin = service.AuthOriginNextCode
	default:
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "not found support token type")
		return
	}

	newToken, err := h.AuthService.RefreshToken(ctx, origin, req.Token)
	if err != nil {
		log.V1.CtxError(ctx, "refresh token origin: %s failed: %+v", origin, err)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "failed to refresh token")
		return
	}

	c.JSON(http.StatusOK, entity.RefreshTokenResponse{
		Token: newToken,
	})
}

type RedirectLogURL struct {
	SessionID string `path:"session_id,required"`
}

func (h *Handler) RedirectLogURL(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[RedirectLogURL](ctx, c)
	if req == nil || req.SessionID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid req")
		return
	}

	run, err := h.Service.GetAgentRun(ctx, runtimeservice.GetAgentRunOption{
		SessionID: req.SessionID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get agent run: %+v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "failed get agent run")
		return
	}
	domain := h.Conf.BaseConfig.Domain
	if run.RuntimeMetadata.RuntimeProvider == entity.RuntimeProviderTypeDocker {
		domain = "http://localhost:6789"
	}
	c.Response.Header.Set("Location", fmt.Sprintf("%s/api/agents/v2/runtime/log/html?run_id=%s&container_id=%s&uri=%s&provider=%s", domain, run.ID, run.RuntimeMetadata.ContainerID, url.PathEscape(run.RuntimeMetadata.URI), run.RuntimeMetadata.RuntimeProvider))
	c.AbortWithStatus(http.StatusFound)
}

func (h *Handler) ProxyToAMapLBS(ctx context.Context, c *app.RequestContext) {
	_, proxyPath, found := strings.Cut(string(c.Request.Path()), "/api/agents/v2/lbsproxy/_AMapService")
	if !found {
		log.V1.CtxError(ctx, "failed to proxy to AMapLBS request")
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid path")
		return
	}

	cfg := h.AMapConfig.GetValue()
	host := cfg.APIHost
	if strings.Contains(proxyPath, "/v4/map/styles") {
		host = cfg.CustomHost
	} else if strings.Contains(proxyPath, "/v3/vectormap") {
		host = cfg.OverseaHost
	}

	query := c.Request.URI().QueryArgs().String()
	if query != "" {
		query = "?" + query + "&jscode=" + cfg.SK
	} else {
		query = "?jscode=" + cfg.SK
	}
	proxyURL := fmt.Sprintf("https://%s", host) + proxyPath
	log.V1.CtxInfo(ctx, "proxy to AMapLBS request: %s", proxyURL)
	o := byted.ClientOption{
		F: func(o *byted.ClientOptions) {
			o.Opts = append(o.Opts, hertzconfig.ClientOption{
				F: func(o *hertzconfig.ClientOptions) {
					o.Dialer = standard.NewDialer()
				},
			})
		}}
	proxy, err := reverseProxy.NewSingleHostReverseProxy(proxyURL, o)
	if err != nil {
		log.V1.CtxError(ctx, "failed new single proxy: %s, err: %v", proxyURL, err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "invalid proxy url")
		return
	}
	proxy.SetDirector(func(req *protocol.Request) {
		req.SetHost(host)
		req.Header.SetHost(host)
		req.SetRequestURI(proxyPath + query)
		log.V1.CtxInfo(ctx, "req uri: %s", req.URI().String())
	})
	proxy.ErrorHandler = func(c *app.RequestContext, err error) {
		log.V1.CtxError(ctx, "proxy to AMapLBS request failed: %+v", err)
	}

	proxy.ServeHTTP(ctx, c)
	c.Abort()
}

func (h *Handler) IngestTraces(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[agentsphere.IngestTracesRequest](ctx, c)
	if req == nil {
		return
	}

	err := h.Service.IngestTraces(ctx, req)
	if err != nil {
		log.V1.CtxError(ctx, "failed to ingest traces: %+v", err)
		c.JSON(http.StatusInternalServerError, agentsphere.IngestTracesResponse{
			BaseResp: &common.BaseResp{
				Code:    int64(common.ErrorCode_ErrInternal),
				Message: err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, agentsphere.IngestTracesResponse{})
}

func (h *Handler) BindingAgent(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[entity.BindAgentRequest](ctx, c)
	if req == nil {
		return
	}
	containerInfo, err := h.TestingService.GetTestingContainerInfo(ctx, req.MainContainerSessionID)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, -1, err.Error())
		return
	}
	agentRun, err := h.RuntimeService.NextCreateAgentRunForPrepare(ctx, runtimeservice.NextCreateAgentRunForPrepare{
		SessionID:   req.SessionID,
		Provider:    testingservice.GetProviderType(),
		URI:         req.URI,
		ContainerID: containerInfo.ContainerID,
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, -1, err.Error())
		return
	}
	_, err = h.SessionService.UpdateSession(ctx, sessionservice.UpdateSessionOption{
		SessionID: req.SessionID,
		Status:    lo.ToPtr(nextentity.SessionStatusBound),
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, -1, err.Error())
		return
	}
	c.JSON(http.StatusOK, map[string]any{"agent_run_id": agentRun.ID})
}

func (h *Handler) GetRunTestCasesContext(ctx context.Context, c *app.RequestContext) {
	account, ok := h.AuthM.GetAccount(ctx, c)
	if !ok {
		hertz.JSONMessage(c, http.StatusUnauthorized, apierror.GetErrorCode(&apierror.ErrNoAuth),
			apierror.GetErrorMessage(&apierror.ErrNoAuth))
		return
	}
	request, err := h.RuntimeService.NextGenerateRunAgentRequest(ctx, nextentity.SessionRoleIndustryVeteran, account.Username)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, -1, err.Error())
		return
	}
	c.JSON(http.StatusOK, request)
}
