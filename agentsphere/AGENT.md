# Next Server 开发流程文档

## 项目结构概览

### next_server/ 文件夹结构
`next_server` 是 AgentSphere 的主要服务层，负责处理业务逻辑和 API 接口。

```
agentsphere/next_server/
├── handler/          # HTTP 处理层 (35+ 个业务处理器)
├── service/          # 业务服务层 (按模块分组的服务实现)
│   ├── a2arouter/    # A2A 路由服务
│   ├── ab/           # A/B 测试服务
│   ├── activity/     # 活动服务
│   ├── agent/        # Agent 服务
│   ├── artifact/     # 制品服务
│   ├── bpm/          # 业务流程管理服务
│   ├── debug/        # 调试服务
│   ├── deploy_review/# 部署审核服务
│   ├── deployment/   # 部署服务
│   ├── icm/          # ICM 服务
│   ├── knowledgebase/# 知识库服务
│   ├── knowledgeset/ # 知识集服务
│   ├── lark/         # Lark 集成服务
│   ├── lock/         # 分布式锁服务
│   ├── mcp/          # MCP 协议服务
│   ├── mention/      # 提及服务
│   ├── monitor/      # 监控服务
│   ├── permission/   # 权限服务
│   ├── prompt/       # 提示词服务
│   ├── replay/       # 重放服务
│   ├── resourcemanage/# 资源管理服务
│   ├── scm/          # 源码管理服务
│   ├── session/      # 会话服务
│   ├── session_collection/ # 会话集合服务
│   ├── share/        # 分享服务
│   ├── space/        # 工作空间服务
│   ├── template/     # 模板服务
│   ├── testing/      # 测试服务
│   ├── trace/        # 追踪服务
│   └── user/         # 用户服务
├── entity/           # 数据实体定义 (40+ 个业务实体)
├── dal/              # 数据访问层 (数据库操作和缓存)
├── static/           # 静态资源
├── pack/             # 打包相关
├── mock/             # 测试 Mock 数据
```


### runtime/ 文件夹结构
`runtime` 是 Agent 运行时环境，负责 Agent 的执行和管理。

```
agentsphere/runtime/
├── service/          # 运行时核心服务 (15 个服务文件)
├── handler/          # HTTP 和消息队列处理器
├── nexthandler/      # Next 协议处理器
├── eval/             # 评估和测试相关
├── dal/              # 数据访问层
├── eventbus/         # 事件总线系统
└── OWNERS            # 代码所有者配置
```

#### 核心运行时服务
- `service.go` - 主要运行时服务
- `nextservice.go` - Next 协议服务
- `next.go` - Next 核心实现
- `nexttool.go` - 工具调用服务
- `provider.go` - 服务提供者
- `nextevent.go` - 事件处理

## 开发流程

### 1. 功能开发流程

#### Step 1: 需求分析与设计
```bash
# 创建功能分支
git checkout -b aime/feature-name

# 分析涉及的模块
# - next_server: 业务逻辑和 API
# - runtime: 运行时执行逻辑
```

#### Step 2: IDL 生成 (如涉及接口变更)
如涉及 API 变更或新增数据模型：

1. **更新对应的 `.thrift` 文件** (位于 `api/idl/nextagent/`)
2. **重新生成模型**:
   ```bash
   cd api/idl/nextagent/
   # 针对修改的 thrift 文件执行，例如：
   go run code.byted.org/devgpt/kiwis/dev/tools/gen hertztool model --rm_tag form --rm_tag query --snake_tag --idl your_file.thrift --model_dir ../hertz_gen -t=template=slim
   ```
3. **确认生成无误**后再进行后续开发

#### Step 3: 数据层开发 (DAL)
1. **next_server/entity/**: 定义业务实体
   ```go
   // 添加新的业务实体
   type NewEntity struct {
       ID          int64     `json:"id"`
       Name        string    `json:"name"`
       CreatedAt   time.Time `json:"created_at"`
       // ... 其他字段
   }
   ```

2. **next_server/dal/**: 实现数据访问逻辑
   ```go
   // 实现 CRUD 操作
   func (d *DAO) CreateNewEntity(ctx context.Context, entity *NewEntity) error
   func (d *DAO) GetNewEntity(ctx context.Context, id int64) (*NewEntity, error)
   ```

3. **runtime/dal/**: 运行时数据访问 (如需要)

#### Step 4: 服务层开发 (Service)
1. **next_server/service/**: 业务逻辑实现
   ```go
   // service/newfeature/service.go
   type Service struct {
       dao DAOInterface
   }
   
   func (s *Service) ProcessNewFeature(ctx context.Context, req *Request) (*Response, error) {
       // 业务逻辑处理
   }
   ```

2. **runtime/service/**: 运行时服务 (如需要)
   - 在现有服务文件中添加功能
   - 或创建新的服务组件

#### Step 5: 处理层开发 (Handler)
1. **next_server/handler/**: HTTP API 处理
   ```go
   // handler/newfeature.go
   func (h *Handler) HandleNewFeature(c context.Context, ctx *app.RequestContext) {
       // 参数解析
       // 调用服务层
       // 返回响应
   }
   ```

2. **runtime/handler/** 或 **runtime/nexthandler/**: 运行时处理
   - HTTP 处理器
   - MQ 消息处理器

#### Step 6: 路由配置
1. **next_server/handler/route.go**: 注册 HTTP 路由
2. **runtime/handler/route.go** 或 **runtime/nexthandler/router.go**: 运行时路由

### 2. 测试开发

#### 单元测试
```bash
# 在对应的包目录下添加 *_test.go 文件
# next_server/dal/newentity_test.go
# next_server/service/newfeature/service_test.go
```

### 3. 部署流程

#### 编译
确认代码变更后，执行编译确保编译通过
```bash
./build.sh agentsphere_nextserver
```

#### 代码提交
```bash
# 遵循 Git 规范
git add .
git commit -m "feat(next_server): implement new feature functionality"

# 推送并创建 PR
git push origin aime/feature-name
```

#### SCM & TCE 部署

Step 1: Local Development & Testing

Step 2: SCM Compilation

- Use SCM MCP tool to compile
- SCM Name: `devgpt/agentsphere/nextserver` (USE THIS SCM NAME, DO NOT USE `devgpt/openapi/server`)
  - DO NOT USE `SCM编译_10_get_scm_name` to get the scm name
- Branch Name: use provided branch name by user
- Wait 1 minute for compilation to finish before next step

Step 3: TCE Deployment

- Use boe or ppe泳道部署 MCP tool to deploy compiled SCM version
  - PSM: flow.agentsphere.nextserver
  - ENV: use provided env name(boe_xxx/ppe_xxx)
- No need wait for deployment to finish, just trigger the deployment, make sure its under running status and return the ticket url to user

## 最佳实践

### 代码结构
- 遵循分层架构：Handler → Service → DAL
- 保持单一职责原则
- 使用依赖注入

## 代码审查规范

- 如果是Merge request review/代码审查，请一定参考 @agentsphere/docs/code_review_guide.md 