package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"runtime/debug"
	"strings"
	"syscall"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cenkalti/backoff/v4"

	// import before any other imports and must not sorted
	// to get accurate init time
	"code.byted.org/devgpt/kiwis/agentsphere/cmd/runtime/aaastarttime"

	"code.byted.org/codebase/codfish/core/daemon"
	"github.com/sirupsen/logrus"
	"go.uber.org/fx"

	autofix_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/autofix/agent"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	panic_fix "code.byted.org/devgpt/kiwis/agentsphere/agents/panic/agent"
	planact_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/agent"
	project_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/projectgen/agent"
	wiki_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/repowiki/agent"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/client"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/trace"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	_ "code.byted.org/devgpt/kiwis/agentsphere/runtime/eval/cases"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/lib/cloud_oauth"
)

type (
	ServerAddr string
	Workspace  string
)

type App struct {
	fx.In
	Config     entity.RuntimeDaemonConfig
	Logger     *logrus.Logger
	APIClient  *client.RuntimeAPIClient
	Listener   net.Listener
	ServerAddr ServerAddr
	Workspace  Workspace
	Runtime    *runtime.RemoteRuntime
}

func main() {
	var runtime App
	app := fx.New(
		fx.Provide(
			initConfig,
			initLogger,
			provideAPIClient,
			provideListener,
			provideWorkspace,
			provideServer,
			provideRuntime,
			cloud_oauth.TryNewCloudOAuthClient,
		),
		fx.Populate(&runtime),
		fx.Invoke(initMetrics),
		fx.Invoke(trace.InitBytedTrace),
		fx.Invoke(registerAgents),
		fx.Invoke(reportStatus),
	)

	if err := app.Start(context.Background()); err != nil {
		reportErr := reportStatusWithRetry(context.Background(), runtime.APIClient, runtime.Logger, &agentsphere.UpdateRuntimeStateRequest{
			SessionID: runtime.Config.SessionID,
			Status:    string(entity.SessionStatusStopped),
			Message:   err.Error(),
			PID:       int32(os.Getpid()),
		})
		if reportErr != nil {
			runtime.Logger.Errorf("Failed to report error status: %v", reportErr)
		}
		os.Exit(1)
	}

	<-app.Done()
}

func provideAPIClient(config entity.RuntimeDaemonConfig, logger *logrus.Logger) (*client.RuntimeAPIClient, error) {
	if !config.Debug {
		log.V1.Stop()
		log.V2.Close()
	}
	if config.WorkspacePath != "" {
		_ = os.Chdir(config.WorkspacePath)
	}
	if config.SUID != 0 {
		_ = syscall.Setuid(config.SUID)
	}

	return client.NewRuntimeAPIClient(config.APIBaseURL, logger, config.SessionType)
}

func provideListener(config entity.RuntimeDaemonConfig) (net.Listener, ServerAddr, error) {
	addr, listener, err := listen(config)
	if err != nil {
		return nil, "", err
	}
	return listener, ServerAddr(addr), nil
}

func provideWorkspace() (Workspace, error) {
	cwd, err := os.Getwd()
	if err != nil {
		return "", err
	}
	return Workspace(cwd), nil
}

func provideRuntime(lc fx.Lifecycle, config entity.RuntimeDaemonConfig, apiClient *client.RuntimeAPIClient,
	logger *logrus.Logger, serverAddr ServerAddr, workspace Workspace, cloudOAuthClient *cloud_oauth.OAuthClient, _ *http.Server) (*runtime.RemoteRuntime, error) {
	rt, err := runtime.NewRemoteRuntime(runtime.RuntimeOption{
		Addr:             string(serverAddr),
		Workspace:        string(workspace),
		SessionID:        config.SessionID,
		Config:           config,
		API:              apiClient,
		Logger:           logger,
		CloudOAuthClient: cloudOAuthClient,
	})

	if err != nil {
		return nil, err
	}

	lc.Append(fx.Hook{
		OnStop: func(ctx context.Context) error {
			return nil
		},
	})

	return rt, nil
}

func registerAgents(runtime *runtime.RemoteRuntime) error {
	runtime.RegisterAgent(panic_fix.NewPanicFixAgent())
	runtime.RegisterAgent(project_agent.NewProjectAgent())
	runtime.RegisterAgent(project_agent.NewNPEFixer())
	runtime.RegisterAgent(autofix_agent.NewAutofixAgent())
	runtime.RegisterAgent(planact_agent.NewPlanActAgent(planact_agent.AgentIDV1))
	runtime.RegisterAgent(planact_agent.NewPlanActAgent(planact_agent.AgentIDV1_5))
	runtime.RegisterAgent(wiki_agent.NewRepoWikiAgent())
	return runtime.Ready()
}

func provideServer(lc fx.Lifecycle, listener net.Listener, logger *logrus.Logger) (*http.Server, error) {
	server := daemon.CreateDaemonServer()
	go func() {
		if err := server.Serve(listener); err != nil {
			logger.Errorf("Server error: %v", err)
		}
	}()
	lc.Append(fx.Hook{
		OnStop: func(ctx context.Context) error {
			return server.Shutdown(ctx)
		},
	})
	return server, nil
}

func reportStatus(lc fx.Lifecycle, app App) error {
	startTime := aaastarttime.StartTime
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			app.Logger.Infof("start finished, time cost: %v", time.Since(startTime))
			err := reportStatusWithOptimization(ctx, app.APIClient, app.Logger, &agentsphere.UpdateRuntimeStateRequest{
				SessionID:    app.Config.SessionID,
				Status:       string(entity.SessionStatusRunning),
				URI:          app.Runtime.ControllerBusURI(),
				PID:          int32(os.Getpid()),
				InitTimeCost: time.Since(startTime).Milliseconds(),
			})

			if err != nil {
				app.Logger.Errorf("Failed to report running status: %v", err)
				return nil
			}
			go func() {
				defer func() {
					if e := recover(); e != nil {
						app.Logger.Errorf("recovered from panic: %v, stack: %v", e, debug.Stack())
					}
				}()

				// 判断是不是重启
				ctxStorage := app.Runtime.GetCtxStorage()
				if ctxStorage == nil {
					return
				}

				restart, err := ctxStorage.IsRestart()
				if err != nil {
					app.Logger.Errorf("failed to check restart: %v", err)
					return
				}
				app.Logger.Infof("start check restart status: %v", restart)
				if restart {
					request, exist, err := ctxStorage.Recover(ctx, iris.StorageTypeRunAgentRequest)
					if err != nil {
						app.Logger.Errorf("failed to recover run agent request: %v", err)
						return
					}
					if !exist {
						app.Logger.Errorf("run agent request is not exist")
						return
					}
					req, ok := request.(*entity.RunAgentRequest)
					if !ok {
						app.Logger.Errorf("failed to assert run agent request")
						return
					}
					req.Restart = true
					if req.AgentName == planact_agent.AgentIDV1_5 || req.AgentName == planact_agent.AgentIDV1 {
						_, err = app.Runtime.RunAgent(context.Background(), *req)
						if err != nil {
							app.Logger.Errorf("failed to restart run agent: %v", err)
						}
					}

				}
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			err := app.APIClient.ReportStatus(ctx, &agentsphere.UpdateRuntimeStateRequest{
				SessionID:    app.Config.SessionID,
				Status:       string(entity.SessionStatusStopped),
				PID:          int32(os.Getpid()),
				InitTimeCost: int64(time.Since(startTime).Milliseconds()),
			})

			if err != nil {
				app.Logger.Errorf("Failed to report stopped status: %v", err)
			}

			return nil
		},
	})

	return nil
}

// reportStatusWithOptimization 带优化逻辑的状态上报包装函数
// 实现 Pod 就绪检查和重试机制
func reportStatusWithOptimization(ctx context.Context, apiClient *client.RuntimeAPIClient, logger *logrus.Logger, req *agentsphere.UpdateRuntimeStateRequest) error {
	// 等待 pod ready 状态检查
	if err := waitForPodReady(ctx, logger); err != nil {
		logger.Warnf("pod ready check failed, proceeding with status report: %v", err)
	}

	// 使用重试机制上报状态
	return reportStatusWithRetry(ctx, apiClient, logger, req)
}

// waitForPodReady 等待 pod 变为 ready 状态
func waitForPodReady(ctx context.Context, logger *logrus.Logger) error {
	const statusFile = "/etc/cube-downward/status"
	const maxWaitTime = 1 * time.Minute
	const checkInterval = 2 * time.Second

	// 检查文件是否存在
	if _, err := os.Stat(statusFile); os.IsNotExist(err) {
		logger.Infof("status file %s does not exist, proceeding with status report", statusFile)
		return nil
	}

	// 创建带超时的 context
	ctx, cancel := context.WithTimeout(ctx, maxWaitTime)
	defer cancel()

	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	logger.Infof("waiting for pod to become ready by checking %s", statusFile)

	for {
		select {
		case <-ctx.Done():
			logger.Warnf("timeout waiting for pod ready status after %v", maxWaitTime)
			return fmt.Errorf("timeout waiting for pod ready status")
		case <-ticker.C:
			content, err := os.ReadFile(statusFile)
			if err != nil {
				logger.Warnf("failed to read status file %s: %v", statusFile, err)
				continue
			}

			status := strings.TrimSpace(string(content))
			logger.Debugf("current pod status: %s", status)

			if status == "Running" {
				logger.Infof("pod is ready (status: %s)", status)
				return nil
			}
		}
	}
}

// reportStatusWithRetry 使用重试机制上报状态
func reportStatusWithRetry(ctx context.Context, apiClient *client.RuntimeAPIClient, logger *logrus.Logger, req *agentsphere.UpdateRuntimeStateRequest) error {
	const retryInterval = 30 * time.Second
	const maxRetries = 3

	// 配置重试策略
	bo := backoff.NewExponentialBackOff()
	bo.InitialInterval = retryInterval
	bo.MaxInterval = retryInterval
	bo.MaxElapsedTime = 0 // 不设置总超时时间，由重试次数控制
	bo.Multiplier = 1.0   // 固定间隔，不使用指数退避

	retryCount := 0
	err := backoff.Retry(func() error {
		retryCount++
		logger.Infof("attempting to report status (attempt %d/%d): %+v", retryCount, maxRetries, req)

		err := apiClient.ReportStatus(ctx, req)
		if err != nil {
			logger.Errorf("failed to report status (attempt %d/%d): %v", retryCount, maxRetries, err)
			return err
		}

		logger.Infof("successfully reported status (attempt %d/%d)", retryCount, maxRetries)
		return nil
	}, backoff.WithMaxRetries(bo, maxRetries-1)) // maxRetries-1 因为第一次尝试不算重试

	if err != nil {
		logger.Errorf("failed to report status after %d attempts: %v", maxRetries, err)
	}

	return err
}
