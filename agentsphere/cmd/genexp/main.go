package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"

	"code.byted.org/gopkg/logid"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/sashabaranov/go-openai"
)

type roundTrip struct{}

func (r *roundTrip) RoundTrip(req *http.Request) (*http.Response, error) {
	sessionID := uuid.New().String()
	req.Header.Add(entity.SessionIDHeader, sessionID)
	req.Header.Add(framework.LLMTagKey, conv.DefaultAny[string](req.Context().Value(framework.LLMTagKey)))
	fmt.Printf("sessionID: %v\n", sessionID)
	logID := logid.GenLogID()
	req.Header.Add("x-tt-logid", logID)
	req.Header.Add("get-svc", "1")
	fmt.Printf("logID: %v\n", logID)
	resp, err := http.DefaultTransport.RoundTrip(req)
	if resp != nil {
		// if resp.StatusCode != 200 {
		// 	body, _ := io.ReadAll(resp.Body)
		// 	fmt.Printf("resp body: %s\n", string(body))
		// 	r, _ := io.ReadAll(req.Body)
		// 	fmt.Printf("req body: %s\n", string(r))
		// }
		fmt.Printf("status: %s, llm logid: %s\n", resp.Status, resp.Header.Get("x-tt-logid"))
	}
	return resp, err
}

// for local testing
func createLLM() (framework.LLM, error) {
	m, err := framework.NewAzureOpenAILLM(
		"",
		// "http://localhost:6789/api/agents/v2/runtime",
		// "https://aime-boe.bytedance.net/api/agents/v2/runtime",
		"https://aime.bytedance.net/api/agents/v2/runtime",
		func(cc *openai.ClientConfig) {
			cc.HTTPClient = &http.Client{
				Timeout:   time.Second * 6000,
				Transport: &roundTrip{},
			}
		},
	)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create llm")
	}
	return m, nil
}

func main() {
	defer func(now time.Time) {
		fmt.Printf("gen_exp_sop total cost: %v\n", time.Since(now))
	}(time.Now())
	// go run agentsphere/cmd/genexp/main.go --trace_file dev/local/trace/traces/trace_git_1.jsonl --result_dir dev/local/trace/result --model gemini-2.5-preview --summarize_actor
	var (
		traceFilePath      = flag.String("trace_file", "dev/local/trace/traces/trace_git_1.jsonl", "trace file path")
		resultDir          = flag.String("result_dir", "dev/local/trace/result", "result directory")
		model              = flag.String("model", "gemini-2.5-preview", "model name")
		genQueryTemplate   = flag.Bool("query_template", false, "generate exp query template")
		genProgressPlan    = flag.Bool("progress_plan", false, "generate exp progress plan")
		genSOP             = flag.Bool("sop", false, "generate exp SOP")
		summarizeActor     = flag.Bool("summarize_actor", false, "summarize actor execution")
		saveSummarizeActor = flag.Bool("save_summarize_actor", false, "save summarize actor result")
		saveInter          = flag.Bool("save_inter", false, "save intermediate result")
		genSOPN            = flag.Int("n", 1, "generate n SOP")
		evalSOPPath        = flag.String("eval_sop_path", "", "filepath of the sop json file to evaluate")
	)
	flag.Parse()
	llm, err := createLLM()
	if err != nil {
		panic(err)
	}
	fmt.Println("simple:", *genProgressPlan)
	fmt.Println("summarize_actor:", *summarizeActor)
	fmt.Println("trace_file:", *traceFilePath)
	fmt.Println("result_dir:", *resultDir)
	fmt.Println("model:", *model)

	trajectory, err := genexp.ParseTraceFile(*traceFilePath)
	if err != nil {
		panic(err)
	}

	if *saveInter {
		os.WriteFile(filepath.Join(*resultDir, "exp", "trajectory.json"), []byte(conv.JSONFormatString(trajectory)), 0644)
	}

	os.MkdirAll(filepath.Join(*resultDir, "exp"), 0755)
	var queryTemplate *entity.ExpUserQueryTemplate
	if *genQueryTemplate {
		opt := genexp.GenerateUserQueryTemplateOption{
			Trajectory: trajectory,
			ResultDir:  *resultDir,
			Model: iris.SceneModelConfig{
				Model:       *model,
				Temperature: 0.5,
				MaxTokens:   16000,
			},
			LLM: llm,
		}
		queryTemplate, err = genexp.GenerateUserQueryTemplate(context.Background(), opt)
		if err != nil {
			panic(err)
		}
		os.WriteFile(filepath.Join(*resultDir, "exp", "query_template.json"), []byte(conv.JSONFormatString(queryTemplate)), 0644)
	}
	if *genProgressPlan {
		opt := genexp.GenerateTemplateExpProgressPlanOption{
			Trajectory: trajectory,
			ResultDir:  *resultDir,
			Model: iris.SceneModelConfig{
				Model:       *model,
				Temperature: 0.5,
				MaxTokens:   16000,
			},
			LLM: llm,
		}
		progressPlan, err := genexp.GenerateTemplateExpProgressPlan(context.Background(), opt)
		if err != nil {
			panic(err)
		}
		os.WriteFile(filepath.Join(*resultDir, "exp", "progress_plan.json"), []byte(conv.JSONFormatString(progressPlan)), 0644)
	}
	if *genSOP {
		opt := genexp.GenerateTemplateExpSOPOption{
			Trajectory: trajectory,
			ResultDir:  *resultDir,
			Model: iris.SceneModelConfig{
				Model:       *model,
				Temperature: 0.5,
				MaxTokens:   16000,
			},
			SummarizeActor:    *summarizeActor,
			SaveIntermediate:  *saveInter,
			LLM:               llm,
			UserQueryTemplate: queryTemplate,
		}
		sop, err := genexp.GenerateTemplateExpSOPWithScores(context.Background(), opt, *genSOPN)
		if err != nil {
			panic(err)
		}
		os.WriteFile(filepath.Join(*resultDir, "exp", "exp_sop.json"), []byte(conv.JSONFormatString(sop)), 0644)
	}
	if *saveSummarizeActor {
		opt := genexp.GenerateTemplateExpSOPOption{
			Trajectory:       trajectory,
			ResultDir:        *resultDir,
			SaveIntermediate: *saveInter,
			Model: iris.SceneModelConfig{
				Model:       *model,
				Temperature: 0.5,
				MaxTokens:   16000,
				Tag:         "summarize_actor",
			},
			LLM: llm,
		}
		for i, item := range trajectory {
			if item.Round == nil || item.Round.Plan.Actor != "mewtwo" {
				continue
			}
			sum, err := genexp.TmplSummarizeActorExecution(context.Background(), opt, item, i)
			if err != nil {
				panic(err)
			}
			os.WriteFile(filepath.Join(*resultDir, "exp", fmt.Sprintf("summarize_actor_%d.xml", i)), []byte(sum), 0644)
			steps, err := genexp.ParseActorExecutionLogicalStep(sum)
			if err != nil {
				panic(err)
			}
			os.WriteFile(filepath.Join(*resultDir, "exp", fmt.Sprintf("summarize_actor_%d.json", i)), []byte(conv.JSONFormatString(steps)), 0644)
			os.WriteFile(filepath.Join(*resultDir, "exp", fmt.Sprintf("summarize_actor_%d.txt", i)), []byte(genexp.GetLogicalStepsText(steps)), 0644)
		}
	}
	if len(*evalSOPPath) > 0 {
		data, err := os.ReadFile(*evalSOPPath)
		if err != nil {
			panic(err)
		}
		sop := &entity.ExpSOP{}
		err = json.Unmarshal(data, sop)
		if err != nil {
			panic(err)
		}
		n := *genSOPN
		if n <= 0 {
			n = 1
		}
		for i := range n {
			res, err := genexp.EvaluateTemplateExpSOP(context.Background(), genexp.GenerateTemplateExpSOPOption{
				SaveIntermediate: true,
				ResultDir:        *resultDir,
				Model: iris.SceneModelConfig{
					Model:       *model,
					Temperature: 0.2,
					MaxTokens:   4000,
				},
				LLM: llm,
			}, sop, i)
			if err != nil {
				panic(err)
			}
			os.WriteFile(filepath.Join(*resultDir, "exp", fmt.Sprintf("eval_sop_%d.json", i)), []byte(conv.JSONFormatString(res)), 0644)
		}
	}
}
