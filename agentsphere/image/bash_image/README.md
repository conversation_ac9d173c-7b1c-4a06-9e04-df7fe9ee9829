# 1. 开发和使用
如果需要改bash tool相关的，修改bash_base.dockerfile，使用bash_general.dockerfile进行编译。

如果需要改AI相关的，比如mcp、project templates等，修改runtime_general.dockerfile即可，不用改bash镜像。

# 2. bash_base.dockerfile
bash镜像的公共部分，被其他dockerfile引用，不直接编译

# 3. bash_general.dockerfile
通用镜像，包含python、js、golang、java的常用版本

ICM链接为 https://cloud.bytedance.net/icm/detail/1437162/versions
# 4. others
不用关注，由魏树银和于洋维护

## 4.1 bash_blade.dockerfile
blade专用镜像，blade太大了，不适合放到通用镜像中

ICM链接：https://cloud.bytedance.net/icm/detail/1445323/versions