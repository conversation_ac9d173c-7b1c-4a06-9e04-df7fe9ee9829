# syntax = hub.byted.org/kubestrato/dockerfile-x:1.4.2

FROM ./agentsphere/image/bash_image/bash_base.dockerfile

ARG TARGETARCH
ARG REGION

ENV HTTP_PROXY=http://sys-proxy-rd-relay.byted.org:3128 \
    HTTPS_PROXY=http://sys-proxy-rd-relay.byted.org:3128

# install tango 1.20-1.24，tango 1.23 ~320MiB
RUN for GO_VERSION in 1_20 1_21 1_22 1_23 1_24; \
    do \
        HTTP_PROXY="" HTTPS_PROXY="" http_proxy="" https_proxy="" bvc clone bytedance/lang/tango${GO_VERSION}_bookworm /usr/local/go${GO_VERSION}; \
    done && \
    # 默认使用tango1.23
    ln -nsf /usr/local/go1_23 /usr/local/go && \
    echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile && \
    echo 'export GOPATH=$HOME/go' >> /etc/profile

ENV PATH="$PATH:/usr/local/go/bin" \
    GOPATH="/root/go" \
    GOBIN="/usr/local/go/bin" \
    GOPROXY="https://goproxy.byted.org|direct" \
    GOPRIVATE="sysrepo.byted.org,*.everphoto.cn,git.smartisan.com" \
    GONOSUMDB="code.byted.org,gitlab.everphoto.cn,git.byted.org,sysrepo.byted.org,git.smartisan.com" \
    GOSUMDB="sum.golang.google.cn"

# bazel会自己下载一个go，和go分布式缓存设置的GOEXPERIMENT有冲突
# 因此禁用go分布式缓存

# setup bazel
# 压缩后约80Mi
RUN if [ -z "$(which -a bazelisk)" ]; then \
        curl --max-time 10 --retry 2 "https://luban-source.byted.org/repository/scm/api/v1/download_latest/?name=devinfra/monorepo/bazelisk_installer&type=online" | tar -xOz ./install.sh | bash; \
        bazel version; \
        if [ -f "WORKSPACE" ]; then bazel info; fi; \
    else \
        echo "Found bazelisk. Skip installing." && bazelisk version; \
    fi

ENV HTTPS_PROXY= \
    HTTP_PROXY=
