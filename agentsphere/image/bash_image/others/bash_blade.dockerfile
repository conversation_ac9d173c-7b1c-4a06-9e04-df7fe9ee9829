# syntax = hub.byted.org/kubestrato/dockerfile-x:1.4.2

FROM hub.byted.org/compile/scm_llvm16_blade:latest
# https://code.byted.org/sysimage/scm_llvm16_blade/blob/master/Dockerfile

INCLUDE ./agentsphere/image/bash_image/bash_base.dockerfile

ARG TARGETARCH
ARG REGION

# setup blade
RUN apt update && apt install -y lsb-release && \
    TIGER_ROOT=/opt/tiger && \
    BLADE_PATH="${TIGER_ROOT}/typhoon-blade" && \
    EXTRA_ARGS="" && \
    HTTP_PROXY="" HTTPS_PROXY="" http_proxy="" https_proxy="" bvc clone inf/blade/blade_build -f "${BLADE_PATH}" ${EXTRA_ARGS} && \
    find "${BLADE_PATH}" -type f -name "*.pyc" -delete && \
    ln -sf ${BLADE_PATH}/blade /usr/local/bin/blade && \
    blade -h
