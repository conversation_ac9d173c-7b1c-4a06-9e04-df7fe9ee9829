---
ID: "dynamic_35"
Title: generate Feishu/Lark doc
EnabledIf: "agent in ['mewtwo'] and 'lark_creation' in tools"
UsedWhen: the user's request involves creating a Feishu/Lark doc
---

## Lark/Feishu Best Practice

### For creating a Lark/Feishu doc

- Lark and Feishu are the same platform.
- `lark.md` is an intermediate result, not a Feishu/Lark doc.
- If data visualization (not including architecture diagrams or flowcharts) is involved，generate chart files first, then remember to provide these chart files to the `lark_creation` tool:
    - Data visualization chart images could be generated by python script, and save each one as an individual image file.
    - Interactable charts can be made by echarts/plotly and saved in HTML files. Make sure each HTML file contains ONLY ONE chart (no text descriptions needed), cause HTML file could directly be inserted into Feishu/Lark doc.
- Before using the `lark_creation` tool, you must first establish a clear outline for the document. Ensure all necessary materials (e.g., data, charts, text content) are fully prepared and readily available. This pre-planning step is mandatory to ensure the final document is well-structured, comprehensive, and meets quality standards.
- Choose tool `lark_creation` to generate Feishu/Lark doc. Do not ask for TOC in task description.
- However, you must prompt the related agent that the Feishu/Lark document should follow some specific doc grammar, and they should find related to the knowledge (they must select the knowledge snippet by themselves).
- Ensure that if reference and citation provided in previous steps, all reference information should be passed to the target agent.
- Even in your task description, if cited information are involved, you must keep the citation number information.
- Do not try to design the report/doc in task description (I mean <task>), others can do much better than you, so just set a brief task description in <task> for lark doc generation.