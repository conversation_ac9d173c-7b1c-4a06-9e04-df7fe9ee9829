---
ID: "planner_7"
Title: read or generate Feishu/Lark doc
EnabledIf: "agent in ['dynamic_planner']"
UsedWhen: the user's request involves creating or reading a Feishu/Lark doc
---

## Lark/Feishu Best Practice

### For reading a Lark/Feishu doc

- When reading a Lark/Feishu link, always choose Agent mewtwo with lark and vision tools as the first choice to extract content.

### For creating a Lark/Feishu doc

- Lark and Feishu are the same platform.
- However, you must prompt the related agent that the Feishu/Lark document should follow some specific doc grammar, and they should find related to the knowledge (they must select the knowledge snippet by themselves).
- Ensure that if reference and citation provided in previous steps, all reference information should be passed to the target agent.
- Even in your task description, if cited information are involved, you must keep the citation number information.
- Do not try to design the report/doc in task description (I mean <task>), others can do much better than you, so just set a brief task description in <task> for lark doc generation.
- Ensure the sources to be used in Feishu/Lark documents (e.g. figure, html interactable charts) are collected and generated ahead the Lark document generation steps.
  - Data visualization chart images could be generated by python script, and save each one as an individual image file.
  - Interactable charts can be made by echarts/plotly and saved in HTML files. Make sure each HTML file contains ONLY ONE chart (no text descriptions needed), cause HTML file could directly be inserted into Feishu/Lark doc.
  - For plantuml diagrams, use plantuml code blocks directly in the document, as Feishu/Lark documents support rendering plantuml code blocks.
- After the target Feishu/Lark doc completely generated, MCPs should be involved after the Feishu/Lark documents generation, make sure this action is allocated to an agent at an appropriate step.
- Choose Agent `mewtwo` to generate Feishu/Lark doc. Do not ask for TOC in task description.
- If you find execution trace contains citation information, you must emphasis the requirement of citation in task description, and emphasis the citation format is `::cite[xx]` (single), `::cite[xx]::cite[xx]` (multiple).
