---
ID: "planner_10"
Title: browser use guidelines
EnabledIf: "agent in ['dynamic_planner','mewtwo']"
UsedWhen: |
  You need access a link or the task involves using a browser to gather information or perform actions especially confused to choose whether mcp or brower, read carefully before choosing the appropriate tool.
---
- **Tool Coordination**
  - If the search tool results lead you to decide to goto and extract specific web pages, you should ensure that the search process is completely completed and then obtain multiple URLs at once for access and extraction.
  - Do not intersperse web page extraction during the search process.
  - If the url source is search_tool, YOU MUST NOTE THIS POINT
  - Wait until all web content extraction is complete before viewing the files saved by the Web Content Tool. MUST not intersperse file reading operations during the extraction of multiple web content
  - Through the web page content, you may get some APIs that meet your needs. Please use the wget command to request these APIs to improve efficiency.

- **Task Assignment Strategy**
  - by default, handover to `browser` tool for task involving interactions
  - for simple content extraction, handover to `browser_goto_and_extraction`
  - ensure moderate granularity of task assignment: one call to `browser` should handle a self-contained task on a webpage
  - **SPECIFIC SCENARIO**
    - doubao.com (豆包): handover `browser` tool
    - aeolus.bytedance.net, data.bytedance.net/aeolus (风神): handover `browser` tool
    - bytetech.info: handover `browser` tool
    - meego.larkoffice.com (Meego): use `browser` tool if user needs to log in meego platform
    - oncall.bytedance.net (Oncall): select `oncall` tool for `mewtwo`
    - bytedance.larkoffice.com, www.feishu.cn (飞书/Lark), bytedance.us.larkoffice.com, bytedance.sg.larkoffice.com: select `lark` tool for `mewtwo`
    - slardar:
      - If Slardar MCP tools is available, prioritize assigning to them for mewtwo; only fall back to browser_use if they are unavailable or fails
      - Especially when accessing https://t.wtturl.cn, operations can be completed via proper Slardar tool without browser participation. If it fails, the browser will then be involved
    - cloud.bytedance.net/argos (Argos): such log query requests or get trace log by log id, select `argos_log_analysis` tool for `mewtwo`