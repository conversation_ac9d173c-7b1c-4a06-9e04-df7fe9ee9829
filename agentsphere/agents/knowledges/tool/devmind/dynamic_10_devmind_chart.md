---
ID: "dynamic_10_devmind_chart"
Title: Devmind Chart Recommend Tool Principle
EnabledIf: "agent in ['data_analyzer', 'mewtwo'] and 'devmind' in tools"
UsedWhen: when working with the DevMind visualization chart recommendation tools（query_data_model, query_analysis_metric, query_analysis_dimension, query_chart_url, query_chart_info tools）.
---

# Devmind Chart Recommend Principle

## 1. Overview of Data Structures
In the context of Devmind Chart Recommendation, the process of generating an appropriate and effective chart relies on four foundational components: **DataModel**, **Metrics**, **Dimensions**, and **Filters**. Each of these components plays a distinct role in ensuring the chart is both relevant to the user’s analytical needs and aligned with the available data structure.

By combining these four components effectively, the Devmind Chart Recommendation system can automatically propose chart configurations that best match the analytical intent, data characteristics, and business context of the user’s request. This structured approach ensures that generated charts are not only visually clear but also directly actionable for decision-making.

You should determine the most appropriate chart type based on the user's natural language input and underlying analytical intention through intepreting the context, indentifying key metrics and dimensions and matching them to one of the supported chart types. There are 9 types of charts supported by Devmind at this stage, including:
1. `sheet`: Displays raw data in a tabular format for direct viewing and basic analysis. Support multiple Metrics and Dimensions.
2. `line`: Shows trends over time or continuous variables using connected data points. Support multiple Metrics and Dimensions.
3. `line_stack`: Similar to a line chart but stacks multiple series to show cumulative values over time. Support multiple Metrics and Dimensions.
4. `bar`: Compares discrete categories using rectangular bars whose lengths represent values. Support multiple Metrics and Dimensions.
5. `bar_stack`: Stacks multiple data series in a single bar to show part-to-whole relationships and totals. Support multiple Metrics and Dimensions.
6. `percent_bar_stack`: Stacks series in each bar but normalizes values to 100% to highlight proportional differences. Support multiple Metrics and Dimensions.
7. `pie`: Represents parts of a whole as slices of a circular chart, showing percentage breakdowns. Support only 1 Metric and multiple Dimensions.
8. `double_axis`: Uses two different Y-axes to plot and compare datasets with different value ranges in one chart. Support multiple Metrics and Dimensions.
9. `card`: Displays key metrics or KPIs in a single, simplified visual block for quick reference. Support only 1 Metric and 0 Dimension.

Before conducting any data analysis, you must completely understand the data structure. For each metric that needs to be calculated, provide clear and explicit calculation logic. It is strictly prohibited to make assumptions or guesses about how metrics should be calculated. Always seek clarification when the calculation logic is not evident.

## 2. Details of Data Structures and Rules Understanding

### 2.1 DataModel
This defines the underlying data source from which the chart will be built. A dataset contains structured information collected from business systems, analytics platforms, or operational databases. It establishes the scope of the analysis by determining which Metrics and Dimensions are available for visualization.

### 2.2 AnalysisMetrics
Metrics are the quantitative measures or calculations that represent the values to be analyzed, compared, or tracked over certain dimensions. Metrics are directly used as the y-axis in the finalized chart and form the numerical backbone of the chart and directly influence the chart type.

There are different types of objects that can be used as Metrics in the context of Devmind Chart Visualization. Firstly, objects with type of "Metric" can be directly used as Metrics which are basically numerical data, consisting of original fields, SQL expressions and UDF (user defined functions).

Objects with type of "Dimensions" can also be selected as Metrics. Specific **Metric Aggregation Logic** should be applied to these Dimensions when they are selected as Metrics because part of them are not numerical data and cannot be utilized as the y-axis of the chart directly. The following is a detailed explanation of Metric Aggregation Logic for Dimensions.

#### 2.2.1 Metric Aggregation Logic for Dimensions
Based on the data type of a metric, the Devmind system provides a series of aggregation methods. Here are some explanations for the aggregation methods:

**Eight Types of Aggregation Methods**

1.  **Count**: Calculates the total number of records.
2.  **Count Distinct**: Counts unique values in a field.
3.  **Sum**: Adds up all numeric values.
4.  **Avg**: Computes the mean of numeric values.
5.  **Max**: Returns the highest value.
6.  **Min**: Returns the lowest value.
7.  **Quantile**: Estimates the value below which a given percentage of data falls (e.g., 90%).
8.  **No Aggregation**: Keeps the original value without summarizing it.

**Mapping Relationship Between Dimension Types and Aggregation Methods**

1. **String, Timestamp, Enum, Date**:
- The recommended aggregation methods for these four data types will include "Count", "Count Distinct", "Max", "Min", and "No Aggregation".
- It should be noted that these aggregation methods are suitable for categorical and time-based data.
2. **Double, Int**:
- The recommended aggregation methods for these two data types will include "Count", "Count Distinct", "Sum", "Average", "Max", "Min", "Percentile", and "No Aggregation" (All methods).
- It should be noted that BIGINT and similar types will be included as well, and these aggregation methods are suitable for numerical metrics.
3. **UDF Dimension Types (User Defined Functions)**:
- These types will not be included in the selection of Metrics at this stage.
- Business Line and Reporting Line dimensions cannot be used as metrics.

### 2.3 Analysis Dimensions
Dimensions are the categorical or descriptive fields used to break down and group metrics. They provide context to the numbers by segmenting them according to attributes such as date, department, product category, or members. Dimensions influence the chart’s structure by determining the x-axes, which is the grouping of data.

In the context of Devmind Chart Recommendation, only objects with type of "Dimension" can be selected as Dimensions. Similar with Metrics, there will be original fields, SQL expressions and UDF (user defined functions) separated as different generations of Dimensions. They will be finally expressed to six different types at this stage as mentioned in the mapping relationship in the last section, including String, Timestamp, Enum, Date, Double and Int.

There are some specific rules for Dimensions to be appropriately used in chart visualization on Devmind.

#### 2.3.1 Time Granularity of Dimensions of "Timestamp" Type
When a "Timestamp" type Dimension is selected as a dimension of a chart, it represents that the data selected will be grouped by a specific time unit, which is "Time Granularity" of a Dimension. Currently Devmind Chart Recommendation provides five "Time Granularity", which have their own ids. You should analyse user's query and try to figure out the target Time Granularity of the Timestamp Dimension of the chart. And if there isn't a input Time Granularity while a Time Granularity is required based on your analysis, use Month (id = 3) as default.
1. Day (id = 1)
2. Week (id = 2)
3. Month (id = 3) (which is the default Time Granularity if user doesn't specify)
4. BiMonth (id = 4)
5. BiWeek (id = 14) (when using BiWeek, a begining date should be specified in order to clarify which period does the BiWeek lies in)
6. Hour (id = 16)
7. HalfJan (id = 27) (HalfJan stands for a shifted half-year window starting from January, including Junuary through June and July through December)
8. HalfFeb (id = 28) (HalfFeb stands for a shifted half-year window starting from February, including February through July and August through January)
9. HalfMar (id = 29) (HalfMar stands for a shifted half-year window starting from March, including March through August and September through February)
10. HalfApr (id = 30) (HalfApr stands for a shifted half-year window starting from April, including April through September and October through March)
11. HalfMay (id = 31) (HalfMay stands for a shifted half-year window starting from May, including May through October and November through April)
12. HalfJun (id = 32) (HalfJun stands for a shifted half-year window starting from June, including June through November and December through May)
13. YearJan (id = 34) (YearJan stands for a one-year window starting from January, including January through December)
14. YearFeb (id = 35) (YearFeb stands for a one-year window starting from February, including February through January)
15. YearMar (id = 36) (YearMar stands for a one-year window starting from March, including March through February)
16. YearApr (id = 37) (YearApr stands for a one-year window starting from April, including April through March)
17. YearMay (id = 38) (YearMay stands for a one-year window starting from May, including May through April)
18. YearJun (id = 39) (YearJun stands for a one-year window starting from June, including June through May)
19. YearJul (id = 40) (YearJul stands for a one-year window starting from July, including July through June)
20. YearAug (id = 41) (YearAug stands for a one-year window starting from August, including August through July)
21. YearSept (id = 42) (YearSept stands for a one-year window starting from September, including September through August)
22. YearOct (id = 43) (YearOct stands for a one-year window starting from October, including October through September)
23. YearNov (id = 44) (YearNov stands for a one-year window starting from November, including November through October)
24. YearDec (id = 45) (YearDec stands for a one-year window starting from December, including December through November)
25. Quater (id = 49)
26. PerformanceHalfYear (id = 51) (On Devmind, PerformanceHalfYear represents a six-month performance evaluation cycle running from September to February (crossing into the following year) and March to August.)
27. PerformanceYear (id = 52) (On Devmind, PerformanceYear represents a full year performance evaluation cycle running from March to February of the next year.)

#### 2.3.2 UDF(User Defined Function) Dimension Handling Logic
There are multiple types of UDF Dimensions supported by Devmind, and they can be separated into none-measuring UDF Dimensions and measuring UDF Dimensions. At current stage, within all measuring UDF Dimensions, there are only three specialized measuring type supported: Business Line, Reporting Line and Reporting Line (including resigned and transferred employees) which can be furtherly categorized into Business Line and Reporting Line as Reporting Line (including resigned and transferred employees) is rarely selected by users.

1. **Business Line**: Refers to the grouping of activities, teams, or roles within an organization based on specific business functions, product categories, or service areas.
2. **Reporting Line**: Describes the formal managerial relationship within the organizational hierarchy, specifying to whom an employee reports and who is responsible for their performance evaluation and task oversight.

In the Dimension data list within datasets from Devmind, these two UDF objects differ from generic fields such as `object_id`, `object_name` and `caliber_sql`. Their identification is as follows:

1. **Business Line**
- `object_id` = `3`
- `object_name` = “Business Line” or “业务线”
- `caliber_sql` LIKE `'%$func_product_mapping%'`
    - Variants:
        1. `$func_product_mapping(0)` → Current level of Business Line
        2. `$func_product_mapping(1)` → Child level of the current level
        3. `$func_product_mapping(2)` → Child of the child level
2. **Reporting Line**
    1. **Standard Reporting Line**
        - `object_id` = `4`
        - `object_name` = “Reporting Line” or “汇报线”
        - `caliber_sql` LIKE `'%$func_where_circle_people%'`
    2. **Reporting Line (including resigned and transferred employees)**
        - `object_id` = `14`
        - `object_name` = “Reporting Line (including resigned and transferred employees)” or “汇报线(包含离职、转出人员)”
        - `caliber_sql` LIKE `'%$func_where_circle_people_by_data_index%'`

There are some rules for selecting "Business Line" and "Reporting Line" dimensions as dimensions or filters:

1.  **Business Line**
    1.  If "Business Line" is selected as a dimension, "Reporting Line" cannot be selected as another dimension or a filter in the same chart.
    2.  If "Business Line" is selected as a dimension with `caliber_sql` LIKE `"$func_product_mapping(0)"`, then `"$func_product_mapping(1)"` and `"$func_product_mapping(2)"` should not be selected as another dimension or a filter in the same chart. The same applies to `"$func_product_mapping(1)"` and `"$func_product_mapping(2)"`. (Three levels of "Business Line" are mutually exclusive).
    3.  If "Business Line" is selected as a dimension, the same "Business Line" dimension should also be selected as a filter in the same chart.
2.  **Reporting Line**
    1.  If "Reporting Line" is selected as a dimension, "Business Line" cannot be selected as another dimension or a filter in the same chart.
    2.  If "Reporting Line" is selected as a dimension, the same "Reporting Line" dimension should also be selected as a filter in the same chart, and the condition of the filter must not be set to the lowest level of a real Reporting Line.
    3.  If "Reporting Line" is selected only as a filter, the condition of the filter can be set to the lowest level in a real Reporting Line.
    4.  A "Reporting Line" dimension with a `caliber_sql` LIKE `'%$func_where_circle_people_by_data_index%'` (Reporting Line including resigned and transferred employees) cannot be used as a dimension in a chart. It can only be selected as a filter.

### 2.4 Filters Conditions
Filters refine the scope of the visualization by including or excluding specific data points based on predefined conditions. They allow users to narrow the analysis to relevant subsets—such as a specific data range, type, region, or classification that the resulting chart is targeted and meaningful. At this stage, all Filters selected should come from Dimensions, which are objects with object type "Dimension".

As Filters are basically Dimensions, there are currently six types of Filters at this stage, which is the same as Dimensions. And a specific filter condition will be applied to each Filter selected. This filter condition includes two sections, which are operators and values, which varies from types of Filters.

#### 2.4.1 Filter Operator Logic
Based on the data type of a dimension, the Devmind system provides different filter operators. The classification is as follows:

1. **String, Enum**:
   The recommended filter operators for these two data types will include `["=", "!=", "IN", "NOT IN", ">", ">=", "<", "<=", "LIKE", "NOT LIKE", "IS NULL", "IS NOT NULL", "IS EMPTY", "IS NOT EMPTY"]` as these operators are applicable to text-based fields.
2. **Double, Int**:
   The recommended filter operators for these two data types will include `["=", "!=", "IN", "NOT IN", ">", ">=", "<", "<=", "IS NULL", "IS NOT NULL"]` as these operators are applicable to numeric data; therefore, BIGINT and similar types will also be included.
3. **Timestamp, Date**:
   The recommended filter operators for date-like data types include: `["LAST", "BETWEEN", "SINCE", "IS NULL", "IS NOT NULL"]`. These operators are specifically designed for handling temporal data. These time-based operators enable precise filtering of records based on datetime fields and are commonly used in analytics and reporting scenarios.
    - The **`LAST`** operator is used to filter records within a certain time window before the current datetime. The time window is defined by a numeric value and a time unit selected from `["Minute", "Hour", "Day", "Week", "Month", "Year"]`. For example, selecting "2 Week" means the system will return records from the past two weeks. An additional option, “Include today”, can be set to true or false to control whether the current day is included in the range.
    - The **`SINCE`** operator filters records from a specific start time onward. The start time must follow the "YYYY-MM-DD HH-mm-ss" format. Similar to “LAST”, there is an “Include today” option that can be toggled.
    - The **`BETWEEN`** operator requires two datetime inputs in the "YYYY-MM-DD HH-mm-ss" format, representing the start and end of the desired time range.
      It should be mentioned that "LAST" and "SINCE" operators are supported by Devmind, however, in the context of chart recommendation and visualization, you will only receive and also should only generate a Timestamp Filter with "BETWEEN" operator as the value of "op" field and two times of "YYYY-MM-DD HH-mm-ss" format as the value of "value" field which represents a specific time range.