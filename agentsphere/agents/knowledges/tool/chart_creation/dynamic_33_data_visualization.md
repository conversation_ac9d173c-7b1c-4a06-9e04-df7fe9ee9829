---
ID: "dynamic_33_chart"
Title: data visualization preferences
EnabledIf: "agent in ['mewtwo'] and 'chart' in tools"
UsedWhen: visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
---

## Data Visualization

When creating data visualizations (not including architecture diagrams or flowcharts), it is essential to separate the process into distinct phases: data analysis and processing should be completed first, followed by the visualization step. Begin by preparing the data file, then proceed to create the visualization.

IMPORTANT RULES:

* The data file must be stored separately and independently.
* The data must be formatted as a JSON file that could be used with the chart_creation tool.
* Create data file first, then create chart file with the chart_creation tool.
* All data visualizations charts must be generated using the chart_creation tool.
* Direct use of Python or HTML to create charts is not allowed unless the chart_creation tool is not sufficient for the requirements.

The data file must be a JSON file that could be used with the chart_creation tool. The file must contain a JSON string representing an object which has two fields: data and fields_meta, for example: {"data": [{"date": "2021-01-01", "mau": 0.75}, {"date": "2021-01-02", mau: 0.2}], "fields_meta": [{"name": "mau_rate", "description": "月活跃用户比例", "role": "measure", "unit": ""}, {"name": "date", "description": "月份", "role": "dimension", "unit": ""}]}
    - data: The data field is an array of data objects, for example: [{date: '2021-01-01', value: 100}, {date: '2021-01-02', value: 200}]. It includes only the fields necessary for chart creation.
    - fields_meta:  An explanation of the structure and semantics of the data, including the field name, data types, and what the data represents, it is an array of
    field meta object, each object has the following fields:
        - Name：The name of the current field, which corresponds to the key value in the data. For example, if the data is [{date:'2021-01-01', value:100}], the possible field names are date and value.
        - Role：The role of the current field, enum values: [“measure”, “dimension”]
            - measure: Represents a numeric field used for measurements.
            - dimension: Represents a categorical field, generally used for classification.
        - Description: A brief explanation of the current field.
        - Unit: This field is generated only when the field type is "measure". It represents the unit of the numeric value in the field. If no unit is specified, the value is treated as a pure number without any unit. Common units include K (thousands), percent, per mille, and so on. Note: Ensure the unit is consistent across all measures within the same axis or scale.
            - Example: If the data is 20 and the unit is percent, the actual value is 0.2
            - Example: If the data is 0.26 and the unit is an empty string, the actual value is 0.26
            - Example: To express 25 percent, you can either use data 0.25 without a unit or data 25 with the unit percent.
            - Example: 20K means 20,000.
            - Note: If the data is 0.25 and the unit is percent, the actual value will be 0.0025. This is an unusually low proportion, so you should consider whether the unit might have been mistaken."

For example, if you need to create 5 charts, you must follow these steps:

1. Create a separate JSON data file for each chart, such as chart1_data.json, chart2_data.json, chart3_data.json, chart4_data.json, and chart5_data.json.
2. Use the chart_creation tool individually for each chart by providing the corresponding JSON data file and a description of the chart. The tool will generate each chart based on the provided description.