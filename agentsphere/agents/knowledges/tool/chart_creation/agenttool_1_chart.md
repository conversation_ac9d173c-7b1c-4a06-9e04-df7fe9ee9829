---
ID: "agenttool_chart"
Title: Chart Creation AgentTool
EnabledIf: "false"
UsedWhen: visualizing data to create charts
---

## Chart Creation Guide

Your task is to generate a data visualization chart based on the provided task description, which includes the following key elements:

- DataFilePath: The absolute path to the input data JSON file (with a .json extension), e.g., /workspace/iris_e7c707a5-ae78-42d0-b045-1882a9f0a4d7/somedata.json. The file contains a JSON string representing an object which has two fields: data and fields_meta, for example: {"data": [{"date": "2021-01-01", "mau_rate": 0.75}, {"date": "2021-01-02", "mau_rate": 0.2}], "fields_meta": [{"name": "mau_rate", "description": "月活跃用户比例", "role": "measure", "unit": ""}, {"name": "date", "description": "月份", "role": "dimension", "unit": ""}]}
    - data: The data field is an array of data objects, for example: [{date: '2021-01-01', value: 100}, {date: '2021-01-02', value: 200}]. It includes only the fields necessary for chart creation.
    - fields_meta:  An explanation of the structure and semantics of the data, including the field name, data types, and what the data represents, it is an array of
    field meta object, each object has the following fields:
        - Name：The name of the current field, which corresponds to the key value in the data. For example, if the data is [{date:'2021-01-01', value:100}], the possible field names are date and value.
        - Role：The role of the current field, enum values: [“measure”, “dimension”]
            - measure: Represents a numeric field used for measurements.
            - dimension: Represents a categorical field, generally used for classification.
        - Description: A brief explanation of the current field.
        - Unit: This field is generated only when the field type is "measure". It represents the unit of the numeric value in the field. If no unit is specified, the value is treated as a pure number without any unit. Common units include K (thousands), percent, per mille, and so on. Note: Ensure the unit is consistent across all measures within the same axis or scale.
            - Example: If the data is 20 and the unit is percent, the actual value is 0.2
            - Example: If the data is 0.26 and the unit is an empty string, the actual value is 0.26
            - Example: To express 25 percent, you can either use data 0.25 without a unit or data 25 with the unit percent.
            - Example: 20K means 20,000.

- ChartDescription: A detailed description of the chart to be generated. This may include the chart type, visual encodings, style configurations, axes, titles, labels, legends, color schemes, annotations, and other visual elements.
- Language: The desired language to be used in the chart.
- ResultFileName: The name of the output chart file (excluding the file extension).

Please strictly follow the rules below during the creation process.

### Chart Creation Options

The main options for data visualization are as follows:

    * Python should be your primary choice, so use Python unless interactive charts are required
    * If interactive charts are required, use HTML

#### Python

- Code Execution: Avoid using "python -c" if you need to execute python script, instead create a python script file and execute it.
- Use `matplotlib` or `seaborn` for chart generation. Ensure Chinese characters are displayed correctly.
- Save the chart as a PNG file (e.g., `output/delonghi_market_share_china_tmall_2023.png`).
- You must use the vision tool to inspect the Python generated chart, ff the image shows significant issues or does not meet the requirements, then fix them,
  the significant issues that you should focus on when validating the Python generated chart include:

    * garbled text, e.g. Chinese characters are not rendered correctly
    * chart image is overlapping, the image contains overlapping elements that cover each other.

#### HTML (Used for interactive charts only)

- Do not use HTML to create chart unless interactive charts are required, if no interactive charts are required, MUST use python to create chart.
- Interactable charts can be made by echarts/plotly/d3 and saved in HTML files. Make sure each HTML file contains ONLY ONE chart (no text descriptions needed), cause HTML file may directly be inserted into other docs.
- Chart library options: Echarts.js, Plotly.js, D3.js
- Use browser version of plotly to generate a plotly chart. Don't use python version to generate HTML.
- Ensure Chinese characters are displayed correctly..
- You must deploy result files & pages then use browser tools to validate the generated html page (contains one chart only), because there might be some javascript errors or display issues, your testing should verify both functionality and chart UI, fix them if you find any issues.
- If you can not validate the generated html page, then you must use python to create chart.

### Input And Output

- Single Input Table:
The provided JSON file contains only one data table. You are expected to generate one chart based solely on this input, as described in the ChartTask.

- Output File Naming:
The ResultFileName does not include a file extension. If you use generate_visactor_chart, the extension will be added automatically. If you use an alternative method (e.g., Python), you must append the appropriate file extension manually.