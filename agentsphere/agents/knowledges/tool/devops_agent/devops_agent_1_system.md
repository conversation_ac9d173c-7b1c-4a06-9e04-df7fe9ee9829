---
ID: "devops_agent_1"
Title: devops agent use principle
EnabledIf: "agent in ['mewtwo'] and 'devops_agent' in tools"
UsedWhen: The task is SCM builds or TCE deployments. 当任务和SCM构建或TCE部署相关时，使用本知识。
---

# DevOpsAgent Tool Usage Guide

The DevOpsAgent tool provides development operations for SCM builds and TCE deployments. It supports 3 main task types with intelligent orchestration.

## Core Capabilities (Production Ready)

1. **SCM Build**: Create and monitor build tasks from repositories
2. **TCE Deploy**: Deploy applications using SCM versions
3. **Build and Deploy**: Combined workflow with automatic version passing

## Task Types (Production Ready)

### 1. Build Task (`build`)
Builds from codebase or SCM repositories and outputs SCM version.

**Required Parameters:**
- `task`: "build"
- Either `codebase_repo` OR `scm_repo`
- `branch` (optional, defaults to "master")

**Repository Formats:**
- `codebase_repo`: Format `{namespace}/{repo}` (2 parts), e.g., "cld/bits-ai-showcase"
- `scm_repo`: Format `{a}/{b}/{c}` (3 parts), e.g., "bits/ai/showcase"

### 2. Deploy Task (`deploy`)
Deploys using provided SCM version to specified PSM.

**Required Parameters:**
- `task`: "deploy"
- `psm`: Platform Service Management identifier
- `scm_version`: SCM version to deploy
- `env`: Environment/lane for deployment (optional, auto-generated if empty)
- `env_type`: Environment type when env is not provided - "ppe" generates ppe_xxx, "boe" generates boe_xxx (optional)

### 3. Build and Deploy Task (`build_and_deploy`)
Combined workflow that builds then deploys, automatically passing SCM version.

**Required Parameters:**
- `task`: "build_and_deploy"
- Either `codebase_repo` OR `scm_repo`
- `psm`: Platform Service Management identifier
- `branch` (optional, defaults to "master")
- `env`: Environment/lane for deployment (optional, auto-generated if empty)
- `env_type`: Environment type when env is not provided - "ppe" generates ppe_xxx, "boe" generates boe_xxx (optional)

## Environment Parameter Logic

The tool uses intelligent parameter inference for environment configuration:

### Priority Order:
1. **If `env` is provided**: Uses specified environment, infers `is_online` from prefix
2. **If `env_type` is provided**: Uses env_type for generation, infers `is_online` from value  
3. **If neither provided**: Uses `is_online` to set `env_type` (true→ppe, false→boe)

### Inference Examples:
- `env: "ppe_staging"` → `is_online: true`
- `env: "boe_testing"` → `is_online: false`
- `env_type: "ppe"` → `is_online: true`
- `env_type: "boe"` → `is_online: false`
- `is_online: true` → `env_type: "ppe"`
- `is_online: false` → `env_type: "boe"`

## Data Flow Intelligence (Production Ready)

- **SCM Version**: Automatically passed from build to deploy in combined workflows
- **PSM**: Used consistently for deployment

## Usage Examples (Production Ready)

### Example 1: Build with Codebase Repository
```
task: build
codebase_repo: cld/bits-ai-showcase
branch: main
```

### Example 2: Build with SCM Repository
```
task: build
scm_repo: bits/ai/showcase
branch: main
```

### Example 3: Deploy with SCM Version
```
task: deploy
psm: my-service-psm
scm_version: v1.2.3-abc123
env_type: ppe
```

### Example 4: Build and Deploy
```
task: build_and_deploy
codebase_repo: cld/bits-ai-showcase
branch: main
psm: my-service-psm
```

### Example 5: Deploy with IsOnline Controlling Environment Type
```
task: deploy
psm: my-service-psm
scm_version: v1.2.3-abc123
is_online: false
```

## Multi-Language Support

The DevOpsAgent tool supports both Chinese and English inputs. When users provide Chinese terms, the agent should map them to the correct English parameters.

### Chinese-English Translation Reference

| Chinese Term | English Parameter | Description |
|--------------|------------------|-------------|
| 任务类型         | task | The development task type |
| 构建           | build | Build operation |
| 部署           | deploy | Deploy operation |
| 构建和部署        | build_and_deploy | Combined build and deploy |
| 代码库仓库        | codebase_repo | Codebase repository |
| SCM仓库        | scm_repo | SCM repository |
| 分支           | branch | Git branch |
| 主分支          | master | Master branch |
| 主要分支         | main | Main branch |
| 服务名          | psm | Platform Service Management identifier |
| SCM版本        | scm_version | SCM version |
| 环境/泳道        | env | Environment/lane for deployment |
| 环境类型         | env_type | Environment type - "ppe" for ppe_xxx, "boe" for boe_xxx |
| 是否线上         | is_online | Online environment preference - true for ppe, false for boe |

### Common Chinese Input Patterns (Production Ready)

1. **Task Type Mapping:**
   - "构建项目" → task: "build"
   - "部署应用" → task: "deploy"
   - "构建并部署" → task: "build_and_deploy"

2. **Repository Mapping:**
   - "代码仓库: xxx/yyy" → codebase_repo: "xxx/yyy"
   - "SCM仓库: xxx/yyy/zzz" → scm_repo: "xxx/yyy/zzz"

3. **Environment Type Mapping:**
   - "线上环境" → env_type: "ppe"
   - "线下环境" → env_type: "boe"
   - "正式环境" → env_type: "ppe"
   - "测试环境" → env_type: "boe"

4. **Boolean Mapping:**
   - "线上环境" → is_online: true
   - "线下环境" → is_online: false

### Usage Guidelines for Chinese Input (Production Ready)

When users provide input in Chinese:

1. **Parse task type** from Chinese descriptions and map to English task parameter (build, deploy, build_and_deploy only)
2. **Extract repository information** and format correctly (2 parts for codebase_repo, 3 parts for scm_repo)
3. **Identify branch names** (commonly "主分支"→"master", "主要分支"→"main")
4. **Map environment terms** to standard environment names
5. **Map environment type** from Chinese terms (线上环境→"ppe", 线下环境→"boe")
6. **Convert boolean expressions** to proper boolean values
7. **Preserve technical identifiers** like PSM names and version numbers as-is

The agent should be flexible in understanding various Chinese expressions while ensuring correct parameter mapping to the English-based tool interface.