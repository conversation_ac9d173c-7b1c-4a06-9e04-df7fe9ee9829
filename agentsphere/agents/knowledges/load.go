package knowledges

import (
	"embed"
	"io/fs"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

var (
	//go:embed */**
	knowledgesFiles embed.FS
	loadOnce        sync.Once
	Knowledges      []KnowledgeItem
)

func LoadKnowledge() []KnowledgeItem {
	loadOnce.Do(func() {
		checkMap := make(map[string]bool)
		err := fs.WalkDir(knowledgesFiles, ".", func(filePath string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}
			if d.IsDir() || !strings.HasSuffix(filePath, ".md") || strings.HasPrefix(filePath, "archived/") {
				return nil // 目录跳过
			}
			folders := []string{string(KgRetrieveCategorySystem), string(KgRetrieveCategoryScenario), string(KgRetrieveCategoryTool)}

			if _, ok := lo.Find(folders, func(item string) bool {
				return strings.HasPrefix(filePath, item+"/")
			}); !ok {
				panic("invalid knowledge path: " + filePath + ", put the file under sub folders: [" + strings.Join(folders, ",") + "]")
			}

			// 读取文件内容
			content, err := knowledgesFiles.ReadFile(filePath)
			if err != nil {
				return errors.WithMessagef(err, "read file %s failed", filePath)
			}
			knowledge, res, err := conv.ParseYAMLFrontMatter[KnowledgeItem](string(content))
			if err != nil {
				return errors.WithMessagef(err, "parse yaml front matter failed for file %s", filePath)
			}
			knowledge.Content = strings.TrimSpace(res)

			// 将路径拆成切片，去除rootDir前缀
			relPath := strings.TrimPrefix(filePath, ".")
			var pathParts []string
			if relPath != "" {
				pathParts = strings.Split(relPath, "/")
			}
			category := KgRetrieveCategory(pathParts[0])
			if category == "" {
				panic("invalid knowledge category: " + category)
			}
			knowledge.Category = category
			if len(pathParts) >= 3 {
				knowledge.CategoryKey = pathParts[1]
				knowledge.ID = pathParts[1] + "." + knowledge.ID
			} else if len(pathParts) == 2 {
				knowledge.ID = string(knowledge.Category) + "." + knowledge.ID
			} else {
				panic("invalid knowledge path: , put the file under sub folders: []" + filePath)
			}

			if checkMap[knowledge.ID] {
				panic("duplicate knowledge id: " + knowledge.ID)
			} else {
				checkMap[knowledge.ID] = true
			}
			Knowledges = append(Knowledges, *knowledge)
			return nil
		})
		if err != nil {
			panic(err)
		}
	})
	return Knowledges
}

func CreateKnowledgebase(run *iris.AgentRunContext) Knowledgebase {
	llmConfig := run.GetConfig().GetModelByScene("retrieve_knowledge")
	return NewSemanticKnowledgebase(run, LoadKnowledge(), llmConfig)
}

func FilterKnowledgeItems(knowledgeItems []KnowledgeItem, ids []string) []KnowledgeItem {
	return lo.Filter(knowledgeItems, func(item KnowledgeItem, _ int) bool {
		return lo.Contains(ids, item.ID)
	})
}
