// knowledge related prompts and prompt utils
// put in knowledges package to avoid import cycle
package knowledges

import (
	_ "embed"
	"fmt"
	"strings"
	"sync"
	"text/template"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

var (
	//go:embed recall_knowledge_system.go.tmpl
	knowledgeSystemPrompt         string
	KnowledgeSystemPromptTemplate = prompt.MustGetTemplate("KnowledgeSystemPrompt", knowledgeSystemPrompt)
	//go:embed recall_knowledge_user.go.tmpl
	knowledgeUserPrompt         string
	KnowledgeUserPromptTemplate = prompt.MustGetTemplate("KnowledgeUserPrompt", knowledgeUserPrompt)
	//go:embed knowledge_ack_user.go.tmpl
	knowledgeAckUserPrompt         string
	KnowledgeAckUserPromptTemplate = prompt.MustGetTemplate("KnowledgeAckUserPrompt", knowledgeAckUserPrompt)
	//go:embed knowledge_ack_assistant.go.tmpl
	knowledgeAckAssistantPrompt         string
	KnowledgeAckAssistantPromptTemplate = prompt.MustGetTemplate("KnowledgeAckAssistantPrompt", knowledgeAckAssistantPrompt)
)

type WithKnowledgeRetrieveMessageOption struct {
	Query     string
	Knowledge Knowledgebase
	Strategy  KgRetrieveStrategy
	Category  KgRetrieveCategory
	Param     RetrieveParam
	Cache     *sync.Map
	Sampling  int
}

func RetrieveKnowledgesWithCache(run *iris.AgentRunContext, opt WithKnowledgeRetrieveMessageOption) (items []KnowledgeItem, err error) {
	cacheKey := fmt.Sprintf("<kg_msg:%s><agent:%s><variant:%s><tools:%v><with_citation:%t><category:%s><scenario:%s>", opt.Query, opt.Param.Agent, opt.Param.Variant, opt.Param.Tools, opt.Param.WithCitation, opt.Category, opt.Param.Scenario)
	if opt.Cache != nil {
		if v, ok := opt.Cache.Load(cacheKey); ok {
			run.GetLogger().Infof("use knowledge cache for key %s", cacheKey)
			return v.([]KnowledgeItem), nil
		} else {
			run.GetLogger().Infof("knowledge cache for key not found: %s", cacheKey)
		}
	} else {
		run.GetLogger().Infof("knowledge cache is not provided, skip cache")
	}

	defer func() {
		if opt.Cache != nil && err == nil {
			opt.Cache.Store(cacheKey, items)
		}
		_ = metrics.AR.AgentKnowledgeRecallThroughput.WithTags(&metrics.AgentKnowledgeRecallTag{
			Scene:  opt.Param.Agent,
			Failed: err != nil,
		}).Add(1)
	}()

	if opt.Knowledge == nil {
		run.GetLogger().Info("no knowledgebase provided, skip recall")
		return nil, nil
	}

	recall, err := opt.Knowledge.RetrieveKnowledge(run, KgRetrieveOption{
		Query:    opt.Query,
		Limit:    20,
		Strategy: opt.Strategy,
		Category: opt.Category,
		Param:    opt.Param,
		Sampling: opt.Sampling,
	})
	if err != nil {
		run.GetLogger().Errorf("failed to recall knowledge: %v", err)
		return nil, nil
	}

	if len(recall) == 0 {
		run.GetLogger().Info("no knowledge found, skip recall")
		return nil, nil
	}

	return recall, nil
}

func WithKnowledgeRetrieveMessage(run *iris.AgentRunContext, opt WithKnowledgeRetrieveMessageOption) prompt.ComposeVaryMessageOption {
	return func() (msgs []*framework.ChatMessage, err error) {
		recall, err := RetrieveKnowledgesWithCache(run, opt)
		if err != nil {
			return nil, err
		}
		return WithKnowledgeMessage(run, recall)()
	}
}

func WithKnowledgeMessage(run *iris.AgentRunContext, knowledgeItems []KnowledgeItem) prompt.ComposeVaryMessageOption {
	return func() (msgs []*framework.ChatMessage, err error) {
		if len(knowledgeItems) == 0 {
			return nil, nil
		}
		knowledgeItems = lo.UniqBy(knowledgeItems, func(item KnowledgeItem) string {
			return item.ID
		})

		userMsg, err := prompt.WithUserMessage(KnowledgeAckUserPromptTemplate, map[string]any{
			"Knowledges": knowledgeItems,
		})()
		if err != nil {
			run.GetLogger().Errorf("failed to render knowledge user message: %v", err)
			return nil, nil
		}

		assistantMsg, err := prompt.WithAssistantMessage(KnowledgeAckAssistantPromptTemplate, map[string]any{})()
		if err != nil {
			run.GetLogger().Errorf("failed to render knowledge assistant message: %v", err)
			return nil, nil
		}

		msgs = append(userMsg, assistantMsg...)
		return msgs, nil
	}
}

type RichSystemPromptOption struct {
	Template        *template.Template
	Variables       map[string]any
	Tools           []iris.Action
	KnowledgeOption *WithKnowledgeRetrieveMessageOption
}

// WithRichSystemMessage 生成支持丰富数据注入的系统消息，支持 tool 列表和知识召回。
// Variables:
//   - tools: {Name, Description, Parameters, Hints}
//   - knowledges: {Title, UsedWhen, Content}
//   - User: {Username, Type}
func WithRichSystemMessage(run *iris.AgentRunContext, opt RichSystemPromptOption) prompt.ComposeVaryMessageOption {
	return func() ([]*framework.ChatMessage, error) {
		var buf strings.Builder
		var vars map[string]any
		if opt.Variables != nil {
			vars = opt.Variables
		} else {
			vars = map[string]any{}
		}

		if _, ok := vars["tools"]; !ok {
			vars["tools"] = lo.Map(opt.Tools, func(tool iris.Action, _ int) prompt.ToolDescription {
				return prompt.GetToolDescription(tool)
			})
		}

		if _, ok := vars["knowledges"]; !ok && opt.KnowledgeOption != nil {
			knowledgeItems, err := RetrieveKnowledgesWithCache(run, *opt.KnowledgeOption)
			if err == nil {
				vars["knowledges"] = knowledgeItems
			}
		}

		if _, ok := vars["User"]; !ok && run != nil {
			vars["User"] = run.User
		}

		err := opt.Template.Execute(&buf, vars)
		if err != nil {
			return nil, err
		}
		return []*framework.ChatMessage{
			{
				Role:    "system",
				Content: buf.String(),
			},
		}, nil
	}
}
