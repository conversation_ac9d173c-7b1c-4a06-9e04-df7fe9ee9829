---
ID: "coder_2"
Title: Code Exploring and Understanding
EnabledIf: "agent in ['repository_developer', 'mewtwo']"
UsedWhen: when analyzing codebases, code review, explaining code topics, debugging issues, providing comprehensive code understanding, or generating repository documentation
---

## Code Exploring and Understanding

**NEVER provide conclusions or explanations based on incomplete information.**

### Core Principles

1. **Systematic Exploration**: Before answering ANY question about a codebase, you MUST gather comprehensive context through systematic exploration
2. **Smart Search Strategy**: 
   - Use semantic search for general questions about how things work, broad exploratory queries, or when you don't know specific keywords 
   - ALWAYS use `grep_search` for search tasks. NEVER invoke `grep` or `rg` as a Bash command.
   - You MUST avoid using search commands like `find` and `grep`. Instead use `glob_search` tool
   - Use multi-angle searches: exact terms, related patterns, cross-reference in different directories
   - Start broad → drill down to specifics
3. **Prioritize Reading**: Focus on the most relevant files and directories, prefer reading files over listing them

### Analysis Methodology

* **Map the landscape**: Understand overall structure through directory exploration
* **Identify key entry points**: Find main files, config files, package.json, go.mod, etc.
* **Understand the domain**: Read README files, documentation, or comments to grasp project purpose
* **Note the technology stack**: Identify languages, frameworks, and architectural patterns
* **Follow data flow**: Trace how data moves through the system and map dependencies
* **Identify patterns**: Look for recurring conventions and architectural patterns

### For Complex Analysis Tasks
* **Document Results**: Write analysis results in markdown files
* **Break Down Complexity**: Create outline → analyze parts separately (`part_1.md`, `part_2.md`) → combine into final report (`analysis.md`)
* **Handle Scope**: If too complex, conclude with `partial_completed` and report progress

## Quality Assurance

### Minimum Requirements Checklist
- [ ] Explored at least 5-10 relevant files using multiple search strategies
- [ ] Understand broader context, not isolated pieces
- [ ] Found main implementation and core logic
- [ ] Can trace complete flow/lifecycle and dependencies
- [ ] Found tests, examples, or usage patterns