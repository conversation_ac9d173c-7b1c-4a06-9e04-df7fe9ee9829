---
ID: 'coder_9'
Title: Get Frontend Development Knowledge
EnabledIf: "agent in ['repository_developer', 'mewtwo', 'dynamic_planner']"
UsedWhen: |
  When you are developing a specific frontend code repository and when the technology stack of this frontend code repository meets these keywords: EdenX, modernjs, rspack, rsbuild, vmok, frontend, garfish, module-federation, eden proxy, emo, eden-monorepo. Or when you need to obtain relevant information about listed above frontend technology stacks.
---

## Get Frontend Development Knowledge

This knowledge can help you acquire more accurate Bytedance's internal and unique frontend development knowledge and develop more accurate Bytedance frontend code. (By using `byted_fe_knowledge` mcp tool `get_general_knowledge`)

### Core Rules（IMPORTANT）

- MUST use the `byted_fe_knowledge` mcp tool `get_general_knowledge` to obtain development knowledge, even if you have already obtained the corresponding knowledge through other search methods;
- When requesting `get_general_knowledge`, must ensure:
  - The `query` parameter: Keep it as close to the user's original query as possible
  - The `lib` parameter: Ensure the correct technology stack is passed to the lib parameter. If the lib parameter is incorrect, retry.

### Workflow

1. **Recheck the Frontend project technology stack**:
   You must recheck whether the frontend project technology stack is correct to avoid incorrect technology stack knowledge acquisition, If there are any errors, they need to be corrected. Here are some rules for determining what technology stack is used:

- EdenX: `@edenx/*`, `@edenx/runtime`, `@edenx/app-tools`,`edenx.config.t(j|cj|mj|ct|mt)s` etc.
- modern.js: `@modern-js/*`, `@modern-js/runtime`, `@modern-js/app-tools`,`modern.config.t(j|cj|mj|ct|mt)s` etc.
- rsbuild: `@rsbuild/*`, `@rsbuild/core`, `@rsbuild/plugin-*` ,`rsbuild.config.t(j|cj|mj|ct|mt)s` etc.
- rspack: `@rspack/*`, `@rspack/core`, `@rspack/plugin-*` ,`rspack.config.t(j|cj|mj|ct|mt)s` etc.
- emo: `@ies/eden-monorepo`, `eden.monorepo.json`, `eden-monorepo` etc.
- garfish: `garfish`, `@garfish/bridge-react`, `@garfish/bridge-vue-v2` etc.
- vmok: `@vmok/kit`, `@vmok/runtime`, `@vmok/webpack-plugin-v5`, `@edenx/plugin-vmok` etc.
- mf: `@module-federation/*`, `ModuleFederation` etc.
- eden proxy: `@edenx/proxy` etc.

2. **Get the frontend development knowledge**:
   Once the frontend project technology stack listed above is determined, you must use the `byted_fe_knowledge` mcp tool `get_general_knowledge` to get the frontend development knowledge.
