---
ID: "coder_6"
Title: Fullstack App Development Workflow
EnabledIf: "agent in ['repository_developer', 'mewtwo', 'web_creation']"
UsedWhen: when user requests to build a web app that needs a backend service
---

## Fullstack App Development Workflow

Only apply this workflow when user specifies they need a backend service
This Web Development Workflow will teach you how to develop a fullstack web app locally, including a backend and frontend. Remember everything you see in this and take note of it when planning!

# Planning
1. Write down a brief description of the API endpoints you need to implement so you can reference this later.

# Database
1. You should use an in-memory database sqlite for the backend. You can refer to the example in the main.py.
2. It's okay that the data will be lost when the app is restarted, but you should message the user and tell them that this is your approach since you're working on a proof of concept.

# Backend
1. If the user did not specify a backend stack, you must use `aime_create_fastapi_app {app name}` to create the app in bash. This will setup a FastAPI app with Postgres already setup.
- **Important**: The backend code should be based on Python 3.8. Make sure to use Python 3.8 compatible syntax and libraries.
- The scaffolding will include an main.py file. You MUST keep the run.sh untouched so that the backend works later when you deploy it. You MUST NOT remove or rename this file. 
2. Now implement the backend. If using FastAPI, you need to add any additional dependencies using `pip install <package name>`. It's important that the requirements.txt file contains all needed dependencies since otherwise the deployment will fail.
- Make sure all dependencies are Python 3.8 compatible.
- If environment variables are needed, you should use a .env file in the backend directory and load them from there.
3. If you need a database, use the in-memory database.
4. Test your backend endpoints using curl and iterate until they work as expected. For FastAPI, you can start the development server using `uvicorn main:app --reload` in background(timeout=-1). This will auto-reload as you make changes. 
5. DO NOT EDIT THE `run.sh` FILE in fastapi app which is used for deployment.
6. DO NOT EDIT the if __name__ == "__main__" block (entry point) in main.py in fastapi app which is used for deployment.    

# Frontend
1. You must use `aime_create_react_app {app name}` in bash to create the app. This will setup a Vite + React Typescript app with Tailwind CSS already setup and shadcn/ui pre-installed.
- If you face import errors after creating the frontend this way DO NOT PANIC. You can just check the "src/components/ui" directory to see which pre-built components are available and create or install any missing ones. You must not use a different way to create the app.
2. Now implement the frontend using these guidelines:
- Use Tailwind classes for styling. DO NOT USE ARBITRARY VALUES (e.g. h-[600px]). When you're really unsure, you can look up the Tailwind docs.
- Use lucide for icons e.g. import { Camera } from "lucid-react" & <Camera color="red" size=48 />
- Never embed data visualization picture in html! Use frontend native charts library to develop interactive elements within HTML.
- The recharts charting library is available to be imported, e.g. import { LineChart, XAxis, ... } from "recharts" & <LineChart ...><XAxis dataKey="name"> ...
- Use prebuilt components from the shadcn/ui library. They are already pre-installed but you need to import them: import { alert, AlertDescription, AlertTitle, AlertDialog, AlertDialogAction } from '@/components/ui/alert';
- Make the backend API URL easily configurable using a .env file and for now set it to http://localhost:8000. Use the .env file for any other frontend-specific env variables that are needed.
3. Now test the frontend locally by running `npm run dev` and navigating to http://localhost:5173 in your browser. This will auto-reload as you make changes. 

# Deployment
1. Once the fullstack app works, and if the backend uses FastAPI, deploy the backend using the command `deploy_backend` tool with the path to the backend app. You will receive a URL to access your backend.
2. Update the frontend app .env file with the deployed backend URL.
3. Deploy the frontend app using the `deploy_frontend` tool with the path to the build directory(/dist). You will receive a URL for the deployed app.
4. Share the frontend URL with the user and let them know that you tested the app locally but still need to test the deployed version.

# Production Testing
1. Test the deployed app
- Only test the page loads correctly using browser_goto_and_extraction and Do NOT perform extensive testing or detailed interaction testing.
- If you make updates to frontend, rebuild and then redeploy the app by using the same `deploy_frontend` command again and let the user know.
- If you make updates to backend, you must redeploy the backend using the same `deploy_backend` command again and let the user know.
2. Notify the user every time you redeployed, share the frontend URL, and tell them what changes you made.
3. If the user gives you feedback or further requests, you should go back to the "Backend" section (if the changes require a backend) or the "Frontend" section (if the changes are frontend-only) and iterate from there.

Important reminders:
- NEVER share a localhost url with the user, since the user is not on your network. Your public/private IP are similarly not accessible. The user can only access the apps via the deployed URLs.
- Run `aime_create_fastapi_app --help` in the shell for more info on how to use the command.
- Run `aime_create_react_app --help` in the shell for more info on how to use the command.
- When setting up a new fullstack project, make a new directory to hold both the frontend and backend.
- Do not use this note when working in an existing repo.
- Note that there is also a separate "Simple Web App Workflow" - you should choose to follow *either* this workflow or the simple web app one, but not both - decide which is more suitable, and stick to it. If the webapp requires a sophisticated frontend that can't easily be implemented in a single App.tsx file, use this workflow, even if it is frontend only.
- If the user does not specify a backend needed for the app, then follow "Simple Web App Workflow" instead.
- Only implement the backend when the user explicitly requests a backend service. Otherwise, only write the frontend application.

