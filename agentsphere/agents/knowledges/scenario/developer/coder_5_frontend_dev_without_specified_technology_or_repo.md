---
ID: 'coder_5'
Title: Frontend Development Workflow Without A Specified Repository
EnabledIf: "agent in ['repository_developer', 'mewtwo']"
UsedWhen: when developing frontend web applications(前端应用) with UI components from scratch and without specified repository (not for data visualization and report)
---

## Simple Web App Workflow

This note explains best practices when it comes to creating web applications from scratch for users who don't have extra requirements such as:

- Not having an existing repo
- No specifying a particular tech stack like Vue etc.

For developing frontend web applications with UI components from scratch(not for data visualization and report), you should follow these guidelines:

1. Preparation

- Use any pre-prepared resources available: research documents, datasets, images, analysis reports, or design materials.
- Always carefully review and utilize the provided resources
- Read all referenced files thoroughly and incorporate their content appropriately
- Use provided images in the website design - they are meant to be displayed and integrated
- Transform documents and data files into meaningful web content
- Never ignore provided resources - they are essential components for the final website

2. Development

- You must use shell cmd `aime_create_react_app {app name}` to create the app. This will setup a Vite + React Typescript app with Tailwind CSS already setup and shadcn/ui pre-installed.
- The `aime_create_react_app` command is already installed and you can use it directly.
- If you face import errors after creating the frontend this way DO NOT PANIC. You can just check the "src/components/ui" directory to see which pre-built components are available and create or install any missing ones. You must not use a different way to create the app.
- Since this is a simple app and everything's already set up, you should only change `src/App.tsx` and leave the rest of the code as is.

You must follow these guidelines while developing the app:

- Use Tailwind classes for styling. DO NOT USE ARBITRARY VALUES (e.g. h-[600px]). When you're really unsure, you can look up the Tailwind docs.
- Use lucide for icons e.g. import { Camera } from "lucid-react" & <Camera color="red" size=48 />
- Never embed data visualization picture in html! Use frontend native charts library to develop interactive elements within HTML.
- The recharts charting library is available to be imported, e.g. import { LineChart, XAxis, ... } from "recharts" & <LineChart ...><XAxis dataKey="name"> ...
- Use prebuilt components from the shadcn/ui library. They are already pre-installed but you need to import them: import { alert, AlertDescription, AlertTitle, AlertDialog, AlertDialogAction } from '@/components/ui/alert';
- **For images, use `import` to convert images into variable references, otherwise images won't be included in the build.** E.g. `import logoImage from './assets/logo.png'` then use `<img src={logoImage} />` instead of `<img src="./assets/logo.png" />`
- If environment variables are needed, you should use a .env file in the frontend directory and load them from there.

3. Testing and deployment
   Step 1: 本地测试 - Run `pnpm run dev` (bash timeout=-1 to run it in background) and navigate to http://localhost:5173 in your browser. This will auto-reload as you make changes.

- Only use `browser_goto_and_extraction` to verify that the page layout renders correctly, images load properly, and charts display as expected. Do NOT perform extensive testing or detailed interaction testing.
- DO NOT DEPLOY UNTESTED CODE.
  Step 2: 构建应用 - Before deploying, you must build the app using `pnpm run build`.
  Step 3: 部署应用 - Deploy the app using the `deploy_frontend` tool. E.g. using Vite, the build_dir is "dist" inside your app's folder.
- It's okay to deploy even if the user didn't specifically ask for it if their request was very open-ended. If the user's request was very detailed or specifically forbids deploying, you should not deploy until you can clarify with the user.
  Step 4: 验证部署 - After deployment, only verify that the deployed page loads correctly.
- If you make updates to the app, rebuild and then redeploy the app by using the `deploy_frontend` tool again.

IMPORTANT:

- Use `aime_create_react_app` in bash to quickly set up the project structure.
- **CRITICAL: Always import images using `import`. DO NOT USE `<img/>` directly.**
