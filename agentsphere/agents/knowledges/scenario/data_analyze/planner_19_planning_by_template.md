---
ID: "planner_19_template"
Title: Planning By Lark Template with De<PERSON>ult SOP
EnabledIf: "agent in ['dynamic_planner']"
UsedWhen: |
  use it when user mentioned '参考飞书模板' or '基于飞书模板' or '完成这个模板' or other such words.
---

# Planning By Lark Template with Default SOP

## Core Principle: Logical Action Units (LAUs) for Template Processing
**⚠️ PRIORITY OVERRIDE**: When this template-specific knowledge conflicts with general LAU principles from the dynamic planner system, **THIS KNOWLEDGE TAKES PRECEDENCE**. Template processing requires specialized handling that may deviate from standard aggregation rules.

## Progress Planning Best Practices:
- **Aggregate related operations**: Combine data collection, template analysis, and initial planning into comprehensive steps
- **Maintain contextual coherence**: Each step should preserve the logical flow and dependencies of template processing
- **Maximize scope per step**: Design each step to encompass the largest meaningful unit of work that can be completed in one turn
- **Template continuity**: Ensure template context is maintained across all steps to prevent overwriting or loss
- Download/Retrieve all related file/data in first step.
- Read the template, understand the structure and purpose.
- Design analysis plan, based on fill in that template.
- Fill in the template step by step progressively, do not try fill all at once.
- After you get the template, pass it to every agent and step, to prevent agent from overwriting the template or missing it.
- If user has provided their own SOP, follow their SOP instead of the default plan below.

## Default Plan Structure for Template Processing:

When user provides a lark template but no specific SOP, follow this approach:

### Example Default Plan:

## Progress
- [ ] Template Analysis and Foundation Setup
  - [ ] Download and consolidate all referenced documents, external data sources, and template files in a single comprehensive action
    [ ] Perform complete template structure analysis, understanding purpose, requirements, and data mapping
- [ ] Data Processing and Analysis for Section 1
  - [ ] **🚨 MANDATORY FIRST**: Your first action MUST be to use the read_file tool on the template original document and all important analysis files for the template documents. Do not proceed to any other actions until this is complete.
  - [ ] Process data according to Section 1 requirements
  - [ ] Generate content for Section 1
- [ ] Data Processing and Analysis for Section 2
  - [ ] **🚨 MANDATORY FIRST**: Your first action MUST be to use the read_file tool on the template original document and all important analysis files for the template documents. Do not proceed to any other actions until this is complete.
  - [ ] Process data according to Section 2 requirements
  - [ ] Generate content for Section 2
- [ ] Data Processing and Analysis for Section 3
  - [ ] **🚨 MANDATORY FIRST**: Your first action MUST be to use the read_file tool on the template original document and all important analysis files for the template documents. Do not proceed to any other actions until this is complete.
  - [ ] Process data according to Section 3 requirements
  - [ ] Generate content for Section 3
- [ ] Template Validation and Final Document Generation
  - [ ] **🚨 MANDATORY FIRST**: Your first action MUST be to use the read_file tool on the template original document and all important analysis files for the template documents. Do not proceed to any other actions until this is complete.
  - [ ] Validate completeness of all template sections and ensure consistency across the document
  - [ ] Generate final lark document with all filled content followed by the template file's format and present to user

## LAU Implementation Guidelines:

- **Scope Maximization**: Each LAU should encompass the largest logical unit possible while maintaining coherence
- **Progressive Execution**: NEVER attempt to complete the entire document at once. Always work chapter-by-chapter or module-by-module
- **Incremental Completion**: Complete each section fully (analysis + filling) before moving to the next section
- **Context Preservation**: Template context must be explicitly passed to all agents within each LAU
- **Dependency Management**: Ensure each LAU respects the logical flow from data collection → analysis → filling → validation
- **Agent Coordination**: When delegating to sub-agents, provide comprehensive instructions that include the full template context and the specific LAU scope

## Template-Specific LAU Considerations:

- **Template Hierarchy Respect**: Use top-level headings (H1, then H2) as natural LAU boundaries
- **Progressive Filling Strategy**: Within each LAU, work incrementally - complete one chapter/module fully before starting the next
- **NO Bulk Processing**: Never attempt to analyze and fill multiple major sections simultaneously
- **Data-Template Coupling**: Combine data analysis with immediate template filling within the same LAU to maintain context
- **Sequential Section Completion**: Within each LAU, complete all dependent sections before moving to sections that reference their conclusions

# IMPORTANT LAU RULES FOR TEMPLATE PROCESSING:
- MUST attach all relevant important files and template files that the mewtwo agent might need in the task.
- If the current task is analyzing template documents, conclude must provide detailed analysis of the template documents and MUST COMPLETELY DISPLAY all analysis document content.
- If the current task is analyzing data, MUST READ previous template analysis documents first before any analysis, if no analysis documents exist, MUST READ the original template itself.
- If the template contain ![preview](*.xlsx) format placeholders, the subsequent content generation plan MUST include an explicit step to create or update the corresponding .xlsx file, not just generate text summaries.
- **Aggregation First**: Always combine related micro-operations into comprehensive steps
- **Progressive Execution**: NEVER attempt to complete entire document at once - work chapter-by-chapter or module-by-module
- **Incremental Approach**: Complete each section fully (analysis + filling) before proceeding to the next
- **Context Continuity**: Each LAU must maintain full template context to prevent fragmentation
- **Scope Maximization**: Design LAUs to handle the largest meaningful work unit possible while respecting progressive execution
- **Sequential Coherence**: Ensure LAUs respect natural dependencies and logical flow

# IF USER PROVIDES CUSTOM SOP:
When user provides their own SOP, adapt it to LAU principles by:
- Grouping their specified steps into logical action units
- Maintaining their intended sequence while maximizing scope per LAU
- Ensuring each LAU represents a coherent, self-contained phase of their process