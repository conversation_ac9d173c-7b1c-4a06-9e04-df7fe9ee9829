---
ID: "2"
Title: LLM Guide for Data Analysis and Classification
EnabledIf: "agent in ['data_analyzer', 'mewtwo']"
UsedWhen: Process text data with natural language processing, Classify content into categories, Filter/Label or annotate data with specific attributes, Evaluate content based on multiple criteria and any other language processing tasks.
---

For data labeling, classification, or annotation tasks, or any language process tasks it is recommended to use llm APIs rather than attempting to perform these tasks directly.

When processing data:
1. Carefully check the number of items. If there are more than 100 items, use a batch processing approach, record results for each batch, and summarize at the end.
2. For all datasets, adopt concurrent batch processing to improve labeling efficiency.
3. Complete the labeling of all data unless explicitly instructed otherwise. If exceptions occur during processing, mark those items as anomalies and continue with the remaining data.

**Do not use the model to generate or fabricate data for labeling, classification, or annotation tasks.**

Example API call for data tasks:

```python
import os
import requests
import json
import time
import concurrent.futures
import threading

SESSION_ID = os.getenv('IRIS_SESSION_ID')
OPENAI_BASE_URL = os.environ.get("OPENAI_BASE_URL")
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
MODEL = "doubao-1.5-pro-32k-250115"
TIMEOUT = 20  # seconds
BATCH_SIZE = 10  # Process 10 items per batch
MAX_WORKERS = 5  # Maximum number of concurrent threads

# Thread-safe lock for results
results_lock = threading.Lock()

def call_llm_api(messages):
    if not OPENAI_BASE_URL or not OPENAI_API_KEY:
        print("Error: OPENAI_BASE_URL or OPENAI_API_KEY environment variables not set.")
        return None

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {OPENAI_API_KEY}",
        "X-Session-ID": SESSION_ID,
        "X-LLM-TAG": "process_data"
    }
    data = {
        "model": MODEL,
        "messages": messages
    }

    try:
        response = requests.post(f"{OPENAI_BASE_URL}/chat/completions", headers=headers, data=json.dumps(data), timeout=TIMEOUT)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def process_item(request_data):
    messages = [
        {"role": "system", "content": "You are an AI assistant..."},
        {"role": "user", "content": request_data["prompt"]}
    ]
    result = call_llm_api(messages)
    return {
        "id": request_data["id"],
        "prompt": request_data["prompt"],
        "result": result,
        "status": "success" if result else "failed",
        "timestamp": time.time()
    }

def save_results(results, filename="processing_results.json"):
    with open(filename, "w") as f:
        json.dump(results, f, indent=2)

def load_results(filename="processing_results.json"):
    try:
        with open(filename, "r") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return []

def process_batch(batch):
    batch_results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = {executor.submit(process_item, item): item for item in batch}
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                batch_results.append(result)
                print(f"Processed item {result['id']}: {'Success' if result['status'] == 'success' else 'Failed'}")
            except Exception as e:
                item = futures[future]
                print(f"Error processing item {item['id']}: {e}")
                batch_results.append({
                    "id": item["id"],
                    "prompt": item["prompt"],
                    "result": None,
                    "status": "error",
                    "error": str(e),
                    "timestamp": time.time()
                })
    return batch_results

if __name__ == "__main__":
    # Example data to process
    all_requests = [
        {"id": i, "prompt": f"Question {i}: What is the capital of country {i}?"}
        for i in range(1, 101)  # 100 items to process
    ]
    
    # Load previously processed results
    processed_results = load_results()
    processed_ids = {item["id"] for item in processed_results}
    
    # Filter out already processed items
    remaining_requests = [req for req in all_requests if req["id"] not in processed_ids]
    print(f"Found {len(processed_results)} previously processed items. {len(remaining_requests)} items remaining.")
    
    # Process in batches
    for i in range(0, len(remaining_requests), BATCH_SIZE):
        batch = remaining_requests[i:i+BATCH_SIZE]
        print(f"Processing batch {i//BATCH_SIZE + 1}/{(len(remaining_requests) + BATCH_SIZE - 1)//BATCH_SIZE}")
        
        batch_results = process_batch(batch)
        
        # Thread-safe update of results
        with results_lock:
            processed_results.extend(batch_results)
            save_results(processed_results)
        
        print(f"Batch {i//BATCH_SIZE + 1} completed. Total processed: {len(processed_results)}/{len(all_requests)}")
    
    print("All processing completed!")
    print(f"Successfully processed: {sum(1 for r in processed_results if r['status'] == 'success')}")
    print(f"Failed: {sum(1 for r in processed_results if r['status'] != 'success')}")
```

`$OPENAI_BASE_URL` provides an OpenAI compatible chat completion api.
The `$OPENAI_API_KEY` and `$OPENAI_BASE_URL` environment variables are available when executing commands using `bash` tool. But don't log them.
Available models: `doubao-1.5-pro-32k-250115`, `doubao-vision-pro-32k-250115`
