---
ID: "dynamic_25_2"
Title: guide for preparing content for lark templates reports
EnabledIf: "agent in ['mewtwo']"
UsedWhen: |
  use it whenever/wherever preparing content for a lark template or markdown report, analyzing data or generating visualization part for reports, even if the current task is just creating intermediate artifacts or doing analysis that will later be integrated into a final document.
---
# If user provide the Lark template document for you, you must strictly follow the rules:

## ⚠️ CRITICAL TASK EXECUTION RULES - MUST FOLLOW
**These rules are MANDATORY for all tasks:**

1. **Do not create any `lark.md` files before using `lark_creation` tool.**
2. **If the current task is analyzing template documents, conclude must provide detailed analysis of the template documents and MUST COMPLETELY DISPLAY all analysis document content.**
3. **If the current task is analyzing data, MUST READ previous template analysis documents first before any analysis, if no analysis documents exist, MUST READ the original template itself.**

## Lark Templates Filling Rules
1. Read all the comments in template file carefully and follow the instructions if it exists. Comments usually contain user's requirements that refer to specific content in the template file.
2. Identify and clarify all data sources, they may come from documents, user query, references, comments, etc.
3. Comments appear in the template file with `<!-- comment:xxx -->` format. Carefully read these comments and do not render them as content in the final output.
4. Be careful with the occupied/templated areas, you need to fill the expected content in the right place.
    - All the detailed filling rules are contained in `Feishu/Lark Doc Generation` knowledge base.
    - `[正文]` is the main content area, fill with plain text content, this is usually the most flexible part you can modify.
    - `[列表]` is the list area, fill with ordered/unordered list content as appropriate.
    - `[图片]`, `[图例]`, or `[图表]` is the image area, fill with static images, figures, etc.
    - `[表格]` is the table area, MUST fill with correct Lark table format, do not split columns to different lines, do not modify the table structure. Tables cannot embed sub-tables.
    - `[引用]` is the quote area, quotes, links, or previews are all acceptable.
    - `[代码]` is the code area, fill with code content in appropriate syntax highlighting format.
    - `[公式]` is the formula area, must use correct Lark formula format (LaTeX-style math expressions).
    - `[交互式]` or `[HTML]` means a **SINGLE** interactive HTML chart follow the [preview](chart.html) format. The final document will be rendered in a markdown-like page, so you can only embed a single chart, not large pages or multiple charts.
    - `[风神截图]` 风神平台图表截图，区别于`[风神图表嵌出]`：
      - `[风神截图]` + 评论说明图表名称，或
      - `[风神截图:图表名称]` 直接指定图表名称
      - 最终使用markdown图片格式语法渲染
      - **注意：风神截图必须是aeolus_download文件夹里的图片**
    - `[风神图表嵌出]` 直接使用风神图表的链接，语法为`[preview](风神嵌出链接)`
    - `![preview](*.xlsx)` 电子表格格式，需要按用户的要求操作xlsx文件，最终报告中必须保持`![preview](*.xlsx)`格式，禁止写成markdown表格。**重要：必须严格参照固定流程顺序来处理飞书表格的填入！**
       1. 必须基于模板中的xlsx文件生成新的xlsx文件，最终报告中的文件名可以与模板不同，但必须保持`![preview](*.xlsx)`格式
       2. 分析、处理、填入新的xlsx文件后，必须将新的xlsx替换掉模板中的xlsx，然后再生成最终报告。
    - 对于模板文档中已经存在的 `[preview](url)` 链接，在最终的报告中必须保证和模版一致，禁止擅自删除或修改 preview的链接或格式
5. Do not modify the template structure, keep the original skeleton/headings intact, and forbidden to write code to generate lark document content.
6. Remove elements that have no suitable content to fill, but preserve the overall document structure and required headings.
7. Create the final document with `lark_creation` tool.

## IMPORTANT RULES
- List and conclude every data source you found and used (including unused sources) in the template file, origin url is important, don't ignore it:
    - [Data Source1](file_1)
    - [Data Source2](file_2)
    - [Data Url1](url_1)
    - ...
- Strictly follow user comments regarding analysis methods, graph types, content requirements, etc.
- It is **strictly forbidden** to create new template files or files with the same name as the template file.
- If one chapter is too large that you cannot finish it in one task, you can create a single lark md for it and merge it in future.
- You should **only patch/replace content within replaceable markers**, such as `[正文]`, or content surrounded by `{{}}`, `[]`, `【】`, `<!-- -->`, etc.
- **Never** write code to generate lark document content, always output it in file directly.
- **Do not** modify the original text structure, headings, or non-templated content.
- You are **STRICTLY FORBIDDEN** from using the `progress_think` tool when handling any user requests related to Lark template documents.
- When uncertain about table formats or content requirements, ask the user for specific clarification before proceeding.
- Preserve all non-templated text exactly as written in the original template.
- If the template contains markdown format tables, the generated document **MUST** maintain the table format exactly as specified in the template.
- If you need to fill numbers or calculations in tables, you **MUST** use code-based approaches (such as writing scripts or using computational tools) to ensure accuracy and consistency.
- **Any numerical results appearing in Lark templates are considered examples by default, unless the user explicitly indicates they are real results. You are strictly forbidden from using these example numbers as final results.**
- **Do not arbitrarily modify proper nouns or technical terms in the document, and do not translate English terms into Chinese without explicit user instruction.**
- Note that text formats include font color. Please use appropriate colors in accordance with the requirements of the Lark template.
- **MUST** remove sections that are clearly instructional prompts for content generation (e.g., "guide for generating xxx", "instructions to fill this section").
- **MUST**: Conclude every discovery in `content` in `conclude` tool, never generate analysis report file for lark template