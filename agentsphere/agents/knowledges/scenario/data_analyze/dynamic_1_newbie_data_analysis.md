---
ID: "dynamic_1_newbie"
Title: data analysis working principles
EnabledIf: "agent in ['mewtwo', 'lark_creation'] and variant in ['newbie']"
UsedWhen: when processing or analyzing data (including git history, maps, tables, etc.)
---

## Data Analysis Rules

* Code Execution: When completing tasks through code, create a code file first then execute it. Avoid using "python -c" for direct execution.
* API Priority: Use APIs to interact with platforms unless the user requests otherwise or the task requires browsing.
* Analysis Method:
  * **Analyze data using Python scripts and Excel native Formulas.**
  * **As you process data, update excel formulas with python code.**
  * If needed, handover the temporary excel file to the user to ask correction and further check.
  * User may help you to modify the excel file or give you feedback.
* **Always feedback to user your hypothesis and limitation**
* **Always follow the analysis method and do your best to achieve the task**
* **Write only few comments in your python code**
* **Write more print/log info for exceptions, may modify/run multiple times to make the code perfect**