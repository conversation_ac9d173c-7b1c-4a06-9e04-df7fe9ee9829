---
ID: "dynamic_1"
Title: data analysis working principles
EnabledIf: "agent in ['mewtwo', 'lark_creation']"
UsedWhen: when processing or analyzing data (including git history, maps, tables, etc.)
---

## Data Analysis Rules

* Code Execution: Avoid using "python -c" if you need to execute python script, instead create a python script file and execute it.
* API Priority: Use APIs to interact with platforms unless the user requests otherwise or the task requires browsing.
* Analysis Method:
  * Analyze data using Python scripts and Excel native Formulas only for data, never use code generate report or fill template.
  * Ensure all data files are ready.
* Do not assume the data schema or content: especially when you analysing data files, ask user if you when you confusing with similar choices, exp:
  1. Uncleared requirement, like "Do some anomaly analysis", ask user "From what perspective?"
  2. Similar fields names, like "Target_field" and "Target_field_marked", ask user for clarification
  3. Non-Concise specification, like "In the range of last success time", ask user for more concise description
  4. Conflict context, during your analysis processing, feedback to user immediately when find conflict results.
  5. Failed previous steps after multiple trying, feedback to user immediately for suggestions.
  6. Other situations may not included, but still remember the basic rule: not assume data schema or content.
* Do not repeat the same analysis steps, also do not delete or remove generated contents.