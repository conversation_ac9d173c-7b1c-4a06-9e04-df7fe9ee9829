---
ID: "coder_4"
Title: Collaboration and Version Control Best Practices
EnabledIf: "agent in ['repository_developer', 'mewtwo', 'planner', 'dynamic_planner']"
UsedWhen: when working with git repositories, merge(pull) requests, cloning repositories, or ByteDance codebase platform
---

## Git Workflow Best Practices

### Repository Cloning
- If the user didn't provide a specific branch/commits to clone, do not assume the default branch is master or main. Just omit the branch settings.
- If the task do not need commit history, use shallow clone to save time and space. Otherwise, use full clone.

### Checkout Commit or Branch
- If the user asks you to checkout a specific reference or commit, do `git fetch origin <reference-or-commit-hash>` first.

### Terminology

- `Codebase`
  - Codebase refers to ByteDance's private git hosting platform (similar to github, gitlab, etc).
  - Platform URL: https://code.byted.org/ or https://code.byted.org/gerrit or https://bits.bytedance.net/code
- `MR`
  - MR stands for Merge Request, it's a feature provided by ByteDance's git hosting platform.
  - An MR is a request to merge changes from one non-default branch into another specified branch, which is commonly the master or main branch.

### Branch naming conventions
- Use descriptive, hyphenated slugs in your branch names
- Format: aime/{timestamp}-{descriptive-slug}
- This branch name convention should be overridden IF AND ONLY IF the user has provided instructions for a specific branch name or there is another note specifying branch name convention.
- Generate timestamps with: date +%s
- Keep slugs descriptive but concise
- Use lowercase and hyphens only, no spaces or special characters

### Make a Merge Request Workflow
The following tips are relevant if you need to make a merge request on a repo on Codebase. If you are just working with a public repo's code but not expected to contribute to it, do not use this workflow. If you are not supposed to create a MR yet (e.g. if you need to wait for user confirmation), this note is not relevant yet.
- Check `git status` before committing or adding files.
- Use `git diff` to see what changes you have made before committing.
- Double check the name of the main branch (which could be `main` or `master`) using `git branch`.
- You should already be authorized to access any repositories the user tells you about. If not, ask the user for access.
- Do NOT try to access `code.byted.org` through the browser, you will not be authenticated.
- If you're updating an existing repo, use the `submit_merge_request` tool to make merge requests. Do NOT push directly to the main branch.
- NEVER force push on branches! Prefer merging over rebasing so that you don't lose any work.
- after making/updating the MR description, share the link to the MR to the user and ask them to review it. You must remind the user that you are able to view comments that they leave on the MR, so they can ask for changes directly on Codebase.
- ALWAYS push your changes to the remote before creating a MR
