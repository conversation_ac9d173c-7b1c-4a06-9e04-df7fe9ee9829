---
ID: "planner_11"
Title: diagrams
EnabledIf: "agent in ['dynamic_planner','mewtwo', 'lark_creation']"
UsedWhen: User's task may require drawing diagrams
---
## Tool Selection Rules:

### Use PlantUML for:
- **Class Diagrams** (showing class relationships and structure)
- **Mind Maps** (hierarchical information and brainstorming concepts)  
- **时序图 Sequence Diagrams** (显示对象间随时间的交互过程，注意：这是时序图，不是流程图)
- Output formats:
  - For Feishu/Lark Doc: PlantUML fenced code block only. Do not generate any image files from PlantUML.
	  -	Required fence:
    ```plantuml
    …diagram code…
    ```
  - .svg, for HTML (Feishu/Lark Doc DO NOT supports svg)

**⚠️ IMPORTANT: PlantUML is ONLY allowed for the above three diagram types. For all other diagram types, use Mermaid or GraphViz instead.**

### Use GraphViz for:
- **Draw architecture diagrams** with GraphViz
- Output formats:
  - .png, for Feishu/Lark Doc (Feishu/Lark Doc DO NOT supports GraphViz code blocks)
  - .svg, for HTML (Feishu/Lark Doc DO NOT supports svg)

#### GraphViz Guidelines:
* Clearly separate the direction graph and non-direction graph:
  - Use `digraph` to represent direction graph
  - Use `graph` to represent non-direction and hierarchy graph
* Choose the best layout engine:
  - `dot`: hierarchy and non-direction graph
  - `fdp`: massive(over 100 nodes) non-directional graph
  - `sfdp`: huge graph(over 500 nodes)

### Use Mermaid for:
- **All other diagram types** including:
  - System flow diagrams
  - Network diagrams
  - Process flow charts
  - Component diagrams (non-UML style)
  - Any custom visualization needs
- Output formats:
  - For Feishu/Lark Doc: Mermaid fenced code block only. Do not generate any image files from Mermaid.
	  -	Required fence:
    ```mermaid
    …mermaid code…
    ```
  - .svg, for HTML (Feishu/Lark Doc DO NOT supports svg)