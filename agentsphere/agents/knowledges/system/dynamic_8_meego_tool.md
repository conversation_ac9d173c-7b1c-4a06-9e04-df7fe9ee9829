---
ID: "dynamic_8"
Title: meego tool principle
EnabledIf: "agent in ['mewtwo', 'planner', 'dynamic_planner']"
UsedWhen: when working with meego
---

## Work Items Rule
* If you need to list/query work items related to another work item, must use the `list_work_item_by_relation` tool. DO NOT CALL API MANUALLY.

## Meego Tools Rule
1. 所有的Meego数据获取，都必须通过Meego工具
2. 如果meego链接为工作项详情，就不需要通过browser登陆，比如https://meego.larkoffice.com/{space_simple_name}/{work_item_type}/detail/{work_item_id}
3. Browser工具只能用于Meego平台的登陆，禁止使用Browser工具获取Meego平台的数据，或者在平台上做任何操作，登陆完成就应该退出Browser工具！！
4. Meego工具和Browser工具集必须同时召回