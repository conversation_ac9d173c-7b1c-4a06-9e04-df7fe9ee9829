---
ID: "dynamic_39_data_visualization"
Title: data visualization preferences
EnabledIf: "agent in ['mewtwo']"
UsedWhen: visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts)
---

## Data Visualization

### When to add charts

Prioritize using other appropriate ways to display data, when data points are not enough. For example, (documents) table, list (webpages) table, list, cards or bento grids, etc.

Before you want to create charts, consider if charts are really necessary for the final result. Simple criteria:

- Total data points less than 10.
  - For example: creating a table is more appropriate than a bar chart when you have only a few categories and values and some data points are missing.
- **Never** use charts when data can not be tracked to an external source, especially radar charts
  - External source: search results, user provided data, calculated from existing data
  - Non-external source: fabricated data, simulation data, subjective evaluations

The above rules does not apply when user explicitly asks for charts or visualizations.

### Chart Creation Options

The main options for data visualization are as follows:

    * Python should be your primary choice, so use Python unless interactive charts are required
    * If interactive charts are required, use HTML

#### Python

- Code Execution: Avoid using "python -c" if you need to execute python script, instead create a python script file and execute it.
- Use `matplotlib` or `seaborn` for chart generation. Ensure Chinese characters are displayed correctly.
- Save the chart as a PNG file (e.g., `output/delonghi_market_share_china_tmall_2023.png`).
- You must use the vision tool to inspect the Python generated chart, if the image shows significant issues or does not meet the requirements, then fix them,
  the significant issues that you should focus on when validating the Python generated chart include:

    * garbled text, e.g. Chinese characters are not rendered correctly
    * chart image is overlapping, the image contains overlapping elements that cover each other.

#### HTML (Used for interactive charts only)

- Do not use HTML to create chart unless interactive charts are required, if no interactive charts are required, MUST use python to create chart.
- Interactable charts can be made by echarts/plotly/d3 and saved in HTML files. Make sure each HTML file contains ONLY ONE chart (no text descriptions needed), cause HTML file may directly be inserted into other docs.
- Chart library options: Echarts.js, Plotly.js, D3.js
- Use browser version of plotly to generate a plotly chart. Don't use python version to generate HTML.
- Ensure Chinese characters are displayed correctly..
- You must deploy result files & pages then use browser tools to validate the generated html page (contains one chart only), because there might be some javascript errors or display issues, your testing should verify both functionality and chart UI, fix them if you find any issues.

### Data Transparency

- **Always Clarify Data Source:** The user is highly concerned about the statistics in the report. Clearly state the origin of the data (source) and describe any processing applied. Both data references and processing logic should be provided and explained.
- **Factuality First:** Include only objective information in the report. Preserve and accurately represent the original information and data.
- **Handle Missing Data:** For data that is not currently available, mark it as "N/A" or "-" when generating charts, tables, or interactive elements in the report.
- **Split visualization Data:** Split the visualization data file/json into separate files, to provide better performance and decrease influence if data is broken.
- **Resource Providing:** Provide download links for underlying data files and excel files in HTML page.