---
ID: "dynamic_37"
Title: PlantUML guidelines and restrictions
EnabledIf: "agent in ['mewtwo', 'lark_creation']"
UsedWhen: when need to draw PlantUML diagrams
---

## PlantUML Guidelines and Restrictions

### Basic Usage Rules
- Use ```plantuml to wrap the diagrams
- Always start with @startuml and end with @enduml
- @startuml MUST be on a line by itself with NO other content
- Keep diagrams simple and readable
- Use descriptive names for elements

### Example:
```plantuml
@startuml
Alice -> Bob: Hello
Bob -> Alice: Hi there
@enduml
```

## CRITICAL RESTRICTIONS - STRICTLY ENFORCED

**The following syntax restrictions MUST be strictly followed. VIOLATION OF THESE RULES WILL CAUSE RENDERING FAILURES:**

### ABSOLUTELY FORBIDDEN Syntax:
1. **!define syntax** - STRICTLY PROHIBITED and WILL CAUSE ERRORS
2. **!theme syntax** - STRICTLY PROHIBITED and WILL CAUSE ERRORS
3. **skinparam syntax** - STRICTLY PROHIBITED and WILL CAUSE ERRORS
4. **Color configurations** - ANY color-related configurations (color names, hex codes, RGB values) are ABSOLUTELY FORBIDDEN
5. **note syntax** - ALL note variations (note right, note left, note top, note bottom, note...as..., end note) are STRICTLY PROHIBITED
6. **caption syntax** - STRICTLY PROHIBITED and WILL CAUSE ERRORS
7. **legend syntax** - STRICTLY PROHIBITED and WILL CAUSE ERRORS
8. **text syntax** - STRICTLY PROHIBITED and WILL CAUSE ERRORS
9. **pie chart syntax** - STRICTLY PROHIBITED and WILL CAUSE ERRORS
10. **bar chart syntax** - STRICTLY PROHIBITED and WILL CAUSE ERRORS

### Relationship Syntax Restrictions:
- **ER diagrams**: ONLY <|--, *--, and o-- relationship notations are allowed
- **Component and Use Case diagrams**: ONLY --> relationship notation is allowed
- **ALL other diagrams**: Use basic relationship arrows only
- **STRICTLY FORBIDDEN**: Complex relationship syntax like ||o--{ is ABSOLUTELY PROHIBITED
- **FORBIDDEN**: Declaring new elements directly in relationship arrows (e.g., A --> rectangle "xxx" as B)

### Comment Language Restriction:
- All comments MUST be in English or Chinese ONLY
- Other languages are STRICTLY PROHIBITED

## Diagram-Specific Restrictions

### Class Diagrams:
- **FORBIDDEN**: package syntax is STRICTLY PROHIBITED
- **FORBIDDEN**: Configuring parameter requirements using * symbol is STRICTLY PROHIBITED

### Activity Diagrams:
- **FORBIDDEN**: goto syntax is STRICTLY PROHIBITED
- **FORBIDDEN**: [xxx] condition label syntax is STRICTLY PROHIBITED
- **FORBIDDEN**: elseif syntax is STRICTLY PROHIBITED
- **FORBIDDEN**: == stage name == syntax for activity phases is ABSOLUTELY NOT ALLOWED

### Mind Map Diagrams:
- **CRITICAL**: ONLY @startmindmap/@endmindmap is allowed
- **FORBIDDEN**: Using @startuml/@enduml for mind maps is STRICTLY FORBIDDEN
- **REQUIRED**: For single-sided mind maps, MUST expand to the right by default

### Sequence Diagrams:
- **REQUIRED**: MUST include Activation syntax for proper message flow visualization
- **CRITICAL**: Activation blocks MUST NOT overlap with each other
- **FORBIDDEN**: stop syntax is STRICTLY PROHIBITED
- **FORBIDDEN**: and syntax in par concurrent blocks is ABSOLUTELY NOT ALLOWED

### Component Diagrams:
- **FORBIDDEN**: [...] syntax for default rectangle nodes is STRICTLY PROHIBITED
- Use rectangle "xxx" format instead
- **FORBIDDEN**: Rectangle nesting is STRICTLY PROHIBITED

## Best Practices

### General Guidelines:
- Keep diagrams simple and focused on the main concept
- Use clear, descriptive names for all elements
- Avoid overly complex relationships
- Test diagrams with simple examples first

### Element Naming:
- Use meaningful names that describe the purpose
- Avoid special characters in element names
- Keep names concise but descriptive

### Layout Tips:
- Arrange elements logically from top to bottom or left to right
- Group related elements together
- Use consistent spacing and alignment

## Safe Syntax Examples

### Sequence Diagram:
```plantuml
@startuml
participant User
participant System
participant Database

User -> System: Login Request
activate System
System -> Database: Validate User
activate Database
Database -> System: User Valid
deactivate Database
System -> User: Login Success
deactivate System
@enduml
```

### Class Diagram:
```plantuml
@startuml
class User {
  +name: String
  +email: String
  +login()
  +logout()
}

class Order {
  +id: String
  +date: Date
  +getTotal()
}

User --> Order
@enduml
```

### Activity Diagram:
```plantuml
@startuml
start
:Input Data;
:Process Data;
if (Valid?) then (yes)
  :Save Data;
else (no)
  :Show Error;
endif
stop
@enduml
```

### Use Case Diagram:
```plantuml
@startuml
actor User
rectangle System {
  usecase Login
  usecase Browse
  usecase Purchase
}

User --> Login
User --> Browse
User --> Purchase
@enduml
```

## Error Prevention Checklist

Before finalizing any PlantUML diagram, verify:
- [ ] No !define or !theme syntax used
- [ ] No color configurations present
- [ ] No note syntax used
- [ ] @startuml is on its own line
- [ ] Only allowed relationship arrows used
- [ ] No forbidden syntax for specific diagram types
- [ ] All comments are in English or Chinese
- [ ] Elements declared before use in relationships
