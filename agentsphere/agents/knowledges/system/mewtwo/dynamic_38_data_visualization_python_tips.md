---
ID: "dynamic_38_chart_with_python"
Title: data visualization with Python preferences
EnabledIf: "agent in ['mewtwo']"
UsedWhen: visualizing data, creating charts (explicitly NOT for architecture diagrams or flowcharts) with Python libraries like matplotlib, seaborn, etc
---

## Data Visualization with Python

Follow the following rules when using the matplotlib/seaborn library.

### Pie Chart Tips

Do **not** set the `shadow` parameter to `True` when creating pie charts with `matplotlib`. While the shadow effect may add a 3D-like appearance to the chart, it can sometimes reduce the clarity of the chart, especially when trying to emphasize the precise proportions of each slice. Keeping the chart simple and clear helps maintain accuracy in data representation.

```python
import matplotlib.pyplot as plt

labels = ['A', 'B', 'C', 'D']
sizes = [30, 25, 35, 10]

# Always set shadow to False, do not use shadow effect
plt.pie(sizes, labels=labels, shadow=False)
plt.title('Pie Chart without Shadow')
plt.show()
```