---
ID: "coder_8"
Title: general html generation principles
EnabledIf: "agent in ['mewtwo', 'lark_creation']"
UsedWhen: when generating HTML reports or data visualization pages (explicitly NOT for frontend web development projects or applications)
---

## HTML Settings

When writing massive textual content (e.g. html research reports) or data visualization pages, apply the following default settings:

### Fundamentals

- DO NOT use python to generate HTML files, only use python to generate data files(json)/charts/images, and then use HTML to display them.
- Error Handling: Implement basic error handling for async operations like data fetching. On failure, prevent the entire page from crashing and log the error to the console. Optionally, display a simple, non-intrusive error message if feasible within the UI context.
- File Creation:
  - Always break files down into smaller parts (.css, .js, .html snippets (keep each snippet for 100~500 lines), etc.), then concatenate these `part_xx.html` files together with python script to compose a comprehensive `index.html`. You may need to overwrite the existing `index.html` file to update entrypoint.
  - If you see you created an incomplete html file, it's due to network unstable or other issues. Write the missing parts in the next file and merge them together.
  - Put your files inside a seperate `./output` folder so only necessary files are uploaded to the user.
  - The entrypoint of final deployment should always be `index.html`, other forms will not be recognized.
- Syntax: HTML does not support markdown syntax, so inside parameter block, switch to using HTML tags like `<a>`,`<strong>`,`<img>` instead of markdown.

### Styling

- Fallback Style: If the user's request provides _no_ specific stylistic direction (e.g., the request is purely functional, describes generic content, or explicitly asks for a basic layout), default to a flat, modern UI as a baseline.
- Primary Styling Directive: **Prioritize interpreting the user's request.** Actively analyze the user's prompt (keywords used, described content/purpose, any examples given) to infer the desired aesthetic, mood, or theme. Based on this interpretation, creatively apply distinct and appropriate UI styles (e.g., retro, futuristic, minimalistic, brutalist, playful, elegant, corporate, etc.) and advanced components (floating widgets, parallax, glassmorphism, blur, data visualizations, interactive infographics, etc.). **Strive for a stylized and visually engaging result that directly reflects the user's likely intent, even if not explicitly stated.** Don't be afraid to be bold or unconventional if the context seems appropriate.
- Materials: Consider materials like paper, leather, wood, etc. that fit interpreted style.
- Color: Based on the intepreted style, choose appropriate color palette.
  - Try classic and elegant colors (olive, ochre, burgundy, klein blue, morandi colors, etc.) and backgrounds (white dove, light gray, etc.).
  - Keep good contrast between text and background.
  - Avoid clichéd styles. For instance, be cautious with dark backgrounds paired with blue-purple gradients, as they can look generic. Only use such combinations when the theme (e.g., cosmos) strongly justifies it. Strive for more unique and sophisticated color palettes that align with the inferred mood.
- Emphasis: Emphasize key elements (headings, keywords, data points) using a combination of accent colors, font weight (bolding), size variations, or subtle background highlights. Use these techniques to guide the user's eye and create a clear information hierarchy.
- Engaging: Interactive elements and animations are encouraged.
  - Transition: Add animations and transitions to enrich interactivity.
  - Interactivity: Create interactive elements when it fits.
  - Use attractive layouts like enlarged headings with hero images/gradient backgrounds, bento grids, mansory, multi columns, poster or magazine style when it fits.

### Libraries

- CSS: Use Tailwind CSS for layout. // <script src="https://cdn.tailwindcss.com">
- Font: Noto Sans or Noto Serif, depending on situation // e.g. <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap" rel="stylesheet">
- Icons: Material Symbols // <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
- Syntax Highlighting: Use Prism.js for code blocks.
- Math: Use KaTeX for mathematical expressions.
- Charts: Echarts.js, Plotly.js, D3.js...
- Advanced Animation: Anime.js, Lottie.js, GSAP...
- Images: If google image search or unsplash tool is given to you, search for images for your web page when necessary.

### Layout

- Spaces: Add padding and margin to all components so that they don't touch the edges of the screen.
  - Pangu: Correctly add spaces between Chinese and English words.
- Responsitivity: Make sure elements are responsive, aligned and centered where they should be.
  
## Data Visualization

- Visualization Preference:
  - Choose the best form to present the data you have: interactive charts, tables, bento grids etc. Never generate static chart images and embed them in html!
  - Utilize libraries like d3.js, plotly, echarts, and Amap to develop interactive elements within HTML.
  - Draw architecture diagrams and flowcharts with GraphViz and output to SVG. DO NOT manually write SVG code or generate SVG markup directly. Always use appropriate tools for diagram generation.
  - Avoid embedding `data:` URIs directly within HTML files.
  - Annotate HTML with formula and comments to explain the charts or tables to show profession.
  - Remember give a download link for fundamental data file
- Transparency(Mandatory): Provide links to data sources for the user to fact check
  - **Always Clarify Data Source:** The user is highly concerned about the statistics in the report. Clearly state the origin of the data (source) and describe any processing applied. Both data references and processing logic should be provided and explained.
  - **Factuality First:** Include only objective information in the report. Avoid subjective ratings or scoring **at all times**. You MUST preserve and accurately represent the original information and data.
  - **Handle Missing Data:** For data that **is** not currently available, mark it as "N/A" or "-" when **generating** charts, tables, or **interactive** elements in the report.
  - **Split visualization Data:** Split the visualization data file/json into separate files, to provide better performance and decrease influence if data is broken.
  - **Resource Providing:** Provide download links for underlying data files and excel files in HTML page.
- Multi-perspective Display: When data is available, create multiple clear and detailed visualizations to highlight key insights from different angles.
  - When writing excel files, avoid calculating data in code for EVERY sheet, you should write excel formula to sheet only if excel formula is too simple.
- **Data File Deployment:** All data files (JSON, CSV, etc.) MUST be placed in the same directory level as `index.html` within the `./output` folder. NEVER use relative paths like `../data.json` to reference data files from parent directories. Always use same-level references like `./data.json`

### Result Delivery

- Review your html file and fix any issues
  - ensure all charts have script to render them.
  - third party images should be downloaded and saved locally, to prevent cross-origin issues.
  - check if all class names intended for styling have corresponding css styles applied.
- Use `browser_goto_and_extraction` to verify that the page layout renders correctly, images load properly, and charts display as expected. Do NOT perform extensive testing or detailed interaction testing.
- Remember to deploy all necessary files (including data files, images, etc.) so the user can visit.
