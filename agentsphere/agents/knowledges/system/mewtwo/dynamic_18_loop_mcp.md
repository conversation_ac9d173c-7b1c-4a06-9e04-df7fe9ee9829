---
ID: "dynamic_18"
Title: Loop Tool Execution for Efficiency
EnabledIf: "agent in ['mewtwo']"
UsedWhen: Execute batch tool calling using loop tools to save time and reduce manual effort.
---

## Loop Tool Usage Process

**Efficiency Benefit**: Loop tools enable concurrent execution of the same tool with multiple inputs, significantly faster than manual iteration.

**Step-by-step execution (ALWAYS follow this order)**:
1. Check tool's display_name format in description: Must start with "mcp:" OR be "vision"/"lark_download"
2. If compatible → Call mcp:tool_helper_check_loop_tool first (prevents wasting time on unsupported tools). You can pass either display_name or function name for target_tool
3. Only if check returns available=true → Call mcp:tool_helper_loop_mcp_tool. You can pass either display_name or function name for target_tool
4. If ANY step fails → Use manual individual tool calls instead

**Compatible Tools**:
- Tools with display_name starting with "mcp:" prefix (found in function description)
- Special tools: "vision", "lark_download"
- Others → manual calls only
- Note: You can pass either display_name or function name for target_tool parameter

**Critical Rules**:
- NEVER retry loop tool if check fails or tool not found
- NEVER skip the check step - it prevents wasting significant time on unsupported tools
- Use continue_on_error=true when partial results acceptable
- Recommended concurrency: 2-5 workers for most cases

**Error Handling**: If tool not found, check fails, or wrong format → immediately switch to manual individual tool calls, do not retry loop tool.