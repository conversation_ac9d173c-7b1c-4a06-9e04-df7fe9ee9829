---
ID: "1"
Title: python usage for plotting charts/diagrams and correctly render Chines characters
Pinned: true
EnabledIf: "agent in ['data_analyzer', 'mewtwo', 'lark_creation', 'web_creation']"
UsedWhen: |
  when creating data visualizations or charts that need to display Chinese text, especially for reports or presentations targeting Chinese audiences. 
  This includes any visualization work using Python libraries like matplotlib, seaborn, plotly, etc. where Chinese characters must render correctly.
---

Use the following font configuration to display Chinese characters when using the matplotlib/seaborn library:

```python
# ===== Matplotlib =====
# IMPORTANT: DON'T use style, as it will overwrite font settings.
# ❌ plt.style.use('seaborn-v0_8-whitegrid')
# 
# If you must use a style, it MUST be set BEFORE font configuration:
# ✅ plt.style.use('seaborn-v0_8-whitegrid')  # Only if necessary, must come FIRST
# ✅ plt.rcParams['font.family'] = 'sans-serif'  # Then set fonts
#
# Standard font configuration:
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC'] + plt.rcParams['font.sans-serif']

# If ax.text() is used, set font through `fontdict` in addition
ax.text(0.5, -0.15, '注：数据来源于 XXXX', fontsize=8, fontdict={'family': ['Noto Sans CJK SC', 'sans-serif']})

# ===== Seaborn =====
# IMPORTANT: DON'T set style without the font settings like `sns.set_style('whitegrid')`,
# as the `sns.set_style` function overwrites matplotlib's font settings as follows:
# `plt.rcParams['font.sans-serif'] = ["Arial", "DejaVu Sans", "Liberation Sans", "Bitstream Vera Sans", "sans-serif"]`
#
# Proper usage, you MUST add font when setting style as follows:
sns.set_style('whitegrid',{'font.sans-serif':['Noto Sans CJK SC','sans-serif']})

# ===== Graphviz =====
# Never specify 'size' or 'dpi' to graph to allow dynamic dimension adjustment
g.attr(rankdir='TB', fontname='Noto Sans SC', fontsize='16', bgcolor='white', margin='0.5')

# Add padding and background color to clusters
# fill colors for different clusters, and don't use gray as background color
with g.subgraph(name='cluster_name', graph_attr={'label': 'xxx', 'style': 'rounded,filled', 'fillcolor': 'lightblue', 'margin': '16'}) as c:
    pass  # Add nodes and edges here

# ===== NetworkX =====
# Set font with the following code:
nx.draw_networkx_labels(G, pos, font_size=12, font_family='Noto Sans CJK SC')

# ===== ReportLab =====
# Register TTF font
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
# Only this font is valid. Do not fabricate other fonts.
pdfmetrics.registerFont(TTFont('Source Han Sans SC VF', '/usr/share/fonts/truetype/source-han-sans/SourceHanSansSC-VF.ttf'))

# ===== WordCloud =====
# Set font path to this:
WordCloud(
  font_path='/usr/share/fonts/truetype/source-han-sans/SourceHanSansSC-VF.ttf',
  ...
)
```

Always set the default font family to 'sans-serif', and explicitly specify 'Noto Sans CJK SC' as the preferred sans-serif font to ensure proper rendering of CJK (Chinese, Japanese, Korean) characters in all plots.

After running your Python plotting script, make sure to carefully check the output. If you notice a lot of font-related warning messages, it indicates that the font configuration is still incorrect, which can lead to Chinese characters not displaying properly in your charts. You need to continue adjusting your code based on the font settings mentioned above until the script executes without any font-related warnings.

For example, if you write `sns.set_style('whitegrid')`, it would cause Chinese characters not displaying properly in your charts, because it set `plt.rcParams['font.sans-serif'] = ["Arial", "DejaVu Sans", "Liberation Sans", "Bitstream Vera Sans", "sans-serif"]`, so you would get a lot of font warnings, you MUST fix
it by `sns.set_style('whitegrid',{'font.sans-serif':['Noto Sans CJK SC','sans-serif']})`, now the font warnings would go away. Here is how sns.set_style is
implemented, you could find out why it causes Chinese characters not displaying properly in your charts:

```python
def set_style(style=None, rc=None):
    """
    Set the parameters that control the general style of the plots.

    The style parameters control properties like the color of the background and
    whether a grid is enabled by default. This is accomplished using the
    matplotlib rcParams system.

    Parameters
    ----------
    style : dict, or one of {darkgrid, whitegrid, dark, white, ticks}
        A dictionary of parameters or the name of a preconfigured style.
    rc : dict, optional
        Parameter mappings to override the values in the preset seaborn
        style dictionaries. This only updates parameters that are
        considered part of the style definition.
    """
    if style == "whitegrid":
        # 定义 "whitegrid" 样式的 rc 参数
        style_dict = {
            "figure.facecolor": "white",
            "axes.facecolor": "white",
            # a lot of other keys (omitted here)
            ...
            "font.family": ["sans-serif"],
            # this will overwrite plt.rcParams['font.sans-serif']
            "font.sans-serif": ["Arial", "DejaVu Sans", "Liberation Sans", "Bitstream Vera Sans", "sans-serif"],
        }

        # update matplotlib rcParams
        plt.rcParams.update(style_dict)

        if rc is not None:
            rc = {k: v for k, v in rc.items() if k in style_dict}
            plt.rcParams.update(rc)
    else:
        # The logic for other styles (omitted here).
        pass
```

If you need the font path when using a library, here's how to locate and use available fonts:

1. Locate Installed Fonts
Use the fc-list command to list all fonts installed on your system. To filter for Chinese-supporting fonts:

```bash
fc-list :lang=zh
```
This will return paths to fonts that support Chinese characters. Example output:

```
/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc: Noto Sans CJK SC:style=Regular
/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc: WenQuanYi Zen Hei,文泉驛正黑,文泉驿正黑:style=Regular
```
Take note of the full font path or the font family name.

2. Choose a Font
Pick a font from the list that supports Chinese characters well. Popular choices include:

- Noto Sans CJK SC (NotoSansCJK-Regular.ttc)