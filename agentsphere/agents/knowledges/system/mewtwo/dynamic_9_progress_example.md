---
# This knowledge significantly affects the performance of newbie with Gemini model series
# Providing a very detailed and well-structured example can help newbie to plan the task better and (also create content rich results)
# Use this knowledge whenever possible
ID: "dynamic_9"
Title: progress example - general
EnabledIf: "agent in ['mewtwo'] and variant in ['newbie']"
UsedWhen: it should always be used for better planning.
---

**[Core Instruction: The Planning Mandate]**

Create a detailed, step-by-step execution plan via the `progress_think` tool. This is a mandatory first step.

The purpose of this plan is to:
1.  **Deconstruct the Request:** Ensure you have fully understood all requirements, inputs, and desired outputs.
2.  **Outline the Strategy:** Define the tools, libraries, and sequence of actions you will take.
3.  **Define Deliverables:** Specify all intermediate and final files or artifacts you will produce.

**[How to Structure Your Plan]**

Your plan must be broken down into logical phases. Use the following phased structure as a template, adapting it to the specific needs of the task.

*   **Phase 1: Deconstruction & Conceptualization**
    *   **Objective:** State the primary goal of the task in one sentence.
    *   **Inputs:** List all provided input files, data sources, or APIs.
    *   **Deliverables:** List all final files and artifacts to be produced (e.g., `report.html`, `data.js`, a deployment URL).
    *   **High-Level Outline:** Create a conceptual outline of the final output. For a report, this would be the sections (e.g., Introduction, Analysis A, Analysis B, Conclusion). For an application, this would be the key components or views.

*   **Phase 2: Technical Specification & Data Strategy**
    *   **Tech Stack:** Specify the languages, libraries, and frameworks you will use (e.g., Python, pandas, JavaScript, ECharts, React, Tailwind CSS).
    *   **Data Flow:** Describe how you will process the input data. Detail the steps from reading the raw data to transforming it into the format needed for the final output.
    *   **Intermediate Artifacts:** Define any intermediate data structures or files you will create (e.g., a `data.js` file containing JSON objects, a temporary SQLite database).

*   **Phase 3: Implementation Plan**
    *   This is the core of your plan. Create a detailed, hierarchical checklist of the implementation steps.
    *   Break down the work by file and component.
    *   **Example Sub-tasks:**
        *   Create `data_processing.py` to handle data transformation.
        *   Use `aime_create_react_app {app_name}` to create the React TypeScript template.
        *   Override default App.css restrictions for full-width layout.
        *   Modify `src/App.tsx` to implement all components and functionality.
        *   Style components using Tailwind CSS classes and shadcn/ui components.

*   **Phase 4: Finalization & Verification**
    *   **Content Generation:** Plan for writing any necessary text, insights, or summaries.
    *   **Quality Assurance:** Outline steps for final review, such as checking for broken links, ensuring data accuracy, and proofreading text.
    *   **Metadata:** Plan to include any required metadata, like a "Generated On" timestamp or source attribution.

*   **Phase 5: Packaging & Deployment**
    *   **File List:** List the exact files that will be included in the final deliverable.
    *   **Execution Instructions:** If applicable, provide instructions on how to run the code.
    *   **Deployment:** Specify the tool or command you will use to deploy the final output (e.g., `deploy` tool).

**[For Your Reference: Example of a High-Quality Plan]**

*This is an example of a plan that successfully follows the structure above. Use it to understand the expected level of detail.*

- [ ] **Phase 1: Conceptual Outline Creation**
  - [ ] Define report title and overall structure.
  - [ ] Identify sections for different analyses (Commit Activity, Contributor Analysis, Top Contributor Activity, Codebase Evolution).
  - [ ] Specify chart types for each data visualization.
  - [ ] Plan for insights and data source links.
- [ ] **Phase 2: Data Preparation (Python Script)**
  - [ ] Create a Python script (`prepare_data.py`).
  - [ ] Read all 9 input CSV files using pandas.
  - [ ] Transform data into JSON structures suitable for ECharts.
    - `commit_activity_daily.csv` -> `dailyCommitData`
    - `commit_activity_weekly.csv` -> `weeklyCommitData`
    - `commit_activity_monthly.csv` -> `monthlyCommitData`
    - `contributor_summary.csv` -> `contributorSummaryData` (for bar and pie charts)
    - `top_contributors_activity_weekly.csv` -> `topContributorsWeeklyData`
    - `top_contributors_activity_monthly.csv` -> `topContributorsMonthlyData`
    - `lines_changed_daily.csv` -> `dailyLinesChangedData`
    - `lines_changed_weekly.csv` -> `weeklyLinesChangedData`
    - `lines_changed_monthly.csv` -> `monthlyLinesChangedData`
  - [ ] Print key insights from the data.
  - [ ] Save the JSON structures into a JavaScript file (`report_data.js`) for import into React app.
- [ ] **Phase 3: React Application Development (TypeScript & Components)**
  - [ ] Use `aime_create_react_app {app_name}` to create Vite + React TypeScript app with Tailwind CSS and shadcn/ui pre-installed.
  - [ ] Override default App.css restrictions for full-width layout.
  - [ ] Import and setup `report_data.js` as data source in the project.
  - [ ] Implement React component structure in TypeScript (.tsx files).
    - [ ] Create `Header.tsx` component
    - [ ] Create `CommitActivity.tsx` component
      - [ ] Chart 1.1: Daily Commits
      - [ ] Chart 1.2: Weekly Commits
      - [ ] Chart 1.3: Monthly Commits
    - [ ] Create `ContributorAnalysis.tsx` component
      - [ ] Chart 2.1: Contributor Commits Bar Chart
      - [ ] Chart 2.2: Contributor Commits Pie Chart
    - [ ] Create `TopContributorActivity.tsx` component
      - [ ] Chart 3.1: Top Contributors Weekly Commits
      - [ ] Chart 3.2: Top Contributors Monthly Commits
    - [ ] Create `CodebaseEvolution.tsx` component
      - [ ] Chart 4.1: Daily Lines Changed
      - [ ] Chart 4.2: Weekly Lines Changed
      - [ ] Chart 4.3: Monthly Lines Changed
    - [ ] Create `Footer.tsx` component
    - [ ] Integrate all components in `App.tsx`
  - [ ] Use Tailwind CSS for styling (avoid arbitrary values), lucide for icons, and shadcn/ui prebuilt components.
  - [ ] Import images using `import` statements for proper build inclusion.
- [ ] **Phase 4: Content - Insights and Final Touches**
  - [ ] Write concise textual insights for each chart/section based on the visualized data.
  - [ ] Add download links for each original CSV file in the footer.
  - [ ] Add a "Generated on" date.
  - [ ] Final review of the report for clarity, accuracy, and aesthetics.
- [ ] **Phase 5: Testing and Validation**
  - [ ] Test the frontend locally by running `pnpm run dev` and navigating to http://localhost:5173.
  - [ ] Verify functionality and UI design, iterate until the whole app works as expected.
  - [ ] Test all interactive elements: charts, navigation, and responsive design.
  - [ ] Check console for errors and validate assets & layout.
  - [ ] When issues are discovered, fix them and re-test only the affected components.
- [ ] **Phase 6: Build and Deployment**
  - [ ] Build the app using `pnpm run build` to create the dist folder.
  - [ ] Use the `deploy` tool to deploy the `dist` folder.
  - [ ] Provide the deployment URL.
