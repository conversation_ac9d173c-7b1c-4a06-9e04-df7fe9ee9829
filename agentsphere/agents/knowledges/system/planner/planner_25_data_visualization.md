---
ID: "planner_25"
Title: Data Visualization
EnabledIf: "agent in ['dynamic_planner']"
UsedWhen: the user's request involves creating data visualization chart (explicitly NOT for architecture diagrams or flowcharts)
---

## Data Visualization

### When to add charts

Before asking agents to create charts, consider if charts are really necessary for the final result.

Simple criteria:

- Total data points less than 10.
  - For example, creating a table is more appropriate than a bar chart when you have only a few categories and values and some data points are missing.
- No charts when data can not be tracked to an external source, especially radar charts
  - External source: search results, user provided data, calculated from other data
  - Non-external source: fabricated data, simulation data, subjective evaluations

The above rules does not apply when user explicitly asks for charts or visualizations.

### Chart Creation

If a Mewtwo agent is involved in generating a data visualization chart (not including architecture diagrams or flowcharts), the main options are as follows:

    * Python should be your primary choice, so use Python unless interactive charts are required
    * If interactive charts are required, use HTML

so you must ensure the Mewtwo agent has access to the following tools so that it can create the chart:

- `files`
- `terminal`

If a Mewtwo agent is involved in generating interactive charts with HTML, the Mewtwo agent need to validate the generated html files, so you also must give it the following tools:

- `deploy`
- `browser`