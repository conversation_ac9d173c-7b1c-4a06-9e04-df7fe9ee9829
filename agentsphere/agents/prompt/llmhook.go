package prompt

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"github.com/samber/lo"
)

type LLMCallRequestHook func(messages []*framework.ChatMessage) []*framework.ChatMessage
type LLMCallResultHook func(result *framework.LLMResult) *framework.LLMResult

type LLMCallHook struct {
	RequestHooks []LLMCallRequestHook
	ResultHooks  []LLMCallResultHook
}

func (h *LLMCallHook) OnRequest(messages []*framework.ChatMessage) []*framework.ChatMessage {
	for _, hook := range h.RequestHooks {
		messages = hook(messages)
	}
	return messages
}

func (h *LLMCallHook) OnResponse(result *framework.LLMResult) *framework.LLMResult {
	for _, hook := range h.ResultHooks {
		result = hook(result)
	}
	return result
}

type SimpleFilterOption struct {
	// 关键字替换，按照顺序替换。
	// 例如 [["business", "b"], ["business data", "bd"]] -> "business" -> "[b-11514ffb]"
	// 最好替换后的字符串也有一定意义，让模型可以理解。
	ReplaceKeywords [][2]string
}

func SimpleFilterPromptMessagesHook(opt SimpleFilterOption) (LLMCallRequestHook, LLMCallResultHook) {
	hash := func(k, v string) string {
		h := sha256.Sum256(([]byte("salt-asdfasdfa" + k)))
		sum := hex.EncodeToString(h[:])
		return fmt.Sprintf("[%s-%s]", v, sum[:4]+sum[len(sum)-4:])
	}
	replaceList := make([]string, 0, len(opt.ReplaceKeywords)*2)
	reverseList := make([]string, 0, len(opt.ReplaceKeywords)*2)
	for _, k := range opt.ReplaceKeywords {
		hashed := hash(k[0], k[1])
		replaceList = append(replaceList, k[0], hashed)
		reverseList = append(reverseList, hashed, k[0])
		// 模型有时候可能会将 '[' 或者 ']' 转义，这里做一下兜底
		escaped := strings.ReplaceAll(hashed, "[", "\\[")
		escaped = strings.ReplaceAll(escaped, "]", "\\]")
		reverseList = append(reverseList, escaped, k[0])
	}
	replacer := strings.NewReplacer(replaceList...)
	reverseReplacer := strings.NewReplacer(reverseList...)
	reqHook := func(messages []*framework.ChatMessage) []*framework.ChatMessage {
		filteredMessages := make([]*framework.ChatMessage, 0)
		for _, message := range messages {
			newMessage := &framework.ChatMessage{
				Role:           message.Role,
				Content:        replacer.Replace(message.Content),
				ContentParts:   nil,
				ToolCalls:      message.ToolCalls,
				ToolCallResult: message.ToolCallResult,
				ToolCallID:     message.ToolCallID,
			}
			for _, part := range message.ContentParts {
				newPart := &framework.LLMChatMessageContentPart{
					Text:     part.Text,
					ImageURL: part.ImageURL,
				}
				if newPart.Text != nil {
					newPart.Text = lo.ToPtr(replacer.Replace(*newPart.Text))
				}
				newMessage.ContentParts = append(newMessage.ContentParts, newPart)
			}
			filteredMessages = append(filteredMessages, newMessage)
		}
		return filteredMessages
	}
	resHook := func(result *framework.LLMResult) *framework.LLMResult {
		if result == nil {
			return nil
		}
		result.Content = reverseReplacer.Replace(result.Content)
		result.ReasoningContent = reverseReplacer.Replace(result.ReasoningContent)
		for _, part := range result.ToolCalls {
			part.Name = reverseReplacer.Replace(part.Name)
			// FIXME(cyx): unmask the tool call arguments.
			// part.Arguments = reverseReplacer.Replace(part.Arguments)
		}

		return result
	}
	return reqHook, resHook
}

type URLCandidates struct {
	DeployURL    []string
	LarkDocURL   []string
	LarkSheetURL []string
}

func (c *URLCandidates) Merge(d URLCandidates) {
	c.DeployURL = append(c.DeployURL, d.DeployURL...)
	c.LarkDocURL = append(c.LarkDocURL, d.LarkDocURL...)
	c.LarkSheetURL = append(c.LarkSheetURL, d.LarkSheetURL...)
}

func (c *URLCandidates) Uniq() {
	c.DeployURL = lo.Uniq(c.DeployURL)
	c.LarkDocURL = lo.Uniq(c.LarkDocURL)
	c.LarkSheetURL = lo.Uniq(c.LarkSheetURL)
}

func (c *URLCandidates) GetReplacerWith(d URLCandidates) *strings.Replacer {
	replaceList := make([]string, 0)
	replaceList = append(replaceList, validate(c.DeployURL, d.DeployURL)...)
	fmt.Println("replaceList", replaceList, "larkddddd", c.LarkDocURL, "to be replace", d.LarkDocURL)
	replaceList = append(replaceList, validate(c.LarkDocURL, d.LarkDocURL)...)
	fmt.Println("done")
	replaceList = append(replaceList, validate(c.LarkSheetURL, d.LarkSheetURL)...)

	return strings.NewReplacer(replaceList...)
}

func extractURLs(content string) (urlList URLCandidates) {
	urlList = URLCandidates{
		DeployURL:    util.DeployReg.FindAllString(content, -1),
		LarkDocURL:   util.LarkDocReg.FindAllString(content, -1),
		LarkSheetURL: util.LarkSheetReg.FindAllString(content, -1),
	}
	fmt.Println("urlList", urlList)
	return urlList
}

// URLValidationHook 用于验证模型输出的 URL 是否与 input 中一致，如果不一致，则尝试将其替换为合法的 URL。
func URLValidationHook() (LLMCallRequestHook, LLMCallResultHook) {
	allURLs := URLCandidates{}
	reqHook := func(messages []*framework.ChatMessage) []*framework.ChatMessage {
		for _, message := range messages {
			allURLs.Merge(extractURLs(message.Content))
			for _, part := range message.ContentParts {
				if part.Text != nil {
					allURLs.Merge(extractURLs(*part.Text))
				}
			}
		}
		allURLs.Uniq()
		return messages
	}

	resHook := func(result *framework.LLMResult) *framework.LLMResult {
		if result == nil {
			return nil
		}
		urls := extractURLs(result.Content)
		if len(result.ToolCalls) > 0 {
			lo.ForEach(result.ToolCalls, func(toolCall framework.LLMToolFunction, _ int) {
				urls.Merge(extractURLs(toolCall.RawParameters))
			})
		}
		replacer := allURLs.GetReplacerWith(urls)

		result.Content = replacer.Replace(result.Content)
		if len(result.ToolCalls) > 0 {
			result.ToolCalls = lo.Map(result.ToolCalls, func(toolCall framework.LLMToolFunction, _ int) framework.LLMToolFunction {
				toolCall.Name = replacer.Replace(toolCall.Name)
				toolCall.RawParameters = replacer.Replace(toolCall.RawParameters)
				toolCall.Parameters = conv.DecodeJSON[iris.Parameters](toolCall.RawParameters)
				return toolCall
			})
		}

		return result
	}
	return reqHook, resHook
}

func LCS(s1, s2 string) int {
	m, n := len(s1), len(s2)
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if s1[i-1] == s2[j-1] {
				dp[i][j] = dp[i-1][j-1] + 1
			} else {
				dp[i][j] = max(dp[i-1][j], dp[i][j-1])
			}
		}
	}
	return dp[m][n]
}

func validate(oldURLs []string, newURLs []string) []string {
	replaceList := make([]string, 0)
	for _, newURL := range newURLs {
		maxLCS := 0
		optURL := ""
		for _, oldURL := range oldURLs {
			if lcs := LCS(oldURL, newURL); lcs > maxLCS {
				maxLCS = lcs
				optURL = oldURL
			}
		}
		if optURL != newURL && maxLCS >= max(len(newURL), len(optURL))-3 {
			replaceList = append(replaceList, newURL, optURL)
		}
	}
	return replaceList
}
