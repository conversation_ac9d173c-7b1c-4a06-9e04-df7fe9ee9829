package prompt_test

import (
	_ "embed"
	"testing"

	"github.com/stretchr/testify/require"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

func TestLLMHook(t *testing.T) {
	tests := []struct {
		name    string
		input   []*framework.ChatMessage
		output  *framework.LLMResult
		want    *framework.LLMResult
		wantErr error
	}{
		{
			name: "miss some thing",
			input: []*framework.ChatMessage{
				{
					Content: "### 附件\n- [安卓开发相关文章汇总 (初步结果)](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
				},
			},
			output: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJ09d)",
			},
			want: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
			},
			wantErr: nil,
		},
		{
			name: "fake url",
			input: []*framework.ChatMessage{
				{
					Content: "### 附件\n- [安卓开发相关文章汇总 (初步结果)](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
				},
			},
			output: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/abcdefg)",
			},
			want: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/abcdefg)",
			},
			wantErr: nil,
		},
		{
			name: "modify some thing",
			input: []*framework.ChatMessage{
				{
					Content: "### 附件\n- [安卓开发相关文章汇总 (初步结果)](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
				},
			},
			output: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VG23JMJxzrEJcn09d)",
			},
			want: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
			},
			wantErr: nil,
		},
		{
			name: "add some thing",
			input: []*framework.ChatMessage{
				{
					Content: "### 附件\n- [安卓开发相关文章汇总 (初步结果)](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
				},
			},
			output: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofddJMJxzrEJcn09dd)",
			},
			want: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
			},
			wantErr: nil,
		},
		{
			name: "difference too long",
			input: []*framework.ChatMessage{
				{
					Content: "### 附件\n- [安卓开发相关文章汇总 (初步结果)](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
				},
			},
			output: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcnabcdefg)",
			},
			want: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcnabcdefg)",
			},
			wantErr: nil,
		},
		{
			name: "no url",
			input: []*framework.ChatMessage{
				{
					Content: "### 附件\n- [安卓开发相关文章汇总 (初步结果)]",
				},
			},
			output: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcnabcdefg)",
			},
			want: &framework.LLMResult{
				Content: "[安卓开发相关文章汇总](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcnabcdefg)",
			},
			wantErr: nil,
		},
		{
			name: "tool call",
			input: []*framework.ChatMessage{
				{
					Content: "### 附件\n- [安卓开发相关文章汇总 (初步结果)](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
				},
			},
			output: &framework.LLMResult{
				ToolCalls: []framework.LLMToolFunction{
					{
						ID:            "1",
						Name:          "create_file",
						RawParameters: `{"path": "test.txt", "content": "https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJ09d"}`,
						Parameters:    iris.Parameters{"path": "test.txt", "content": "https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJ09d"},
					},
				},
			},
			want: &framework.LLMResult{
				ToolCalls: []framework.LLMToolFunction{
					{
						ID:            "1",
						Name:          "create_file",
						RawParameters: `{"path": "test.txt", "content": "https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d"}`,
						Parameters:    iris.Parameters{"path": "test.txt", "content": "https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d"},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "incompletetool call",
			input: []*framework.ChatMessage{
				{
					Content: "### 附件\n- [安卓开发相关文章汇总 (初步结果)](https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d)",
				},
			},
			output: &framework.LLMResult{
				ToolCalls: []framework.LLMToolFunction{
					{
						ID:            "1",
						Name:          "create_file",
						RawParameters: `{"path": "test.txt", "content": "https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJ09d"`,
						Parameters:    iris.Parameters{},
					},
				},
			},
			want: &framework.LLMResult{
				ToolCalls: []framework.LLMToolFunction{
					{
						ID:            "1",
						Name:          "create_file",
						RawParameters: `{"path": "test.txt", "content": "https://bytedance.larkoffice.com/docx/RP42dZ6VGofJMJxzrEJcn09d"`,
						Parameters:    iris.Parameters{},
					},
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reqhook, reshook := prompt.URLValidationHook()
			_ = reqhook(tt.input)
			got := reshook(tt.output)
			require.Equal(t, tt.want, got)
		})
	}
}
