package prompt

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"html"
	"io"
	"regexp"
	"strconv"
	"strings"

	"github.com/hashicorp/go-multierror"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	projectgen_entity "code.byted.org/devgpt/kiwis/agentsphere/agents/projectgen/entity"
)

type Parser struct {
	Validate func(thought *iris.Thought) error
	ToolTag  string
}

func (p *Parser) ParsePlan(content string) (*iris.Thought, error) {
	thought := &iris.Thought{
		Content:    content,
		Rationale:  "",
		Tool:       "",
		Parameters: map[string]any{},
		Data:       map[string]any{},
	}
	plan := projectgen_entity.Plan{}
	topTags, err := ParseTopTagsV2(content)
	if err != nil {
		return thought, iris.ThoughtParseErrorf("Failed to parse your output, please provide valid plan in XML format described above: %v", err)
	}
	for _, tag := range topTags {
		switch strings.TrimSpace(tag.XMLName.Local) {
		case "step":
			innerTags, err := ParseTopTagsV2(tag.Content)
			if err != nil {
				return thought, iris.ThoughtParseErrorf("Failed to parse your output, please provide valid plan in XML format described above: %v", err)
			}
			planStep := projectgen_entity.PlanStep{}
			for _, innerTag := range innerTags {
				switch strings.TrimSpace(innerTag.XMLName.Local) {
				case "focus":
					planStep.Focus = innerTag.Content
				case "abstract":
					planStep.Abstract = innerTag.Content
				case "implementation":
					planStep.Implementation = innerTag.Content
				}
			}
			plan.Steps = append(plan.Steps, planStep)
		}
	}
	if len(plan.Steps) == 0 {
		return thought, iris.ThoughtParseErrorf("No plan found in the output. Please provide a plan in stap tag.\n" +
			"Example: <step>This is one step of a plan.</step>")
	}
	thought.Data["plan"] = plan
	return thought, nil
}

func (p *Parser) Parse(content string) (*iris.Thought, error) {
	if p.ToolTag == "" {
		p.ToolTag = "tool"
	}
	thought := &iris.Thought{
		Content:    content,
		Rationale:  "",
		Tool:       "",
		Parameters: map[string]any{},
		Data:       map[string]any{},
	}

	topTags, err := ParseTopTagsV2(content)
	// we will return error later after setting the thought

	hasTool := false
	for _, tag := range topTags {
		switch strings.TrimSpace(tag.XMLName.Local) {
		case "rationale":
			thought.Rationale = tag.Content
		case p.ToolTag:
			if len(tag.Attr) == 0 {
				return thought, iris.ThoughtParseError("No tool name found in the output. Your response should contain one rationale and one tool call.")
			}
			if hasTool {
				// if multiple tool calls are found, only the first one is used.
				// or `conclusion` may be the last tool call and get executed
				break
			}
			thought.Tool = tag.Attr[0].Value // Only one attr currently.
			// Parsing parameters.
			paramTags, err := ParseTopTagsV2(tag.Content)
			if err != nil {
				return thought, iris.ThoughtParseErrorf("Error parsing parameters: %v.\nPlease provide valid tool parameters in XML format describe above.", err)
			}
			for _, paramTag := range paramTags {
				paramName := paramTag.XMLName.Local
				paramValue := strings.Trim(paramTag.Content, "\n")
				// FIXME(cyx): parsing into concrete types(int, string, float, etc.) according to the schema.
				if intValue, err := strconv.Atoi(strings.TrimSpace(paramValue)); err == nil {
					thought.Parameters[paramName] = intValue
				} else if floatValue, err := strconv.ParseFloat(strings.TrimSpace(paramValue), 64); err == nil {
					thought.Parameters[paramName] = floatValue
				} else if paramValue == "true" || paramValue == "false" {
					thought.Parameters[paramName] = paramValue == "true"
				} else {
					// If it's not an integer or a float, it's a string.
					thought.Parameters[paramName] = paramValue
				}
			}
			hasTool = true
		default:
			thought.Data[tag.XMLName.Local] = tag.Content // Other undefined tags.
		}
	}

	if p.Validate != nil {
		if err := p.Validate(thought); err != nil {
			return thought, iris.ThoughtParseError(err.Error())
		}
	} else {
		if len(thought.Rationale) == 0 {
			return thought, iris.ThoughtParseError("No rationale found in the output. Your response should contain one rationale and one tool call.")
		}

		if len(thought.Tool) == 0 && thought.Data["plan"] == nil && thought.Data["result"] == nil {
			return thought, iris.ThoughtParseError("No tool name found in the output. Your response should contain one rationale and one tool call.")
		}
	}

	if err != nil {
		return thought, iris.ThoughtParseErrorf("Failed to parse your output, please provide only one valid rationale and one tool usage in XML format described above: %v", err)
	}
	return thought, nil
}

type Tag struct {
	XMLName xml.Name
	Attr    []xml.Attr `xml:",any,attr"`
	Content string     `xml:",innerxml"`
}

func ParseTopTags(data string) ([]Tag, error) {
	data = tryEscapeXMLChars(data)
	decoder := xml.NewDecoder(strings.NewReader(data))
	decoder.Strict = false
	inTopTag := false
	curTag := Tag{}
	var tags []Tag
	for {
		token, err := decoder.RawToken()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}

		switch t := token.(type) {
		case xml.StartElement:
			if !inTopTag {
				inTopTag = true
				curTag.XMLName = t.Name
				curTag.Attr = t.Attr
				curTag.Content = ""
			} else {
				curTag.Content += tokenString(t)
			}
		case xml.EndElement:
			if inTopTag && t.Name.Local == curTag.XMLName.Local {
				inTopTag = false
				tags = append(tags, curTag)
				curTag = Tag{}
			} else {
				curTag.Content += tokenString(t)
			}
		default:
			if inTopTag {
				curTag.Content += tokenString(t)
			}
		}
	}
	return tags, nil
}

// ParseTopTagsV2 is a more efficient way to parse top tags without XML library
// so that we can parse arbitrary content without escaping sequence created by llms
// Limitation:
// 1. top tag name must not present in its child tag, e.g. <xxx><xxx></xxx></xxx> is not supported
func ParseTopTagsV2(data string) (result []Tag, err error) {
	// 1. find the first <xxx k="v" ...> tag, create a corresponding tag
	// 2. extract attributes to tag
	// 3. continue to parse the rest of the data
	// 4. if </xxx> is found, add the tag to the list
	// 5. return the list
	var errs = &multierror.Error{}
	var i int
	for i < len(data) {
		// Find opening tag
		startIdx := strings.Index(data[i:], "<")
		if startIdx == -1 {
			break // No more tags
		}
		startIdx += i

		// Skip comments, processing instructions, etc.
		if startIdx+4 <= len(data) && data[startIdx:startIdx+4] == "<!--" {
			endComment := strings.Index(data[startIdx:], "-->")
			if endComment == -1 {
				i = len(data)
				break
			}
			i = startIdx + endComment + 3
			continue
		}

		// Check if it's a closing tag
		if startIdx+1 < len(data) && data[startIdx+1] == '/' {
			endIdx := strings.Index(data[startIdx:], ">")
			if endIdx == -1 {
				return result, fmt.Errorf("unclosed tag at position %d", startIdx)
			}
			i = startIdx + endIdx + 1
			continue
		}

		// Find end of opening tag
		endIdx := strings.Index(data[startIdx:], ">")
		if endIdx == -1 {
			return result, fmt.Errorf("unclosed tag at position %d", startIdx)
		}
		endIdx += startIdx

		// Extract tag name and attributes
		tagContent := data[startIdx+1 : endIdx]
		parts := strings.Fields(tagContent)
		if len(parts) == 0 {
			i = endIdx + 1
			continue
		}

		tagName := parts[0]
		var attrs []xml.Attr

		// Parse attributes
		restAttrStr := strings.TrimPrefix(tagContent, tagName)
		if len(restAttrStr) > 0 {
			attrs = parseAttributes(restAttrStr)
		}

		// Find closing tag
		closingTag := fmt.Sprintf("</%s>", tagName)
		closingIdx := strings.Index(data[endIdx+1:], closingTag)
		var content string
		if closingIdx == -1 {
			errs = multierror.Append(errs, fmt.Errorf("missing closing tag for <%s> at position %d", tagName, startIdx))
			content = data[endIdx+1:]
			i = len(data)
		} else {
			// Extract content between tags
			content = data[endIdx+1 : endIdx+1+closingIdx]
			i = endIdx + 1 + closingIdx + len(closingTag)
		}

		// models may return escaped xml entities, so we need to unescape it
		// however xml does not have unescape function, so we use html unescape
		// this reg avoid &region=cn being unescaped into ®ion=cn
		re := regexp.MustCompile(`&(amp|lt|gt|quot|apos|reg|copy|trade|nbsp|mdash|ndash);`)
		if re.MatchString(content) {
			content = html.UnescapeString(content)
		}
		// Create and append tag
		tag := Tag{
			XMLName: xml.Name{Local: tagName},
			Attr:    attrs,
			Content: content,
		}
		result = append(result, tag)
	}

	return result, errs.ErrorOrNil()
}

// Helper function to parse attributes
func parseAttributes(attrStr string) []xml.Attr {
	var attrs []xml.Attr
	attrStr = strings.TrimSpace(attrStr)

	for len(attrStr) > 0 {
		// Find attribute name
		eqIdx := strings.Index(attrStr, "=")
		if eqIdx == -1 {
			break
		}

		attrName := strings.TrimSpace(attrStr[:eqIdx])
		attrStr = attrStr[eqIdx+1:]

		// Find attribute value
		if len(attrStr) == 0 {
			break
		}

		var valueEnd int
		var attrValue string

		if attrStr[0] == '"' || attrStr[0] == '\'' {
			quote := attrStr[0]
			valueStart := 1
			valueEnd = strings.Index(attrStr[valueStart:], string(quote))

			if valueEnd == -1 {
				// Unclosed quote, just use the rest of the string
				attrValue = attrStr[valueStart:]
				attrStr = ""
			} else {
				valueEnd += valueStart
				attrValue = attrStr[valueStart:valueEnd]

				if valueEnd+1 < len(attrStr) {
					attrStr = attrStr[valueEnd+1:]
				} else {
					attrStr = ""
				}
			}
		} else {
			// Unquoted value
			valueEnd = strings.IndexAny(attrStr, " \t\r\n")
			if valueEnd == -1 {
				attrValue = attrStr
				attrStr = ""
			} else {
				attrValue = attrStr[:valueEnd]
				attrStr = attrStr[valueEnd:]
			}
		}

		// Add attribute to list
		attrs = append(attrs, xml.Attr{
			Name:  xml.Name{Local: attrName},
			Value: attrValue,
		})

		attrStr = strings.TrimSpace(attrStr)
	}

	return attrs
}

func tokenString(token xml.Token) string {
	switch t := token.(type) {
	case xml.StartElement:
		attrs := ""
		for _, attr := range t.Attr {
			attrs += fmt.Sprintf(` %s="%s"`, attr.Name.Local, attr.Value)
		}
		return fmt.Sprintf("<%s%s>", t.Name.Local, attrs)
	case xml.EndElement:
		return fmt.Sprintf("</%s>", t.Name.Local)
	case xml.CharData:
		return string(t)
	case xml.Comment:
		return fmt.Sprintf("<!-- %s -->", string(t))
	case xml.ProcInst:
		return fmt.Sprintf("<?%s?>", t.Target)
	case xml.Directive:
		return fmt.Sprintf("<!%s>", string(t))
	default:
		return fmt.Sprintf("%v", token)
	}
}

// tryEscapeXMLChars trys escaping the non-escaped chars in the top tag values if they caused the parsing errors.
// FIXME(cyx): This is a hacky way to fix the parsing errors, and is not high-performance.
func tryEscapeXMLChars(data string) string {
	bytesData := []byte(data)
	const maxTry = 200 // try fix at most 200 errors.
loop:
	for idx := 0; idx < maxTry; idx++ {
		decoder := xml.NewDecoder(bytes.NewReader(bytesData))
		for {
			_, err := decoder.RawToken()
			if err == io.EOF {
				return string(bytesData)
			}
			if err != nil {
				errPos := int64(1)
				for decoder.InputOffset()-errPos > 0 && bytes.Equal(escapeXMLChars(bytesData[decoder.InputOffset()-errPos:decoder.InputOffset()]), bytesData[decoder.InputOffset()-errPos:decoder.InputOffset()]) {
					errPos++
				}
				newBytesData := bytes.NewBuffer(nil)
				newBytesData.Write(bytesData[:decoder.InputOffset()-errPos])
				newBytesData.Write(escapeXMLChars(bytesData[decoder.InputOffset()-errPos : decoder.InputOffset()]))
				newBytesData.Write(bytesData[decoder.InputOffset():])
				bytesData = newBytesData.Bytes()
				continue loop
			}
		}
	}
	return string(bytesData)
}

func escapeXMLChars(data []byte) []byte {
	buf := bytes.NewBuffer(nil)
	xml.Escape(buf, data)
	return buf.Bytes()
}
