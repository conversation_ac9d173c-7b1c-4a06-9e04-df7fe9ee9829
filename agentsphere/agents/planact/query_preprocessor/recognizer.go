package queryrecognizer

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"code.byted.org/gopkg/env"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/toolset"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/hertz"
)

// QueryRecognizer is used to recognize the query and parse information for later planning
type QueryRecognizer interface {
	Recognize(run *iris.AgentRunContext, messages []*iris.Message) error
}

type URIRecognizer struct{}

var (
	httpURLRegex           = regexp.MustCompile(`(?i)https?://[^\s\p{Han}<>"'{}|\\^` + "`" + `\[\]，。；！？、（）《》〈〉「」『』【】]+`)
	gitURLRegex            = regexp.MustCompile(`(?i)ssh://[^\s\p{Han}<>"'{}|\\^` + "`" + `\[\]，。；！？、（）《》〈〉「」『』【】]+|git@[^\s\p{Han}<>"'{}|\\^` + "`" + `\[\]，。；！？、（）《》〈〉「」『』【】]+[:/][^\s\p{Han}<>"'{}|\\^` + "`" + `\[\]，。；！？、（）《》〈〉「」『』【】]+`)
	larkDocumentURLPattern = regexp.MustCompile(`^https://(bytedance|bytedance\.sg|bytedance\.us)\.larkoffice\.com/(doc|docx|sheet|bitable|wiki)/([a-zA-Z0-9_-]+)`) // for sheets, the url may have a query string `?sheet=id`
	aeolusDashboardPattern = regexp.MustCompile(`^https://data\.bytedance\.net/aeolus/pages/dashboard/`)
	aeolusDataQueryPattern = regexp.MustCompile(`^https://data\.bytedance\.net/aeolus/pages/dataQuery`)
	slardarShortURLPattern = regexp.MustCompile(`^https://(t.wtturl.cn|slardar-us.bytedance.net\/s)/([a-zA-Z0-9_-]+)`)
	slardarLongURLPattern  = regexp.MustCompile(`^https://slardar(?:-us)?.bytedance.net/node/app_detail/`)
)

func ExtractURIs(s string) []string {
	uris := make([]string, 0)

	httpFound := httpURLRegex.FindAllString(s, -1)
	for _, f := range httpFound {
		// trim common trailing punctuations
		f = strings.Trim(f, "<>.,;!?()[]{}\"'\n")
		uris = append(uris, f)
	}

	gitFound := gitURLRegex.FindAllString(s, -1)
	for _, f := range gitFound {
		// trim common trailing punctuations
		f = strings.Trim(f, "<>.,;!?()[]{}\"'\n")
		uris = append(uris, f)
	}

	return uris
}

func (e *URIRecognizer) Recognize(run *iris.AgentRunContext, messages []*iris.Message) error {
	logger := run.GetLogger()
	for _, msg := range messages {
		uris := ExtractURIs(msg.Content)
		logger.Infof("[recognizer] found %d uris in message: %s", len(uris), strings.Join(uris, "\n"))
		mentions := make([]iris.Mention, 0)
		mentions = append(mentions, ConvertCodebaseURLsToMentions(run, uris)...)
		mentions = append(mentions, ConvertLarkDocumentURLsToMentions(run, uris)...)
		mentions = append(mentions, ConvertAeolusURLsToMentions(run, uris)...)
		mentions = append(mentions, ConvertSlardarURLsToMentions(run, uris)...)
		for _, att := range msg.Attachments {
			if att.ArtifactID == "" {
				continue
			}
			mentions = append(mentions, &iris.AttachmentMention{
				BaseMention: iris.BaseMention{
					Type: iris.MentionTypeAttachment,
					ID:   att.ArtifactID,
				},
				ArtifactID: att.ArtifactID,
				Path:       att.Path,
			})
		}

		// deduplicate mentions
		existing := map[string]bool{}
		for _, m := range msg.Mentions {
			existing[m.GetID()] = true
		}
		for _, m := range mentions {
			if _, ok := existing[m.GetID()]; ok {
				continue
			}
			msg.Mentions = append(msg.Mentions, m)
			existing[m.GetID()] = true
		}
	}
	return nil
}

func ConvertCodebaseURLsToMentions(run *iris.AgentRunContext, uris []string) []iris.Mention {
	mentions := make([]iris.Mention, 0)
	lo.ForEach(uris, func(uri string, _ int) {
		repo, err := workspace.ParseRepoCloneInfo(run, uri)
		if err != nil || repo == nil {
			run.GetLogger().Errorf("[recognizer] failed to parse codebase url: %s, err: %v", uri, err)
			return
		}
		if repo.URLType == workspace.URLTypeBitsDevopsMR {
			getMRInfoTool, err := toolset.GetBitsMRInfoTool(run)
			if err != nil {
				run.GetLogger().Errorf("[recognizer] failed to get get_mr_info tool: %v", err)
				return
			}
			step := &iris.AgentRunStep{
				Inputs: map[string]any{
					"crUrl": uri,
				},
			}
			getMRInfoTool.Execute(run, step)
			run.GetLogger().Infof("[recognizer] get_mr_info tool result: %+v", step)
			var mrInfo struct {
				MRInfo struct {
					URL string `json:"url"`
				} `json:"mrInfo"`
			}
			if textContents, ok := step.Outputs["text_contents"].([]string); ok && len(textContents) > 0 {
				json.Unmarshal([]byte(textContents[0]), &mrInfo)
				run.GetLogger().Infof("[recognizer] bits_workflow_get_mr_info tool result: %+v", mrInfo)
				repo, err = workspace.ParseRepoCloneInfo(run, mrInfo.MRInfo.URL)
				if err != nil {
					run.GetLogger().Errorf("[recognizer] failed to parse repo clone info: %v", err)
					return
				}
			} else {
				run.GetLogger().Errorf("[recognizer] bits_workflow_get_mr_info tool result is not a string: %+v", step.Outputs["text_contents"])
				return // skip this uri
			}
		}
		mentions = append(mentions, &iris.CodebaseMention{
			BaseMention: iris.BaseMention{
				Type: iris.MentionTypeCodebase,
				ID:   repo.RepoName,
			},
			Platform: repo.Platform,
			RawURL:   uri,
			RepoName: repo.RepoName,
			Branch:   repo.Ref,
		})
	})
	return mentions
}

func ConvertLarkDocumentURLsToMentions(run *iris.AgentRunContext, uris []string) []iris.Mention {
	mentions := make([]iris.Mention, 0)
	lo.ForEach(uris, func(uri string, _ int) {
		matches := larkDocumentURLPattern.FindStringSubmatch(uri)
		if len(matches) != 4 {
			return
		}
		mentions = append(mentions, &iris.LarkDocMention{
			BaseMention: iris.BaseMention{
				Type: iris.MentionTypeLarkDoc,
				ID:   matches[3],
			},
			DocID: matches[3],
			URL:   uri,
		})
	})
	return mentions
}

func ConvertSlardarURLsToMentions(run *iris.AgentRunContext, uris []string) []iris.Mention {
	logger := run.GetLogger()
	mentions := make([]iris.Mention, 0)
	baseURL := "https://slardar-ai.bytedance.net/"
	if iris.CurrentRegion() == iris.RegionI18n {
		baseURL = "https://slardar-us-ai.byteintl.net/"
	}
	cli, err := hertz.NewClient(baseURL, hertz.NewHTTPClientOption{
		Timeout: 10 * time.Second,
	})
	if err != nil {
		logger.Errorf("[recognizer] failed to create slardar client: %v", err)
		return mentions
	}
	lo.ForEach(uris, func(uri string, _ int) {
		slardarURL := ""
		matches := slardarShortURLPattern.FindStringSubmatch(uri)
		if len(matches) == 3 {
			slardarURL = uri
		} else {
			matches = slardarLongURLPattern.FindStringSubmatch(uri)
			if len(matches) == 2 {
				slardarURL = uri
			}
		}
		if slardarURL == "" {
			logger.Errorf("[recognizer] not a slardar url: %s", uri)
			return
		}

		var resp struct {
			ErrMsg string `json:"errmsg"`
			Errno  int    `json:"errno"`
			Data   struct {
				URLType string `json:"url_type"`
				Params  struct {
					OS        string `json:"os"`
					CrashType string `json:"crash_type"`
					IssueID   string `json:"issue_id"`
				} `json:"params"`
				Platform  string `json:"platform"`
				SourceURL string `json:"source_url"`
			} `json:"data"`
		}
		_, err := cli.DoJSONReq(run, "POST", "/api/autofix/mcp/get_slardar_url_info", hertz.ReqOption{
			Body: map[string]any{
				"link": uri,
			},
			Result: &resp,
		})
		if err != nil {
			logger.Errorf("[recognizer] failed to get slardar issue detail: %s, err: %v", uri, err)
			return
		}
		if resp.Errno != 200 {
			logger.Errorf("[recognizer] failed to get slardar issue detail: %s, resp: %v", uri, resp)
			return
		}
		logger.Infof("[recognizer] slardar issue detail: %+v", resp)
		scenario := resp.Data.Platform
		subScenario := ""
		if scenario == "SlardarApp" {
			subScenario = fmt.Sprintf("%s_%s_%s", resp.Data.URLType, resp.Data.Params.OS, resp.Data.Params.CrashType)
		}

		mentions = append(mentions, &iris.SlardarIssueMention{
			BaseMention: iris.BaseMention{
				Type: iris.MentionTypeSlardarIssue,
				ID:   resp.Data.Params.IssueID,
			},
			URLType:                       resp.Data.URLType,
			URL:                           resp.Data.SourceURL,
			OS:                            resp.Data.Params.OS,
			CrashType:                     resp.Data.Params.CrashType,
			KnowledgeMatchingType:         "accurate",
			ScenarioKnowledgeMatchingType: scenario,
			SubScenario:                   subScenario,
		})
	})
	return mentions
}

func ConvertAeolusURLsToMentions(run *iris.AgentRunContext, uris []string) []iris.Mention {
	logger := run.GetLogger()

	// Check cookie validity first
	if !isAeolusCookieValid(run) {
		logger.Info("[recognizer] Aeolus cookie is invalid or expired, skipping Aeolus URL conversion")
		return []iris.Mention{}
	}

	mentions := make([]iris.Mention, 0)
	lo.ForEach(uris, func(uri string, _ int) {
		// Check if it's an Aeolus dashboard or dataQuery URL
		if aeolusDashboardPattern.MatchString(uri) || aeolusDataQueryPattern.MatchString(uri) {
			mentions = append(mentions, &iris.AeolusMention{
				BaseMention: iris.BaseMention{
					Type: iris.MentionTypeAeolus,
					ID:   uri,
				},
				URL: uri,
			})
		}
	})
	return mentions
}

// CookieInfo represents a single cookie in the response
type CookieInfo struct {
	Name    string  `json:"name"`
	Value   string  `json:"value"`
	Path    string  `json:"path"`
	Domain  string  `json:"domain"`
	Expires float64 `json:"expires"`
	// Other fields omitted for brevity
}

// CookieResponse represents the response from strato-keystore API
type CookieResponse struct {
	Code    int          `json:"code"`
	Data    []CookieInfo `json:"data"`
	Message string       `json:"message"`
}

// isAeolusCookieValid checks if the Aeolus cookie is valid by calling strato-keystore API
func isAeolusCookieValid(run *iris.AgentRunContext) bool {
	logger := run.GetLogger()

	// Get JWT token from environment
	jwtToken := run.GetEnv(entity.RuntimeEnvironUserCloudJWT)
	if jwtToken == "" {
		logger.Warn("[recognizer] No JWT token found in environment, cannot check cookie validity")
		return false
	}

	// Create HTTP request
	url := "https://strato-keystore.bytedance.net/api/v1/cookie"
	if env.GetCurrentVRegion() == env.VREGION_SINGAPORECENTRAL {
		url = "https://strato-keystore.byteintl.net/api/v1/cookie"
	}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.Errorf("[recognizer] Failed to create HTTP request: %v", err)
		return false
	}

	req.Header.Set("x-jwt-token", jwtToken)

	// Make HTTP request
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		logger.Errorf("[recognizer] Failed to call strato-keystore API: %v", err)
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.Errorf("[recognizer] strato-keystore API returned non-200 status: %d", resp.StatusCode)
		return false
	}

	// Parse response
	var cookieResp CookieResponse
	if err := json.NewDecoder(resp.Body).Decode(&cookieResp); err != nil {
		logger.Errorf("[recognizer] Failed to decode cookie response: %v", err)
		return false
	}

	if cookieResp.Code != 0 {
		logger.Errorf("[recognizer] strato-keystore API returned error code: %d, message: %s", cookieResp.Code, cookieResp.Message)
		return false
	}

	// Find titan_passport_id cookie
	now := time.Now().Unix()
	for _, cookie := range cookieResp.Data {
		if cookie.Name == "titan_passport_id" {
			// Check if cookie is expired
			if cookie.Expires > 0 && int64(cookie.Expires) < now {
				logger.Infof("[recognizer] titan_passport_id cookie is expired, expires: %f, current: %d", cookie.Expires, now)
				return false
			}
			// Cookie exists and is not expired
			return true
		}
	}

	logger.Info("[recognizer] titan_passport_id cookie not found")
	return false
}
