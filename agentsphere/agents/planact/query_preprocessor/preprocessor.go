package queryrecognizer

import (
	"context"
	_ "embed"
	"encoding/xml"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"github.com/cenk/backoff"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/conc/iter"

	"code.byted.org/bcc/conf_engine/jsoniter"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/browseract"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

type PreprocessedMentionItem struct {
	ID      string `json:"id"`
	Content string `json:"content"`
	Cost    int64  `json:"cost"` // cost in milliseconds
}

type MentionPreprocessor interface {
	Visible() bool // whether the preprocessor is visible to the user, displayed in "prepare" step
	Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error
	// prompt given to the LLM on how to use the preprocessed data
	GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem
}

type ProcessedResult[T iris.Mention] struct {
	Mention T
	Path    string
	Cost    int64
}

const (
	CodebaseMentionStoreKey   = "codebase_mention_store"
	LarkDocMentionStoreKey    = "lark_doc_mention_store"
	AeolusMentionStoreKey     = "aeolus_mention_store"
	AttachmentMentionStoreKey = "attachment_mention_store"
)

type CodebaseMentionPreprocessor struct{}

type CodebaseMentionStore struct {
	Downloaded map[string]ProcessedResult[iris.CodebaseMention] `json:"downloaded"`
}

func (p *CodebaseMentionPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem {
	store := iris.RetrieveStoreByKey[CodebaseMentionStore](run, CodebaseMentionStoreKey)
	if store.Downloaded == nil {
		return []PreprocessedMentionItem{}
	}
	return lo.MapToSlice(store.Downloaded, func(k string, v ProcessedResult[iris.CodebaseMention]) PreprocessedMentionItem {
		content := fmt.Sprintf("repository %s downloaded to %s", v.Mention.RepoName, v.Path)
		if v.Mention.Branch != "" {
			content += fmt.Sprintf(" (branch: %s)", v.Mention.Branch)
		} else if v.Mention.CommitID != "" {
			content += fmt.Sprintf(" (commit: %s)", v.Mention.CommitID)
		} else if v.Mention.Tag != "" {
			content += fmt.Sprintf(" (tag: %s)", v.Mention.Tag)
		} else {
			content += " (on default branch), you may need to checkout or fetch the desired branch"
		}
		ws := workspace.GetWorkspace(run)
		repo := ws.GetRepositoryByRepoName(v.Mention.RepoName)
		if repo != nil {
			languages, _ := repo.Languages()
			content += fmt.Sprintf("; primary language(s): %s", strings.Join(lo.FilterMap(languages, func(lang workspace.RepoLanguage, _ int) (string, bool) {
				return fmt.Sprintf("%s(%.2f%%)", lang.Language, lang.Percentage*100), lang.Percentage > 0.3
			}), ", "))
		}
		return PreprocessedMentionItem{
			ID:      fmt.Sprintf("@[%s]", v.Mention.RepoName),
			Content: content,
			Cost:    v.Cost,
		}
	})
}

func (p *CodebaseMentionPreprocessor) Visible() bool {
	return true
}

func (p *CodebaseMentionPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error {
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[CodebaseMentionStore](run, CodebaseMentionStoreKey)
	defer func() {
		iris.UpdateStoreByKey(run, CodebaseMentionStoreKey, store)
	}()
	if store.Downloaded == nil {
		store.Downloaded = make(map[string]ProcessedResult[iris.CodebaseMention])
	}

	wg := conc.NewWaitGroup()
	lo.ForEach(mentions, func(m iris.Mention, _ int) {
		wg.Go(func() {
			var mention *iris.CodebaseMention
			var ok bool
			if mention, ok = m.(*iris.CodebaseMention); !ok {
				return
			}
			// already downloaded
			if _, ok := store.Downloaded[mention.RepoName]; ok {
				return
			}

			start := time.Now()
			defer func() {
				metrics.AR.QueryPreprocessorCost.WithTags(&metrics.QueryPreprocessorTag{
					Type: string(mention.Type),
				}).Observe(float64(time.Since(start).Milliseconds()))
			}()

			dir := path.Base(mention.RepoName)
			// full clone if branch/commit/tag are not specified, as it may be a history analysis task
			referenceName := mention.Branch
			if mention.Tag != "" {
				referenceName = mention.Tag
			} else if mention.CommitID != "" {
				referenceName = mention.CommitID
			}
			unshallow := referenceName == ""
			// as we can't recognize the branch/commit/tag from github url for now, we need a full clone
			if mention.Platform == entity.GitPlatformGithub {
				unshallow = true
			}
			logger.Infof("[preprocessor] cloning codebase repo %s(%s) to %s", mention.RepoName, mention.RawURL, dir)
			_, err := workspace.GitClone(run, workspace.GitCloneArgs{
				Directory:     dir,
				RepoPath:      lo.Ternary(mention.RawURL != "", mention.RawURL, mention.RepoName),
				Platform:      lo.Ternary(mention.Platform != "", mention.Platform, entity.GitPlatformCodebase), // leave empty to detect from repo url
				ReferenceName: referenceName,
				UnShallow:     unshallow,
			})
			if err != nil {
				logger.Errorf("[preprocessor] failed to preprocess codebase repo %s to %s, err: %v", mention.RepoName, dir, err)
				return
			}
			cost := time.Since(start).Milliseconds()
			store.Downloaded[mention.RepoName] = ProcessedResult[iris.CodebaseMention]{
				Mention: *mention,
				Path:    dir,
				Cost:    cost,
			}
			logger.Infof("[preprocessor] cloned codebase repo %s to %s", mention.RepoName, dir)
		})
	})
	wg.Wait()
	return nil
}

type LarkDocMentionPreprocessor struct{}

type LarkDocMentionStore struct {
	Downloaded map[string]ProcessedResult[iris.LarkDocMention] `json:"downloaded"`
}

func (p *LarkDocMentionPreprocessor) Visible() bool {
	return true
}

func (p *LarkDocMentionPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error {
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[LarkDocMentionStore](run, LarkDocMentionStoreKey)
	defer func() {
		iris.UpdateStoreByKey(run, LarkDocMentionStoreKey, store)
	}()
	if store.Downloaded == nil {
		store.Downloaded = make(map[string]ProcessedResult[iris.LarkDocMention])
	}

	// lark openapi has a rate limit of 5 requests per second
	it := iter.Iterator[iris.Mention]{
		MaxGoroutines: 2,
	}
	it.ForEach(mentions, func(m *iris.Mention) {
		var mention *iris.LarkDocMention
		var ok bool
		if mention, ok = (*m).(*iris.LarkDocMention); !ok || len(mention.URL) == 0 {
			return
		}
		if _, ok := store.Downloaded[mention.URL]; ok {
			return
		}

		start := time.Now()
		defer func() {
			metrics.AR.QueryPreprocessorCost.WithTags(&metrics.QueryPreprocessorTag{
				Type: string(mention.Type),
			}).Observe(float64(time.Since(start).Milliseconds()))
		}()
		var result *lark.DownloadLarkContentResult
		var err error
		exponentialBackoff := &backoff.ExponentialBackOff{
			InitialInterval:     1 * time.Second, // at least 1 second between retries, as lark rate limit is 5 requests per second
			RandomizationFactor: 0.5,
			Multiplier:          1.5,
			MaxInterval:         5 * time.Second,
			MaxElapsedTime:      1 * time.Minute, // should not take effect as we have a max retries, but needed to be set
			Clock:               backoff.SystemClock,
		}
		exponentialBackoff.Reset() // reset need to be called before using it
		backoff.Retry(func() error {
			result, err = lark.DownloadContentFromLark(run, lark.LarkToolArgs{
				DocumentURL: mention.URL,
			})
			if err == nil {
				return nil
			}
			logger.Infof("fallback to download from dataset: %+v", mention)
			result, err = p.downloadDatasetFile(run, mention)
			if err != nil {
				logger.Errorf("failed to download from dataset %+v:%+v", mention, err)
				return err
			}
			return nil
		}, backoff.WithContext(backoff.WithMaxRetries(exponentialBackoff, 3), run))
		if err != nil || result == nil {
			logger.Errorf("[preprocessor] failed to download lark doc %s, err: %v", mention.URL, err)
			return
		}
		store.Downloaded[mention.URL] = ProcessedResult[iris.LarkDocMention]{
			Mention: *mention,
			Path:    result.FilePath,
			Cost:    time.Since(start).Milliseconds(),
		}
		logger.Infof("[preprocessor] downloaded lark doc %s to %s, cost: %dms", mention.URL, result.FilePath, time.Since(start).Milliseconds())
	})
	return nil
}

func (p *LarkDocMentionPreprocessor) downloadDatasetFile(run *iris.AgentRunContext, mention *iris.LarkDocMention) (*lark.DownloadLarkContentResult, error) {
	content, err := run.GetAPIClient().GetKnowledgeContent(run, mention.KnowledgeBaseID, mention.DocumentID)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get lark doc %s", mention.URL)
	}
	mdFileName := mention.Title + ".lark.md"
	err = os.WriteFile(mdFileName, []byte(content), 0644)
	if err != nil {
		return nil, iris.NewRecoverable(errors.Wrap(err, "failed to write markdown file"))
	}
	return &lark.DownloadLarkContentResult{FilePath: mdFileName}, nil
}
func (p *LarkDocMentionPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem {
	store := iris.RetrieveStoreByKey[LarkDocMentionStore](run, LarkDocMentionStoreKey)
	if store.Downloaded == nil {
		return []PreprocessedMentionItem{}
	}
	return lo.MapToSlice(store.Downloaded, func(k string, v ProcessedResult[iris.LarkDocMention]) PreprocessedMentionItem {
		return PreprocessedMentionItem{
			ID:      fmt.Sprintf("@[%s]", v.Mention.URL),
			Content: fmt.Sprintf("lark document downloaded as markdown to %s", v.Path),
			Cost:    v.Cost,
		}
	})
}

type AttachmentMentionPreprocessor struct{}
type AttachmentMentionStore struct {
	Downloaded map[string]ProcessedResult[iris.AttachmentMention] `json:"downloaded"`
}

func (p *AttachmentMentionPreprocessor) Visible() bool {
	return false
}

func (p *AttachmentMentionPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error {
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[AttachmentMentionStore](run, AttachmentMentionStoreKey)
	defer func() {
		iris.UpdateStoreByKey(run, AttachmentMentionStoreKey, store)
	}()
	if store.Downloaded == nil {
		store.Downloaded = make(map[string]ProcessedResult[iris.AttachmentMention])
	}

	wg := conc.NewWaitGroup()
	lo.ForEach(mentions, func(m iris.Mention, _ int) {
		wg.Go(func() {
			var mention *iris.AttachmentMention
			var ok bool
			if mention, ok = m.(*iris.AttachmentMention); !ok {
				return
			}
			// already downloaded
			if _, ok := store.Downloaded[mention.ArtifactID]; ok {
				return
			}

			start := time.Now()
			defer func() {
				metrics.AR.QueryPreprocessorCost.WithTags(&metrics.QueryPreprocessorTag{
					Type: string(mention.Type),
				}).Observe(float64(time.Since(start).Milliseconds()))
			}()

			logger.Infof("[preprocessor] downloading attachment file %s(%s) to %s", mention.ArtifactID, mention.Path, mention.Path)

			// download artifact file.
			artifactService := run.GetArtifactService()
			_, err := artifactService.DownloadFiles(run, iris.ArtifactDownloadOptions{
				ArtifactID: mention.ArtifactID,
				Files:      []string{mention.Path},
				All:        false,
				Directory:  ".",
			})
			if err != nil {
				logger.Errorf("[preprocessor] failed to download attachment file %s(%s), err: %v", mention.ArtifactID, mention.Path, err)
				return
			}

			cost := time.Since(start).Milliseconds()
			store.Downloaded[mention.ArtifactID] = ProcessedResult[iris.AttachmentMention]{
				Mention: *mention,
				Path:    mention.Path,
				Cost:    cost,
			}
			logger.Infof("[preprocessor] downloaded attachment file %s to %s", mention.Path, mention.Path)
		})
	})
	wg.Wait()
	return nil
}

func (p *AttachmentMentionPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem {
	store := iris.RetrieveStoreByKey[AttachmentMentionStore](run, AttachmentMentionStoreKey)
	if store.Downloaded == nil {
		return []PreprocessedMentionItem{}
	}
	return lo.MapToSlice(store.Downloaded, func(k string, v ProcessedResult[iris.AttachmentMention]) PreprocessedMentionItem {
		return PreprocessedMentionItem{
			ID:      fmt.Sprintf("@[%s]", v.Mention.Path),
			Content: fmt.Sprintf("attachment downloaded to %s", v.Path),
			Cost:    v.Cost,
		}
	})
}

type AeolusMentionPreprocessor struct{}

type AeolusMentionStore struct {
	Downloaded map[string]ProcessedResult[iris.AeolusMention] `json:"downloaded"`
}

func (p *AeolusMentionPreprocessor) Visible() bool {
	return true
}

func (p *AeolusMentionPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error {
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[AeolusMentionStore](run, AeolusMentionStoreKey)
	defer func() {
		iris.UpdateStoreByKey(run, AeolusMentionStoreKey, store)
	}()
	if store.Downloaded == nil {
		store.Downloaded = make(map[string]ProcessedResult[iris.AeolusMention])
	}

	browserClient, err := browser.NewBrowserMCPClient(run)
	if err != nil {
		run.GetLogger().Errorf("failed to new a browser mcp service")
		return errors.WithMessage(err, "failed to new a browser mcp service")
	}

	// Process mentions sequentially
	for _, m := range mentions {
		var mention *iris.AeolusMention
		var ok bool
		if mention, ok = m.(*iris.AeolusMention); !ok || len(mention.URL) == 0 {
			continue
		}
		if _, ok := store.Downloaded[mention.URL]; ok {
			continue
		}

		logger.Infof("[preprocessor] processing aeolus url %s", mention.URL)

		start := time.Now()
		err = executeAeolusScript(run, browserClient, mention.URL)
		if err != nil {
			logger.Errorf("[preprocessor] failed to execute script for aeolus url %s, err: %v", mention.URL, err)
			continue
		}

		store.Downloaded[mention.URL] = ProcessedResult[iris.AeolusMention]{
			Mention: *mention,
			Cost:    time.Since(start).Milliseconds(),
		}
		logger.Infof("[preprocessor] processed aeolus url %s", mention.URL)
	}
	return nil
}

func (p *AeolusMentionPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem {
	store := iris.RetrieveStoreByKey[AeolusMentionStore](run, AeolusMentionStoreKey)
	if store.Downloaded == nil {
		return []PreprocessedMentionItem{}
	}
	return lo.MapToSlice(store.Downloaded, func(k string, v ProcessedResult[iris.AeolusMention]) PreprocessedMentionItem {
		return PreprocessedMentionItem{
			ID:   fmt.Sprintf("@aeolus:%s", v.Mention.URL),
			Cost: v.Cost,
		}
	})
}

// executeAeolusScript executes the Aeolus monitoring script via browser MCP client
func executeAeolusScript(run *iris.AgentRunContext, browserClient *browser.MCPClient, url string) error {
	ctx, cancel := context.WithTimeout(run, 10*time.Minute)
	defer cancel()

	// First goto the URL
	gotoRequest := mcp.CallToolRequest{}
	gotoRequest.Params.Name = browseract.ToolGoto
	gotoRequest.Params.Arguments = map[string]any{
		"url": url,
	}
	_, err := browserClient.Client.CallTool(ctx, gotoRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to goto url: %v", err)
		return errors.WithMessage(err, "failed to goto url")
	}

	// Then execute the script
	scriptRequest := mcp.CallToolRequest{}
	scriptRequest.Params.Name = browseract.ToolBrowserExecuteScript
	scriptRequest.Params.Arguments = map[string]any{
		"script": "from playwright.async_api import Page\nimport os\n\nasync def run(page: Page):\n    return await monitor_aeolus_dashboard_and_download_reports(page)",
	}
	_, err = browserClient.Client.CallTool(ctx, scriptRequest)
	if err != nil {
		return errors.WithMessage(err, "failed to execute script")
	}

	return nil
}

type ScenarioPreprocessor struct{}

var (
	//go:embed prompt/scenario_recognition_system.go.tmpl
	scenarioSystemPrompt         string
	ScenarioSystemPromptTemplate = prompt.MustGetTemplate("ScenarioSystemPrompt", scenarioSystemPrompt)
	//go:embed prompt/scenario_recognition_user.go.tmpl
	scenarioUserPrompt         string
	ScenarioUserPromptTemplate = prompt.MustGetTemplate("ScenarioUserPrompt", scenarioUserPrompt)
	scenarioTagMap             = map[string]string{
		"Data":          "data_analyze",
		"Reporting":     "data_analyze",
		"SlardarApp":    "SlardarApp",
		"SlardarWeb":    "SlardarWeb",
		"Investigation": "search",
		"Info_process":  "search",
		"Coding":        "developer",
	}
)

type ScenarioPreprocessorResult struct {
	QueryAnalysis []DomainAnalysis `xml:"QueryAnalysis"`
}

type DomainAnalysis struct {
	DomainAnalysis   string `xml:"DomainAnalysis"`
	PrimaryDomain    string `xml:"PrimaryDomain"`
	PrimarySubDomain string `xml:"PrimarySubDomain"`
}

func (p *ScenarioPreprocessor) Visible() bool {
	return false
}

func (p *ScenarioPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) (err error) {
	scenario := iris.RetrieveStoreByKey[knowledges.Scenario](run, knowledges.ScenarioStoreKey)

	if scenario.Key != "" {
		return nil
	}

	span, ctx := agentrace.GetRuntimeTracerFromContext(run).
		StartCustomSpan(
			run,
			agentrace.SpanTypeStep,
			"scenario_recognition",
		)
	// add a default timeout to prevent llm call from hanging too long
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	start := time.Now()
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"scenario": scenario,
		}))
		agentrace.AddErrorTag(span, err)
		span.Finish()
		telemetry.EmitScenarioRecognition(run, scenario.Key, scenario.SubKey, scenario.Tags, time.Since(start))
	}()

	message := run.State.Conversation.GetDirtyMessage()
	_ = RewriteMentions(message)

	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(ScenarioSystemPromptTemplate, map[string]any{}),
		prompt.WithUserMessage(
			ScenarioUserPromptTemplate,
			map[string]any{"Query": jsoniter.MustMarshal(message)},
		),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to compose messages")
	}

	llmOpt := run.GetConfig().GetModelByScene("dynamic_planner")
	completion, err := run.GetLLM().ChatCompletion(ctx, messages, framework.LLMCompletionOption{
		Model:       llmOpt.Model,
		Temperature: llmOpt.Temperature,
		MaxTokens:   llmOpt.MaxTokens,
		Tag:         "scenario_recognition",
	})
	if err != nil {
		agentrace.AddErrorTag(span, err)
		return errors.WithMessage(err, "failed to call llm")
	}

	run.GetLogger().Infof("scenario recognition result: %s", completion.Content)

	topTags, err := prompt.ParseTopTagsV2(completion.Content)
	// if response is incomplete, but some tags are found, we still can use them
	if err != nil && len(topTags) == 0 {
		return errors.WithMessage(err, "failed to parse top tags")
	}

	subScenario := ""
	otherScenarios := make([]string, 0)
	for _, tag := range topTags {
		if tag.XMLName.Local != "AnalysisResults" {
			continue
		}
		var result ScenarioPreprocessorResult
		err = xml.Unmarshal([]byte("<AnalysisResults>"+tag.Content+"</AnalysisResults>"), &result)
		if err != nil {
			run.GetLogger().Errorf("failed to unmarshal scenario recognition result: %v", err)
			continue
		}
		for _, item := range result.QueryAnalysis {
			if mapped, ok := scenarioTagMap[item.PrimaryDomain]; !ok {
				run.GetLogger().Errorf("unsupport scenario: %s", item.PrimaryDomain)
			} else {
				if scenario.Key == "" {
					scenario.Key = mapped
				} else {
					otherScenarios = append(otherScenarios, mapped)
				}
			}
			if item.PrimarySubDomain != "" {
				subScenario = item.PrimarySubDomain
				scenario.SubKey = subScenario
			}
		}
	}

	for _, mention := range mentions {
		if mention.GetType() == iris.MentionTypeSlardarIssue {
			if slardarMention, ok := mention.(*iris.SlardarIssueMention); ok {
				// slardar need force rewrite the scenario, and manually force set the subScenario with the subScenario as prefix
				if slardarMention.ScenarioKnowledgeMatchingType != "" {
					scenario.Key = slardarMention.ScenarioKnowledgeMatchingType
				}
				if slardarMention.SubScenario != "" {
					// backward compatible
					scenario.SubKey = subScenario + "_" + slardarMention.SubScenario
				} else {
					scenario.SubKey = ""
				}
				break
			}
		}
	}

	run.GetLogger().Infof("scenario recognition result: %s, subScenario: %s, other scenarios: %v", scenario, scenario.SubKey, otherScenarios)
	iris.UpdateStoreByKey(run, knowledges.ScenarioStoreKey, scenario)

	return nil
}

func (p *ScenarioPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) []PreprocessedMentionItem {
	store := iris.RetrieveStoreByKey[knowledges.Scenario](run, knowledges.ScenarioStoreKey)
	if store.Key == "" {
		return []PreprocessedMentionItem{}
	}
	return []PreprocessedMentionItem{
		{
			ID:      "scenario",
			Content: store.Key,
		},
	}
}
