package planact_agent

import (
	"fmt"
	"io"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"slices"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

func (a *PlanActAgent) uploadFinalResult(run *iris.AgentRunContext, content string) []iris.Attachment {

	run.GetLogger().Infof("start upload final result: %s", content)

	// initial upload attachments
	titleReg := regexp.MustCompile(`# (.*)`)
	titleMatches := titleReg.FindStringSubmatch(content)
	title := "final_report"
	if len(titleMatches) == 2 {
		title = strings.TrimSpace(titleMatches[1])
	}
	artifact, err := newFileArtifact(run)
	if err != nil {
		return []iris.Attachment{}
	}
	attachmentList := []iris.Attachment{}

	err = saveFileArtifact(run, artifact, []iris.ArtifactFile{
		{
			Path:    title + ".md",
			Content: []byte(content),
		},
	})
	if err != nil {
		return attachmentList
	}
	attachmentList = append(attachmentList, iris.Attachment{
		ArtifactID: artifact.ArtifactID,
		Path:       title + ".md",
		Type:       iris.AttachmentTypeFile,
	})

	return attachmentList
}

func (a *PlanActAgent) uploadAttachments(run *iris.AgentRunContext, reference string, projectReference iris.ProjectReference) []iris.Attachment {
	logger := run.GetLogger()

	logger.Infof("start upload attachments: %s, %s", reference, projectReference)

	attachmentList := []iris.Attachment{}
	// upload project attachments
	projectAttachments := a.uploadProjectArtifacts(run, projectReference)
	attachmentList = append(attachmentList, projectAttachments...)

	// extract attachments without duplicates
	linkAttachReg := regexp.MustCompile(`\[(.*)\]\(\s?http(.*?)\)`)
	fileAttachReg := regexp.MustCompile(`\[(.*)\]\((.*)\)`)
	// Match Code URLs
	codeReg := util.CodeReg
	// Match deploy URLs
	deployReg := util.DeployReg
	// Match Lark document URLs
	larkDocReg := util.LarkDocReg
	// Match Lark sheet URLs
	larkSheetReg := util.LarkSheetReg
	// Match Bits URLs
	bitsReg := util.BitsReg

	linkMatches := linkAttachReg.FindAllStringSubmatch(reference, -1)
	matches := [][]string{}
	for _, match := range linkMatches {
		if len(match) < 3 {
			continue
		}
		match[2] = "http" + match[2]
		matches = append(matches, match)
	}
	fileMatches := fileAttachReg.FindAllStringSubmatch(reference, -1)
	for _, match := range fileMatches {
		if len(match) < 3 {
			continue
		}
		if strings.HasPrefix(match[2], "http") {
			continue
		}
		matches = append(matches, match)
	}

	deployToUpload := []string{}
	externalLinkToUpload := make(map[string]string)
	attachmentsToUpload := make(map[string]bool)
	larkDocsToUpload := make(map[string]string)
	larkSheetsToUpload := make(map[string]string)
	for _, match := range matches {
		if len(match) < 3 {
			continue
		}
		if url := deployReg.FindString(match[2]); len(url) > 0 {
			if slices.Contains(deployToUpload, url) {
				continue
			}
			deployToUpload = append(deployToUpload, url)
		} else if url := codeReg.FindString(match[2]); len(url) > 0 {
			externalLinkToUpload[url] = match[1]
		} else if url := bitsReg.FindString(match[2]); len(url) > 0 {
			externalLinkToUpload[url] = match[1]
		} else if url := larkDocReg.FindString(match[2]); len(url) > 0 {
			larkDocsToUpload[url] = match[1]
		} else if url := larkSheetReg.FindString(match[2]); len(url) > 0 {
			larkSheetsToUpload[url] = match[1]
		} else { // file attachment
			fileName := filepath.Base(match[2])
			attachmentsToUpload[fileName] = true
		}
	}
	userInputs := lo.Reduce(run.State.Conversation.GetMessages(), func(acc string, msg *iris.Message, _ int) string {
		return acc + lo.Ternary(msg.From == iris.MessageFromUser, msg.Content, "")
	}, "")

	// first upload lark docs to attachment list (deduped)
	for larkDocURL, title := range larkDocsToUpload {
		if strings.Contains(userInputs, larkDocURL) {
			continue
		}
		attachmentList = append(attachmentList, iris.Attachment{
			Path: title,
			URL:  larkDocURL,
			Type: iris.AttachmentTypeLarkDoc,
		})
	}

	for larkSheetURL, title := range larkSheetsToUpload {
		if strings.Contains(userInputs, larkSheetURL) {
			continue
		}
		attachmentList = append(attachmentList, iris.Attachment{
			Path: title,
			URL:  larkSheetURL,
			Type: iris.AttachmentTypeLarkSheet,
		})
	}

	// add deploy attachments
	for _, fileName := range deployToUpload {
		if strings.Contains(fileName, "bytedance.net") || strings.Contains(fileName, "tiktok-row.net") {
			if _, ok := run.Parameters["deploy_id"]; !ok {
				continue
			}
			attachmentList = append(attachmentList, iris.Attachment{
				ArtifactID: conv.DefaultAny[string](run.Parameters["deploy_id"].(map[string]any)[fileName]),
				Path:       fileName,
				Type:       iris.AttachmentTypeDeployment,
			})
		}
	}

	// add external link attachments
	for link, title := range externalLinkToUpload {
		attachmentList = append(attachmentList, iris.Attachment{
			ArtifactID: "",
			URL:        link,
			Path:       title,
			Type:       iris.AttachmentTypeURL,
		})
	}

	// initial upload attachments
	editor := workspace.GetEditor(run)
	artifact, uploadErr := newFileArtifact(run)
	if uploadErr != nil {
		logger.Errorf("attachments: new file artifact failed: %v", uploadErr)
		return attachmentList
	}

	// upload attachments
	for fileName, exist := range attachmentsToUpload {
		if !exist {
			continue
		}

		path, err := url.PathUnescape(fileName)
		if err != nil {
			path = fileName
		}

		output, err := editor.SearchFileName(workspace.SearchFileNameArgs{
			Directory: ".",
			FileName:  path,
		})
		if err != nil || len(output.Files) == 0 {
			continue
		}

		fileName = output.Files[0].Path
		// 如果 fileName 在 project artifact 内，则不上传
		isBelongsToProject := false
		for _, attachment := range projectAttachments {
			projectPath := attachment.Path
			fileFullPath := filepath.Join(editor.WorkingDirectory, fileName)
			isBelongsToProject, err = belongsToProject(fileFullPath, projectPath)
			if err != nil {
				logger.Errorf("belongsToProject failed: %v, fileName: %s, projectPath: %s", err, fileFullPath, projectPath)
				continue
			}
			if isBelongsToProject {
				logger.Infof("fileName: %s belongs to project: %s", fileFullPath, projectPath)
				break
			}
			logger.Infof("fileName: %s does not belong to project: %s", fileFullPath, projectPath)
		}
		if isBelongsToProject {
			continue
		}

		fileContent, err := a.readFileIfExists(fileName)
		if err != nil {
			continue
		}
		err = saveFileArtifact(run, artifact, []iris.ArtifactFile{
			{
				Path:    fileName,
				Content: fileContent,
			},
		})
		if err != nil {
			continue
		}
		attachmentList = append(attachmentList, iris.Attachment{
			ArtifactID: artifact.ArtifactID,
			Path:       fileName,
			Type:       iris.AttachmentTypeFile,
		})
	}

	// upload full reference list but donot attach to final message

	store := iris.RetrieveStoreByKey[entity.ReferenceStore](run, entity.ReferenceStoreKey)
	if len(store.SearchedRef.List) > 0 {
		_ = saveFileArtifact(run, artifact, []iris.ArtifactFile{
			{
				Path:    "参考文献.md",
				Content: []byte("# 参考文献\n\n" + iris.ReferenceSimpleString(store.SearchedRef.List)),
			},
		})
	}

	logger.Infof("the final attachments list %+v", attachmentList)
	return attachmentList
}

func belongsToProject(filePath, projectPath string) (bool, error) {
	absProjectPath, err := filepath.Abs(projectPath)
	if err != nil {
		return false, err
	}
	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		return false, err
	}
	rel, err := filepath.Rel(absProjectPath, absFilePath)
	if err != nil {
		return false, err
	}
	// rel == "." 说明 filePath 和 projectPath 相同
	// rel 以 ".." 开头说明 filePath 不在 projectPath 下
	if rel == "." {
		return true, nil
	}
	if strings.HasPrefix(rel, ".."+string(filepath.Separator)) || rel == ".." {
		return false, nil
	}
	return true, nil
}

func (a *PlanActAgent) readFileIfExists(filename string) ([]byte, error) {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return nil, err
	}
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	return io.ReadAll(file)
}

func newFileArtifact(run *iris.AgentRunContext) (*iris.ResultArtifact, error) {
	artifacts, logger := run.GetArtifactService(), run.GetLogger()
	if artifacts.IsNil() {
		logger.Errorf("failed to connect to artifact service.")
		return nil, fmt.Errorf("failed to connect to artifact service.")
	}
	return artifacts.NewFileArtifact(run, nextentity.FileArtifactTypeMetadata{})
}

func saveFileArtifact(run *iris.AgentRunContext, artifact *iris.ResultArtifact, files []iris.ArtifactFile) error {
	artifactservice, logger := run.GetArtifactService(), run.GetLogger()
	if artifactservice.IsNil() {
		return fmt.Errorf("failed to connect to artifact service.")
	}
	err := artifactservice.UploadFiles(run, artifact, files)
	if err != nil {
		logger.Errorf("failed to upload artifact: %v", err)
		return errors.WithMessage(err, "failed to upload artifact")
	}
	return nil
}
