package browser

import (
	"fmt"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
)

type BrowserToolArgs struct {
	Persona string `json:"persona" mapstructure:"persona" description:"The persona of the browser agent. The description should give out background context and the specific goals."`
	URL     string `json:"URL" mapstructure:"url" description:"At the start of the task, the webpage URL displayed by the browser"`
	Task    string `json:"task" mapstructure:"task" description:" A self contained task prompt that can be completed by the browser agent."`
}

func browserInputFunc(run *iris.AgentRunContext, args BrowserToolArgs) (string, error) {
	return fmt.Sprintf("Task: %s.\n The url the browser sholud display: %s\n.", args.Task, args.URL), nil
}

func browserOutputFunc(run *iris.AgentRunContext, output *controltool.ConclusionOutput) *controltool.ConclusionOutput {
	return output
}

func browserOptionFunc(run *iris.AgentRunContext, args BrowserToolArgs) actors.RunOptions {
	opts := actors.RunOptions{}
	opts.DynamicPersona = args.Persona
	opts.ToolKnowledgesID = []string{
		"browser.dynamic_19",
		"browser.dynamic_20",
		"browser.dynamic_23",
	}
	return opts
}

func NewBrowserAgentTool(run *iris.AgentRunContext, step *actors.ExecutionStep) iris.Action {
	return agenttool.NewAgentToolV2[BrowserToolArgs](
		browserInputFunc,
		browserOutputFunc,
		browserOptionFunc,
		agenttool.AgentToolOption{
			Name:        "browser_use",
			Description: "Explore, interact and login with the web browser to complete a task. Please use browser_goto_and_extraction if you need to extract information from a specific web page simply.",
			Toolsets: []string{
				"browser_use",
			},
			CandidateKnowledges: knowledges.LoadKnowledge(),
			ParentStep:          step,
			DisableMcpHelper:    true,
		})

}
