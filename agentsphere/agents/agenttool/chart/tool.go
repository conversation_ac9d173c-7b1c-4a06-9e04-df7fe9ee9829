package chartagenttool

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
)

type ChartToolArgs struct {
	DataFilePath     string `json:"data_file_path" mapstructure:"data_file_path" description:"必填，需要生成图表的数据JSON文件绝对路径，（.json），比如：/workspace/iris_e7c707a5-ae78-42d0-b045-1882a9f0a4d7/somedata.json，注意：1. 禁止填url2. 文件路径必须真实存在（必须提前使用 pwd && ls 确认文件路径） 3. json文件内存放 JSON 字符串，数据为字典包含两个字段 data 和 fields_meta，示例数据：{\"data\": [{\"date\": \"2021-01-01\", \"mau_rate\": 0.75}, {\"date\": \"2021-01-02\", \"mau_rate\": 0.2}], \"fields_meta\": [{\"name\": \"mau_rate\", \"description\": \"月活跃用户比例\", \"role\": \"measure\", \"unit\": \"\"}, {\"name\": \"date\", \"description\": \"月份\", \"role\": \"dimension\", \"unit\": \"\"}]} 4. data 中为数据列表，列表中的每个元素为一个字典，字典中的 key 为字段名称，value 为字段值，只提供作图所需的字段，其他无关字段请不要提供，并且一定要按照展示需求对数据进行排序，因为默认会按照数据的顺序来生成图表5. fields_meta 中为数据对应的字段元信息，是一个字段信息列表，详细 schema 定义：{\"type\":\"array\",\"description\":\"数据对应的数据字段信息，与数据中的字段一一对应，描述了当前数据字段的内容，名称，类型以及数据单位信息\",\"items\":{\"type\":\"object\",\"properties\":{\"name\":{\"type\":\"string\",\"description\":\"当前字段的名称，与数据中的key值一一对应，例如当数据为[{date:'2021-01-01',value:100}]时，此时的字段可选为:[date,value]\"},\"role\":{\"type\":\"string\",\"description\":\"当前字段的类型，-measure:度量，为数值字段；-dimension:维度，一般用于分类\",\"enum\":[\"measure\",\"dimension\"]},\"description\":{\"type\":\"string\",\"description\":\"当前字段的简单介绍\"},\"unit\":{\"type\":\"string\",\"description\":\"仅当字段为度量字段时产生，代表此时字段数值对应的单位，真实数值没有单位空字符串即可，例如K,万,percent 等常见数值单位，此时真实数值为当前数值加上单位，对于百分比类 unit 使用务必慎重确认数据是否为百分数，例如data中数据为20，unit=percent时，代表实际数值20 percent=0.2;如果数据为 0.26 unit 为空字符串，代表实际数值就是 0.26；如果你想表达 25 percent 可以使用数据 0.25 没有单位或者数据 25 单位为 percent；如果数据为 0.25 单位为 percent 那么实际数值会变为 0.0025，这个一般是不正常的低比例，你要思考下是不是搞错单位了；同样20K=20,000;Note:同个轴/Scale上的度量确保unit一致\"}},\"required\":[\"name\",\"role\",\"unit\",\"description\"]}}"`
	ChartDescription string `json:"chart_description" mapstructure:"chart_description" description:"必填，表达对图表的详细描述，包括图表的类型、可视化映射、样式配置、坐标轴、标题、标签、图例、颜色、标注等，注意：1. 描述需要尽量详细，所有图表需要的信息都需要表达出来"`
	Language         string `json:"language,omitempty" mapstructure:"language" description:"选填，语言选择, 默认不填为中文"`
	ResultFileName   string `json:"result_file_name" mapstructure:"result_file_name" description:"必填，生成的图表结果文件名，1. 禁止填路径，只需要文件名 2. 不允许提供文件后缀名"`
}

type FieldMeta struct {
	Name        string `json:"name"`
	Role        string `json:"role"`
	Description string `json:"description"`
	Unit        string `json:"unit"`
}

type ChartData struct {
	Data       []map[string]interface{} `json:"data"`
	FieldsMeta []FieldMeta              `json:"fields_meta"`
}

func chartInputFunc(run *iris.AgentRunContext, args ChartToolArgs) (string, error) {
	if strings.TrimSpace(args.DataFilePath) == "" {
		return "", iris.NewRecoverable(fmt.Errorf("data_file_path is required"))
	}

	fileContent, err := os.ReadFile(args.DataFilePath)
	if err != nil {
		return "", iris.NewRecoverable(fmt.Errorf("please check and fix data file path, failed to read data file %s: %w", args.DataFilePath, err))
	}

	trimmedContent := strings.TrimSpace(string(fileContent))
	var v ChartData
	if unmarshalErr := json.Unmarshal([]byte(trimmedContent), &v); unmarshalErr != nil {
		return "", iris.NewRecoverable(fmt.Errorf("please check and fix data file content, failed to parse file content as JSON object (with data and fields_meta): %s: %v", args.DataFilePath, unmarshalErr))
	}

	if len(v.Data) == 0 {
		return "", iris.NewRecoverable(fmt.Errorf("please check and fix data file content, 'data' list is empty in JSON file: %s", args.DataFilePath))
	}

	if len(v.FieldsMeta) == 0 {
		return "", iris.NewRecoverable(fmt.Errorf("please check and fix data file content, 'fields_meta' list is empty in JSON file: %s", args.DataFilePath))
	}

	input := fmt.Sprintf("the data for the chart is stored in the JSON file path: %s\n", args.DataFilePath)
	input += fmt.Sprintf("chart description: %s\n", args.ChartDescription)
	input += fmt.Sprintf("the chart result file should use this file name: %s\n", args.ResultFileName)
	language := "中文"
	if args.Language != "" {
		language = args.Language
	}
	input += fmt.Sprintf("language: %s\n", language)

	return input, nil
}

func ChartOutputFunc(run *iris.AgentRunContext, output *controltool.ConclusionOutput) *controltool.ConclusionOutput {
	return output
}

func chartOptionFunc(run *iris.AgentRunContext, args ChartToolArgs) actors.RunOptions {
	options := actors.RunOptions{}
	options.ToolKnowledgesID = []string{
		"chart_creation.agenttool_chart",
	}
	options.SysKnowledgesID = []string{
		"mewtwo.1",
		"mewtwo.3",
		"mewtwo.dynamic_38_chart_with_python",
	}
	options.DynamicPersona = "You are one chart expert, you will create a single chart file according to the chart task description."
	return options
}

func NewChartAgentTool(run *iris.AgentRunContext, step *actors.ExecutionStep) iris.Action {
	return agenttool.NewAgentToolV2(
		chartInputFunc,
		ChartOutputFunc,
		chartOptionFunc,
		agenttool.AgentToolOption{
			Name: "chart_creation",
			Description: `chart_creation can create data visualization chart files, you must use this tool if you need to create any data visualization chart (including interactive charts):
This tool will generate a single chart file, if you need to create multiple charts, you can call this tool multiple times.
Data analysis features (e.g., query, edit, filter, aggregate, or auto-insights) are not supported, so the data must be fully processed and ready before visualization.
This tool is intended solely for creating data visualization chart files. DO NOT use it for generating architecture diagrams or flowcharts.
`,
			Toolsets: []string{
				// "visactor",
				"files",
				"terminal",
				"deploy",
				"browser_use",
			},
			CandidateKnowledges: knowledges.LoadKnowledge(),
			// SystemKnowledges:    knowledges.FilterKnowledgeItems(knowledges.LoadKnowledge(), sysKnowledgesID),
			ParentStep: step,
		})
}
