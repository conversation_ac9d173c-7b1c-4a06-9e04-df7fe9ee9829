package web

import (
	"fmt"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
)

const (
	AGENT_TOOL_NAME = "web_creation"
	PERSONA         = `You are a web development specialist who excels at creating complete web applications from scratch.
	You have expertise in building web applications, HTML reports, and full-stack solutions using modern technologies and frameworks.
	You can rapidly prototype and develop everything from simple data visualization reports to complex interactive web applications with both frontend and backend components.
	You focus on creating production-ready applications with proper architecture, clean code structure, and optimal user experience.
	You must follow the provided knowledge to develop.
	
	CRITICAL: 
	- Download and save required images before starting to develop the application
	- Read all referenced files thoroughly and incorporate their content into the NEW application
	- Use provided images, data, and resources as integral components of the new application
	- Transform documents and data files into functional web application features
	- Only implement the backend when the user explicitly requests a backend service. Otherwise, prefer to only write the frontend application.
	- Always organize your code within a new, well-structured folder. Avoid scattering files in the root directory.
	- DO NOT perform extensive interactive testing. Only test the page loads correctly using browser_goto_and_extraction
	- The final conclusion should include the complete project overview, all test results, any fixes applied, and final deployment status
	- Output a comprehensive summary of what was built, how it was tested, and confirm all functionality works as expected
	`

	DESCRIPTION = `Professional web application creation tool that builds complete web pages and html report from scratch:
IMPORTANT: This tool is ONLY for creation of NEW web applications and html report.
DO USE IT FOR:
- Creating visualized reports(可视化报告), HTML reports(HTML报告), fullstack apps, and frontend applications
- Building data visualization dashboards, showcase websites and interactive reports
- Developing demo apps, prototypes and MVPs, transforming static documents into dynamic web experiences
Do NOT use for:
- Modifying existing projects or codebases
- Adding features to current applications
- Updating or maintaining existing code
IMPORTANT: Prepare all required resources (images, reports, data files, etc.) before calling this tool.
Resources are essential for building comprehensive applications with relevant content and functionality.
The tool requires these resources to be properly referenced and accessible to create a complete, functional application`
)

type WebAppToolArgs struct {
	Task      string `json:"task" mapstructure:"task" description:"Clear web application development task description including specific requirements, functionality, design style, etc. Example: 'Create a product showcase web app with carousel, product list, and contact form'."`
	Resources string `json:"resources" mapstructure:"resources" description:"Available resources using reference links, no need to repeat the whole content of files. Use markdown format including: research reports, analysis documents, data files, images, etc. Example: '- [research report about product](./report.md)\n- [sales data of product](./data.json)\n- [image about product](./image.jpg)'"`
	TechStack string `json:"tech_stack" mapstructure:"tech_stack" description:"Tech stack requirements from user request. If there is no tech stack requirements, you should leave it empty. Do not invent or decide on tech stack if user has not explicitly specified them."`
}

func webAppInputFunc(run *iris.AgentRunContext, args WebAppToolArgs) (string, error) {
	var input string
	if args.Resources != "" {
		input += fmt.Sprintf("[Resources]\nThere are some resources provided by previous steps: \n%s\n\n", args.Resources)
	}

	input += fmt.Sprintf("[MAIN TASK]\n%s\n=======END OF MAIN TASK========", args.Task)

	if args.TechStack != "" {
		input += fmt.Sprintf("[Tech Stack]\n%s\n\n", args.TechStack)
	}

	input += "\nIf the task is about creating web applications from scratch and does not have extra tech stack requirements, you should use `aime_create_react_app` to create the app.\n"
	input += "\nIf the task is about creating html report or data visualization, you should use HTML/CSS/JS "

	return input, nil
}

func webAppOutputFunc(run *iris.AgentRunContext, output *controltool.ConclusionOutput) *controltool.ConclusionOutput {
	if output != nil && output.Evaluation == controltool.ConclusionEvaluationSuccess {
		output.Content += "\n\n" + "[REMINDER] If the `web_creation` is successful and outputs already includes testing, DO NOT use browser to retest again."
	}

	return output
}
func webAppOptionFunc(run *iris.AgentRunContext, args WebAppToolArgs) actors.RunOptions {
	sysKnowledgesIDs := []string{
		"mewtwo.1",          // chinese font
		"mewtwo.3",          // echarts
		"mewtwo.5",          // map display
		"mewtwo.coder_1",    // coding best practices
		"developer.coder_3", // testing and debugging best practices
		"mewtwo.coder_7",    // media guidelines
		"mewtwo.coder_8",    // html generation principles
		"developer.coder_5", // simple web app development workflow
	}
	referenceStore := iris.RetrieveStoreByKey[planactentity.ReferenceStore](run, planactentity.ReferenceStoreKey)
	var needCitation bool
	if len(referenceStore.SearchedRef.List) > 0 {
		needCitation = true
	}
	// if the reference is not empty, we need to use the citation knowledge
	if needCitation {
		sysKnowledgesIDs = append(sysKnowledgesIDs, "mewtwo.dynamic_4_html_citation")
	}
	return actors.RunOptions{
		SysKnowledgesID: sysKnowledgesIDs,
	}
}

func NewWebAppAgentTool(run *iris.AgentRunContext, step *actors.ExecutionStep) iris.Action {
	kg := knowledges.CreateKnowledgebase(run)
	return agenttool.NewAgentTool[WebAppToolArgs](
		webAppInputFunc,
		webAppOutputFunc,
		webAppOptionFunc,
		agenttool.AgentToolOption{
			Name:        AGENT_TOOL_NAME,
			Description: DESCRIPTION,
			Toolsets: []string{
				"files",               // for write/read files
				"terminal",            // for running terminal commands
				"google_image_search", // for searching images
				"deploy",              // for deploying the application
			},
			DisableMcpHelper:    false,
			CandidateKnowledges: kg.AllKnowledgeItems(),
			ParentStep:          step,
			Persona:             PERSONA,
		})
}
