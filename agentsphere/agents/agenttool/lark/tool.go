package larkagenttool

import (
	"fmt"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
)

type LarkToolArgs struct {
	Persona string `json:"persona" mapstructure:"persona" description:"Persona to put at the very first of the system prompt. Describe the agent's role, capabilities, and any other relevant information. Should establish: 1) Specific expertise domain and professional level 2) Working methodology and quality standards 3) Problem-solving approach and attention to detail 4) Communication style that balances clarity with technical accuracy. The persona should inspire confidence while setting high performance expectations."`
	Task    string `json:"task" mapstructure:"task" description:"A self contained task prompt that can be completed by the lark agent. The description should give out background context and the specific goals, but not detailed datapoints or libraries to use."`
	// Reference              string `json:"reference" mapstructure:"reference" description:"the important results (files, images, deployed url, etc.) to be refered, in markdown format"`
	OutputLanguage string `json:"output_language" mapstructure:"output_language" description:"The language of the final output Lark document"`
}

func larkInputFunc(run *iris.AgentRunContext, args LarkToolArgs) (string, error) {
	input := fmt.Sprintf("task: %s\nlark document language: %s\n", args.Task, args.OutputLanguage)
	return input, nil
}

func larkOutputFunc(run *iris.AgentRunContext, output *controltool.ConclusionOutput) *controltool.ConclusionOutput {
	return output
}

func larkOptionFunc(run *iris.AgentRunContext, args LarkToolArgs) actors.RunOptions {
	options := actors.RunOptions{}
	referenceStore := iris.RetrieveStoreByKey[planactentity.ReferenceStore](run, planactentity.ReferenceStoreKey)
	scenario := iris.RetrieveStoreByKey[knowledges.Scenario](run, knowledges.ScenarioStoreKey)
	if scenario.SubKey == knowledges.LarkTemplateScenarioKey {
		if len(referenceStore.SearchedRef.List) > 0 {
			// 飞书模版+需要citation
			options.ToolKnowledgesID = []string{
				"lark_creation.dynamic_5_citation",
				"lark_creation.dynamic_25",
			}
		} else {
			// 飞书模版+不需要citation
			options.ToolKnowledgesID = []string{
				"lark_creation.dynamic_5",
				"lark_creation.dynamic_25",
			}
		}
	} else {
		if len(referenceStore.SearchedRef.List) > 0 {
			// 非飞书模版+需要citation
			options.ToolKnowledgesID = []string{
				"lark_creation.dynamic_10_lark_agenttool_creation",
				"lark_creation.dynamic_5_citation",
			}
			options.SysKnowledgesID = []string{
				"mewtwo.dynamic_33",
			}
		} else {
			// 非飞书模版+不需要citation
			options.ToolKnowledgesID = []string{
				"lark_creation.dynamic_10_lark_agenttool_creation",
				"lark_creation.dynamic_5",
			}
			options.SysKnowledgesID = []string{
				"mewtwo.dynamic_33",
			}
		}
	}
	options.DynamicPersona = args.Persona
	return options
}

func NewLarkAgentTool(run *iris.AgentRunContext, step *actors.ExecutionStep) iris.Action {
	kg := knowledges.CreateKnowledgebase(run)
	return agenttool.NewAgentToolV2[LarkToolArgs](
		larkInputFunc,
		larkOutputFunc,
		larkOptionFunc,
		agenttool.AgentToolOption{
			Name: "lark_creation",
			Description: fmt.Sprintf(`lark_creation can create Lark/Feishu Document, Lark/Feishu Sheets(飞书表格), or Lark/Feishu Base(飞书多维表格), **Do not create any lark.md files before using lark_creation tool.**:
1. When creating a Lark/Feishu Document, input the materials needed for the document (such as search results, data analysis results, etc.), and lark_creation will automatically generate charts or images, organize content, and form a Feishu Doc with a clear structure, professional content, and rich styles.
2. When creating a Lark/Feishu Sheets / Base, input the data to be written into the sheet, and lark_creation will generate files such as csv, xlsx, xls, etc. according to the data and requirements, and then convert them into Lark/Feishu Sheets / Base.
lark_creation will return a Lark/Feishu link, they are the actual final Lark/Feishu documents or sheets. There is no need to further verify them. However, if the link url is not provided, it indicates there are some thing wrong and need to retry.
`),
			Toolsets: []string{
				"files",
				"terminal",
				"deploy",
				"lark",
			},
			DisableMcpHelper:    false,
			CandidateKnowledges: kg.AllKnowledgeItems(),
			ParentStep:          step,
		})
}
