package util

import (
	"regexp"
)

var (
	CodeReg      = regexp.MustCompile(`https://code\.byted\.org/.*`)
	BitsReg      = regexp.MustCompile(`https://(bits|bits-boe)\.bytedance\.net/.*`)
	DeployReg    = regexp.MustCompile(`https://(\S*)\.(bytedance\.net|tiktok-row\.net)`)
	LarkDocReg   = regexp.MustCompile(`https://bytedance\.(larkoffice\.com|feishu\.cn)/docx/[a-zA-Z0-9]+`)
	LarkSheetReg = regexp.MustCompile(`https://bytedance\.(larkoffice\.com|feishu\.cn)/(sheets|base)/[a-zA-Z0-9]+`)
)
