package util_test

import (
	"encoding/json"
	"reflect"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
)

func TestTypeToJSONSchema(t *testing.T) {
	type TestStruct struct {
		Field1 string `json:"field1" default:"field1 default" description:"field1 description"`
		Field2 int    `json:"field2,omitempty"`
		Field3 int    `json:"-"`
	}

	testCases := []struct {
		name     string
		input    reflect.Type
		expected framework.Schema
	}{
		{
			name:     "struct",
			input:    reflect.TypeOf(TestStruct{}),
			expected: framework.Schema{Type: framework.TypeObject, Properties: map[string]framework.Schema{"field1": {Type: framework.TypeString, Description: "field1 description", Default: "field1 default"}, "field2": {Type: framework.TypeInteger, Default: nil}}, Required: []string{"field1"}},
		},
		{
			name:     "slice",
			input:    reflect.TypeOf([]int{}),
			expected: framework.Schema{Type: framework.TypeArray, Items: &framework.Schema{Type: framework.TypeInteger}},
		},
		{
			name:     "map",
			input:    reflect.TypeOf(map[string]int{}),
			expected: framework.Schema{Type: framework.TypeObject},
		},
		{
			name:     "string",
			input:    reflect.TypeOf(""),
			expected: framework.Schema{Type: framework.TypeString},
		},
		{
			name:     "bool",
			input:    reflect.TypeOf(true),
			expected: framework.Schema{Type: framework.TypeBoolean},
		},
		{
			name:     "int",
			input:    reflect.TypeOf(int(0)),
			expected: framework.Schema{Type: framework.TypeInteger},
		},
		{
			name:     "float",
			input:    reflect.TypeOf(float64(0)),
			expected: framework.Schema{Type: framework.TypeNumber},
		},
		{
			name:     "unsupported",
			input:    reflect.TypeOf(func() {}),
			expected: framework.Schema{Type: framework.TypeNull},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := util.TypeToJSONSchema(tc.input)
			if !reflect.DeepEqual(result, tc.expected) {
				t.Errorf("Expected %#v, but got %#v", tc.expected, result)
			}
		})
	}
}

func TestValueToMap(t *testing.T) {
	type nestedStruct struct {
		A int `mapstructure:"a"`
	}
	type recursiveStruct struct {
		B nestedStruct `mapstructure:"b"`
	}
	type args struct {
		v any
	}
	tests := []struct {
		name string
		args args
		want map[string]any
	}{
		{
			name: "nil",
			args: args{
				v: nil,
			},
			want: map[string]any{},
		},
		{
			name: "string",
			args: args{
				v: "str",
			},
			want: map[string]any{"value": "str"},
		},
		{
			name: "map",
			args: args{
				v: map[string]string{"key": "val"},
			},
			want: map[string]any{"key": "val"},
		},
		{
			name: "struct ptr",
			args: args{
				v: &struct {
					A int
					B int
					C struct {
						D int
					}
				}{A: 1, B: 2, C: struct{ D int }{D: 3}},
			},
			want: map[string]any{"A": 1, "B": 2, "C": map[string]any{"D": 3}},
		},
		{
			name: "recursive",
			args: args{
				v: recursiveStruct{B: nestedStruct{A: 1}},
			},
			want: map[string]any{"b": map[string]any{"a": 1}},
		},
		{
			name: "mapstructure",
			args: args{
				v: struct {
					A int `mapstructure:"a"`
					B int `mapstructure:"b"`
				}{A: 1, B: 2},
			},
			want: map[string]any{"a": 1, "b": 2},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := util.ValueToMap(tt.args.v); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ValueToMap() = %#v, want %#v", got, tt.want)
			}
		})
	}
}

type testTLSConfig struct {
	CertFile string
	KeyFile  string
}

type testConfig struct {
	Host            string
	Port            int
	Enabled         bool
	Timeout         *int
	TLS             *testTLSConfig
	Headers         map[string]string // Added Map
	Aliases         []string          // Added Slice
	unexportedField string            // Unexported field
}

func TestAssignStructNonZero_Values(t *testing.T) {
	// Define the argument structure for table tests
	type args struct {
		opts []testConfig // Use a slice of the concrete type T
	}

	// Define the test cases
	cases := []struct {
		name    string
		args    args
		want    testConfig // Expected result is a value of type T
		wantErr bool       // Whether an error is expected
		// wantErrMsg string // Optional: if checking specific error messages
	}{
		{
			name:    "no options provided",
			args:    args{opts: []testConfig{}},
			want:    testConfig{}, // Expect the zero value of the struct
			wantErr: false,
		},
		{
			name: "single option (all non-zero)",
			args: args{opts: []testConfig{
				{
					Host:    "one.com",
					Port:    80,
					Enabled: true,
					Timeout: lo.ToPtr(10),
					TLS:     lo.ToPtr(testTLSConfig{CertFile: "one.crt"}),
					Headers: map[string]string{"X-Req-ID": "123"},
					Aliases: []string{"one", "uno"},
				},
			}},
			want: testConfig{ // Expect the same values back
				Host:    "one.com",
				Port:    80,
				Enabled: true,
				Timeout: lo.ToPtr(10),
				TLS:     lo.ToPtr(testTLSConfig{CertFile: "one.crt"}),
				Headers: map[string]string{"X-Req-ID": "123"},
				Aliases: []string{"one", "uno"},
			},
			wantErr: false,
		},
		{
			name: "single option (some zero)",
			args: args{opts: []testConfig{
				{Host: "one.com", Port: 0, Enabled: false, Timeout: nil}, // Port, Enabled, Timeout are zero
			}},
			want: testConfig{ // Only Host should be set
				Host: "one.com",
			},
			wantErr: false,
		},
		{
			name: "basic merge with two options",
			args: args{opts: []testConfig{
				{Host: "a.com", Port: 1111, Enabled: true, Timeout: lo.ToPtr(5)},                        // Opt 1
				{Host: "b.com", Port: 0, Enabled: false, Timeout: lo.ToPtr(10), Aliases: []string{"b"}}, // Opt 2
			}},
			want: testConfig{
				Host:    "b.com",       // From Opt 2 (non-zero string)
				Port:    1111,          // From Opt 1 (Opt 2's Port 0 is zero)
				Enabled: true,          // From Opt 1 (Opt 2's Enabled false is zero)
				Timeout: lo.ToPtr(10),  // From Opt 2 (non-nil pointer)
				Aliases: []string{"b"}, // From Opt 2 (non-nil slice)
			},
			wantErr: false,
		},
		{
			name: "multiple options merge",
			args: args{opts: []testConfig{
				{Host: "a.com", Port: 1, Enabled: true, Headers: map[string]string{"h1": "v1"}}, // Opt 1
				{Host: "b.com", Port: 2, Aliases: []string{"b"}},                                // Opt 2
				{Host: "", Port: 3, Enabled: false, Headers: map[string]string{"h2": "v2"}},     // Opt 3
			}},
			want: testConfig{
				Host:    "b.com",                       // From Opt 2 (Opt 3's Host "" is zero)
				Port:    3,                             // From Opt 3 (non-zero int)
				Enabled: true,                          // From Opt 1 (Opt 3's Enabled false is zero)
				Headers: map[string]string{"h2": "v2"}, // From Opt 3 (non-nil map)
				Aliases: []string{"b"},                 // From Opt 2 (non-nil slice)
			},
			wantErr: false,
		},
		{
			name: "pointer field handling",
			args: args{opts: []testConfig{
				{Timeout: lo.ToPtr(10)},       // Opt 1: Set Timeout
				{Timeout: nil},                // Opt 2: Timeout is nil (zero), should not overwrite
				{Timeout: lo.ToPtr(20)},       // Opt 3: Set Timeout again
				{Host: "c.com", Timeout: nil}, // Opt 4: Timeout is nil (zero), should not overwrite Opt 3's value
			}},
			want: testConfig{
				Host:    "c.com",      // From Opt 4
				Timeout: lo.ToPtr(20), // From Opt 3, not overwritten by nil pointers later
			},
			wantErr: false,
		},
		{
			name: "nested struct pointer handling (replacement)",
			args: args{opts: []testConfig{
				{TLS: lo.ToPtr(testTLSConfig{CertFile: "a.crt", KeyFile: "a.key"})}, // Opt 1
				{TLS: lo.ToPtr(testTLSConfig{CertFile: "b.crt"})},                   // Opt 2: Replace TLS entirely
				{TLS: nil}, // Opt 3: TLS is nil (zero), should not overwrite
			}},
			want: testConfig{
				TLS: lo.ToPtr(testTLSConfig{CertFile: "b.crt"}), // From Opt 2, KeyFile is zero value in Opt 2's TLS
			},
			wantErr: false,
		},
		{
			name: "slice handling",
			args: args{opts: []testConfig{
				{Aliases: []string{"a1", "a2"}}, // Opt 1
				{Aliases: nil},                  // Opt 2: nil slice (zero), should not overwrite
				{Aliases: []string{"b1"}},       // Opt 3: non-nil slice, should overwrite
				{Aliases: []string{}},           // Opt 4: empty non-nil slice (IsZero=false), should overwrite!
			}},
			want: testConfig{
				Aliases: []string{}, // From Opt 4 (empty slice is NOT zero for IsZero())
			},
			wantErr: false,
		},
		{
			name: "map handling",
			args: args{opts: []testConfig{
				{Headers: map[string]string{"h1": "v1"}}, // Opt 1
				{Headers: nil},                           // Opt 2: nil map (zero), should not overwrite
				{Headers: map[string]string{"h2": "v2"}}, // Opt 3: non-nil map, should overwrite
				{Headers: map[string]string{}},           // Opt 4: empty non-nil map (IsZero=false), should overwrite!
			}},
			want: testConfig{
				Headers: map[string]string{}, // From Opt 4 (empty map is NOT zero for IsZero())
			},
			wantErr: false,
		},
		{
			name: "unexported field ignored",
			args: args{opts: []testConfig{
				{Host: "host1", unexportedField: "secret1"},
				{Port: 123, unexportedField: "secret2"},
			}},
			want: testConfig{ // Only exported fields should be merged
				Host: "host1",
				Port: 123,
				// unexportedField remains its zero value ("") because it cannot be set
			},
			wantErr: false,
		},
		{
			name: "all options contain only zero values",
			args: args{opts: []testConfig{
				{Host: "", Port: 0, Enabled: false, Timeout: nil},
				{TLS: nil, Headers: nil, Aliases: nil},
			}},
			want:    testConfig{}, // Expect zero value struct
			wantErr: false,
		},
	}

	// Run the test cases
	for _, tt := range cases {
		t.Run(tt.name, func(t *testing.T) {
			// Call the generic function, specifying the type parameter
			got, err := util.AssignStructNonZero[testConfig](tt.args.opts...)

			// Assertions using testify/assert
			if tt.wantErr {
				assert.Error(t, err, "AssignStructNonZero() expected an error, but got none")
			} else {
				assert.NoError(t, err, "AssignStructNonZero() returned an unexpected error: %v", err)
				// assert.Equal performs a deep comparison suitable for structs, slices, maps, pointers.
				assert.Equal(t, tt.want, got, "AssignStructNonZero() returned unexpected result")
			}
		})
	}
}

func TestParameters_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		expected iris.Parameters
	}{
		{
			name:     "simple integer conversion",
			jsonData: `{"count": 42, "negative": -10}`,
			expected: iris.Parameters{
				"count":    int(42),
				"negative": int(-10),
			},
		},
		{
			name:     "large integer conversion",
			jsonData: `{"large": 9223372036854775807, "small": 100}`,
			expected: iris.Parameters{
				"large": int(9223372036854775807), // On 64-bit systems, this fits in int
				"small": int(100),
			},
		},
		{
			name:     "float conversion",
			jsonData: `{"pi": 3.14159, "ratio": 2.5}`,
			expected: iris.Parameters{
				"pi":    float64(3.14159),
				"ratio": float64(2.5),
			},
		},
		{
			name:     "mixed types",
			jsonData: `{"string": "hello", "number": 123, "float": 4.56, "bool": true, "null": null}`,
			expected: iris.Parameters{
				"string": "hello",
				"number": int(123),
				"float":  float64(4.56),
				"bool":   true,
				"null":   nil,
			},
		},
		{
			name:     "nested map with numbers",
			jsonData: `{"config": {"timeout": 30, "retries": 3}, "metadata": {"version": 1.2}}`,
			expected: iris.Parameters{
				"config": map[string]any{
					"timeout": int(30),
					"retries": int(3),
				},
				"metadata": map[string]any{
					"version": float64(1.2),
				},
			},
		},
		{
			name:     "array with numbers",
			jsonData: `{"numbers": [1, 2, 3], "mixed": [10, 3.14, "text"]}`,
			expected: iris.Parameters{
				"numbers": []any{int(1), int(2), int(3)},
				"mixed":   []any{int(10), float64(3.14), "text"},
			},
		},
		{
			name:     "deeply nested structure",
			jsonData: `{"level1": {"level2": {"numbers": [42, 3.14], "config": {"port": 8080}}}}`,
			expected: iris.Parameters{
				"level1": map[string]any{
					"level2": map[string]any{
						"numbers": []any{int(42), float64(3.14)},
						"config": map[string]any{
							"port": int(8080),
						},
					},
				},
			},
		},
		{
			name:     "boundary values",
			jsonData: `{"maxInt": 9223372036854775807, "minInt": -9223372036854775808, "regularInt": 2147483647}`,
			expected: iris.Parameters{
				"maxInt":     int(9223372036854775807),  // On 64-bit systems, this fits in int
				"minInt":     int(-9223372036854775808), // On 64-bit systems, this fits in int
				"regularInt": int(2147483647),
			},
		},
		{
			name:     "zero and negative values",
			jsonData: `{"zero": 0, "negativeFloat": -1.5, "negativeInt": -999}`,
			expected: iris.Parameters{
				"zero":          int(0),
				"negativeFloat": float64(-1.5),
				"negativeInt":   int(-999),
			},
		},
		{
			name:     "empty structures",
			jsonData: `{"emptyMap": {}, "emptyArray": []}`,
			expected: iris.Parameters{
				"emptyMap":   map[string]any{},
				"emptyArray": []any{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var params iris.Parameters
			err := json.Unmarshal([]byte(tt.jsonData), &params)
			if err != nil {
				t.Fatalf("UnmarshalJSON failed: %v", err)
			}

			assert.Equal(t, params, tt.expected)
		})
	}
}

func TestConvertJSONNumber(t *testing.T) {
	tests := []struct {
		name     string
		number   json.Number
		expected any
	}{
		{
			name:     "small positive integer",
			number:   json.Number("42"),
			expected: int(42),
		},
		{
			name:     "small negative integer",
			number:   json.Number("-10"),
			expected: int(-10),
		},
		{
			name:     "zero",
			number:   json.Number("0"),
			expected: int(0),
		},
		{
			name:     "max int value",
			number:   json.Number("2147483647"),
			expected: int(2147483647),
		},
		{
			name:     "min int value",
			number:   json.Number("-2147483648"),
			expected: int(-2147483648),
		},
		{
			name:     "large positive integer",
			number:   json.Number("9223372036854775807"),
			expected: int(9223372036854775807), // On 64-bit systems, this fits in int
		},
		{
			name:     "large negative integer",
			number:   json.Number("-9223372036854775808"),
			expected: int(-9223372036854775808), // On 64-bit systems, this fits in int
		},
		{
			name:     "positive float",
			number:   json.Number("3.14159"),
			expected: float64(3.14159),
		},
		{
			name:     "negative float",
			number:   json.Number("-2.718"),
			expected: float64(-2.718),
		},
		{
			name:     "scientific notation",
			number:   json.Number("1.23e10"),
			expected: float64(1.23e10),
		},
		{
			name:     "invalid number string",
			number:   json.Number("not-a-number"),
			expected: "not-a-number",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := util.ConvertJSONNumbers(tt.number)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertNumbers_EdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected any
	}{
		{
			name:     "nil value",
			input:    nil,
			expected: nil,
		},
		{
			name:     "string value",
			input:    "hello world",
			expected: "hello world",
		},
		{
			name:     "boolean value",
			input:    true,
			expected: true,
		},
		{
			name: "map with json.Number",
			input: map[string]any{
				"num": json.Number("123"),
				"str": "text",
			},
			expected: map[string]any{
				"num": int(123),
				"str": "text",
			},
		},
		{
			name: "slice with json.Number",
			input: []any{
				json.Number("456"),
				"text",
				json.Number("7.89"),
			},
			expected: []any{
				int(456),
				"text",
				float64(7.89),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Make a copy for testing since convertNumbers modifies in place
			var input any
			if tt.input != nil {
				switch v := tt.input.(type) {
				case map[string]any:
					input = make(map[string]any)
					for k, val := range v {
						input.(map[string]any)[k] = val
					}
				case []any:
					input = make([]any, len(v))
					copy(input.([]any), v)
				default:
					input = tt.input
				}
			}

			result := util.ConvertJSONNumbers(input)

			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestParameters_MarshalJSON_Integration(t *testing.T) {
	// Test that marshal and unmarshal work together properly
	original := iris.Parameters{
		"integer": 42,
		"float":   3.14159,
		"string":  "hello",
		"nested": map[string]any{
			"count": 100,
			"ratio": 2.5,
		},
		"array": []any{1, 2.718, "world"},
	}

	// Marshal to JSON
	data, err := json.Marshal(original)
	if err != nil {
		t.Fatalf("Marshal failed: %v", err)
	}

	// Unmarshal back
	var result iris.Parameters
	err = json.Unmarshal(data, &result)
	if err != nil {
		t.Fatalf("Unmarshal failed: %v", err)
	}

	// Verify types are converted correctly
	assert.Equal(t, result["integer"], int(42))
	assert.Equal(t, result["float"], float64(3.14159))
	assert.Equal(t, result["nested"], map[string]any{
		"count": int(100),
		"ratio": float64(2.5),
	})
}

func TestIntRangeBoundary(t *testing.T) {
	tests := []struct {
		name         string
		jsonData     string
		key          string
		expectedType string
	}{
		{
			name:         "exactly max int",
			jsonData:     `{"value": 9223372036854775807}`,
			key:          "value",
			expectedType: "int", // On 64-bit systems, this fits in int
		},
		{
			name:         "regular int value",
			jsonData:     `{"value": 2147483647}`,
			key:          "value",
			expectedType: "int",
		},
		{
			name:         "regular negative int",
			jsonData:     `{"value": -2147483648}`,
			key:          "value",
			expectedType: "int",
		},
		{
			name:         "large positive int64",
			jsonData:     `{"value": 9223372036854775808}`,
			key:          "value",
			expectedType: "float64", // > max int64, will be converted to float64
		},
		{
			name:         "large negative int64",
			jsonData:     `{"value": -9223372036854775809}`,
			key:          "value",
			expectedType: "float64", // < min int64, will be converted to float64
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var params iris.Parameters
			err := json.Unmarshal([]byte(tt.jsonData), &params)
			if err != nil {
				t.Fatalf("UnmarshalJSON failed: %v", err)
			}

			value := params[tt.key]
			assert.Equal(t, tt.expectedType, reflect.TypeOf(value).String())
		})
	}
}
