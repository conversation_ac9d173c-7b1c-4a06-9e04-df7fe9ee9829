package util

import (
	"encoding/json"
	"fmt"
	"math"
	"reflect"
	"strconv"
	"strings"

	"code.byted.org/devgpt/kiwis/lib/mapstructure"

	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

func TypeToJSONSchema(t reflect.Type) framework.Schema {
	schema := framework.Schema{}

	switch t.Kind() {
	case reflect.Struct:
		schema.Type = framework.TypeObject
		schema.Properties = make(map[string]framework.Schema)
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			if field.Anonymous {
				continue // Skip embedded fields
			}
			fieldSchema := TypeToJSONSchema(field.Type)
			jsonTag := field.Tag.Get("json")
			if jsonTag == "-" {
				continue // Skip fields with json:"-" tag
			}
			fieldSchema.Description = field.Tag.Get("description")
			if field.Tag.Get("default") != "" {
				fieldSchema.Default = field.Tag.Get("default")
			}
			name := strings.Split(jsonTag, ",")[0]
			if name == "" {
				name = field.Name
			}
			schema.Properties[name] = fieldSchema
			if !strings.Contains(jsonTag, "omitempty") {
				schema.Required = append(schema.Required, name)
			}
		}
	case reflect.Slice, reflect.Array:
		schema.Type = framework.TypeArray
		itemSchema := TypeToJSONSchema(t.Elem())
		schema.Items = &itemSchema
	case reflect.Map:
		schema.Type = framework.TypeObject
		// For maps, we can't determine properties in advance
	case reflect.String:
		schema.Type = framework.TypeString
	case reflect.Bool:
		schema.Type = framework.TypeBoolean
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		schema.Type = framework.TypeInteger
	case reflect.Float32, reflect.Float64:
		schema.Type = framework.TypeNumber
	case reflect.Ptr:
		return TypeToJSONSchema(t.Elem())
	default:
		// For unsupported types, we'll use "null" as a fallback
		schema.Type = framework.TypeNull
	}

	return schema
}

// ValueToMap recursively converts a struct value to a map
// if the value is not a struct, it will return the {"value": value} map
// returns empty map if the value is nil or failed to convert
func ValueToMap(v any) (result map[string]any) {
	result = make(map[string]any)
	if v == nil {
		return
	}

	var err error
	if reflect.TypeOf(v).Kind() == reflect.Ptr {
		v = reflect.ValueOf(v).Elem().Interface()
	}
	decoder, err := mapstructure.NewDecoder(&mapstructure.DecoderConfig{
		DecodeHook: mapstructure.RecursiveStructToMapHookFunc(),
		Result:     &result,
	})
	if err != nil {
		return
	}

	err = decoder.Decode(v)
	if err != nil {
		return map[string]any{"value": v}
	}
	return result
}

// AssignStructNonZero merges fields from one or more source structs (opts) into a destination struct (base).
// CAUTION: zero values will not override non-zero values.
func AssignStructNonZero[T any](opts ...T) (T, error) {
	// 1. Prepare a zero value of type T for potential return
	var zero T

	// 2. Get the reflect.Type for T using the zero value trick, which works even for interfaces.
	//    Then validate that T is actually a struct type.
	//    Using TypeOf(zero) might fail if T is an interface. Pointer trick is more robust.
	rt := reflect.TypeOf((*T)(nil)).Elem()
	if rt.Kind() != reflect.Struct {
		return zero, fmt.Errorf("type T must be a struct type, but got %v", rt.Kind())
	}

	// 3. Handle the case where no options are provided.
	//    Return the zero value of the struct type T.
	if len(opts) == 0 {
		return zero, nil // zero is already correctly typed as T
	}

	// 4. Create the result struct instance.
	//    We need an addressable value to set fields via reflection.
	//    reflect.New creates a pointer to a zero value of type T (*T).
	//    .Elem() dereferences the pointer to get the actual struct value (T), which remains addressable.
	resultPtr := reflect.New(rt)   // Creates *T (pointer to zero T)
	resultElem := resultPtr.Elem() // Gets the addressable T value

	// 5. Iterate through the provided option structs (values)
	for i, opt := range opts {
		optVal := reflect.ValueOf(opt) // Get reflect.Value of the current struct T

		// Quick sanity check: Ensure the concrete type matches (should be guaranteed by generics).
		// Using rt (derived from TypeOf((*T)(nil)).Elem()) is crucial here,
		// especially if T could be an interface wrapping a struct.
		if optVal.Type() != rt {
			// This might happen if T is an interface and different opts contain structs
			// that implement T but are not the *same* concrete struct type used for rt.
			// However, the assignment logic below relies on field indices matching,
			// which requires the same concrete struct type. Let's enforce this.
			return zero, fmt.Errorf("mismatched concrete types in opts: expected %v, but option %d has type %v", rt, i, optVal.Type())
		}

		// 6. Iterate through the fields defined by the struct type T
		numFields := rt.NumField()
		for j := 0; j < numFields; j++ {
			optField := optVal.Field(j)

			// 7. Check if the option field is non-zero according to Go's definition
			if !optField.IsZero() {
				// 8. Get the corresponding field in the result struct
				resultField := resultElem.Field(j)

				// 9. Check if the result field is settable (i.e., it's exported)
				//    We check CanSet on the result field, which reflects the struct definition.
				if resultField.CanSet() {

					// 10. Double-check type compatibility (mostly defense-in-depth with generics)
					if resultField.Type() == optField.Type() {
						// 11. Assign the non-zero value from the option field to the result field
						resultField.Set(optField)
					} else {
						// This is highly unlikely given the T constraint and type checks,
						// but could theoretically occur with complex embedding or type aliases.
						fieldName := rt.Field(j).Name // Get field name for clarity
						return zero, fmt.Errorf("internal error: type mismatch for field %q despite checks: result field type %v, option field type %v",
							fieldName, resultField.Type(), optField.Type())
					}
				}
				// If resultField.CanSet() is false (unexported field), we silently skip it.
			}
		}
	}

	// 12. Return the populated result struct value.
	//     resultElem is the reflect.Value holding the final state.
	//     .Interface() converts it back to an interface{} containing the struct value.
	//     .(T) asserts it back to the specific type T.
	return resultElem.Interface().(T), nil
}

// ConvertJSONNumbers recursively converts json.Number values to int or int64 based on their value range
// value: a json value, parsed with decoder.UseNumber()
func ConvertJSONNumbers(value any) any {
	switch v := value.(type) {
	case json.Number:
		return convertJSONNumber(v)
	case map[string]any:
		for key, val := range v {
			if num, ok := val.(json.Number); ok {
				v[key] = convertJSONNumber(num)
			} else {
				ConvertJSONNumbers(val)
			}
		}
	case []any:
		for i, val := range v {
			if num, ok := val.(json.Number); ok {
				v[i] = convertJSONNumber(num)
			} else {
				ConvertJSONNumbers(val)
			}
		}
	case any:
		rv := reflect.ValueOf(value)
		if rv.Kind() == reflect.Slice || rv.Kind() == reflect.Array {
			for i := 0; i < rv.Len(); i++ {
				elem := rv.Index(i)
				if elem.CanInterface() {
					ConvertJSONNumbers(elem.Interface())
				}
			}
		} else if rv.Kind() == reflect.Map {
			for _, key := range rv.MapKeys() {
				val := rv.MapIndex(key)
				if val.CanInterface() {
					ConvertJSONNumbers(val.Interface())
				}
			}
		}
	}
	return value
}

func convertJSONNumber(num json.Number) any {
	if i64, err := num.Int64(); err == nil {
		if i64 >= math.MinInt && i64 <= math.MaxInt {
			return int(i64) // keep type int if it fits in int range
		}
		return i64
	}
	if f64, err := strconv.ParseFloat(string(num), 64); err == nil {
		return f64
	}
	return string(num)
}
