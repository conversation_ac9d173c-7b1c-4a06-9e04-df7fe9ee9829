package server

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-git/go-git/v5/config"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	runtimeentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/port/codebase"
	"code.byted.org/devgpt/kiwis/port/nextcode"
)

const (
	RepoPath = ""

	WikiRepoPath          = "/.wiki"
	WikiDefaultBranch     = "master"
	DefaultConfigFilePath = ".wiki/config.yaml"

	WikiGenCommitIDFooterKey   = "Wiki-Gen-Commit-Id"
	WikiOverwriteCommitMessage = "Create Wiki Content. Generated by Aime-DeepWiki"
)

const (
	codebaseAPIBaseURL = "https://codebase-api.byted.org"
	nextCodeAPIBaseURL = "https://codebase-api.byted.org/v2/"
)

func newNextCodeClient(run *iris.AgentRunContext) (nextcode.Client, error) {
	return nextcode.NewClient(nextCodeAPIBaseURL, run.GetEnv(runtimeentity.RuntimeDeepWikiNextCodeAppID), run.GetEnv(runtimeentity.RuntimeDeepWikiNextCodeSecret), time.Second*15, "")
}

func newCodebaseClient(run *iris.AgentRunContext) (codebase.Client, error) {
	baseURL := lo.Ternary(run.GetEnv(runtimeentity.RuntimeEnvironCodebaseAPIBaseURL) != "", run.GetEnv(runtimeentity.RuntimeEnvironCodebaseAPIBaseURL), codebaseAPIBaseURL)
	return codebase.NewClient(baseURL, run.GetEnv(runtimeentity.RuntimeDeepWikiCodebaseServiceJWT), time.Second*15)
}

type CloneRepoInputs struct {
	RepoName   string `json:"repo_name" mapstructure:"repo_name"`
	BaseCommit string `json:"base_commit" mapstructure:"base_commit"`
}

type cloneRepoOutputs struct{}

// cloneRepo wraps workspace.CloneRepository with pretty printing
// and we don't make it an independent tool since it may expose git credentials in inputs to the user
func CloneRepo(run *iris.AgentRunContext, inputs CloneRepoInputs) (*cloneRepoOutputs, error) {
	repoPath := "." + RepoPath
	ws, term := workspace.GetWorkspace(run), workspace.GetTerminal(run, "")
	if strings.HasPrefix(inputs.RepoName, "https://") {
		term.PrintCommandPrompt(exec.Command("outsource", "clone", inputs.RepoName, inputs.BaseCommit))
		_, err := workspace.CloneRepository(run, workspace.CloneOption{
			Directory: repoPath,
			URL:       inputs.RepoName,
			Checkout:  inputs.BaseCommit,
			Shallow:   true,
			Terminal:  term,
			Progress:  false,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to clone repo")
		}
	} else {
		codebaseCli, err := newCodebaseClient(run)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create codebase client")
		}

		repoInfo, err := codebaseCli.GetRepo(run, inputs.RepoName)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get repo info")
		}

		// get clone token
		codebaseJWT, err := codebaseCli.SignCodebaseJWTByServiceJWT(run, run.GetEnv(runtimeentity.RuntimeDeepWikiCodebaseServiceJWT), []string{"repos.contents:fetch"})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to sign codebase jwt")
		}

		gitUrl := "<EMAIL>"
		gitPlatform := "gitlab"
		if strings.Contains(repoInfo.Platform, "gerrit") {
			gitUrl = "<EMAIL>"
			gitPlatform = "gerrit"
			run.GetLogger().Infof("gerrit repo detected: %s %s", inputs.RepoName, repoInfo.GitURL)
		}

		term.PrintCommandPrompt(exec.Command("codebase", "clone", inputs.RepoName, inputs.BaseCommit))
		_, err = workspace.CloneRepository(run, workspace.CloneOption{
			Directory: repoPath,
			URL:       fmt.Sprintf("%s:%s", gitUrl, inputs.RepoName),
			Checkout:  inputs.BaseCommit,
			Shallow:   true,
			Terminal:  term,
			Progress:  false,
		}.WithSSHCredential(gitPlatform, codebaseJWT))
		if err != nil {
			return nil, errors.WithMessage(err, "failed to clone repo")
		}
	}
	ws.AddRepository(repoPath)
	return &cloneRepoOutputs{}, nil
}

type CloneWikiRepoInputs struct {
	RepoName string `json:"repo_name" mapstructure:"repo_name"`
}

// CloneWikiRepo
func CloneWikiRepo(run *iris.AgentRunContext, inputs CloneWikiRepoInputs) (*cloneRepoOutputs, error) {
	repoPath := "." + WikiRepoPath
	var err error
	term := workspace.GetTerminal(run, "")

	codebaseCli, err := newCodebaseClient(run)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create codebase client")
	}

	// get clone token
	codebaseJWT, err := codebaseCli.SignCodebaseJWTByServiceJWT(run, run.GetEnv(runtimeentity.RuntimeDeepWikiCodebaseServiceJWT), []string{"repos.contents:fetch", "repo.wiki:write"})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to sign codebase jwt")
	}

	gitUrl := "<EMAIL>"

	repoURL := fmt.Sprintf("%s:%s.wiki", gitUrl, inputs.RepoName)
	cmd := exec.Command("git", "clone", repoURL, repoPath)
	cloneResult, err := term.ExecuteCmd(cmd, workspace.ExecuteCmdOption{
		Environment: map[string]string{
			"GIT_SSH_COMMAND":     fmt.Sprintf("sshpass -p '%s' ssh -o StrictHostKeyChecking=no", codebaseJWT),
			"GIT_TRACE":           "2",
			"GIT_AUTHOR_NAME":     "aime-deepwiki",
			"GIT_AUTHOR_EMAIL":    "<EMAIL>",
			"GIT_COMMITTER_NAME":  "aime-deepwiki",
			"GIT_COMMITTER_EMAIL": "<EMAIL>",
		},
	})
	if err != nil {
		return nil, iris.NewRecoverable(errors.WithMessagef(err, "failed to clone wiki repo: %s", cloneResult))
	}

	run.GetLogger().Infof("successfully cloned wiki repo: %s", repoPath)

	return &cloneRepoOutputs{}, nil
}

type CommitAndPushWikiRepoInputs struct {
	RepoName string `json:"repo_name" mapstructure:"repo_name"`
	CommitID string `json:"commit_id" mapstructure:"commit_id"`
}

type pushRepoOutputs struct{}

// CommitAndPushWikiRepo
func CommitAndPushWikiRepo(run *iris.AgentRunContext, inputs CommitAndPushWikiRepoInputs) (*pushRepoOutputs, error) {
	repoPath := "." + WikiRepoPath
	_, term := workspace.GetWorkspace(run), workspace.GetTerminal(run, "")

	// refs/heads/<source>:refs/heads/<target>
	refspec := config.RefSpec(fmt.Sprintf("refs/heads/%s:refs/heads/%s", WikiDefaultBranch, WikiDefaultBranch))
	if refspec.Validate() != nil {
		return nil, iris.NewRecoverable(fmt.Errorf("invalid refspec: '%s', example: 'refs/heads/<branch-name>:refs/heads/<branch-name>' or 'refs/tags/<tag-name>:refs/tags/<tag-name>' or '<commit-sha>:refs/heads/<branch-name>'", WikiDefaultBranch))
	}

	// git -C ./.wiki .
	cmd := exec.Command("git", "-C", repoPath, "add", ".")
	err := cmd.Run()
	if err != nil {
		return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to add files to the commit"))
	}

	cmd = exec.Command("git", "-C", repoPath, "commit", "-m", buildCommitMessage(inputs.CommitID))
	commitResult, err := term.ExecuteCmd(cmd, workspace.ExecuteCmdOption{
		Environment: map[string]string{
			"GIT_AUTHOR_NAME":     "aime-deepwiki",
			"GIT_AUTHOR_EMAIL":    "<EMAIL>",
			"GIT_COMMITTER_NAME":  "aime-deepwiki",
			"GIT_COMMITTER_EMAIL": "<EMAIL>",
		},
	})
	if err != nil {
		return nil, iris.NewRecoverable(errors.WithMessagef(err, "failed to commit %s", commitResult))
	}

	run.GetLogger().Infof("successfully committed: %s", commitResult)

	codebaseCli, err := newCodebaseClient(run)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create codebase client")
	}

	// get clone token
	codebaseJWT, err := codebaseCli.SignCodebaseJWTByServiceJWT(run, run.GetEnv(runtimeentity.RuntimeDeepWikiCodebaseServiceJWT), []string{"repos.contents:fetch", "repo.wiki:write"})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to sign codebase jwt")
	}

	cmd = exec.Command("git", "-C", repoPath, "push", "origin", refspec.String())
	pushResult, err := term.ExecuteCmd(cmd, workspace.ExecuteCmdOption{
		Environment: map[string]string{
			"GIT_SSH_COMMAND":     fmt.Sprintf("sshpass -p '%s' ssh -o StrictHostKeyChecking=no", codebaseJWT),
			"GIT_TRACE":           "2",
			"GIT_AUTHOR_NAME":     "aime-deepwiki",
			"GIT_AUTHOR_EMAIL":    "<EMAIL>",
			"GIT_COMMITTER_NAME":  "aime-deepwiki",
			"GIT_COMMITTER_EMAIL": "<EMAIL>",
		},
	})
	if err != nil {
		return nil, iris.NewRecoverable(errors.WithMessagef(err, "failed to push %s %s", refspec, pushResult))
	}

	run.GetLogger().Infof("successfully pushed: %s", pushResult)

	return &pushRepoOutputs{}, nil
}

type UploadWikiContentInputs struct {
	RepoName string
	Content  map[string]string
	CommitID string
}

func OverwriteWikiContent(run *iris.AgentRunContext, inputs UploadWikiContentInputs) error {
	repoPath := "." + WikiRepoPath

	// 确保 .wiki 目录存在
	if _, err := os.Stat(repoPath); os.IsNotExist(err) {
		return iris.NewRecoverable(errors.New("wiki repository not found, please clone wiki repo first"))
	}

	// 清理 . 目录下的所有文件(除了 .wiki 目录)
	if err := CleanWikiDirectory(".", []string{WikiRepoPath, ".wiki"}); err != nil {
		return iris.NewRecoverable(errors.WithMessagef(err, "failed to clean wiki directory %s", repoPath))
	}

	// 移除 .wiki 目录下的所有文件(除了 .git 目录)
	if err := CleanWikiDirectory(repoPath, []string{".git"}); err != nil {
		return iris.NewRecoverable(errors.WithMessagef(err, "failed to clean wiki directory %s", repoPath))
	}

	// 批量写入文件内容
	for filePath, content := range inputs.Content {
		// replace %
		filePath = strings.ReplaceAll(filePath, "%", "")
		safeFilePath := filePath

		if strings.Contains(filePath, ".wiki/") {
			safeFilePath = filePath
		} else if !strings.HasPrefix(filePath, "en/") && !strings.HasPrefix(filePath, "zh/") {
			safeFilePath = strings.ReplaceAll(filePath, "/", "%2F")
		} else {
			// 新版
			splits := strings.Split(filePath, "/")
			// en/xxxx.md
			if len(splits) <= 2 {
				safeFilePath = filePath
			} else {
				// en/CI/CD.md
				safeFilePath = filepath.Join(splits[0], strings.ReplaceAll(strings.Join(splits[1:], "/"), "/", "%2F"))
			}
		}
		fullPath := filepath.Join(repoPath, safeFilePath)

		// 确保目录存在
		dir := filepath.Dir(fullPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return iris.NewRecoverable(errors.WithMessagef(err, "failed to create directory %s", dir))
		}

		// 写入文件内容
		if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
			return iris.NewRecoverable(errors.WithMessagef(err, "failed to write file %s", fullPath))
		}

		run.GetLogger().Infof("successfully wrote file: %s", filePath)
	}

	// 提交和推送更改
	if _, err := CommitAndPushWikiRepo(run, CommitAndPushWikiRepoInputs{
		RepoName: inputs.RepoName,
		CommitID: inputs.CommitID,
	}); err != nil {
		return errors.WithMessage(err, "failed to commit and push wiki content")
	}

	return nil
}

// CleanWikiDirectory removes all files and directories in wiki directory except .git
func CleanWikiDirectory(wikiPath string, exceptPaths []string) error {
	entries, err := os.ReadDir(wikiPath)
	if err != nil {
		return errors.WithMessagef(err, "failed to read wiki directory %s", wikiPath)
	}

	for _, entry := range entries {
		// 跳过 .git 目录
		if lo.Contains(exceptPaths, entry.Name()) {
			continue
		}

		fullPath := filepath.Join(wikiPath, entry.Name())
		if err := os.RemoveAll(fullPath); err != nil {
			return errors.WithMessagef(err, "failed to remove %s", fullPath)
		}
	}

	return nil
}

func buildCommitMessage(commitID string) string {
	return fmt.Sprintf("%s\n%s: %s", WikiOverwriteCommitMessage, WikiGenCommitIDFooterKey, commitID)
}

func ReadTemplateFromWikiRepo(run *iris.AgentRunContext) (string, error) {
	repoPath := "." + WikiRepoPath

	// 确保 .wiki 目录存在
	if _, err := os.Stat(repoPath); os.IsNotExist(err) {
		return "", iris.NewRecoverable(errors.New("wiki repository not found, please clone wiki repo first"))
	}

	// 读取 DefaultConfigFilePath 文件
	filePath := filepath.Join(repoPath, DefaultConfigFilePath)

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", iris.NewRecoverable(errors.WithMessagef(err, "failed to read file %s", filePath))
	}

	return string(content), nil
}
