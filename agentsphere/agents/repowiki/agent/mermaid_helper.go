package wiki_agent

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/repowiki/prompts"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

func CheckAndFixMermaid(run *iris.AgentRunContext, content string) string {
	// Create a temporary directory for output
	tempDir, err := os.MkdirTemp("", "mermaid-check")
	if err != nil {
		return content
	}
	defer os.RemoveAll(tempDir) // Clean up on function exit

	configContent := `{"args": ["--no-sandbox", "--disable-setuid-sandbox"]}`
	configFile := filepath.Join(tempDir, "puppeteer-config.json")
	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		return content
	}

	// 1. Extract mermaid code blocks from the markdown content
	originalMermaidCodes := extractMermaidCode(content)
	// Keep a copy of the truly original codes for replacement
	trulyOriginalMermaidCodes := make([]string, len(originalMermaidCodes))
	copy(trulyOriginalMermaidCodes, originalMermaidCodes)

	fixedMermaidCodes := make([]string, len(originalMermaidCodes))

	// First, try to fix common markdown list issues automatically
	for i, code := range originalMermaidCodes {
		originalMermaidCodes[i] = fixMermaidMarkdownLists(code)
	}

	// 2. Check the syntax of each mermaid code block
	errorCount := 0
	fixedCount := 0
	for i, code := range originalMermaidCodes {
		err := checkMermaidSyntax(run, tempDir, code, configFile, false)
		if err != nil {
			run.GetLogger().Errorf("mermaid syntax error: code: %s, error: %s", code, err)
			errorCount++
			// 3. If the syntax is wrong, try to fix it using LLM
			fixedCode, err := fixMermaid(run, tempDir, configFile, code, err.Error())
			if err != nil {
				run.GetLogger().Errorf("failed to fix mermaid code: %s", err)
				continue
			}
			fixedMermaidCodes[i] = fixedCode
			fixedCount++
		}
	}

	run.GetLogger().Infof("mermaid code check and fix completed, total %d codes, found %d errors, fixed %d codes", len(originalMermaidCodes), errorCount, fixedCount)

	// 4. Replace the original mermaid code blocks with the fixed ones in the markdown content
	for i, originalCode := range trulyOriginalMermaidCodes {
		if fixedMermaidCodes[i] != "" {
			content = strings.Replace(content, originalCode, fixedMermaidCodes[i], 1)
		}
	}

	return content

}

// extractMermaidCode extracts mermaid code from markdown
// returns a list of mermaid code blocks in the order they appear in the markdown
func extractMermaidCode(markdown string) []string {
	var results []string

	// Regular expression to match mermaid code blocks
	// Supports two common formats:
	// Format 1: ```mermaid\n...\n```
	reMermaidFormat := regexp.MustCompile("```mermaid\n((?:.|\n)+?)```")
	matches := reMermaidFormat.FindAllStringSubmatch(markdown, -1)

	for _, match := range matches {
		if len(match) >= 2 {
			code := strings.TrimSpace(match[1])
			results = append(results, code)
		}
	}
	return results
}

// checkMermaidSyntax check mermaid syntax
// return the error message if syntax is wrong
// use mmdc to check the syntax， the mmdc is alread installed
// only need check the syntax, put the generated image in a temp folder and delete it if its success
func checkMermaidSyntax(run *iris.AgentRunContext, tempDir, code, configFile string, isLocal bool) error {
	logger := run.GetLogger()

	inputFile := filepath.Join(tempDir, "input.mmd")
	if err := os.WriteFile(inputFile, []byte(code), 0644); err != nil {
		return fmt.Errorf("failed to write temporary mermaid file: %w", err)
	}

	var result string
	if isLocal {
		cmd := exec.Command("mmdc", "-q", "-i", inputFile, "-p", configFile)
		output, err := cmd.CombinedOutput()
		result, sytaxErr := extractSyntaxError(string(output), err)
		if sytaxErr != nil {
			return fmt.Errorf("mermaid syntax error:\n%s", result)
		}
	} else {
		terminal := workspace.GetTerminal(run, "")
		cmd := exec.CommandContext(run, "mmdc", "-q", "-i", inputFile, "-p", configFile)
		output, err := terminal.ExecuteCmd(cmd, workspace.ExecuteCmdOption{MaxDisplayLines: 1000, Timeout: 30000})
		logger.Infof("mmdc output: %s", output)
		result, sytaxErr := extractSyntaxError(output, err)
		logger.Infof("mmdc result: %s", result)
		if sytaxErr != nil {
			logger.Errorf("failed to execute command: %s", sytaxErr)
			return fmt.Errorf("mermaid syntax error:\n%s", result)
		}
	}

	if strings.TrimSpace(result) != "" {
		logger.Errorf("mmdc output: %s", result)
		return fmt.Errorf("mermaid syntax error: %s", result)
	}

	return nil
}

// extractSyntaxError extracts the relevant syntax error message from the full mmdc error output
// It ignores the stack trace and other unnecessary information
func extractSyntaxError(fullError string, err error) (string, error) {
	// 如果 error 包含 [@zenuml/core] Store is a function and is not initiated in 1 second.
	// https://github.com/mermaid-js/zenuml-core/issues/169
	// 则不返回错误
	if strings.Contains(fullError, "[@zenuml/core]") {
		return "", nil
	}

	if strings.Contains(fullError, "Error:") {
		lines := strings.Split(fullError, "\n")
		var relevantLines []string

		for _, line := range lines {
			// Skip empty lines
			if strings.TrimSpace(line) == "" {
				continue
			}

			// skip mermaid-js trace error
			if strings.Contains(line, "mermaid-js") {
				continue
			}

			relevantLines = append(relevantLines, line)
		}

		return strings.Join(relevantLines, "\n"), err
	}

	return fullError, err
}

// fixMermaidMarkdownLists automatically fixes markdown list syntax in mermaid code
// Replaces unsupported markdown list syntax with supported alternatives
// this is a workaround for https://github.com/mermaid-js/mermaid/issues/6099
//
//	"- item" → "• item"
//	"* item" → "• item"
//	"1. item" → "1-item"
func fixMermaidMarkdownLists(code string) string {
	lines := strings.Split(code, "\n")
	fixedLines := make([]string, len(lines))

	for i, line := range lines {
		// Use regex to find and replace markdown list patterns anywhere in the line
		// Fix unordered lists: - item or * item → • item
		// Ensure we don't match mermaid syntax like "--" by checking for space after
		//
		// This pattern matches:
		// - \s: space
		// - ^: start of line
		// - \[": left bracket + quote (for node labels like A["- item"])
		// - ": quote (for quoted text in labels)
		// - \(: left parenthesis (for round nodes like A("- item"))
		// - \{: left brace (for diamond nodes like A{"- item"})
		// - \{": left brace + quote (for diamond nodes like A{"- item"})
		// - \|: pipe (for connection labels like A-->|"- item"|B)
		// - =: equals (for thick connections like A=="- item"==>B)
		// - \[: left bracket (for rectangular nodes like A[- item])
		// - \(\(: double parenthesis (for circle nodes like A(("- item")))
		// - \[\[: double bracket (for subroutine nodes like A[["- item"]])
		// - \{\{: double brace (for hexagon nodes like A{{"- item"}})
		// - \[\(: bracket + parenthesis (for database nodes like A[("- item")])
		// - \[/: bracket + slash (for parallelogram nodes like A[/"- item"/])
		// - \[\\: bracket + backslash (for parallelogram alt nodes like A[\"- item"\])
		// - >: greater than (for asymmetric nodes like A>"- item"])
		line = regexp.MustCompile(`(\s|^|\["|"|\(|\{|\{"|\||=|\[|\(\(|\[\[|\{\{|\[\(|\[/|\[\\|>)- `).ReplaceAllString(line, "${1}• ")
		line = regexp.MustCompile(`(\s|^|\["|"|\(|\{|\{"|\||=|\[|\(\(|\[\[|\{\{|\[\(|\[/|\[\\|>)\* `).ReplaceAllString(line, "${1}• ")

		// Fix ordered lists: 1. item → 1-item
		// Match digit(s) followed by period and space
		// Extended to cover all possible Mermaid node shapes and connection labels:
		// - \s: space
		// - ^: start of line
		// - \[": left bracket + quote (for rectangular nodes like A["1. item"])
		// - ": quote (for quoted text)
		// - \(: left parenthesis (for round nodes like A("1. item"))
		// - \{: left brace (for diamond nodes like A{"1. item"})
		// - \{": left brace + quote (for diamond nodes like A{"1. item"})
		// - \|: pipe (for connection labels like A-->|"1. item"|B)
		// - =: equals (for thick connections like A=="1. item"==>B)
		// - \[: left bracket (for rectangular nodes like A[1. item])
		// - \(\(: double parenthesis (for circle nodes like A(("1. item")))
		// - \[\[: double bracket (for subroutine nodes like A[["1. item"]])
		// - \{\{: double brace (for hexagon nodes like A{{"1. item"}})
		// - \[\(: bracket + parenthesis (for database nodes like A[("1. item")])
		// - \[/: bracket + slash (for parallelogram nodes like A[/"1. item"/])
		// - \[\\: bracket + backslash (for parallelogram alt nodes like A[\"1. item"\])
		// - >: greater than (for asymmetric nodes like A>"1. item"])
		line = regexp.MustCompile(`(\s|^|\["|"|\(|\{|\{"|\||=|\[|\(\(|\[\[|\{\{|\[\(|\[/|\[\\|>)(\d+)\.\s`).ReplaceAllString(line, "${1}${2}-")

		fixedLines[i] = line
	}

	return strings.Join(fixedLines, "\n")
}

// fixMermaid fix mermaid using LLM with retry mechanism
func fixMermaid(run *iris.AgentRunContext, tempDir, configFile, code, errMsg string) (string, error) {
	const maxRetries = 5
	llm := run.GetLLM()
	modelConfig := run.GetConfig().GetModelByScene("mermaid_fix")

	currentCode := code
	currentErrMsg := errMsg

	for attempt := 1; attempt <= maxRetries; attempt++ {
		run.GetLogger().Infof("mermaid fix attempt %d/%d", attempt, maxRetries)

		fixerPrompt := bytes.NewBuffer(nil)
		err := prompts.MermaidFixUserPromptTemplate.Execute(fixerPrompt, map[string]any{
			"mermaid_code": currentCode,
			"error_msg":    currentErrMsg,
		})
		if err != nil {
			return "", errors.WithMessage(err, "failed to execute mermaid fix prompt")
		}

		messages := []*framework.ChatMessage{
			{
				Role:    "user",
				Content: fixerPrompt.String(),
			},
		}

		content, err := llm.ChatCompletion(run, messages, framework.LLMCompletionOption{
			Model:       modelConfig.Model,
			MaxTokens:   modelConfig.MaxTokens,
			Temperature: modelConfig.Temperature,
			Tag:         "mermaid_fix",
		})
		if err != nil {
			run.GetLogger().Errorf("failed to fix mermaid by llm: %s", err)
			continue
		}

		run.GetLogger().Debugf("fixed mermaid attempt %d: %s", attempt, content.Content)

		// post check
		result := content.Content
		result = strings.TrimSpace(result)
		// 检测是否有 ```mermaid```
		if !strings.HasPrefix(result, "```mermaid") || !strings.HasSuffix(result, "```") {
			run.GetLogger().Warnf("attempt %d: fix mermaid code is not valid, missing ```mermaid tag: %s", attempt, result)
			if attempt == maxRetries {
				return "", errors.Errorf("fix mermaid code is not valid, missing ```mermaid tag: %s", result)
			}
			continue
		}

		result = strings.TrimPrefix(result, "```mermaid")
		result = strings.TrimSuffix(result, "```")
		result = strings.TrimSpace(result)

		// 验证修复后的代码语法
		syntaxErr := checkMermaidSyntax(run, tempDir, result, configFile, false)
		if syntaxErr == nil {
			run.GetLogger().Infof("mermaid fix succeeded on attempt %d", attempt)
			return result, nil
		}

		run.GetLogger().Warnf("attempt %d: after fix mermaid code still has syntax error: %s", attempt, syntaxErr.Error())

		// 如果还有重试机会，将当前结果和错误作为下一次的输入
		if attempt < maxRetries {
			currentCode = result
			currentErrMsg = syntaxErr.Error()
		}
	}

	return "", errors.Errorf("failed to fix mermaid code after %d attempts", maxRetries)
}
