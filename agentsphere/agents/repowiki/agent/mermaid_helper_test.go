package wiki_agent

import (
	"reflect"
	"testing"
)

func TestExtractMermaidCode(t *testing.T) {
	tests := []struct {
		name     string
		markdown string
		want     []string
	}{
		{
			name:     "标准mermaid格式",
			markdown: "这是一些文本\n```mermaid\ngraph TD;\nA-->B;\nB-->C;\n```\n更多文本",
			want:     []string{"graph TD;\nA-->B;\nB-->C;"},
		},
		{
			name:     "mermaid在内容第一行",
			markdown: "这是一些文本\n```mermaid\ngraph TD;\nA-->B;\n```\n更多文本",
			want:     []string{"graph TD;\nA-->B;"},
		},
		{
			name:     "多个mermaid代码块",
			markdown: "第一个图表:\n```mermaid\ngraph TD;\nA-->B;\n```\n第二个图表:\n```mermaid\npie title 分布图\n\"A\" : 25\n\"B\" : 75\n```",
			want:     []string{"graph TD;\nA-->B;", "pie title 分布图\n\"A\" : 25\n\"B\" : 75"},
		},
		{
			name:     "没有mermaid代码块",
			markdown: "这是一些普通文本\n```\nconst a = 1;\n```\n更多文本",
			want:     nil,
		},
		{
			name:     "mermaid代码块",
			markdown: "```mermaid\ngraph TD\n   A[Client] --> B[Load Balancer]\n   B --> C[Server1]\n   B --> D[Server2]\n```\n\n\n```mermaid\ngraph TD\n   A[Client] --> B[Load Balancer]\n   B --> C[Server1\n   B --> D[Server2]\n```\n\n\n\n```mermaid\ngraph TD\n   A[Client] --> B[Load Balancer]\n   B --> C[Server1]\n   B --> D[@Server2]\n```",
			want: []string{
				"graph TD\n   A[Client] --> B[Load Balancer]\n   B --> C[Server1]\n   B --> D[Server2]",
				"graph TD\n   A[Client] --> B[Load Balancer]\n   B --> C[Server1\n   B --> D[Server2]",
				"graph TD\n   A[Client] --> B[Load Balancer]\n   B --> C[Server1]\n   B --> D[@Server2]",
			},
		},
		{
			name:     "空的mermaid代码块",
			markdown: "这是一些文本\n```mermaid\n\n```\n更多文本",
			want:     []string{""},
		},
		{
			name:     "复杂的mermaid流程图",
			markdown: "```mermaid\nflowchart LR\nA[开始] --> B{判断}\nB -->|Yes| C[处理1]\nB -->|No| D[处理2]\nC --> E[结束]\nD --> E\n```",
			want:     []string{"flowchart LR\nA[开始] --> B{判断}\nB -->|Yes| C[处理1]\nB -->|No| D[处理2]\nC --> E[结束]\nD --> E"},
		},
		{
			name:     "mermaid与其他代码块混合",
			markdown: "代码示例:\n```go\nfunc test() {}\n```\n图表:\n```mermaid\nsequenceDiagram\nAlice->>John: Hello John\nJohn-->>Alice: Hi Alice\n```",
			want:     []string{"sequenceDiagram\nAlice->>John: Hello John\nJohn-->>Alice: Hi Alice"},
		},
		{
			name:     "多个的mermaid流程图",
			markdown: "```mermaid\nflowchart LR\nA[开始] --> B{判断}\nB -->|Yes| C[处理1]\nB -->|No| D[处理2]\nC --> E[结束]\nD --> E\n```代码示例:\n```go\nfunc test() {}\n```\n图表:\n```mermaid\nsequenceDiagram\nAlice->>John: Hello John\nJohn-->>Alice: Hi Alice\n```",
			want:     []string{"flowchart LR\nA[开始] --> B{判断}\nB -->|Yes| C[处理1]\nB -->|No| D[处理2]\nC --> E[结束]\nD --> E", "sequenceDiagram\nAlice->>John: Hello John\nJohn-->>Alice: Hi Alice"},
		},
		{
			name:     "mermaid代码块中有中文",
			markdown: "**图示：特征元数据的使用流程**\n\n```mermaid\nsequenceDiagram\n    participant Developer as 开发者/数据科学家\n    participant MetadataRepo as lineage_meta_json (元数据存储)\n    participant MLSystem as 机器学习系统/数据处理管道\n\n    Developer->>MLSystem: 计划使用特征 \"X\"\n    MLSystem->>MetadataRepo: 查询特征 \"X\" 的元数据\n    activate MetadataRepo\n    Note over MetadataRepo: 读取 lineage_meta json 查找 \"X\"\n    MetadataRepo-->>MLSystem: 返回 \"X\" 的元数据 (名称 是否弃用 是否原始 隐私标签)\n    deactivate MetadataRepo\n    activate MLSystem\n    MLSystem->>Developer: 展示特征 \"X\" 的元数据详情\n    deactivate MLSystem\n    Developer->>Developer: 分析元数据 (例如 检查 is_deprecated)\n    alt 特征可用且符合要求\n        Developer->MLSystem: 确认使用特征 \"X\"\n    else 特征已弃用或不适用\n        Developer->Developer: 寻找替代特征\n    end\n```\n这个序列图展示了一个典型的场景：开发者通过查询元数据来了解特征的属性，然后基于这些信息做出是否使用该特征的决定。",
			want:     []string{"sequenceDiagram\n    participant Developer as 开发者/数据科学家\n    participant MetadataRepo as lineage_meta_json (元数据存储)\n    participant MLSystem as 机器学习系统/数据处理管道\n\n    Developer->>MLSystem: 计划使用特征 \"X\"\n    MLSystem->>MetadataRepo: 查询特征 \"X\" 的元数据\n    activate MetadataRepo\n    Note over MetadataRepo: 读取 lineage_meta json 查找 \"X\"\n    MetadataRepo-->>MLSystem: 返回 \"X\" 的元数据 (名称 是否弃用 是否原始 隐私标签)\n    deactivate MetadataRepo\n    activate MLSystem\n    MLSystem->>Developer: 展示特征 \"X\" 的元数据详情\n    deactivate MLSystem\n    Developer->>Developer: 分析元数据 (例如 检查 is_deprecated)\n    alt 特征可用且符合要求\n        Developer->MLSystem: 确认使用特征 \"X\"\n    else 特征已弃用或不适用\n        Developer->Developer: 寻找替代特征\n    end"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := extractMermaidCode(tt.markdown)
			if len(got) != len(tt.want) {
				t.Errorf("extractMermaidCode() = %v, want %v", got, tt.want)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("extractMermaidCode() = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestCheckMermaidSyntax(t *testing.T) {
//	tempDir, err := os.MkdirTemp("./", "mermaid_test")
//	if err != nil {
//		t.Fatalf("Failed to create temp directory: %v", err)
//	}
//	defer os.RemoveAll(tempDir)
//
//	run := iris.NewRunContext(context.Background(), nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
//
//	tests := []struct {
//		name    string
//		code    string
//		wantErr bool
//	}{
//		{
//			name: "valid graph TD code",
//			code: `graph TD
//   A[Client] --> B[Load Balancer]
//   B --> C[Server1]
//   B --> D[Server2]`,
//			wantErr: false,
//		},
//		{
//			name: "valid mermaid code",
//			code: `graph TD
//   A[Client] --> B[Load Balancer]
//   B --> C[Server1]
//   B --> D[Server2`,
//			wantErr: true,
//		},
//		{
//			name: "valid sequence Diagram code",
//			code: `sequenceDiagram
//    participant Developer as 开发者/数据科学家
//    participant MetadataRepo as lineage_meta_json (元数据存储)
//    participant MLSystem as 机器学习系统/数据处理管道
//
//    Developer->>MLSystem: 计划使用特征 "X"
//    MLSystem->>MetadataRepo: 查询特征 "X" 的元数据
//    activate MetadataRepo
//    Note over MetadataRepo: 读取 lineage_meta json 查找 "X"
//    MetadataRepo-->>MLSystem: 返回 "X" 的元数据 (名称 是否弃用 是否原始 隐私标签)
//    deactivate MetadataRepo
//    activate MLSystem
//    MLSystem->>Developer: 展示特征 "X" 的元数据详情
//    deactivate MLSystem
//    Developer->>Developer: 分析元数据 (例如 检查 is_deprecated)
//    alt 特征可用且符合要求
//        Developer->>MLSystem: 确认使用特征 "X"
//    else 特征已弃用或不适用
//        Developer->Developer: 寻找替代特征
//    end`,
//			wantErr: false,
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			err = checkMermaidSyntax(run, tempDir, tt.code, "", true)
//			if err != nil {
//				fmt.Println(err.Error())
//			}
//			if (tt.wantErr && err == nil) || (!tt.wantErr && err != nil) {
//				t.Errorf("checkMermaidSyntax() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//
//}

func TestFixMermaidMarkdownLists(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "bad",
			input: `stateDiagram-v2
    [*] --> SystemStart
    note right of SystemStart
        * Initialize database
        * Load configuration  
        * Start services
    end note`,
			expected: `stateDiagram-v2
    [*] --> SystemStart
    note right of SystemStart
        • Initialize database
        • Load configuration  
        • Start services
    end note`,
		},

		{
			name:     "real case",
			input:    "graph TD\n    A[Client] -- 1.POST /tasks <br/>(启动任务) --> B{API Server}\n    B -- 2.202 Accepted <br/>{ \"status_url\": \"/tasks/123\" } --> A\n    B -- 3.(后台处理任务) --> C[Worker]\n    C -- 4.(更新状态) --> D[(Database)]\n    subgraph \"轮询或回调\"\n        A -- 5a.GET /tasks/123 <br/>(轮询状态) --> B\n        B -- 6a.从DB读取状态 --> D\n        D -- 7a.返回状态 --> B\n        B -- 8a.返回任务状态 --> A\n        C -- 5b.(任务完成) --> E{Webhook}\n        E -- 6b.POST callback_url --> F[Client Endpoint]\n    end",
			expected: "graph TD\n    A[Client] -- 1.POST /tasks <br/>(启动任务) --> B{API Server}\n    B -- 2.202 Accepted <br/>{ \"status_url\": \"/tasks/123\" } --> A\n    B -- 3.(后台处理任务) --> C[Worker]\n    C -- 4.(更新状态) --> D[(Database)]\n    subgraph \"轮询或回调\"\n        A -- 5a.GET /tasks/123 <br/>(轮询状态) --> B\n        B -- 6a.从DB读取状态 --> D\n        D -- 7a.返回状态 --> B\n        B -- 8a.返回任务状态 --> A\n        C -- 5b.(任务完成) --> E{Webhook}\n        E -- 6b.POST callback_url --> F[Client Endpoint]\n    end",
		},
		{
			name:     "real case",
			input:    "graph TD\n    A[Client] -- 1. POST /tasks <br/>(启动任务) --> B{API Server}\n    B -- 2. 202 Accepted <br/>{ \"status_url\": \"/tasks/123\" } --> A\n    B -- 3. (后台处理任务) --> C[Worker]\n    C -- 4. (更新状态) --> D[(Database)]\n    subgraph \"轮询或回调\"\n        A -- 5a. GET /tasks/123 <br/>(轮询状态) --> B\n        B -- 6a. 从DB读取状态 --> D\n        D -- 7a. 返回状态 --> B\n        B -- 8a. 返回任务状态 --> A\n        C -- 5b. (任务完成) --> E{Webhook}\n        E -- 6b. POST callback_url --> F[Client Endpoint]\n    end",
			expected: "graph TD\n    A[Client] -- 1-POST /tasks <br/>(启动任务) --> B{API Server}\n    B -- 2-202 Accepted <br/>{ \"status_url\": \"/tasks/123\" } --> A\n    B -- 3-(后台处理任务) --> C[Worker]\n    C -- 4-(更新状态) --> D[(Database)]\n    subgraph \"轮询或回调\"\n        A -- 5a. GET /tasks/123 <br/>(轮询状态) --> B\n        B -- 6a. 从DB读取状态 --> D\n        D -- 7a. 返回状态 --> B\n        B -- 8a. 返回任务状态 --> A\n        C -- 5b. (任务完成) --> E{Webhook}\n        E -- 6b. POST callback_url --> F[Client Endpoint]\n    end",
		},
		{
			name:     "fix dash lists",
			input:    "flowchart LR\n    A[Node 1] --> B[\"- Line1\n    - Line 2\"]",
			expected: "flowchart LR\n    A[Node 1] --> B[\"• Line1\n    • Line 2\"]",
		},
		{
			name:     "fix asterisk lists",
			input:    "flowchart LR\n    A[Node 1] --> B[\"* Item1\n    * Item 2\"]",
			expected: "flowchart LR\n    A[Node 1] --> B[\"• Item1\n    • Item 2\"]",
		},
		{
			name:     "fix ordered lists",
			input:    "flowchart LR\n    A[Node 1] --> B[\"1. First\n    2. Second\"]",
			expected: "flowchart LR\n    A[Node 1] --> B[\"1-First\n    2-Second\"]",
		},
		{
			name:     "fix mixed lists",
			input:    "flowchart LR\n    A[Node 1] --> B[\"- Unordered\n    1. Ordered\n    * Another\"]",
			expected: "flowchart LR\n    A[Node 1] --> B[\"• Unordered\n    1-Ordered\n    • Another\"]",
		},
		{
			name:     "preserve indentation",
			input:    "flowchart LR\n    A[Node 1] --> B[\"    - Indented\n        1. More\"]",
			expected: "flowchart LR\n    A[Node 1] --> B[\"    • Indented\n        1-More\"]",
		},
		{
			name:     "multiple nodes",
			input:    "flowchart LR\n    A[\"- Item A\"] --> B[\"1. Item B\"]",
			expected: "flowchart LR\n    A[\"• Item A\"] --> B[\"1-Item B\"]",
		},
		{
			name:     "no lists to fix",
			input:    "flowchart LR\n    A[Node 1] --> B[Node 2]",
			expected: "flowchart LR\n    A[Node 1] --> B[Node 2]",
		},
		{
			name:     "lists everywhere are fixed",
			input:    "flowchart LR\n    A[Node] --> B[Node]\n    \n    - This is a comment\n    1. Also comment",
			expected: "flowchart LR\n    A[Node] --> B[Node]\n    \n    • This is a comment\n    1-Also comment",
		},
		{
			name:     "empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "double digit numbers",
			input:    "flowchart LR\n    A[\"10. Tenth\n    11. Eleventh\"]",
			expected: "flowchart LR\n    A[\"10-Tenth\n    11-Eleventh\"]",
		},
		{
			name: "double digit numbers",
			input: `graph TD
    A[输入: Interaction] --> B{1. init_step: 多面初始打分};
    B --> C[facet_lm_logits_arr: List of 全量分数];
    C --> D{2. filter_step: Top-K 筛选};
    D --> E[candidate_token_ids: K个候选ID];
    C --> F{3. generator_step: 精排面打分};
    E --> F;
    F --> G[logit_hidden_reranker_topn: K个新分数];
    C --> H{4. evaluator_step: 分数融合};
    G --> H;
    H --> I[修改后的 facet_lm_logits_arr];
    I --> J{5. final_step: 多面加权融合};
    J --> K[最终概率分布 prediction_prob];`,
			expected: `graph TD
    A[输入: Interaction] --> B{1-init_step: 多面初始打分};
    B --> C[facet_lm_logits_arr: List of 全量分数];
    C --> D{2-filter_step: Top-K 筛选};
    D --> E[candidate_token_ids: K个候选ID];
    C --> F{3-generator_step: 精排面打分};
    E --> F;
    F --> G[logit_hidden_reranker_topn: K个新分数];
    C --> H{4-evaluator_step: 分数融合};
    G --> H;
    H --> I[修改后的 facet_lm_logits_arr];
    I --> J{5-final_step: 多面加权融合};
    J --> K[最终概率分布 prediction_prob];`,
		},
		{
			name: "test",
			input: `graph TD
    subgraph "UI Layer"
        A["VerifySmsFragment (短信验证页)"]
        B["CJRowSwitchText (积分开关组件)"]
    end

    subgraph "ViewModel/State Layer"
        C["VerifyVMContext (状态管理核心)"]
    end

    subgraph "Data/Model Layer"
        D["AssetInfoBean (数据模型)"]
    end

    subgraph "Service/Event Layer"
        F["EventManager (事件总线)"]
        G["BankCardPointChangeEvent (积分状态事件)"]
    end

    subgraph "Backend"
        H["Server API"]
    end

    H -- "1. 提供支付数据" --> D
    D -- "2. 驱动UI渲染" --> A
    A -- "3. 包含" --> B
    B -- "4. 用户操作回调" --> A
    A -- "5. 更新/读取状态" --> C
    C -- "6. 发布事件" --> F
    F -- "7. 广播" --> G
    G -- "8. 通知UI更新" --> A
    A -- "9. 构建支付请求" --> H`,
			expected: `graph TD
    subgraph "UI Layer"
        A["VerifySmsFragment (短信验证页)"]
        B["CJRowSwitchText (积分开关组件)"]
    end

    subgraph "ViewModel/State Layer"
        C["VerifyVMContext (状态管理核心)"]
    end

    subgraph "Data/Model Layer"
        D["AssetInfoBean (数据模型)"]
    end

    subgraph "Service/Event Layer"
        F["EventManager (事件总线)"]
        G["BankCardPointChangeEvent (积分状态事件)"]
    end

    subgraph "Backend"
        H["Server API"]
    end

    H -- "1-提供支付数据" --> D
    D -- "2-驱动UI渲染" --> A
    A -- "3-包含" --> B
    B -- "4-用户操作回调" --> A
    A -- "5-更新/读取状态" --> C
    C -- "6-发布事件" --> F
    F -- "7-广播" --> G
    G -- "8-通知UI更新" --> A
    A -- "9-构建支付请求" --> H`,
		},
		{
			name: "circle nodes with double parentheses",
			input: `flowchart TD
    A(("1. Start Process")) --> B(("2. End Process"))
    C(("* Item A")) --> D(("- Item B"))`,
			expected: `flowchart TD
    A(("1-Start Process")) --> B(("2-End Process"))
    C(("• Item A")) --> D(("• Item B"))`,
		},
		{
			name: "subroutine nodes with double brackets",
			input: `flowchart TD
    A[["1. Initialize System"]] --> B[["2. Configure Settings"]]
    C[["- Database Setup"]] --> D[["* Cache Setup"]]`,
			expected: `flowchart TD
    A[["1-Initialize System"]] --> B[["2-Configure Settings"]]
    C[["• Database Setup"]] --> D[["• Cache Setup"]]`,
		},
		{
			name: "hexagon nodes with double braces",
			input: `flowchart TD
    A{{"1. Prepare Data"}} --> B{{"2. Validate Input"}}
    C{{"- Check Rules"}} --> D{{"* Apply Logic"}}`,
			expected: `flowchart TD
    A{{"1-Prepare Data"}} --> B{{"2-Validate Input"}}
    C{{"• Check Rules"}} --> D{{"• Apply Logic"}}`,
		},
		{
			name: "database cylinder nodes",
			input: `flowchart TD
    A[("1. MySQL Database")] --> B[("2. Redis Cache")]
    C[("- User Data")] --> D[("* Session Store")]`,
			expected: `flowchart TD
    A[("1-MySQL Database")] --> B[("2-Redis Cache")]
    C[("• User Data")] --> D[("• Session Store")]`,
		},
		{
			name: "parallelogram nodes with slash",
			input: `flowchart TD
    A[/"1. Input Data"/] --> B[/"2. Process Data"/]
    C[/"- Raw Input"/] --> D[/"* Clean Data"/]`,
			expected: `flowchart TD
    A[/"1-Input Data"/] --> B[/"2-Process Data"/]
    C[/"• Raw Input"/] --> D[/"• Clean Data"/]`,
		},
		{
			name: "parallelogram alt nodes with backslash",
			input: `flowchart TD
    A[\"1. Output Data"\] --> B[\"2. Final Result"\]
    C[\"- Processed Data"\] --> D[\"* Formatted Output"\]`,
			expected: `flowchart TD
    A[\"1-Output Data"\] --> B[\"2-Final Result"\]
    C[\"• Processed Data"\] --> D[\"• Formatted Output"\]`,
		},
		{
			name: "asymmetric nodes with greater than",
			input: `flowchart TD
    A>"1. Decision Point"] --> B>"2. Result Path"]
    C>"- Option A"] --> D>"* Option B"]`,
			expected: `flowchart TD
    A>"1-Decision Point"] --> B>"2-Result Path"]
    C>"• Option A"] --> D>"• Option B"]`,
		},
		{
			name: "connection labels with pipes",
			input: `flowchart TD
    A --> |"1. First Step"| B
    C --> |"2. Second Step"| D
    E --> |"- Task A"| F
    G --> |"* Task B"| H`,
			expected: `flowchart TD
    A --> |"1-First Step"| B
    C --> |"2-Second Step"| D
    E --> |"• Task A"| F
    G --> |"• Task B"| H`,
		},
		{
			name: "thick connections with equals",
			input: `flowchart TD
    A == "1. Primary Path" ==> B
    C == "2. Secondary Path" ==> D
    E == "- Branch A" ==> F
    G == "* Branch B" ==> H`,
			expected: `flowchart TD
    A == "1-Primary Path" ==> B
    C == "2-Secondary Path" ==> D
    E == "• Branch A" ==> F
    G == "• Branch B" ==> H`,
		},
		{
			name: "mixed node types with various lists",
			input: `flowchart TD
    A["1. Rectangle Node"] --> B("2. Round Node")
    C{"3. Diamond Node"} --> D[["4. Subroutine Node"]]
    E(("5. Circle Node")) --> F{{"6. Hexagon Node"}}
    G[("7. Database Node")] --> H[/"8. Parallelogram Node"/]
    I[\"9. Parallelogram Alt"\] --> J>"10. Asymmetric Node"]`,
			expected: `flowchart TD
    A["1-Rectangle Node"] --> B("2-Round Node")
    C{"3-Diamond Node"} --> D[["4-Subroutine Node"]]
    E(("5-Circle Node")) --> F{{"6-Hexagon Node"}}
    G[("7-Database Node")] --> H[/"8-Parallelogram Node"/]
    I[\"9-Parallelogram Alt"\] --> J>"10-Asymmetric Node"]`,
		},
		{
			name: "complex mermaid with various connections and lists",
			input: `flowchart TD
    A["1. Start"] --> |"2. Process"| B("3. Check")
    B --> |"- Valid"| C{{"4. Success"}}
    B --> |"* Invalid"| D[["5. Error Handler"]]
    C == "6. Continue" ==> E[("7. Database Save")]
    D --> |"8. Retry"| A
    E --> F>"9. Final Result"]`,
			expected: `flowchart TD
    A["1-Start"] --> |"2-Process"| B("3-Check")
    B --> |"• Valid"| C{{"4-Success"}}
    B --> |"• Invalid"| D[["5-Error Handler"]]
    C == "6-Continue" ==> E[("7-Database Save")]
    D --> |"8-Retry"| A
    E --> F>"9-Final Result"]`,
		},
		{
			name: "basic rectangular nodes without quotes",
			input: `flowchart TD
    A[1. First Step] --> B[2. Second Step]
    C[- Task A] --> D[* Task B]`,
			expected: `flowchart TD
    A[1-First Step] --> B[2-Second Step]
    C[• Task A] --> D[• Task B]`,
		},
		{
			name: "should not replace in step names without proper context",
			input: `graph TD
    A[输入: Interaction] --> B{1. init_step: 多面初始打分}
    B --> C[facet_lm_logits_arr: List of 全量分数]
    C --> D{2. filter_step: Top-K 筛选}`,
			expected: `graph TD
    A[输入: Interaction] --> B{1-init_step: 多面初始打分}
    B --> C[facet_lm_logits_arr: List of 全量分数]
    C --> D{2-filter_step: Top-K 筛选}`,
		},
		{
			name: "diamond nodes with brace quote pattern - basic case",
			input: `flowchart TD
    A --> B{"1. Process Step"}
    C --> D{"- Task Item"}
    E --> F{"* Action Item"}`,
			expected: `flowchart TD
    A --> B{"1-Process Step"}
    C --> D{"• Task Item"}
    E --> F{"• Action Item"}`,
		},
		{
			name: "diamond nodes with brace quote pattern - user case",
			input: `flowchart TD
    subgraph "应用层"
        A["开发者应用调用 search(query)"]
    end

    subgraph "Mem0 核心库"
        B["核心记忆引擎 (Memory Engine)"]
        C{"1. 查询向量化 (Embeddings)"}
        D{"2. 相似度搜索 (Vector Store)"}
    end

    subgraph "计算层"
        E["嵌入模型 (Embeddings)"]
    end

    subgraph "数据存储层"
        F["向量数据库 (Vector Store)"]
    end

    A --> B
    B --> C --> E --> C
    B --> D --> F --> D`,
			expected: `flowchart TD
    subgraph "应用层"
        A["开发者应用调用 search(query)"]
    end

    subgraph "Mem0 核心库"
        B["核心记忆引擎 (Memory Engine)"]
        C{"1-查询向量化 (Embeddings)"}
        D{"2-相似度搜索 (Vector Store)"}
    end

    subgraph "计算层"
        E["嵌入模型 (Embeddings)"]
    end

    subgraph "数据存储层"
        F["向量数据库 (Vector Store)"]
    end

    A --> B
    B --> C --> E --> C
    B --> D --> F --> D`,
		},
		{
			name: "potentially missing quote combinations",
			input: `flowchart TD
    A("1. Round with quote") --> B(("2. Circle with quote"))
    C[["3. Subroutine with quote"]] --> D{{"4. Hexagon with quote"}}
    E[("5. Database with quote")] --> F[/"6. Para with quote"/]
    G[\"7. Para alt with quote"\] --> H>"8. Asymmetric with quote"]`,
			expected: `flowchart TD
    A("1-Round with quote") --> B(("2-Circle with quote"))
    C[["3-Subroutine with quote"]] --> D{{"4-Hexagon with quote"}}
    E[("5-Database with quote")] --> F[/"6-Para with quote"/]
    G[\"7-Para alt with quote"\] --> H>"8-Asymmetric with quote"]`,
		},
		{
			name: "edge cases - verify each node type with quote combinations handles lists",
			input: `flowchart TD
    A("1. Round") --> B("- Dash")
    C("* Star") --> D(("1. Double round"))  
    E(("- Double dash")) --> F(("* Double star"))
    G[["1. Subroutine"]] --> H[["- Sub dash"]]
    I[["* Sub star"]] --> J{{"1. Hexagon"}}
    K{{"• Hex dash"}} --> L{{"* Hex star"}}
    M[("1. Database")] --> N[("- DB dash")]
    O[("* DB star")] --> P[/"1. Parallelogram"/]
    Q[/"- Para dash"/] --> R[/"* Para star"/]
    S[\"1. Para alt"\] --> T[\"- Para alt dash"\]
    U[\"* Para alt star"\] --> V>"1. Asymmetric"]
    W>"- Asym dash"] --> X>"* Asym star"]`,
			expected: `flowchart TD
    A("1-Round") --> B("• Dash")
    C("• Star") --> D(("1-Double round"))  
    E(("• Double dash")) --> F(("• Double star"))
    G[["1-Subroutine"]] --> H[["• Sub dash"]]
    I[["• Sub star"]] --> J{{"1-Hexagon"}}
    K{{"• Hex dash"}} --> L{{"• Hex star"}}
    M[("1-Database")] --> N[("• DB dash")]
    O[("• DB star")] --> P[/"1-Parallelogram"/]
    Q[/"• Para dash"/] --> R[/"• Para star"/]
    S[\"1-Para alt"\] --> T[\"• Para alt dash"\]
    U[\"• Para alt star"\] --> V>"1-Asymmetric"]
    W>"• Asym dash"] --> X>"• Asym star"]`,
		},
		{
			name: "connection line patterns and other edge cases",
			input: `flowchart TD
    A --> B
    A ---|"1. Dotted line with list"| C
    A -.-|"2. Dash dot with list"| D  
    A -.->|"- Dash arrow with list"| E
    A -->|"* Normal arrow with list"| F
    
    subgraph "1. Subgraph with list"
        G[Node]
    end
    
    subgraph "- Subgraph dash list"
        H[Node]
    end
    
    subgraph "* Subgraph star list" 
        I[Node]
    end`,
			expected: `flowchart TD
    A --> B
    A ---|"1-Dotted line with list"| C
    A -.-|"2-Dash dot with list"| D  
    A -.->|"• Dash arrow with list"| E
    A -->|"• Normal arrow with list"| F
    
    subgraph "1-Subgraph with list"
        G[Node]
    end
    
    subgraph "• Subgraph dash list"
        H[Node]
    end
    
    subgraph "• Subgraph star list" 
        I[Node]
    end`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := fixMermaidMarkdownLists(tt.input)
			if result != tt.expected {
				t.Errorf("fixMermaidMarkdownLists() = %q, want %q", result, tt.expected)
			}
		})
	}
}
