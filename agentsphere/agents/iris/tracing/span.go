package tracing

import (
	"encoding/json"
	"sync"
	"time"
)

type SpanType string

const (
	SpanTypeSpan  SpanType = "span"
	SpanTypeEvent SpanType = "event"
)

type SpanEventType string

const (
	SpanEventThinking SpanEventType = "thinking"
	SpanEventTool     SpanEventType = "tool_call"
	SpanEventPlan     SpanEventType = "plan"
	SpanEventExecute  SpanEventType = "execute"
	SpanEventQuery    SpanEventType = "query"
	SpanEventResponse SpanEventType = "response"
)

var _ Span = &SpanImpl{}

type SpanImpl struct {
	Name      string
	Type      SpanType
	StartTime time.Time
	EndTime   time.Time
	TraceID   string
	SpanID    string
	ParentID  string
	mu        sync.Mutex

	tags      map[string]interface{}
	err       error
	isEnded   bool
	processor Processor
	prevSpan  *SpanImpl
}

// newSpan 创建一个新的span
func newSpan(name string, spanID string, parentID string, spanType SpanType, attributes map[string]interface{}, processor Processor) *SpanImpl {
	span := &SpanImpl{
		Name:      name,
		StartTime: time.Now(),
		tags:      attributes,
		Type:      spanType,
		processor: processor,
		SpanID:    spanID,
		ParentID:  parentID,
	}

	return span
}

// SetTag 设置span的标签
func (s *SpanImpl) SetTag(key string, value interface{}) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.tags == nil {
		s.tags = make(map[string]interface{})
	}
	s.tags[key] = value
}

// SetError 设置错误信息
func (s *SpanImpl) SetError(err error) {
	s.mu.Lock()

	if s.isEnded {
		s.mu.Unlock()
		return
	}

	s.err = err
	s.mu.Unlock()
	s.SetTag("error", err.Error())
}

func (s *SpanImpl) Start() {
	if s.isEnded {
		return
	}
	// 异步调用OnSpanStart hook
	if s.processor != nil {
		go s.processor.OnSpanStart(s)
	}

	if s.Type == SpanTypeSpan {
		if prevSpan := GetCurrentSpan(); prevSpan != nil {
			s.prevSpan = prevSpan
		}
		setCurrentSpan(s)
	}
}

// End 结束span
func (s *SpanImpl) End() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isEnded {
		return
	}

	s.EndTime = time.Now()
	s.isEnded = true

	// 调用OnSpanEnd hook
	if s.processor != nil {
		go s.processor.OnSpanEnd(s)
	}
	if s.Type == SpanTypeSpan {
		setCurrentSpan(s.prevSpan)
	}
}

// GetID 获取span ID
func (s *SpanImpl) GetID() string {
	return s.SpanID
}

// GetTraceID 获取trace ID
func (s *SpanImpl) GetTraceID() string {
	return s.TraceID
}

// GetTags 获取所有标签
func (s *SpanImpl) GetTags() map[string]interface{} {
	s.mu.Lock()
	defer s.mu.Unlock()

	tags := make(map[string]interface{}, len(s.tags))
	for k, v := range s.tags {
		tags[k] = v
	}
	return tags
}

// GetError 获取错误信息
func (s *SpanImpl) GetError() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.err
}

// IsEnded 检查span是否已结束
func (s *SpanImpl) IsEnded() bool {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.isEnded
}

// DeepCopy implements deepcopy.Interface to preserve unexported fields like tags.
// It avoids copying synchronization primitives and processor hooks.
func (s *SpanImpl) DeepCopy() interface{} {
	if s == nil {
		return nil
	}

	copied := &SpanImpl{
		Name:      s.Name,
		Type:      s.Type,
		StartTime: s.StartTime,
		EndTime:   s.EndTime,
		TraceID:   s.TraceID,
		SpanID:    s.SpanID,
		ParentID:  s.ParentID,
		// mu: zero value
	}

	// Copy unexported fields we rely on at export time
	if s.tags != nil {
		// shallow copy
		copied.tags = s.GetTags()
	}
	copied.err = s.err
	copied.isEnded = s.isEnded

	// Do not carry over processor or prevSpan to avoid side effects in copies
	// copied.processor = nil
	// copied.prevSpan = nil

	return copied
}

func (s *SpanImpl) MarshalJSON() ([]byte, error) {
	copied := s.DeepCopy().(*SpanImpl)
	type spanAlias SpanImpl
	return json.Marshal(&struct {
		*spanAlias
		Tags map[string]interface{} `json:"tags"`
	}{
		spanAlias: (*spanAlias)(copied),
		Tags:      copied.tags, // 直接使用 copied 中的 tags
	})
}

func (s *SpanImpl) Truncate(fn func(any) any) interface{} {
	copied := s.DeepCopy()
	if original, ok := copied.(*SpanImpl); ok {
		original.tags = fn(original.GetTags()).(map[string]interface{})
	}
	return copied
}
