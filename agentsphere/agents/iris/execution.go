package iris

import (
	"code.byted.org/devgpt/kiwis/lib/signal"

	"github.com/mohae/deepcopy"
	"github.com/samber/lo"
)

type AgentMemory interface {
	GetStep(stepID string) *AgentRunStep
	HasSteps() bool
	LastStep() *AgentRunStep
	Clone() AgentMemory
}

type AgentRunState struct {
	AssignmentID string            `json:"assignment_id"`
	SessionID    string            `json:"session_id"`
	RunID        string            `json:"run_id"`
	AgentID      string            `json:"agent_id"`
	Agent        ConfigurableAgent `json:"-"`
	Status       AgentRunStatus    `json:"status"`
	// Steps        []*AgentRunStep   `json:"steps"`
	Memory       AgentMemory      `json:"-"`
	CurrentStep  *AgentRunStep    `json:"current_step"` // current pending step, will be appended to steps once think is done
	Variables    map[string]any   `json:"variables"`    // extract variables using mapstrucure
	Signals      *signal.Notifier `json:"-"`
	Conversation *Conversation    `json:"conversation"`
	Store        map[string]any   `json:"store"` // store custom states
}

// DO NOT modify agent state directly
func (s *AgentRunState) ReadOnly() {}

// LastStep returns the last step or nil if there are no steps
func (s *AgentRunState) LastStep() *AgentRunStep {
	if s.Memory.HasSteps() {
		return s.Memory.LastStep()
	}
	return nil
}

func (s *AgentRunState) Clone() *AgentRunState {
	newStore := deepcopy.Copy(s.Store).(map[string]any)
	newVariables := deepcopy.Copy(s.Variables).(map[string]any)
	return &AgentRunState{
		SessionID:    s.SessionID,
		RunID:        s.RunID,
		AgentID:      s.AgentID,
		Agent:        s.Agent,
		Status:       s.Status,
		Memory:       s.Memory.Clone(),
		CurrentStep:  s.CurrentStep,
		Signals:      s.Signals,
		Variables:    newVariables,
		Conversation: s.Conversation,
		Store:        newStore,
	}
}

type AgentRunOption struct {
	RunID      string
	Agent      Agent
	Parameters map[string]any
}

// AgentRunStatus defines running status for a single agent
type AgentRunStatus string

const (
	AgentRunStatusCreated   AgentRunStatus = "created"
	AgentRunStatusRunning   AgentRunStatus = "running"
	AgentRunStatusPaused    AgentRunStatus = "paused"
	AgentRunStatusCompleted AgentRunStatus = "completed"
	AgentRunStatusFailed    AgentRunStatus = "failed"
	AgentRunStatusCanceled  AgentRunStatus = "canceled"
)

func (s AgentRunStatus) IsStopped() bool {
	return s == AgentRunStatusCompleted || s == AgentRunStatusCanceled || s == AgentRunStatusFailed
}

// StepStatus defines running status for a step
type StepStatus string

const (
	// step is created, waiting for agent to think
	AgentRunStepStatusCreated StepStatus = "created"
	// agent is thinking
	AgentRunStepStatusThinking StepStatus = "thinking"
	// waiting for user action etc.
	AgentRunStepStatusPending StepStatus = "pending"
	// skipped step due to internal workflow logic
	AgentRunStepStatusSkipped StepStatus = "skipped"
	// running the action
	AgentRunStepStatusRunning StepStatus = "running"
	// current step runs successfully
	AgentRunStepStatusSuccess StepStatus = "success"
	// current step failed but may be recoverable (e.g. network error, llm provides wrong args)
	AgentRunStepStatusError StepStatus = "error"
	// current step failed and not recoverable (e.g. the user has no permission to access resource)
	AgentRunStepStatusFailed StepStatus = "failed"
)

type AgentRunStep struct {
	StepID        string     `json:"step_id"`
	ExecutorAgent string     `json:"executor"` // id of the agent that created this step
	Status        StepStatus `json:"status"`

	// for agent think
	Thought *Thought `json:"thought"`

	// for thought parsing
	Action     Action     `json:"-"`
	Inputs     Parameters `json:"inputs"`
	Outputs    Parameters `json:"outputs"`
	Conclusion Parameters `json:"conclusion"`

	// thought parse error or action execution error
	Error error `json:"-"`

	Message *Message `json:"message"`
	Parent  string   `json:"parent"`
}

type StepUpdate struct {
	StepID  *string     `json:"step_id"`
	Status  *StepStatus `json:"status"`
	Thought *Thought    `json:"thought"`
	Inputs  Parameters  `json:"inputs"`
	Outputs Parameters  `json:"outputs"`
	Message *Message    `json:"message"`
	Error   error       `json:"error"`
	Parent  string      `json:"parent"`
}

func (step *AgentRunStep) Finish(outputs map[string]any, err error) {
	if err != nil {
		step.Status = lo.Ternary(IsRecoverable(err), AgentRunStepStatusError, AgentRunStepStatusFailed)
	} else {
		step.Status = AgentRunStepStatusSuccess
	}

	step.Outputs = outputs
	step.Error = err
}

// takes a copy of the step
func (step *AgentRunStep) Clone() *AgentRunStep {
	return &AgentRunStep{
		StepID:  step.StepID,
		Status:  step.Status,
		Action:  step.Action,
		Thought: step.Thought,
		Inputs:  step.Inputs,
		Outputs: step.Outputs,
		Message: step.Message,
		Error:   step.Error,
	}
}

type AgentTaskStatus string

const (
	AgentTaskStatusCreated       AgentTaskStatus = "created"
	AgentTaskStatusRunning       AgentTaskStatus = "running"
	AgentTaskStatusCompleted     AgentTaskStatus = "completed"
	AgentTaskStatusFailed        AgentTaskStatus = "failed"
	AgentTaskStatusCanceled      AgentTaskStatus = "canceled"
	AgentTaskStatusInputRequired AgentTaskStatus = "input-required"
)
