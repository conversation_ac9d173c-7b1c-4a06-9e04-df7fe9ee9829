package iris

import (
	"fmt"
	"regexp"
	"strings"
)

type ReferenceItem struct {
	ID       int            `json:"id" mapstructure:"id"`
	Title    string         `json:"title" mapstructure:"title"`
	URI      string         `json:"uri" mapstructure:"uri"`
	MetaData map[string]any `json:"meta_data" mapstructure:"meta_data"`
}

type Reference []ReferenceItem

var (
	refListReg     = regexp.MustCompile(`-\s*\[(.*?)\]\((.*?)\)`)
	refFallbackReg = regexp.MustCompile(`\[(.*?)\]\((.*?)\)`)
)

var example = `
- [description_1](relative_file_path)
Authors: <AUTHORS>
Date: [submission_date]  
...other metadata
- [description_2](deployed_url)
Type: [type]
Resolution: [resolution]
...other metadata`

func ParseReference(s string) Reference {
	// 定义正则表达式来匹配每个参考项
	itemRegex := refListReg
	indices := itemRegex.FindAllStringSubmatchIndex(s, -1)

	var result Reference
	for _, index := range indices {
		if len(index) != 6 {
			continue
		}
		titleStart, titleEnd := index[2], index[3]
		uriStart, uriEnd := index[4], index[5]
		title := s[titleStart:titleEnd]
		uri := s[uriStart:uriEnd]

		// 提取元数据
		matchEnd := index[1]
		startIndex := matchEnd
		endIndex := strings.Index(s[startIndex:], "- [")
		if endIndex == -1 {
			endIndex = len(s) - startIndex
		}
		metaDataStr := strings.TrimSpace(s[startIndex : startIndex+endIndex])

		metaData := make(map[string]any)
		lines := strings.Split(metaDataStr, "\n")
		for _, line := range lines {
			if line == "" {
				continue
			}
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				metaData[key] = value
			}
		}

		result = append(result, ReferenceItem{
			Title:    title,
			URI:      uri,
			MetaData: metaData,
		})
	}
	if len(result) == 0 {
		// fallback: try to parse [xxx](xxx) format
		itemRegex := refFallbackReg
		indices := itemRegex.FindAllStringSubmatchIndex(s, -1)
		for _, index := range indices {
			if len(index) != 6 {
				continue
			}
			titleStart, titleEnd := index[2], index[3]
			uriStart, uriEnd := index[4], index[5]
			title := s[titleStart:titleEnd]
			uri := s[uriStart:uriEnd]
			result = append(result, ReferenceItem{
				Title:    title,
				URI:      uri,
				MetaData: make(map[string]any),
			})
		}
	}

	return result
}

func ReferenceString(ref Reference) string {
	var result string
	for _, item := range ref {
		result += fmt.Sprintf("- [%s](%s)\n", item.Title, item.URI)
		for key, value := range item.MetaData {
			result += fmt.Sprintf("    %s: %s\n", key, value)
		}
	}
	return result
}

func ReferenceSimpleString(ref Reference) string {
	var result string
	for _, item := range ref {
		result += fmt.Sprintf("- [%d] [%s](%s)\n", item.ID, item.Title, item.URI)
	}
	return result
}
