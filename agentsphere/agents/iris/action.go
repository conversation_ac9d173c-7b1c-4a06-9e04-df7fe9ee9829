package iris

import (
	"bytes"
	"context"
	"encoding/json"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

type Action interface {
	framework.Action
	Execute(ctx *AgentRunContext, step *AgentRunStep) error
	Observation(inputs map[string]any, outputs map[string]any, err error) string
	// Serialize and Deserialize action will be stored in the database
	Serialize() ([]byte, error)
	Deserialize(data string) error
}

type ActionInfo struct {
	ActionName        string           `json:"name"`
	ActionDescription string           `json:"description"`
	ActionInputSpec   framework.Schema `json:"-"`
	ActionOutputSpec  framework.Schema `json:"-"`
}

func (ad *ActionInfo) Name() string {
	return ad.ActionName
}

func (ad *ActionInfo) Description() string {
	return ad.ActionDescription
}

func (ad *ActionInfo) InputSpec() framework.Schema {
	return ad.ActionInputSpec
}

func (ad *ActionInfo) OutputSpec() framework.Schema {
	return ad.ActionOutputSpec
}

// RemoteToolExecutor is copied from actions.RemoteToolExecutor to avoid import cycle.
type RemoteToolExecutor interface {
	CallRemoteTool(ctx context.Context, id, stepID, tool string, parameters map[string]any) (map[string]any, error)
}

type DumbAction struct {
	ActionInfo
}

var _ Action = &DumbAction{}

func (d *DumbAction) Execute(ctx *AgentRunContext, step *AgentRunStep) error {
	return nil
}
func (d *DumbAction) Observation(inputs map[string]any, outputs map[string]any, err error) string {
	return ""
}
func (d *DumbAction) Serialize() ([]byte, error) {
	return json.Marshal(d)
}
func (d *DumbAction) Deserialize(data string) error {
	return json.Unmarshal([]byte(data), d)
}

func (d *DumbAction) Run(ctx context.Context, input framework.ActionInput) (*framework.Output, error) {
	return &framework.Output{}, nil
}

// Parameters is a map specialized for function call inputs and outputs, with custom json marshal and unmarshal
// to support int64 parameters and avoid url being encoded
type Parameters map[string]any

func (p Parameters) MarshalJSON() ([]byte, error) {
	bodyBytes := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(bodyBytes)
	encoder.SetEscapeHTML(false) // avoid url being encoded

	// Use the underlying map type to avoid infinite recursion
	temp := map[string]any(p)
	err := encoder.Encode(temp)
	if err != nil {
		return nil, err
	}
	return bodyBytes.Bytes(), nil
}

func (p *Parameters) UnmarshalJSON(data []byte) error {
	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.UseNumber()

	// Use the underlying map type to avoid infinite recursion
	var temp map[string]any
	err := decoder.Decode(&temp)
	if err != nil {
		return err
	}

	// Convert json.Number
	// if it does not contain float, it will be converted to int/int64
	// if it contains float, it will be converted to float64
	util.ConvertJSONNumbers(temp)

	// Assign the converted map to Parameters
	*p = Parameters(temp)
	return nil
}
