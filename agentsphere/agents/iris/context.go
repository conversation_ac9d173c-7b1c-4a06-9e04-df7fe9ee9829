package iris

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"sync"
	"time"

	"code.byted.org/lang/gg/gmap"
	"github.com/samber/lo"

	codfishmodel "code.byted.org/codebase/codfish/core/model"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/tracing"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/uuid"
)

type Workspace interface {
	Close()
}

type StorageType string

const (
	StorageTypeAgentRunContext      StorageType = "agent_run_context"
	StorageTypeAgentRunContextStore StorageType = "agent_run_context_store"
	StorageTypeRunAgentRequest      StorageType = "run_agent_request"
	StorageTypeAgentEvent           StorageType = "agent_event"
	StorageTypeAgentRequestMeta     StorageType = "run_agent_request_meta"
)

func (s *StorageType) FileName() string {
	ext := "json"
	if *s == StorageTypeAgentEvent {
		ext = "jsonl"
	}
	return fmt.Sprintf("%s.%s", *s, ext)
}

type StoreOption struct {
	AgentRunContext      *AgentRunContext
	AgentRunContextStore map[string]any
	AgentRunEvent        *AgentRunEventBuffer
	RunAgentRequest      *entity.RunAgentRequest
	RPCMeta              *codfishmodel.RPCMeta
}

type ContextStorage interface {
	Close() error
	Store(ctx context.Context, storageType StorageType, opt StoreOption) error
	Recover(ctx context.Context, storageType StorageType) (interface{}, bool, error)
	IsRestart() (bool, error)
}

type PromptLoader interface {
	LoadPrompt(key string) (string, error)
}

// RuntimeAPIClient is a subset of client.RuntimeAPIClient, just to avoid cyclic dependency
type RuntimeAPIClient interface {
	RecallKnowledgeBase(ctx context.Context, datasetID string, query string, topK int64) ([]*nextagent.RecallSegment, error)
	ListKnowledge(ctx context.Context, req *nextagent.ListKnowledgeRequest) (*nextagent.ListKnowledgeResponse, error)
	GetKnowledgeSetInfo(run *AgentRunContext, knowledgeSetIds []string) (*nextagent.ListKnowledgesetResponse, error)
	GetKnowledgeContent(ctx context.Context, datasetID, documentID string) (string, error)
}

type Telemetry interface {
	Collect(event string, params map[string]interface{})
}

type NoopTelemetry struct{}

func (n *NoopTelemetry) Collect(event string, params map[string]interface{}) {}

// RunEnviron contains environment variables that may be refreshed during agent run, and they need to be spreaded to all child contexts
// we use a pointer to avoid copying the map when passing it to child contexts
type RunEnviron struct {
	m  map[string]string
	mu sync.RWMutex
}

// NewRunEnviron 创建一个新的RunEnviron实例
func NewRunEnviron() *RunEnviron {
	return &RunEnviron{
		m: make(map[string]string),
	}
}

func (e *RunEnviron) Replace(m map[string]string) {
	if m == nil {
		m = make(map[string]string)
	}
	e.mu.Lock()
	e.m = m
	e.mu.Unlock()
}

func (e *RunEnviron) ToMap() map[string]string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return gmap.Clone(e.m)
}

func (e *RunEnviron) Set(key, value string) {
	e.mu.Lock()
	if e.m == nil {
		e.m = make(map[string]string)
	}
	e.m[key] = value
	e.mu.Unlock()
}

func (e *RunEnviron) Get(key string) string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.m[key]
}

type AgentRunContext struct {
	User         entity.User                        `json:"user"`       // user info running the agent
	UserInfo     entity.UserInfo                    `json:"user_info"`  // user info running the agent
	Config       *config.AgentRunConfig             `json:"config"`     // server configured agent config, stored in tcc
	PromptLoader PromptLoader                       `json:"-"`          // prompt loader for the agent
	Environ      *RunEnviron                        `json:"environ"`    // environment variables provided by server, stores sensitive data like JWTs
	Parameters   map[entity.RuntimeParameterKey]any `json:"parameters"` // user provided parameters
	State        *AgentRunState                     `json:"state"`      // state maintained by agent itself
	CtxStorage   ContextStorage                     `json:"-"`

	ctx       context.Context
	llm       framework.LLM
	bus       EventBus
	logger    Logger
	publisher *AgentEventPublisher
	uuid      uuid.Generator
	cli       RuntimeAPIClient

	background         BackgroundServiceManager
	workspace          Workspace
	artifacts          ArtifactService
	telemetry          Telemetry
	telemetryTracer    tracing.Tracer
	remoteToolExecutor RemoteToolExecutor
	StartedAt          time.Time

	// Knowledge recall timing tracking
	knowledgeRecallStartTime   time.Time
	knowledgeRecallEndTime     time.Time
	activeKnowledgeRecallCount int
	knowledgeRecallMutex       sync.Mutex
}

func (c *AgentRunContext) AddLogger(logger Logger) {
	c.logger = logger
}

var _ context.Context = &AgentRunContext{}

func NewRunContext(ctx context.Context,
	bus EventBus,
	llm framework.LLM,
	logger Logger,
	state *AgentRunState,
	parameters map[entity.RuntimeParameterKey]any,
	environ *RunEnviron,
	config *config.AgentRunConfig,
	promptLoader PromptLoader,
	pub *AgentEventPublisher,
	idGen uuid.Generator,
	remoteToolExecutor RemoteToolExecutor,
	backgroundService BackgroundServiceManager,
	artifactService ArtifactService,
	ctxStorage ContextStorage,
	runtimeAPIClient RuntimeAPIClient,
	telemetry Telemetry,
	telemetryTracer tracing.Tracer,
) *AgentRunContext {
	return &AgentRunContext{
		State:        state,
		Config:       config,
		PromptLoader: promptLoader,
		Environ:      lo.Ternary(environ != nil, environ, NewRunEnviron()),
		Parameters:   lo.Ternary(parameters != nil, parameters, map[entity.RuntimeParameterKey]any{}),
		ctx:          ctx,
		bus:          bus,
		llm:          llm,
		logger:       logger,
		publisher:    pub,
		uuid:         lo.Ternary(idGen != nil, idGen, uuid.GetDefaultGenerator(nil)),
		CtxStorage:   ctxStorage,

		artifacts:          artifactService,
		background:         backgroundService,
		remoteToolExecutor: remoteToolExecutor,
		cli:                runtimeAPIClient,
		telemetry:          telemetry,
		telemetryTracer:    telemetryTracer,
	}
}
func (c *AgentRunContext) GetAPIClient() RuntimeAPIClient {
	return c.cli
}

func (c *AgentRunContext) Deadline() (deadline time.Time, ok bool) {
	return c.ctx.Deadline()
}

func (c *AgentRunContext) Done() <-chan struct{} {
	return c.ctx.Done()
}

func (c *AgentRunContext) Err() error {
	return c.ctx.Err()
}

// Finish early ends the agent run
func (c *AgentRunContext) Finish(err error) {
	if err != nil {
		c.State.Status = AgentRunStatusFailed
	} else {
		c.State.Status = AgentRunStatusCompleted
	}
}

func (c *AgentRunContext) Value(key any) any {
	return c.ctx.Value(key)
}

func (c *AgentRunContext) Fork() *AgentRunContext {
	state := c.State.Clone()
	// TODO: init workspace by runtime
	context := NewRunContext(c.ctx, c.bus, c.llm, c.logger, state, c.Parameters, c.Environ, c.Config, c.PromptLoader, c.publisher, c.uuid, c.remoteToolExecutor, c.background, c.artifacts, c.CtxStorage, c.cli, c.telemetry, c.telemetryTracer)
	context.workspace = c.workspace
	context.User = c.User
	context.UserInfo = c.UserInfo
	return context
}

func (c *AgentRunContext) ForkWithContext(ctx context.Context) *AgentRunContext {
	newContext := c.Fork()
	newContext.workspace = c.workspace
	newContext.User = c.User
	newContext.UserInfo = c.UserInfo
	newContext.ctx = ctx
	return newContext
}

// 创建
func (c *AgentRunContext) WithContext(ctx context.Context) *AgentRunContext {
	newRun := NewRunContext(ctx, c.bus, c.llm, c.logger, c.State, c.Parameters, c.Environ, c.Config, c.PromptLoader,
		c.publisher, c.uuid, c.remoteToolExecutor, c.background, c.artifacts, c.CtxStorage, c.cli, c.telemetry, c.telemetryTracer)
	newRun.workspace = c.workspace
	newRun.User = c.User
	newRun.UserInfo = c.UserInfo
	return newRun
}

func (c *AgentRunContext) GetEnv(key string) string {
	v := c.Environ.Get(key)
	if v == "" {
		return os.Getenv(key)
	}
	return v
}

func (c *AgentRunContext) GetConfig() *AgentRunConfig {
	return &AgentRunConfig{
		AgentRunConfig: *c.Config,
	}
}

// StartKnowledgeRecallOperation marks the start of a knowledge recall operation
func (c *AgentRunContext) StartKnowledgeRecallOperation() {
	c.knowledgeRecallMutex.Lock()
	defer c.knowledgeRecallMutex.Unlock()

	// If this is the first knowledge recall operation since reset, record the start time
	if c.knowledgeRecallStartTime.IsZero() {
		c.knowledgeRecallStartTime = time.Now()
	}
	c.activeKnowledgeRecallCount++
}

// EndKnowledgeRecallOperation marks the end of a knowledge recall operation
func (c *AgentRunContext) EndKnowledgeRecallOperation() {
	c.knowledgeRecallMutex.Lock()
	defer c.knowledgeRecallMutex.Unlock()

	if c.activeKnowledgeRecallCount > 0 {
		c.activeKnowledgeRecallCount--
		// Always update the end time to track the latest operation end
		c.knowledgeRecallEndTime = time.Now()
	}
}

// GetTotalKnowledgeRecallTime returns the total time spent on knowledge recall
// This calculates the time from the first knowledge recall start to the last knowledge recall end
func (c *AgentRunContext) GetTotalKnowledgeRecallTime() time.Duration {
	c.knowledgeRecallMutex.Lock()
	defer c.knowledgeRecallMutex.Unlock()

	if c.knowledgeRecallStartTime.IsZero() {
		return 0
	}

	// If there are still active operations, use current time as end time
	endTime := c.knowledgeRecallEndTime
	if c.activeKnowledgeRecallCount > 0 {
		endTime = time.Now()
	}

	// If no end time has been recorded yet (no operations have completed),
	// and there are active operations, use current time
	if endTime.IsZero() && c.activeKnowledgeRecallCount > 0 {
		endTime = time.Now()
	}

	if endTime.IsZero() {
		return 0
	}

	return endTime.Sub(c.knowledgeRecallStartTime)
}

// ResetKnowledgeRecallTiming resets the knowledge recall timing
func (c *AgentRunContext) ResetKnowledgeRecallTiming() {
	c.knowledgeRecallMutex.Lock()
	defer c.knowledgeRecallMutex.Unlock()
	c.knowledgeRecallStartTime = time.Time{}
	c.knowledgeRecallEndTime = time.Time{}
	c.activeKnowledgeRecallCount = 0
}

func (c *AgentRunContext) GetTracer(ctx context.Context) agentrace.Tracer {
	return agentrace.GetRuntimeTracerFromContext(ctx)
}

func (c *AgentRunContext) GetStep(id string) *AgentRunStep {
	return c.State.Memory.GetStep(id)
}

// Deprecated: for Aime, the current step is not always the latest step; manually report the thought instead
func (c *AgentRunContext) UpdateThought(thought *Thought) {
	step := c.State.CurrentStep
	if step.Thought == nil {
		step.Thought = thought
		c.publisher.ReportThought(step)
		return
	}

	// only send thought if rational is different
	// otherwise it's outputting tool calling tokens
	if step.Thought.Rationale != thought.Rationale {
		c.publisher.ReportThought(step)
	}
	step.Thought = thought
}

func (c *AgentRunContext) UpdateThoughtDelta(delta string) {
	c.publisher.ReportThoughtDelta(c.State.AgentID, c.State.CurrentStep.StepID, delta)
}

func (c *AgentRunContext) UpdatePlan(plan string) {
	c.publisher.ReportPlanUpdate(plan)
}

func (c *AgentRunContext) GetRemoteToolExecutor() RemoteToolExecutor {
	return c.remoteToolExecutor
}

func (c *AgentRunContext) GetLLM() framework.LLM {
	return c.llm
}

func (c *AgentRunContext) GetLogger() Logger {
	return c.logger
}

func (c *AgentRunContext) GetPublisher() *AgentEventPublisher {
	return c.publisher
}

func (c *AgentRunContext) GetArtifactService() ArtifactService {
	return c.artifacts
}

func (c *AgentRunContext) GetTelemetry() Telemetry {
	if c.telemetry == nil {
		return &NoopTelemetry{}
	}
	return c.telemetry
}

func (c *AgentRunContext) RegisterBackgroundService(disposable Disposable) {
	c.background.AddDisposable(disposable)
}

func (c *AgentRunContext) GetBackgroundServiceManager() BackgroundServiceManager {
	return c.background
}

// Agents need cast workspace to actual type it uses
func (c *AgentRunContext) GetRawWorkspace() Workspace {
	return c.workspace
}

func (c *AgentRunContext) SetWorkspace(workspace Workspace) {
	c.workspace = workspace
}

type CreateStepOption struct {
	ExecutorAgent string
	Parent        *AgentRunStep
}

func (c *AgentRunContext) CreateStep(opts ...*CreateStepOption) *AgentRunStep {
	opt := CreateStepOption{}
	for _, o := range opts {
		if o == nil {
			continue
		}
		if o.ExecutorAgent != "" {
			opt.ExecutorAgent = o.ExecutorAgent
		}
		if o.Parent != nil {
			opt.Parent = o.Parent
		}
	}

	step := &AgentRunStep{
		StepID:        c.uuid.NewID(),
		ExecutorAgent: lo.Ternary(opt.ExecutorAgent != "", opt.ExecutorAgent, c.State.AgentID),
		Status:        AgentRunStepStatusCreated,
		Inputs:        make(map[string]any),
		Outputs:       make(map[string]any),
		Conclusion:    make(map[string]any),
		Parent:        lo.TernaryF(opt.Parent != nil, func() string { return opt.Parent.StepID }, func() string { return "" }),
	}
	c.State.CurrentStep = step
	c.publisher.ReportStep(step)
	c.logger.Infof("step created: %s, parent: %s, %s", step.StepID, step.Parent, step.ExecutorAgent)
	return step
}

func (c *AgentRunContext) Close() {
	c.workspace.Close()
	c.background.Dispose()
	c.CtxStorage.Close()
}

func (c *AgentRunContext) RestartFromOldRunCtx(oldCtx *AgentRunContext) {
	c.Environ = oldCtx.Environ
	c.Parameters = oldCtx.Parameters
	c.State.Status = oldCtx.State.Status
	c.State.Memory = oldCtx.State.Memory
	c.State.CurrentStep = oldCtx.State.CurrentStep
	c.State.Variables = oldCtx.State.Variables
	c.State.Conversation.Messages = oldCtx.State.Conversation.Messages
	c.State.Conversation.MessageStates = oldCtx.State.Conversation.MessageStates
}

func (c *AgentRunContext) GetTelemetryTracer() tracing.Tracer {
	if c.telemetryTracer == nil {
		return tracing.NewNoopTracer()
	}
	return c.telemetryTracer
}

func storeKey(t reflect.Type) string {
	return t.PkgPath() + "." + t.Name()
}

// RetrieveStore returns the store of the given type
// the type MUST be a serializable type and should be immutable
func RetrieveStore[T any](run *AgentRunContext) T {
	store := new(T)
	t := reflect.TypeOf(*store)
	k := storeKey(t)
	return RetrieveStoreByKey[T](run, k)
}

func RetrieveStoreByKey[T any](run *AgentRunContext, key string) T {
	store, ok := run.State.Store[key]
	// 如果内存里面不存在，先从历史数据里面捞
	if !ok {
		if run.CtxStorage != nil {
			run.GetLogger().Infof("key: %s, not found in memory, try to recover from history", key)
			data, _, _ := run.CtxStorage.Recover(context.Background(), StorageTypeAgentRunContextStore)
			if data != nil {
				// 如果能捞到则直接返回数据
				if storeData, ok := data.(map[string]json.RawMessage); ok {
					run.GetLogger().Infof("store data = %+v", lo.MapToSlice(storeData, func(key string, _ json.RawMessage) string {
						return key
					}))
					var result T
					if v, exist := storeData[key]; exist {
						run.GetLogger().Infof("found retrieved store data key: %s from history", key)
						err := json.Unmarshal(v, &result)
						if err != nil {
							run.GetLogger().Errorf("failed to unmarshal store data: %v, key: %s", err, key)
						} else {
							run.GetLogger().Infof("successfully retrieved history store data: %s", key)
							run.State.Store[key] = result
							return result
						}
					} else {
						run.GetLogger().Infof("not found retrieved store data key: %s from history", key)
					}
				}

			}
		}
		if store != nil {
			run.State.Store[key] = store
		}
	}
	return conv.DefaultAny[T](run.State.Store[key])
}

func UpdateStore[T any](run *AgentRunContext, store T) {
	t := reflect.TypeOf(store)
	run.State.Store[storeKey(t)] = store
}

func UpdateStoreByKey[T any](run *AgentRunContext, key string, store T) {
	run.State.Store[key] = store
}

func UpdateStoreFnByKey[T any](run *AgentRunContext, key string, fn func(T) T) {
	store := RetrieveStoreByKey[T](run, key)
	store = fn(store)
	UpdateStoreByKey(run, key, store)
}
