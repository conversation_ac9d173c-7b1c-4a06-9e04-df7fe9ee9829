package iris

import (
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

type Thought struct {
	// raw LLM output
	Content string `json:"content" mapstructure:"content"`

	// tool call
	Rationale    string     `json:"rationale" mapstructure:"rationale"`
	FunctionCall bool       `json:"function_call" mapstructure:"function_call"` // whether native function call is used
	ToolCallID   string     `json:"tool_call_id" mapstructure:"tool_call_id"`   // function call id corresponding to original tool call produced by LLM
	Tool         string     `json:"tool" mapstructure:"tool"`                   // tool name
	Parameters   Parameters `json:"parameters" mapstructure:"parameters"`       // parameters of the tool call

	// extra data parsed from LLM
	Data map[string]any `json:"data" mapstructure:"data"`

	// Usually for debugging.
	LLMCall LLMCall `json:"llm_call" mapstructure:"llm_call"`
}

type LLMCall struct {
	ModelName    string                   `json:"model_name" mapstructure:"model_name"`
	Temperature  float64                  `json:"temperature" mapstructure:"temperature"`
	Usage        *framework.TokenUsage    `json:"usage" mapstructure:"usage"`
	Prompt       []*framework.ChatMessage `json:"prompt" mapstructure:"prompt"`
	FinishReason string                   `json:"finish_reason" mapstructure:"finish_reason"`
	TraceID      string                   `json:"trace_id" mapstructure:"trace_id"`
}

func (t *Thought) IsNilOrEmpty() bool {
	return t == nil || (t.Content == "" &&
		t.Rationale == "" &&
		t.Tool == "" &&
		len(t.Parameters) == 0 &&
		len(t.Data) == 0)
}
