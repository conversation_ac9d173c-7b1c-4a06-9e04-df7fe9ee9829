package iris

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/agext/levenshtein"
	"github.com/samber/lo"
	"gopkg.in/yaml.v3"
)

type ProjectReferenceItem struct {
	Name string `json:"name" mapstructure:"name" yaml:"name"`
	Path string `json:"path" mapstructure:"path" yaml:"path"`
}

type ProjectReference []ProjectReferenceItem

func (p ProjectReference) Normalize(workingDirectory string) ProjectReference {
	for i, ref := range p {
		if filepath.IsAbs(ref.Path) {
			continue
		}
		p[i].Path = filepath.Join(workingDirectory, ref.Path)
	}

	// 只保留一级目录 (所有项目产物均只识别 workingDirectory 下的一级目录)
	for i, ref := range p {
		rel, err := filepath.Rel(workingDirectory, ref.Path)
		if err != nil || strings.HasPrefix(rel, "..") {
			// 跳过不在 workingDirectory 下的路径
			continue
		}
		parts := strings.Split(rel, string(os.PathSeparator))
		if len(parts) > 0 {
			p[i].Path = filepath.Join(workingDirectory, parts[0])
		}
	}

	// 针对 Path 进行最大努力修正（如果 path 不存在）
	entries, _ := os.ReadDir(workingDirectory)
	for i, ref := range p {
		_, err := os.Stat(ref.Path)
		if err == nil {
			continue // 路径存在，无需修正
		}
		bestPath := ""
		bestDistance := -1
		target := filepath.Base(ref.Path)
		// 遍历一级目录
		for _, entry := range entries {
			candidate := entry.Name()
			distance := levenshtein.Distance(candidate, target, nil)
			if bestDistance == -1 || distance < bestDistance {
				bestDistance = distance
				bestPath = filepath.Join(workingDirectory, candidate)
			}
		}
		// 如果找到相似度最高的，替换,阈值可调整
		if bestPath != "" && bestDistance <= 3 {
			p[i].Path = bestPath
		}
	}

	// 修正 name
	for i, ref := range p {
		p[i].Name = filepath.Base(ref.Path)
	}

	// 针对修正后的 project 进行过滤
	var filtered ProjectReference
	for _, ref := range p {
		// 过滤 workingDirectory
		if ref.Path == "" || ref.Path == workingDirectory {
			continue
		}
		// 过滤不存在的文件,或者不是目录
		if fileInfo, err := os.Stat(ref.Path); err != nil || !fileInfo.IsDir() {
			continue
		}
		filtered = append(filtered, ref)
	}
	filtered = lo.UniqBy(filtered, func(item ProjectReferenceItem) string {
		return item.Path
	})
	return filtered
}

func ParseProjectReference(markdown string) ProjectReference {
	var projectReference ProjectReference

	projectReferencesContent, err := ExtractProjectReferenceContent(markdown)
	if err != nil {
		if unmarshalErr := yaml.Unmarshal([]byte(markdown), &projectReference); unmarshalErr == nil {
			return projectReference
		}
		return projectReference
	}

	err = yaml.Unmarshal([]byte(projectReferencesContent), &projectReference)
	if err != nil {
		return projectReference
	}
	return projectReference
}

// ExtractProjectReferenceContent 提取 <project_reference> ... </project_reference> 之间的内容
func ExtractProjectReferenceContent(input string) (string, error) {
	// (?s) 让 . 匹配换行符
	re := regexp.MustCompile(`(?s)<project_reference>(.*?)</project_reference>`)
	matches := re.FindStringSubmatch(input)
	if len(matches) < 2 {
		return "", fmt.Errorf("no <project_reference> block found")
	}
	// 去除首尾空白
	content := strings.TrimSpace(matches[1])
	return content, nil
}
