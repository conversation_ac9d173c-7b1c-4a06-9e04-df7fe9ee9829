package iris

import (
	"os"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/gopkg/env"
)

// DeploymentRegion agent 部署区域（统一映射到控制面所在的区域）
type DeploymentRegion string

const (
	RegionCN   DeploymentRegion = "cn"
	RegionI18n DeploymentRegion = "i18n"
	RegionBOE  DeploymentRegion = "boe"
)

func CurrentRegion() DeploymentRegion {
	if len(os.Getenv(entity.RuntimeDockerArch)) > 0 {
		return RegionBOE // local docker
	}
	switch env.GetCurrentVRegion() {
	case env.VREGION_CHINANORTH:
		return RegionCN
	case env.VREGION_SINGAPORECENTRAL:
		return RegionI18n
	case env.VREGION_CHINABOE, env.VREGION_CHINABOE2:
		return RegionBOE
	default:
		return RegionCN
	}
}
