package agents

import (
	"context"
	"fmt"
	"time"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/core/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	condenser "code.byted.org/devgpt/kiwis/agentsphere/memory/condenser/impl"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"code.byted.org/devgpt/kiwis/lib/util"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type ProgreActState struct {
	ParentStepID string
	AgentStepID  string
	CurrentRound int

	LastFailedAction string
	CurrentFailures  int
	StartTime        int64

	Progress string
}

type StopConfig struct {
	MaxSteps    int
	MaxFailures int
	MaxDuration int64
}

type ProgreActAgent struct {
	name  string
	id    string
	Agent iris.RunnableAgent
	Tools []iris.Action

	ThinkFn         func(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *ProgreActState) (*iris.Thought, error)
	Composer        func(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *ProgreActState) []*framework.ChatMessage
	ShouldStopFn    func(run *iris.AgentRunContext, state *ProgreActState, stopConfig *StopConfig) bool
	ValidateThought func(thought *iris.Thought) error
	LLMHook         *prompt.LLMCallHook
	ThinkCallbacks  []ThinkCallback
	Condenser       *condenser.AutoSummarizer

	ForceConcludeResult *iris.Thought

	stopConfig *StopConfig

	// 是否强制生成工具调用，如果上一轮没有生成工具调用，则这一轮把 toolchoice 参数设置为 any 来强制生成工具调用。
	// 但是注意：目前 gemini 和 claude 在强制生成工具调用时无法同时生成 rationale，而 rationale 对效果有影响，因此该参数会被置为 false，直到下一次没有生成工具调用的时候。
	forceToolCall bool
}

var _ iris.Agent = &ProgreActAgent{}
var _ iris.RunnableAgent = &ProgreActAgent{}

func (a *ProgreActAgent) Name() string {
	return a.name
}

func (a *ProgreActAgent) Description() string {
	return a.Agent.Description()
}

func (a *ProgreActAgent) UID() string {
	return a.id
}

func (a *ProgreActAgent) StoreKey() string {
	return fmt.Sprintf("%s-%s-ProgreAct", a.name, a.id)
}

func (a *ProgreActAgent) ExpandMaxStep(maxSteps int) {
	a.stopConfig.MaxSteps = maxSteps
}

func (a *ProgreActAgent) NextStep(ctx context.Context, run *iris.AgentRunContext) (*iris.AgentRunStep, error) {
	state := iris.RetrieveStoreByKey[ProgreActState](run, a.StoreKey())
	logger := run.GetLogger()

	defer func() {
		if run.Err() == nil {
			state.CurrentRound++
		}
		iris.UpdateStoreByKey(run, a.StoreKey(), state)
	}()

	var (
		thought *iris.Thought
		err     error
	)

	if state.StartTime == 0 {
		state.StartTime = time.Now().Unix()
	}

	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: a.Agent.Name(),
		Parent:        lo.Ternary(state.AgentStepID != "", run.GetStep(state.AgentStepID), nil),
	})

	executionTime := time.Now().Unix() - state.StartTime
	logger.Infof("agent %s current round: %d,  execution time: %ds",
		a.id, state.CurrentRound, executionTime)

	mem := memory.GetAgentMemory(run)
	filteredSteps := lo.Filter(mem.ActionMemory, func(step *memory.ActionMemoryItem, _ int) bool {
		return step.ExecutorAgent == a.Agent.Name()
	})
	previousSteps := lo.Slice(filteredSteps, len(filteredSteps)-state.CurrentRound, len(filteredSteps))

	if lastStep := util.Last(previousSteps); lastStep != nil && lastStep.Error != nil {
		if lastStep.Action != nil {
			state.LastFailedAction = lastStep.Action.Name()
		} else {
			state.LastFailedAction = "no tool"
		}
		state.CurrentFailures++
	} else {
		state.LastFailedAction = ""
		state.CurrentFailures = 0
	}

	iris.UpdateStoreByKey(run, a.StoreKey(), state)

	validMemItems := lo.Filter(previousSteps, func(item *memory.ActionMemoryItem, _ int) bool {
		return item.Thought != nil && len(item.Thought.Content) > 0
	})
	validSteps := lo.Map(validMemItems, func(item *memory.ActionMemoryItem, _ int) *iris.AgentRunStep {
		return (*iris.AgentRunStep)(item)
	})

	thinkSpan, thinkCtx := agentrace.GetRuntimeTracerFromContext(ctx).
		StartCustomSpan(
			ctx,
			agentrace.SpanTypeStep,
			"think",
		)
	thinkRun := run.WithContext(thinkCtx)

	if a.ShouldStopFn != nil && a.ShouldStopFn(run, &state, a.stopConfig) {
		thought, err = a.forceConclude(thinkRun, validSteps, &state)
	} else {
		thought, err = a.ThinkFn(thinkRun, validSteps, &state)
	}
	if thought != nil {
		thinkSpan.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"thought": &iris.Thought{
				Tool:       thought.Tool,
				Parameters: thought.Parameters,
				Data:       thought.Data,
				Rationale:  thought.Rationale,
			},
		}))
	}
	agentrace.AddErrorTag(thinkSpan, err)
	thinkSpan.Finish()
	if err != nil {
		return step, err
	}

	a.UpdateState(run, thought, &state)

	tool, _ := lo.Find(a.Tools, func(tool iris.Action) bool {
		return tool.Name() == thought.Tool
	})

	if tool == nil {
		metrics.AR.LLMAbnormalThroughput.WithTags(&metrics.LLMAbnormalTag{
			Model:  run.GetConfig().GetModelByScene(a.Name()).Model,
			Reason: "tool_not_found",
		}).Add(1)
		err := iris.ThoughtParseErrorf("Tool `%s` is not found, make sure your output format is valid or you have selected the right tool", thought.Tool)
		logger.Errorf("Tool not found: %v", err)
		step.Inputs = thought.Parameters
		return step, err
	}

	step.Action = tool
	step.Inputs = thought.Parameters
	if thought.Tool == controltool.ToolProgreActConclusion {
		step.Inputs["progress"] = state.Progress
		return step, iris.NewCompleted(nil)
	}
	return step, nil
}

func (a *ProgreActAgent) UpdateState(run *iris.AgentRunContext, thought *iris.Thought, state *ProgreActState) {
	dataProg := conv.DefaultAny[string](thought.Data["progress"])
	paramProg := conv.DefaultAny[string](thought.Parameters["progress"])
	if len(dataProg) > 0 {
		state.Progress = dataProg
	} else if len(paramProg) > 0 {
		state.Progress = paramProg
	}
}

func (a *ProgreActAgent) ResetFailures(run *iris.AgentRunContext) {
	store := iris.RetrieveStoreByKey[ProgreActState](run, a.StoreKey())
	store.CurrentFailures = 0
	store.LastFailedAction = ""
	iris.UpdateStoreByKey(run, a.StoreKey(), store)
}

func (a *ProgreActAgent) forceConclude(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *ProgreActState) (*iris.Thought, error) {
	if a.ForceConcludeResult != nil {
		run.UpdateThought(a.ForceConcludeResult)
		return a.ForceConcludeResult, nil
	}

	logger := run.GetLogger()

	traceTemplates := prompt.TemplateSet{
		ThoughtTmpl: prompts.ThoughtPromptTemplate,
	}
	traceMessages := ComposeMessage(run, ComposerOptions{
		Templates:             traceTemplates,
		Tools:                 []iris.Action{},
		SystemPromptVariables: map[string]any{},
		UserPromptVariables:   map[string]any{},
		Trace:                 previousSteps,
	})

	templates := prompt.TemplateSet{
		SystemTmpl: prompts.ForceConcludeSystemPromptTemplate,
		UserTmpl:   prompts.ForceConcludeUserPromptTemplate,
	}
	messages := ComposeMessage(run, ComposerOptions{
		Templates: templates,
		Tools: []iris.Action{
			controltool.NewProgreActConclusionTool(),
		},
		SystemPromptVariables: map[string]any{},
		UserPromptVariables: map[string]any{
			"Trace": traceMessages,
		},
	})

	content, err := Think(run, "force_conclude", messages,
		ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 50,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{"<rationale>"},
				EndTokens:    []string{"</rationale>"},
				BreakTokens:  []string{"<tool>"},
			},
			Hooks: a.LLMHook,
			Tools: []iris.Action{
				controltool.NewProgreActConclusionTool(),
			},
			DisableThinkDelta: true,
		},
	)

	defaultThought := &iris.Thought{
		Content: "This task is finished accidently",
		Tool:    controltool.ToolProgreActConclusion,
		Parameters: map[string]any{
			"content":    "This task is finished accidently",
			"evaluation": "failed",
		},
	}

	if err != nil {
		return defaultThought, nil
	}

	thought := &iris.Thought{}
	if len(content.ToolCalls) > 0 {
		thought.Tool = content.ToolCalls[0].Tool.Name()
		thought.Parameters = content.ToolCalls[0].Parameters
		thought.ToolCallID = content.ToolCalls[0].ID
		thought.FunctionCall = true
	} else {
		parser := prompt.MDParser{
			Tools: []framework.Action{
				controltool.NewProgreActConclusionTool(),
			},
		}
		thought, err = parser.Parse(content.Content, prompt.ParseOption{Model: content.Model})
		if err != nil {
			logger.Errorf("Failed to parse thought: %v, content: %s", err, content)
			return defaultThought, nil
		}
	}

	thought.LLMCall = iris.LLMCall{
		ModelName:    content.Model,
		Temperature:  content.Temperature,
		Usage:        content.Usage,
		Prompt:       messages,
		FinishReason: content.FinishReason,
		TraceID:      content.TraceID,
	}
	run.UpdateThought(thought)
	return thought, lo.Ternary(thought.Tool == controltool.ToolProgreActConclusion, nil, iris.NewRecoverable(errors.WithMessage(err, "you can only use the `conclude` tool. Do not try to use other tools!!!")))
}

type ProgreActAgentRunOption struct {
	Task       string
	Memory     string
	Step       *iris.AgentRunStep
	CustomData map[string]string
}

func (a *ProgreActAgent) RunWithOption(run *iris.AgentRunContext, opt *ProgreActAgentRunOption) error {
	publisher := run.GetPublisher()
	state := iris.RetrieveStoreByKey[ProgreActState](run, a.StoreKey())

	step := opt.Step
	if step == nil {
		return errors.New("step is nil")
	}

	step.Inputs["task"] = opt.Task
	state.AgentStepID = step.StepID
	state.StartTime = time.Now().Unix()
	iris.UpdateStoreByKey(run, a.StoreKey(), state)

	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(step))

	originalStep := run.State.CurrentStep
	run.State.CurrentStep = nil
	defer func() {
		last := run.State.LastStep()
		step.Finish(last.Conclusion, last.Error)
		publisher.ReportStep(step)
		run.State.CurrentStep = originalStep
	}()

	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)
	return a.Agent.Run(run)
}

func (a *ProgreActAgent) Run(run *iris.AgentRunContext) error {
	return a.RunWithOption(run, nil)
}

func (a *ProgreActAgent) defaultThink(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *ProgreActState) (thought *iris.Thought, err error) {
	logger := run.GetLogger()
	messages := a.Composer(run, previousSteps, state)

	withCacheOption := false
	for _, m := range messages {
		if m.Content == "" && len(m.ContentParts) == 0 && len(m.ToolCalls) == 0 {
			logger.Errorf("empty %s message passed to llm at round %d", m.Role, state.CurrentRound)
		}
		for _, p := range m.ContentParts {
			if len(p.CacheControl) > 0 {
				withCacheOption = true
				break
			}
		}
		if len(m.CacheControl) > 0 {
			withCacheOption = true
		}
	}

	var toolChoice any
	if len(a.Tools) > 0 && a.forceToolCall {
		toolChoice = "any"
		a.forceToolCall = false
	}
	content, err := Think(run, a.Name(), messages,
		ThinkOption{
			PromptCacheSessionKey: lo.Ternary(withCacheOption, a.UID(), ""),
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 50,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{"<rationale>"},
				EndTokens:    []string{"</rationale>"},
				BreakTokens:  []string{"<tool>"},
			},
			Hooks:             a.LLMHook,
			Callbacks:         a.ThinkCallbacks,
			Tools:             a.Tools,
			DisableThinkDelta: true,
			ToolChoice:        toolChoice,
		},
	)

	if err != nil {
		// if iris.IsTokenLimted(err) {
		// 	// force condense the current messages
		// 	logger.Infof("condense messages at round %d, with condenser %v", state.CurrentRound, a.Condenser)
		// 	if a.Condenser != nil {
		// 		_, _ = a.Condenser.Summarize(run, messages, true)
		// 	}
		// }
		logger.Errorf("llm api not available: %v", err)
		return nil, iris.NewRecoverable(errors.WithMessage(err, "llm api not available"))
	}
	if len(a.Tools) > 0 && len(content.ToolCalls) == 0 {
		modelConfig := run.GetConfig().GetModelByScene(a.Name())
		metrics.AR.LLMAbnormalThroughput.WithTags(&metrics.LLMAbnormalTag{
			Model:  modelConfig.Model,
			Reason: "no_tool_call",
		}).Add(1)
		a.forceToolCall = true
		run.GetLogger().Warnf("model %s returned no tool call, set force tool call in next round", content.Model)
	}

	thought = &iris.Thought{
		Parameters: iris.Parameters{},
		Data:       iris.Parameters{},
	}
	if len(content.ToolCalls) > 0 {
		logger.Infof("model %s returned tool calls: %s", content.Model, conv.JSONFormatString(content.ToolCalls))
		logger.Infof("model %s returned tool name: %v", content.Model, lo.Map(content.ToolCalls, func(toolCall ToolCall, _ int) string {
			if toolCall.Tool != nil {
				return toolCall.Tool.Name()
			}
			return "unknown"
		}))
		thought.Content = content.Content
		thought.Tool = content.ToolCalls[0].Tool.Name()
		thought.Parameters = content.ToolCalls[0].Parameters
		thought.ToolCallID = content.ToolCalls[0].ID
		thought.FunctionCall = true
		thought.Rationale = content.Content
	} else {
		// for planner, use XML parser
		parser := prompt.Parser{ToolTag: "agent"}
		thought, err = parser.Parse(content.Content)
		if err != nil {
			logger.Errorf("Failed to parse thought: %v, content: %s", err, content)
			thought.Rationale = content.Content // the content is not parsable, treat the whole output as rationale
		}
		/*
			parser := prompt.MDParser{
				Tools: lo.Map(a.Tools, func(t iris.Action, _ int) framework.Action {
					return t
				}),
				Validate: a.ValidateThought,
			}
			thought, err = parser.Parse(content.Content, prompt.ParseOption{Model: content.Model})
			if err != nil {
				logger.Errorf("Failed to parse thought: %v, content: %s", err, content)
				thought.Rationale = content.Content // the content is not parsable, treat the whole output as rationale
			}
		*/
	}

	thought.LLMCall = iris.LLMCall{
		ModelName:    content.Model,
		Temperature:  content.Temperature,
		Usage:        content.Usage,
		Prompt:       messages,
		FinishReason: content.FinishReason,
		TraceID:      content.TraceID,
	}
	run.UpdateThought(thought)
	return thought, iris.NewRecoverable(errors.WithMessage(err, "failed to parse thought"))
}

func (a *ProgreActAgent) defaultShouldStop(run *iris.AgentRunContext, state *ProgreActState, config *StopConfig) bool {
	logger := run.GetLogger()
	if config.MaxFailures > 0 && state.CurrentFailures >= config.MaxFailures {
		logger.Errorf("Reached maximum failures (%d/%d). Current progress: %s",
			state.CurrentFailures, config.MaxFailures, state.Progress)
		return true
	}
	if config.MaxSteps > 0 && state.CurrentRound >= config.MaxSteps {
		logger.Errorf("Reached maximum steps (%d/%d). Current progress: %s",
			state.CurrentRound, config.MaxSteps, state.Progress)
		return true
	}
	if config.MaxDuration > 0 && time.Since(time.Unix(state.StartTime, 0)) > time.Duration(config.MaxDuration)*time.Minute {
		logger.Errorf("Reached maximum time (%d minutes). Current progress: %s",
			config.MaxDuration, state.Progress)
		return true
	}
	return false
}

type ProgreActAgentConfig struct {
	Info                                  iris.AgentInfo
	Tools                                 []iris.Action
	ThinkFn                               func(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *ProgreActState) (*iris.Thought, error)
	Composer                              func(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *ProgreActState) []*framework.ChatMessage
	ShouldStop                            func(run *iris.AgentRunContext, state *ProgreActState, config *StopConfig) bool
	ValidateThought                       func(thought *iris.Thought) error
	MaxSteps                              int
	MaxFailures                           int
	MaxDuration                           int64
	LLMHook                               *prompt.LLMCallHook
	ThinkCallbacks                        []ThinkCallback
	SerialIdentifier, ProgreActIdentifier string
	DisableSummarizer                     bool // whether to summarize tool calls, disabled for testing&benchmarking
}

func NewProgreActAgent(config ProgreActAgentConfig) *ProgreActAgent {
	tools := config.Tools

	id := config.ProgreActIdentifier
	if id == "" {
		id = fmt.Sprintf("%s-ProgreAct-%s", config.Info.Identifier, util.RandomString(8))
	}
	agent := &ProgreActAgent{
		name:            config.Info.Identifier,
		id:              id,
		Tools:           tools,
		Composer:        config.Composer,
		ThinkFn:         config.ThinkFn,
		ShouldStopFn:    config.ShouldStop,
		ValidateThought: config.ValidateThought,
		stopConfig: &StopConfig{
			MaxSteps:    config.MaxSteps,
			MaxFailures: config.MaxFailures,
			MaxDuration: config.MaxDuration,
		},
		LLMHook:        config.LLMHook,
		ThinkCallbacks: config.ThinkCallbacks,
	}

	if agent.ThinkFn == nil {
		agent.ThinkFn = agent.defaultThink
	}
	if agent.ShouldStopFn == nil {
		agent.ShouldStopFn = agent.defaultShouldStop
	}
	opts := []SerialAgentOption{
		WithSerialStages([]SerialStage{
			{
				Agent: func(run *iris.AgentRunContext) iris.Agent {
					return agent
				},
			},
		}),
	}
	if !config.DisableSummarizer {
		opts = append(opts, WithSerialSummarizer(Summarize))
	}
	identifier := config.SerialIdentifier
	if identifier == "" {
		identifier = fmt.Sprintf("%s-serial-%s", config.Info.Identifier, util.RandomString(8))
	}
	agent.Agent = NewSerialAgent(iris.AgentInfo{
		Identifier: identifier,
		Desc:       config.Info.Desc,
	}, opts...)

	return agent
}
