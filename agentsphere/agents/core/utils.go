package agents

import (
	"context"
	"crypto/md5"
	"fmt"
	"regexp"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"

	"github.com/bytedance/gopkg/util/logger"
	"github.com/cenkalti/backoff/v4"
	"github.com/google/uuid"
	"github.com/mozillazg/go-pinyin"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	action_prompts "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/core/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/tracing"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"code.byted.org/devgpt/kiwis/search/dal/llm"
)

type ComposerOptions struct {
	Templates             prompt.TemplateSet
	Tools                 []iris.Action
	SystemPromptVariables map[string]any // {"tools": []ToolDescription, "variables": SystemPromptVariables}
	UserPromptVariables   any
	MemoryRounds          int // how many round of tool call results to remember
	Trace                 []*iris.AgentRunStep
	FormatObservation     func(run *iris.AgentRunContext, step *iris.AgentRunStep) framework.ChatMessage
}

func ComposeMessage(run *iris.AgentRunContext, options ComposerOptions) []*framework.ChatMessage {
	composer := prompt.NewPromptComposer(options.Templates, run.GetLogger())

	messages := []*framework.ChatMessage{}
	if options.Templates.SystemTmpl != nil {
		systemPrompt, _ := composer.SystemPrompt(options.Tools, options.SystemPromptVariables)
		messages = append(messages, &framework.ChatMessage{
			Role:    llm.RoleSystem,
			Content: systemPrompt,
		})
	}
	if options.Templates.UserTmpl != nil {
		userPrompt, _ := composer.UserPrompt(options.UserPromptVariables)
		messages = append(messages, &framework.ChatMessage{
			Role:    llm.RoleUser,
			Content: userPrompt,
		})
	}

	if len(options.Trace) > 0 {
		observations := lo.FlatMap(options.Trace, func(step *iris.AgentRunStep, idx int) []*framework.ChatMessage {
			thought, _ := composer.ThoughtPrompt(step.Thought)
			observation := framework.ChatMessage{
				Role: llm.RoleUser,
			}
			var message string
			if options.FormatObservation != nil {
				observation = options.FormatObservation(run, step)
				message = observation.Content
			} else if options.MemoryRounds > 0 && len(options.Trace)-idx > options.MemoryRounds {
				message = action_prompts.ForgottenToolObservation(run, step.Action.Name(), step.Inputs, step.Outputs, step.Error)
			} else if step.Action != nil {
				message = action_prompts.ToolObservation(run, step.StepID, step.Action.Name(), step.Inputs, step.Outputs, step.Error)
			} else if step.Error != nil {
				message = step.Error.Error()
			}
			if len(message) == 0 {
				message = "No action is executed."
				logger.Warnf("empty observation")
			}
			observation.Content = message
			return []*framework.ChatMessage{
				{
					Role:    llm.RoleAssistant,
					Content: thought,
				},
				&observation,
			}
		})
		messages = append(messages, observations...)
	}
	return messages
}

type ToolCall struct {
	// raw fields from llm
	ID            string `json:"id"`
	Name          string `json:"name"`
	RawParameters string `json:"raw_parameters"`

	// parsed fields
	Tool       iris.Action     `json:"-"`
	Parameters iris.Parameters `json:"parameters"`
}

type ThinkResult struct {
	Content          string     `json:"content"`
	ReasoningContent string     `json:"reasoning_content"`
	ToolCalls        []ToolCall `json:"tool_calls"`

	Model        string                `json:"model"`
	Temperature  float64               `json:"temperature"`
	Usage        *framework.TokenUsage `json:"usage"`
	FinishReason string                `json:"finish_reason"`

	TraceID string `json:"trace_id"`
}

var (
	functionNameRegex    = regexp.MustCompile("[^a-zA-Z0-9_]+")
	multiUnderscoreRegex = regexp.MustCompile("__+")
)

func SanitizeToolName(name string) string {
	// Claude: [a-zA-Z0-9_-]+, len<64
	// 将中文字符转换为拼音，用下划线分隔，并替换掉空格/制表符等特殊字符
	// 兼容旧的 MCP 中可能出现的非 ASCII 字符（已知有空格/特殊字符）
	// 新 MCP 仅支持中英文+下划线
	// 08.19 update: 模型function call虽然能接收"-"，但在输出时总是会把 - 处理成 _ or __，因此对-做转义处理
	result := name
	type segment struct {
		content   string
		isChinese bool
	}
	segments := make([]segment, 0, len(result))
	for _, r := range result {
		if unicode.Is(unicode.Han, r) {
			pys := pinyin.SinglePinyin(r, pinyin.NewArgs())
			if len(pys) > 0 {
				segments = append(segments, segment{
					content:   strings.ToLower(pys[0]),
					isChinese: true,
				})
			}
		} else if len(segments) > 0 && !segments[len(segments)-1].isChinese {
			segments[len(segments)-1].content += string(r)
		} else {
			segments = append(segments, segment{
				content:   string(r),
				isChinese: false,
			})
		}
	}
	fmt.Printf("segments: %v\n", segments)
	result = strings.Join(lo.Map(segments, func(s segment, _ int) string {
		return s.content
	}), "_")

	// 处理特殊字符映射，原本就是分割符的替换成 '-'，其他的替换成 '_'
	replacer := strings.NewReplacer(
		"@", "_at_",
		"#", "_hash_",
		"&", "_and_",
		"\\", "_",
		"/", "_",
		":", "_",
		"+", "_",
		"=", "_",
		"|", "_",
		"<", "_",
		">", "_",
		"?", "_",
		";", "_",
		"'", "_",
		"\"", "_",
		",", "_",
		".", "_",
		"!", "_",
		"~", "_",
		"`", "_",
		"\t", "_",
		"\n", "_",
		"\r", "_",
		"\f", "_",
		"\v", "_",
		"\b", "_",
		" ", "_",
		"-", "_",
	)
	result = replacer.Replace(result)

	// 清理无效字符（保留字母、数字、下划线、连字符）
	result = functionNameRegex.ReplaceAllString(result, "_")

	// mcp:中文 会变成 mcp-_
	result = strings.ReplaceAll(result, "-_", "_")

	// 处理多个连续下划线
	result = multiUnderscoreRegex.ReplaceAllString(result, "_")

	// 去掉前后的下划线
	result = strings.Trim(result, "_")

	// 如果结果为空或者超长，返回MD5哈希
	if len(result) == 0 || len(result) >= 64 {
		return fmt.Sprintf("tool_%x", md5.Sum([]byte(name)))
	}

	return strings.ToLower(result)
}

type ThinkOption struct {
	// 要命中 prompt cache，同序列的请求需要根据 key 请求到同一个账号上，例如一个 chat 的多次 message。
	PromptCacheSessionKey string
	Tools                 []iris.Action
	StreamFilter          streamparser.StreamFilter
	Hooks                 *prompt.LLMCallHook
	DisableThinkDelta     bool
	ToolChoice            any

	// Callbacks for the Think function, following a middleware pattern.
	Callbacks []ThinkCallback
}

// ThinkEndCallback is executed at the end of the Think process.
type ThinkEndCallback func(result ThinkResult, messages []*framework.ChatMessage, err error)

// ThinkCallback is executed at the start of the Think process and may return a ThinkEndCallback.
type ThinkCallback func(ctx *iris.AgentRunContext, agentTag string, messages []*framework.ChatMessage, opt framework.LLMCompletionOption) ThinkEndCallback

func Think(run *iris.AgentRunContext, agentTag string, messages []*framework.ChatMessage, opt ThinkOption) (result ThinkResult, err error) {
	llmapi, logger := run.GetLLM(), run.GetLogger()
	span, c := run.GetTracer(run).
		StartCustomSpan(run, agentrace.SpanTypeLLMCall, agentTag,
			agentrace.WithObjectSpanData(map[string]any{"prompt_messages": messages}),
		)
	traceId := uuid.New().String()
	span.SetTags(agentrace.NewTagKV("trace_id", traceId))
	run = run.WithContext(context.WithValue(c, framework.LLMTraceIDKey, traceId))
	result.TraceID = traceId

	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	if opt.Hooks != nil {
		messages = opt.Hooks.OnRequest(messages)
	}

	config := run.Config
	sb := new(strings.Builder)
	reasoningSb := new(strings.Builder)
	var modelConfig framework.LLMCompletionOption
	if len(config.ModelScenesConfig.SceneSpecifications) > 0 {
		sceneConfig := run.GetConfig().GetModelByScene(agentTag)
		modelConfig = framework.LLMCompletionOption{
			Model:          sceneConfig.Model,
			Temperature:    sceneConfig.Temperature,
			TopP:           sceneConfig.TopP,
			MaxTokens:      sceneConfig.MaxTokens,
			Thinking:       sceneConfig.Thinking,
			Tag:            sceneConfig.Tag,
			FallbackModels: sceneConfig.FallbackModels,

			PromptCacheSessionKey: opt.PromptCacheSessionKey,
		}
	} else {
		modelConfig = framework.LLMCompletionOption{
			Model:       config.Model.Model,
			Temperature: config.Model.Temperature,
			TopP:        lo.ToPtr(config.Model.TopP),
			MaxTokens:   config.Model.MaxTokens,
			Thinking:    config.Model.Thinking,
			Tag:         agentTag,

			PromptCacheSessionKey: opt.PromptCacheSessionKey,
		}
	}
	modelConfig.ToolChoice = opt.ToolChoice
	modelConfig.Tools = lo.Map(opt.Tools, func(tool iris.Action, _ int) framework.LLMToolFunction {
		return framework.LLMToolFunction{
			Name:             SanitizeToolName(tool.Name()),
			Description:      tool.Description(),
			ParametersSchema: tool.InputSpec(),
		}
	})
	result.Model = modelConfig.Model
	result.Temperature = float64(modelConfig.Temperature)
	var finishReason framework.LLMFinishReason
	repetitionCount := 0
	var curMessages []*framework.ChatMessage
	// trace think callback
	var endCallbacks []ThinkEndCallback
	if opt.Callbacks != nil {
		for _, cb := range opt.Callbacks {
			if cb != nil {
				if endCb := cb(run, agentTag, messages, modelConfig); endCb != nil {
					endCallbacks = append(endCallbacks, endCb)
				}
			}
		}
	}
	defer func() {
		// Execute end callbacks in reverse order (LIFO).
		for i := len(endCallbacks) - 1; i >= 0; i-- {
			endCallbacks[i](result, curMessages, err)
		}
	}()
	err = backoff.Retry(func() error {
		// convert historical tool calls to sanitized names
		curMessages = lo.Map(messages, func(msg *framework.ChatMessage, _ int) *framework.ChatMessage {
			if msg.Role == llm.RoleAssistant && len(msg.ToolCalls) > 0 {
				msg.ToolCalls = lo.Map(msg.ToolCalls, func(toolCall framework.LLMToolFunction, _ int) framework.LLMToolFunction {
					toolCall.Name = SanitizeToolName(toolCall.Name)
					return toolCall
				})
			}
			return msg
		})
		var (
			streamErr error
		)
		cnt := 0
		for {
			modelConfig.Tools = lo.Map(opt.Tools, func(tool iris.Action, idx int) framework.LLMToolFunction {
				toolFunc := framework.LLMToolFunction{
					Name:             SanitizeToolName(tool.Name()),
					Description:      tool.Description(),
					ParametersSchema: tool.InputSpec(),
				}
				if len(opt.PromptCacheSessionKey) > 0 && idx == len(opt.Tools)-1 {
					// FIXME(cyx): fix hardcode
					toolFunc.CacheControl = "ephemeral"
				}
				return toolFunc
			})

			ch := llmapi.ChatCompletionStream(run, curMessages, modelConfig)
			batchLen := 0
			streamErr = stream.ForEachWithInterrupt(run, ch, func(chunk framework.LLMChunk) error {
				if len(chunk.ReasoningContent) > 0 {
					reasoningSb.WriteString(chunk.ReasoningContent)
				}
				sb.WriteString(chunk.Content)
				batchLen += len(chunk.Content)
				filtered := opt.StreamFilter.FilterToken(chunk.Content)
				if len(filtered) > 0 && !opt.DisableThinkDelta {
					run.UpdateThoughtDelta(filtered)
				}
				if len(chunk.ToolCalls) > 0 {
					for _, toolCall := range chunk.ToolCalls {
						// Gemini 返回的 tool call 没有 ID，只有 Name 来区分不同 tool call
						if len(toolCall.ID) > 0 || len(toolCall.Name) > 0 {
							result.ToolCalls = append(result.ToolCalls, ToolCall{
								ID:            toolCall.ID,
								Name:          toolCall.Name,
								RawParameters: conv.DefaultAny[string](toolCall.Parameters),
								Parameters:    iris.Parameters{},
							})
						} else {
							result.ToolCalls[len(result.ToolCalls)-1].RawParameters += conv.DefaultAny[string](toolCall.Parameters)
						}
					}
				}
				if chunk.TokenUsage != nil && chunk.TokenUsage.TotalTokens > 0 {
					result.Usage = chunk.TokenUsage
				}
				if len(chunk.FinishReason) > 0 {
					finishReason = chunk.FinishReason
				}
				if batchLen > 4096 {
					batchLen = 0
					if prompt.DetectRepetition(sb.String()) {
						_ = metrics.AR.LLMAbnormalThroughput.WithTags(&metrics.LLMAbnormalTag{
							Model:  modelConfig.Model,
							Reason: "repetition",
						}).Add(1)
						return errors.New("mass repetitive content detected")
					}
				}
				return nil
			})
			if finishReason != framework.LLMFinishReasonLength || run.Err() != nil {
				break
			}
			content := sb.String()
			// try to detect for infinite repetetion if response seems to reach max tokens
			// and regenerate if detected
			// in some abnormal case, we see output of 1 character per output token, so the threshold is 1:1
			// if max_tokens is not set, use minimal 4K as threshold
			if len(content) >= min(int(modelConfig.MaxTokens), 4096) {
				if prompt.DetectRepetition(content) {
					_ = metrics.AR.LLMAbnormalThroughput.WithTags(&metrics.LLMAbnormalTag{
						Model:  modelConfig.Model,
						Reason: "repetition",
					}).Add(1)
					repetitionCount++
					if repetitionCount > 3 {
						run.GetLogger().Infof("llm response is too repetitive, skip retry")
						return backoff.Permanent(errors.New("mass repetitive content detected"))
					} else {
						run.GetLogger().Infof("llm response is too repetitive, rejected and regenerate... (count: %d)", repetitionCount)
						return errors.New("mass repetitive content detected")
					}
				}
			}

			if unicode.IsSpace(lo.LastOrEmpty([]rune(content))) {
				content = strings.TrimRight(content, " \t\n\r")
				sb.Reset()
				sb.WriteString(content)
			}

			curMessages = messages
			curMessages = append(curMessages, &framework.ChatMessage{
				Role:    llm.RoleAssistant,
				Content: content,
			})

			cnt += 1
			if cnt > 3 {
				break
			}

			run.GetLogger().Infof("model returned incomplete response, retrying...")
			if streamErr != nil {
				run.GetLogger().Infof("think stream error: %v", streamErr)
			}
		}
		err = streamErr
		// The run is canceled, not retry.
		if run.Err() != nil {
			return backoff.Permanent(run.Err())
		}
		// 只在没有任何 chunk 时才返回错误
		if sb.Len() != 0 || len(result.ToolCalls) > 0 {
			return nil
		}
		if iris.IsTokenLimted(streamErr) {
			_ = metrics.AR.LLMAbnormalThroughput.WithTags(&metrics.LLMAbnormalTag{
				Model:  modelConfig.Model,
				Reason: "bad_request",
			}).Add(1)
			// should not retry: prompt too large the same request will still get bad request error
			return backoff.Permanent(streamErr)
		}
		if streamErr == nil {
			streamErr = fmt.Errorf("the model returned empty response")
		}
		return streamErr // trigger retry
	},
		backoff.WithMaxRetries(backoff.NewExponentialBackOff(backoff.WithInitialInterval(time.Second*5), backoff.WithMaxInterval(time.Second*60)), 10),
	)
	if err != nil {
		return result, err
	}

	result.Content = sb.String()
	result.ReasoningContent = reasoningSb.String()
	result.FinishReason = string(finishReason)

	if opt.Hooks != nil {
		hookRes := opt.Hooks.OnResponse(&framework.LLMResult{
			Content:          result.Content,
			ReasoningContent: result.ReasoningContent,
			FinishReason:     framework.LLMFinishReason(result.FinishReason),
			TokenUsage:       result.Usage,
		})
		result.Content = hookRes.Content
		result.ToolCalls = lo.Map(hookRes.ToolCalls, func(toolCall framework.LLMToolFunction, _ int) ToolCall {
			return ToolCall{
				ID:            toolCall.ID,
				Name:          toolCall.Name,
				RawParameters: toolCall.RawParameters,
				Parameters:    conv.DecodeJSON[iris.Parameters](toolCall.RawParameters),
			}
		})
		result.ReasoningContent = hookRes.ReasoningContent
		result.Usage = hookRes.TokenUsage
	}

	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"llm_call": result}))

	// 安全地获取 StepID，避免空指针异常
	var stepID string
	if run.State.CurrentStep != nil {
		stepID = run.State.CurrentStep.StepID
	} else {
		stepID = "unknown"
	}

	logger.Debugf("step %s think finished (finished reason: %v), agentTag: %s, (by model %s)\n---\n%s\n---\n",
		stepID, finishReason, agentTag, modelConfig.Model, result.Content)
	if finishReason != framework.LLMFinishReasonStop {
		_ = metrics.AR.LLMAbnormalThroughput.WithTags(&metrics.LLMAbnormalTag{
			Model:  modelConfig.Model,
			Reason: fmt.Sprintf("finish_%s", finishReason),
		}).Add(1)
	}
	if len(result.ReasoningContent) > 0 {
		run.GetLogger().Infof("model %s returned reasoning content: %s", modelConfig.Model, result.ReasoningContent)
	}
	if len(result.ToolCalls) > 0 {
		result.ToolCalls = lo.FilterMap(result.ToolCalls, func(toolCall ToolCall, _ int) (ToolCall, bool) {
			tool, found := lo.Find(opt.Tools, func(tool iris.Action) bool {
				return SanitizeToolName(tool.Name()) == toolCall.Name
			})
			if !found {
				run.GetLogger().Infof("model %s returned unknown tool call: %s", modelConfig.Model, toolCall.Name)
				return toolCall, false
			}
			toolCall.Tool = tool
			toolCall.Parameters = conv.DecodeJSON[iris.Parameters](toolCall.RawParameters)
			return toolCall, true
		})
		run.GetLogger().Infof("model %s completed tool calls: %s", modelConfig.Model, conv.JSONFormatString(result.ToolCalls))
	}
	return result, nil
}

// Claude 模型的 tokenizer 没有开源，本身阈值就已经留了30%的buffer，这里做一个简略计算就行
func estimateClaudeTokens(text string) int {
	// Claude 模型大约每 4 个字符算作 1 个 token
	charCount := utf8.RuneCountInString(text)
	return charCount / 4
}

// 计算消息列表中的总 token 数
func CalculateTotalTokens(messages []*framework.ChatMessage) int {
	totalTokens := 0
	for _, msg := range messages {
		totalTokens += estimateClaudeTokens(msg.Content)
	}
	return totalTokens
}

// generate a summary of the tool call operation by llm
func Summarize(run *iris.AgentRunContext, step *iris.AgentRunStep) string {
	// 检查 step 是否为 nil
	if step == nil {
		run.GetLogger().Warn("step is nil in Summarize function")
		return ""
	}

	action := step.Action
	inputs := step.Inputs
	thought := step.Thought

	// 检查 action 或 thought 是否为 nil
	var actionName, actionDescription, thoughtRationale string
	if action != nil {
		actionName = action.Name()
		actionDescription = action.Description()
	} else {
		actionName = "未知操作"
		actionDescription = "未知描述"
	}

	if thought != nil {
		thoughtRationale = thought.Rationale
	} else {
		thoughtRationale = "未知原因"
	}

	messages := []*framework.ChatMessage{
		{
			Role: llm.RoleSystem,
			Content: fmt.Sprintf(`
Summarize tool call operations in a brief, concise phrase(less than 20 words).
Focus on the final purpose of the tool call or the main action.
Do not include the tool name in the output.

Examples:
Convert markdown to html file
Checking if the domain is valid
Executing command: ls -al /home/<USER>
Cloning repo {repo_name}

Notes:
- Respond in %s by default
- Only use another language if it's explicitly requested by the initial request. The initial request is: "%s"
`, GetUserLanguage(run.Parameters), run.State.Conversation.GetMessages()[0].Content),
		},
		{
			Role: llm.RoleUser,
			Content: fmt.Sprintf(`
Reasoning for the tool call: %s
Tool Name: %s
Tool Description: %s
Tool Inputs: %v
`, thoughtRationale, actionName, actionDescription, inputs),
		},
	}
	content, err := Think(run, "summarize", messages, ThinkOption{
		StreamFilter: streamparser.StreamFilter{
			MaxCheckSize: 10,
			FlushSize:    1,
			AheadSize:    10,
			StartTokens:  []string{},
			EndTokens:    []string{},
			BreakTokens:  []string{},
		},
	})
	if err != nil {
		run.GetLogger().Warn("failed to generate tool call summary: %v", err)
		return ""
	}
	return content.Content
}

func GetUserLanguage(params map[entity.RuntimeParameterKey]any) string {
	lang, ok := params[entity.RuntimeParametersUserLocale]
	if !ok {
		return "Chinese (Simplified)"
	}
	switch strings.ToLower(conv.DefaultAny[string](lang)) {
	case "zh", "zh-cn":
		return "Chinese (Simplified)"
	case "zh-tw":
		return "Chinese (Traditional)"
	case "en", "en-us":
		return "English"
	case "ja-jp":
		return "Japanese"
	default:
		return "Chinese (Simplified)"
	}
}

// ExtractFirstCodeBlock 提取字符串中第一个代码块内容
// 如果没有找到代码块，则返回原内容
func ExtractFirstCodeBlock(input string) string {
	// 定义匹配代码块的正则表达式
	// 匹配 ``` 后跟可选的语言标识符，然后是代码内容，最后是 ```
	re := regexp.MustCompile(`(?s)\x60{3}[a-zA-Z0-9]*\n(.*?)(?:\x60{3}|$)`)

	matches := re.FindStringSubmatch(input)
	if len(matches) >= 2 {
		// 找到代码块，返回代码内容（去除前后空白）
		return strings.TrimSpace(matches[1])
	}

	// 没有找到代码块，返回原内容
	return input
}

func ExpandFileContent(run *iris.AgentRunContext, filepath, contentDescription string) (string, error) {
	lastStep := run.State.LastStep()
	originalTask := ""
	if lastStep != nil && run.GetStep(lastStep.Parent) != nil {
		originalTask = conv.DefaultAny[string](run.GetStep(lastStep.Parent).Inputs["task"])
	}
	// preSteps := lo.Filter(run.State.Steps, func(item *iris.AgentRunStep, index int) bool {
	// 	return item.ExecutorAgent == lastStep.ExecutorAgent && item.StepID != lastStep.StepID
	// })
	messages := ComposeMessage(run, ComposerOptions{
		Templates: prompt.TemplateSet{
			SystemTmpl:  prompts.ExpandFileContentSystemPromptTemplate,
			UserTmpl:    prompts.ExpandFileContentUserPromptTemplate,
			ThoughtTmpl: prompts.ThoughtPromptTemplate,
		},
		SystemPromptVariables: map[string]any{
			"Language":     GetUserLanguage(run.Parameters),
			"OriginalTask": originalTask,
		},
		UserPromptVariables: map[string]any{
			"Filepath":           filepath,
			"ContentDescription": contentDescription,
		},
		// Trace: preSteps,
	})
	// var traces []*framework.ChatMessage
	// if len(messages) > 2 {
	// 	traces = messages[2:]
	// 	builder := strings.Builder{}
	// 	for _, trace := range traces {
	// 		builder.WriteString(trace.Content + "\n")
	// 	}
	// 	messages = []*framework.ChatMessage{
	// 		messages[0],
	// 		{
	// 			Role:    llm.RoleUser,
	// 			Content: "Following is the previous action execution trace",
	// 		},
	// 		{
	// 			Role:    llm.RoleAssistant,
	// 			Content: builder.String(),
	// 		},
	// 		messages[1],
	// 	}
	// }
	result, err := Think(run, "expand_file_content", messages, ThinkOption{
		StreamFilter: streamparser.StreamFilter{},
	})
	if err != nil {
		return "", errors.WithMessage(err, "failed to generate content based on description")
	}
	if run.State.CurrentStep != nil {
		originalThought := run.State.CurrentStep.Thought
		run.State.CurrentStep.Thought = &iris.Thought{
			Content: result.Content,
			LLMCall: iris.LLMCall{
				ModelName:    result.Model,
				Prompt:       messages,
				Usage:        result.Usage,
				Temperature:  result.Temperature,
				FinishReason: result.FinishReason,
				TraceID:      result.TraceID,
			},
		}
		run.GetPublisher().ReportThought(run.State.CurrentStep)
		run.State.CurrentStep.Thought = originalThought
	}
	content := ExtractFirstCodeBlock(result.Content)

	return content, nil
}

func NewThinkCallback(logger iris.Logger, run *iris.AgentRunContext, tag string) ThinkCallback {
	return func(_ *iris.AgentRunContext, agentTag string, messages []*framework.ChatMessage, opt framework.LLMCompletionOption) ThinkEndCallback {
		parentSpan := tracing.GetCurrentSpan()
		tSpan, _ := run.GetTelemetryTracer().StartSpan(run, "think", map[string]interface{}{
			"type":   tracing.SpanEventThinking,
			"tool":   lo.Ternary(tag == "", agentTag, tag),
			"inputs": messages,
			"config": opt,
		}, tracing.SpanTypeSpan, parentSpan)

		return func(result ThinkResult, messages []*framework.ChatMessage, err error) {
			tSpan.SetTag("inputs", messages)
			tSpan.SetTag("outputs", result.Content)
			tSpan.SetTag("tool_calls", result.ToolCalls)
			if err != nil {
				tSpan.SetError(err)
			}
			tSpan.End()
		}
	}
}
