package dynamicactor

import (
	"embed"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	action_prompts "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/toolset"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	websearchactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	agenttoolset "code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool/toolset"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience/filter"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	condenser "code.byted.org/devgpt/kiwis/agentsphere/memory/condenser/impl"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
)

var (
	Identifier = "mewtwo"
	parameters = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "A self contained task prompt that can be completed by the agent. The description should give out background context and the specific goals, but not detailed datapoints or libraries to use.",
		},
		{
			Name:        "persona",
			Description: "Persona to put at the very first of the system prompt. Describe the agent's role, capabilities, and any other relevant information",
		},
		{
			Name:        "tools_selection_thought",
			Description: "Iterate through the toolset list ONE BY ONE, consider each toolset's suitability for the task. Format: `<toolset_name>:yes/maybe/nice to have/no`. Do not skip any toolset.",
		},
		{
			Name:        "tools",
			Description: "Tools given to the created agent in comma separated strings, e.g. `tool1,tool2`. Include all toolsets possible to be used by the agent",
		},
	}
	parametersNewbie = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "A self contained task prompt that can be completed by the agent. The description should give out background context ,the specific goals and `Important Notes`, but not detailed datapoints or libraries to use. `Important Notes` section should containing all relevant items from the `Important Notes` section of the [Problem Statement] that pertain to the current task. At the same time, add some important points of attention that can help complete the task with higher quality in this section too.",
		},
		{
			Name:        "persona",
			Description: "Persona to put at the very first of the system prompt. Describe the agent's role, capabilities, and any other relevant information. Should establish: 1) Specific expertise domain and professional level 2) Working methodology and quality standards 3) Problem-solving approach and attention to detail 4) Communication style that balances clarity with technical accuracy. The persona should inspire confidence while setting high performance expectations.",
		},
		{
			Name:        "tools_selection_thought",
			Description: "Iterate through the toolset list ONE BY ONE, consider each toolset's suitability for the task. Format: `<toolset_name>:yes/maybe/nice to have/no`, no explanation. Do not skip any toolset.",
		},
		{
			Name:        "tools",
			Description: "Tools given to the created agent in comma separated strings, e.g. `tool1,tool2`. Include all toolsets possible to be used by the agent",
		},
	}
	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")

	_ = lo.Must(promptset.ExecutePrompt("description", "", nil))
)

func init() {
	websearchtool.SearchAgentCreator = func(run *iris.AgentRunContext) websearchtool.InternalAISearcher {
		enableInternalSearch := cast.ToBool(run.Parameters[websearchactor.EnableInternalSearchParameterKey])
		run.GetLogger().Infof("enable_internal_search: %v", enableInternalSearch)
		return websearchactor.New(websearchactor.CreateOption{EnableBytedSearch: enableInternalSearch})
	}
}

type DynamicAgentStore struct {
	CurrentTask string
}

type Agent struct {
	actors.BaseActor

	ProgreAct *agents.ProgreActAgent
	Condenser *condenser.AutoSummarizer
	Variant   string

	Persona    string
	Knowledges []knowledges.KnowledgeItem

	ContextParts ContextParts

	Toolsets []toolset.Toolset

	PromptCache *agents.PromptCache
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	Name      string
	MaxSteps  int
	LLMHooks  *prompt.LLMCallHook
	ActorStep *iris.AgentRunStep

	// dynamically decided by model
	Variant        string
	Persona        string
	Knowledges     []knowledges.KnowledgeItem
	Toolsets       []string
	TaskContext    string
	ExecutionTrace string

	DisableSummarizer bool // whether to summarize tool calls, defaults to true, disabled for testing&benchmarking
}

func New(run *iris.AgentRunContext, opt CreateOption) *Agent {
	// builtin toolsets.
	toolsets, _ := toolset.GetToolsets(run, opt.ActorStep)
	toolsets = append(toolsets, agenttoolset.GetAgentToolsets(run, opt.ActorStep)...)
	userDefineToolsets, _ := toolset.GetUserDefineToolsets(run)
	plannerStore := iris.RetrieveStoreByKey[planactentity.PlannerStore](run, planactentity.PlannerStoreKey)
	// bultin toolsets 和 userDefineToolsets 一起过滤
	filteredToolsets := make([]toolset.Toolset, 0, len(opt.Toolsets))
	allToolsets := append(toolsets, userDefineToolsets...)
	withTemplateExpSOP := iris.RetrieveStoreByKey[genexp.TemplateExpSOPStore](run, genexp.TemplateExpSOPStoreKey).Enabled
	for _, requiredToolset := range opt.Toolsets {
		if len(strings.TrimSpace(requiredToolset)) == 0 {
			continue
		}
		toolset, ok := lo.Find(allToolsets, func(t toolset.Toolset) bool {
			return t.Identifier == requiredToolset
		})
		if !ok {
			run.GetLogger().Warnf("with template exp sop: %v, missing required toolset %s", withTemplateExpSOP, requiredToolset)
			continue
		}
		run.GetLogger().Infof("with template exp sop: %v, assigned required toolset %s", withTemplateExpSOP, requiredToolset)
		filteredToolsets = append(filteredToolsets, toolset)
	}
	filteredToolsets = append(filteredToolsets, toolset.GetMCPLoopToolset(run)...)
	info := iris.AgentInfo{
		Identifier: lo.Ternary(opt.Name == "", Identifier, opt.Name),
		Desc: lo.Must(promptset.ExecutePrompt("description", opt.Variant, map[string]any{
			"Toolsets":           lo.Filter(toolsets, func(t toolset.Toolset, _ int) bool { return !t.HiddenFromPlanner }),
			"UserDefineToolsets": userDefineToolsets,
		})),
	}
	executionStep := &actors.ExecutionStep{
		Step: opt.ActorStep,
	}
	reactor := agents.NewProgreActAgent(agents.ProgreActAgentConfig{
		Info:              info,
		Tools:             toolset.BuildTools(run, filteredToolsets, executionStep, knowledges.NewSemanticKnowledgebase(run, knowledges.LoadKnowledge(), run.GetConfig().GetModelByScene("retrieve_knowledge")), Identifier),
		MaxSteps:          opt.MaxSteps,
		MaxFailures:       3,
		DisableSummarizer: opt.DisableSummarizer,
		ThinkCallbacks:    []agents.ThinkCallback{agents.NewThinkCallback(run.GetLogger(), run, info.Identifier)},
	})

	adapterParams := parameters
	if opt.Variant == agententity.VariantNewbie {
		adapterParams = parametersNewbie
	}
	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: adapterParams,
		},
		Variant:    opt.Variant,
		Persona:    opt.Persona,
		Knowledges: opt.Knowledges,
		ContextParts: ContextParts{
			TaskContext:    opt.TaskContext,
			ExecutionTrace: opt.ExecutionTrace,
			Locale:         agents.GetUserLanguage(run.Parameters),
			UserRequest:    strings.TrimSpace(lo.Ternary(plannerStore.EnhancedRequirements != "", plannerStore.EnhancedRequirements, plannerStore.Requirements)),
			RepoMemory:     GetWorkspaceRepoMemory(run),
		},
		Condenser: condenser.NewAutoSummarizer(&condenser.NewAutoSummarizerConfig{
			Name:    Identifier,
			LLM:     run.GetLLM(),
			Config:  run.Config,
			Variant: opt.Variant,
		}),
		Toolsets: filteredToolsets,

		PromptCache: nil,
	}
	if opt.Variant == agententity.VariantExpert {
		agent.PromptCache = &agents.PromptCache{
			MaxCachePoints:       3, // `tools` list uses 1 cache point.
			CacheType:            "ephemeral",
			NoCacheLastNMessages: 1,
			NewCachePointChars:   5000,
			CachePoints:          map[int]bool{},
		}
	}
	reactor.Composer = agent.Compose
	reactor.Condenser = agent.Condenser
	agent.ProgreAct = reactor
	agent.ExecutionStep = executionStep

	return agent
}

func Create(run *iris.AgentRunContext, input string, actorStep *iris.AgentRunStep, llmHook *prompt.LLMCallHook) *Agent {
	variant := run.GetConfig().GetVariantByScene(Identifier)
	tags, err := prompt.ParseTopTagsV2(input)
	if err != nil {
		run.GetLogger().Errorf("failed to parse top tags: %s", err)
		return nil
	}
	persona, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "persona"
	})
	toolsetsContent, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "tools"
	})
	toolsets := lo.Map(strings.Split(toolsetsContent.Content, ","), func(toolset string, _ int) string {
		return strings.TrimSpace(toolset)
	})
	run.GetLogger().Infof("toolsets: %+v", toolsets)
	task, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "task"
	})
	executionTrace, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "execution_trace"
	})

	return New(run, CreateOption{
		ActorStep:         actorStep,
		Variant:           variant,
		Persona:           persona.Content,
		Toolsets:          toolsets,
		TaskContext:       task.Content,
		ExecutionTrace:    executionTrace.Content,
		Knowledges:        nil,
		LLMHooks:          llmHook,
		DisableSummarizer: conv.DefaultAny[bool](run.Parameters[entity.RuntimeParametersDisableToolSummarizer]),
	})
}

func (a *Agent) Compose(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *agents.ProgreActState) []*framework.ChatMessage {
	systemMessages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(promptset.GetTemplate("system", a.Variant), util.ValueToMap(a.GetSystemParts(run))),
		prompt.WithUserMessage(promptset.GetTemplate("user", a.Variant), util.ValueToMap(a.ContextParts)),
	})
	if err != nil {
		run.GetLogger().Errorf("failed to compose messages: %s", err)
	}
	cacheType := ""
	// Use ephemeral cache for agent using claude.
	if a.Variant == agententity.VariantExpert {
		cacheType = "ephemeral"
	}
	a.Condenser.SetPrefix(run, systemMessages) // 压缩时估算前序消息 token 数防止超 token
	workspaceStructure := GetWorkspaceStructure(run)

	// 自定义观察格式化函数，为 Action 结果添加前缀
	observationFormatFunc := func(run *iris.AgentRunContext, step *iris.AgentRunStep) framework.ChatMessage {
		var message string
		if step.Action != nil {
			message = action_prompts.ToolObservation(run, step.StepID, step.Action.Name(), step.Inputs, step.Outputs, step.Error)
			// even if the action does not have a template, it should have a SimpleToolObservation which renders outputs as json
			// this should be a last resort
			if len(message) == 0 {
				message = fmt.Sprintf("`%s` is executed, but no result is returned.", step.Action.Name())
			}
		} else if step.Error != nil {
			message = step.Error.Error()
			if len(message) == 0 {
				message = "No action is executed."
			}
		}
		// 当 AI 看到 user 消息以 [ACTION_RESULT] 开头时，减少不必要的注意力转移
		return framework.ChatMessage{
			Role:       lo.Ternary(step.Thought.FunctionCall, framework.RoleTool, framework.RoleUser),
			Content:    "[ACTION_RESULT]\n" + message,
			ToolCallID: step.Thought.ToolCallID,
		}
	}
	mem := memory.GetAgentMemory(run)
	lastStep := mem.LastStep()

	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(promptset.GetTemplate("system", a.Variant), util.ValueToMap(a.GetSystemParts(run))),
		prompt.WithPromptCache(
			prompt.WithUserMessage(promptset.GetTemplate("user", a.Variant), util.ValueToMap(a.ContextParts)),
			cacheType,
		),
		mem.WithCurrentContext(run, &memory.MemoryComposeOptions{
			ThoughtPrompt:         promptset.GetTemplate("thought", a.Variant),
			ObservationPrompt:     promptset.GetTemplate("user", a.Variant),
			Variant:               a.Variant,
			StoreKey:              memory.GetMemoryStoreKey(a.ProgreAct.Agent.Name()),
			TaskContext:           a.ContextParts.TaskContext,
			ObservationFormatFunc: observationFormatFunc,
			Condenser:             a.Condenser,
		}),
		prompt.WithUserMessage(promptset.GetTemplate("last_message", a.Variant), map[string]any{
			"Workspace":             workspaceStructure.Workspace,
			"WorkspaceRepositories": workspaceStructure.WorkspaceRepositories,
			"RootStructure":         workspaceStructure.RootStructure,
			"LastActions": lo.TernaryF(lastStep != nil && lastStep.Action != nil, func() string {
				return lastStep.Action.Name() // TODO: parallel function call
			}, func() string {
				return "none"
			}),
		}),
	})
	if err != nil {
		run.GetLogger().Errorf("failed to compose messages: %s", err)
	}
	if a.PromptCache != nil {
		a.PromptCache.UpdateCachePoints(messages)
	}
	return messages
}

func (a *Agent) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	logger := run.GetLogger()
	tags, _ := prompt.ParseTopTagsV2(input)
	task, _ := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "task"
	})
	if options.ReuseTrace {
		mem := memory.GetAgentMemory(run)
		previousSteps := lo.Filter(mem.ActionMemory, func(step *memory.ActionMemoryItem, _ int) bool {
			return step.ExecutorAgent == a.ProgreAct.Agent.Name()
		})
		lastStep, exist := lo.Last(previousSteps)
		logger.Infof("reuse trace last step: %+v of total %d steps", lastStep, len(previousSteps))
		if exist {
			lastStep.Outputs = map[string]any{"new_user_requirements": "The previous task is finished. Here is the new requirements of user:\n" + input}
		}
	}
	span, ctx := agentrace.GetRuntimeTracerFromContext(run).
		StartCustomSpan(
			run,
			agentrace.SpanTypeStep,
			"mewtwo_run",
			agentrace.WithObjectSpanData(
				map[string]any{
					"input": input,
					"tools": lo.Map(a.ProgreAct.Tools, func(tool iris.Action, _ int) prompt.ToolDescription {
						return prompt.GetToolDescription(tool)
					}),
				},
			),
		)
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(
			map[string]any{
				"result": result,
			},
		))
		span.Finish()
	}()
	start := time.Now()
	defer func() {
		telemetry.EmitActorExecution(run, a.Name(), lo.Map(a.Toolsets, func(t toolset.Toolset, _ int) string { return t.Identifier }), string(result.Evaluation), time.Since(start))
	}()

	// 并行执行知识召回和经验召回
	logger.Infof("retrieving knowledge and experience for task: %s", input)

	experienceRetriever, err := experience.NewExperienceRetriever()
	if err != nil {
		logger.Errorf("failed to create experience retriever: %v", err)
	}

	scenario := iris.RetrieveStoreByKey[knowledges.Scenario](run, knowledges.ScenarioStoreKey)

	group := errgroup.Group{}
	group.SetLimit(-1)

	waitChan := make(chan struct{}, 1)

	// Retrieve builtin knowledges.
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("knowledge retrieval panic: %v", r)
			}
		}()

		kb := knowledges.CreateKnowledgebase(run)
		referenceStore := iris.RetrieveStoreByKey[planactentity.ReferenceStore](run, planactentity.ReferenceStoreKey)
		// update knowledge store
		// shared with progress_think
		knowledgeStore := iris.RetrieveStoreByKey[knowledges.Store](run, knowledges.StoreKey)
		knowledgeStore.Renders = make([]*knowledges.KnowledgeRender, 0)

		var knowledgeList []knowledges.KnowledgeItem

		param := knowledges.RetrieveParam{
			Scenario:     scenario,
			Agent:        a.Identifier,
			Variant:      a.Variant,
			Tools:        lo.Map(a.Toolsets, func(t toolset.Toolset, _ int) string { return t.Identifier }),
			WithCitation: len(referenceStore.SearchedRef.List) > 0,
		}
		kgGroup := errgroup.Group{}
		mu := sync.Mutex{}
		// system knowledge
		kgGroup.Go(func() error {
			kg, err := kb.RetrieveKnowledge(run, knowledges.KgRetrieveOption{
				Query:    task.Content,
				Strategy: knowledges.KgRetrieveStrategyLLM,
				Category: knowledges.KgRetrieveCategorySystem,
				Param:    param,
			})
			if err == nil {
				mu.Lock()
				knowledgeList = append(knowledgeList, kg...)
				mu.Unlock()
			}
			return nil
		})

		// scenario knowledge
		kgGroup.Go(func() error {
			kg, err := kb.RetrieveKnowledge(run, knowledges.KgRetrieveOption{
				Query:    task.Content,
				Strategy: knowledges.KgRetrieveStrategyLLM,
				Category: knowledges.KgRetrieveCategoryScenario,
				Param:    param,
			})
			if err == nil {
				knowledge, _ := promptset.ExecutePrompt("scenario_knowledge", a.Variant, map[string]any{
					"Knowledges": kg,
				})
				// scenario knowledge inject in user task, not system prompt
				a.ContextParts.ScenarioKnowledge = knowledge
				for _, item := range kg {
					knowledgeStore.Renders = append(knowledgeStore.Renders, &knowledges.KnowledgeRender{
						Knowledge: item,
						StepID:    knowledges.SystemKnowledge,
					})
				}
			}
			return nil
		})
		// tool knowledge
		kgGroup.Go(func() error {
			kg, err := kb.RetrieveKnowledge(run, knowledges.KgRetrieveOption{
				Query:    task.Content,
				Strategy: knowledges.KgRetrieveStrategyLLM,
				Category: knowledges.KgRetrieveCategoryTool,
				Param:    param,
			})
			if err == nil {
				knowledgeList = append(knowledgeList, kg...)
			}
			return nil
		})
		kgGroup.Wait()

		knowledgeList = lo.UniqBy(knowledgeList, func(item knowledges.KnowledgeItem) string {
			return item.ID
		})

		knowledge, _ := promptset.ExecutePrompt("system_knowledge", a.Variant, map[string]any{
			"Knowledges": knowledgeList,
		})
		logger.Infof("retrieved system knowledge: %s", knowledge)

		for _, knowledge := range knowledgeList {
			knowledgeStore.Renders = append(knowledgeStore.Renders, &knowledges.KnowledgeRender{
				Knowledge: knowledge,
				StepID:    knowledges.SystemKnowledge,
			})
		}
		iris.UpdateStoreByKey(run, knowledges.StoreKey, knowledgeStore)
		a.Knowledges = knowledgeList
		span.UpdateData(agentrace.NewObjectSpanData(
			map[string]any{
				"knowledges": knowledgeList,
			},
		))

		return nil
	})

	// Retrieve experience Insights.
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("experience retrieval panic: %v", r)
			}
		}()

		if !experience.EnabledExpInsights(run) {
			logger.Infof("experience insights is disabled")
			return nil
		}

		experienceTypes := []string{
			experience.Insights,
			experience.InsightsBadCase,
		}

		logger.Infof("experience scenario: %s", scenario.Key)
		if scenario.Key == "data_analyze" {
			experienceTypes = append(experienceTypes, experience.InsightsScript)
		}

		queryInput := task.Content
		if queryInput == "" {
			logger.Warn("no valid query input for experience retrieval")
			return nil
		}
		plannerStore := iris.RetrieveStoreByKey[planactentity.PlannerStore](run, planactentity.PlannerStoreKey)
		var filteredExperiences []filter.ExperienceItem
		experiencePrompt, filteredExperiences, err := experienceRetriever.RetrieveInsights(
			run,
			queryInput,
			strings.TrimSpace(lo.Ternary(plannerStore.EnhancedRequirements != "", plannerStore.EnhancedRequirements, plannerStore.Requirements)),
			experience.MewtwoApplyType,
			experienceTypes,
			experience.DefaultLimitExperience,
			[]string{},
		)
		if err != nil {
			logger.Errorf("failed to retrieve experience: %s", err)
			return nil
		}

		storeKey := fmt.Sprintf("retrieved_ids_%s", a.ProgreAct.Agent.Name())
		retrieverStore := iris.RetrieveStoreByKey[experience.RetrieverStore](run, storeKey)
		retrieverStore.RetrievedIds = lo.Map(filteredExperiences, func(item filter.ExperienceItem, _ int) string {
			return item.ID
		})
		retrieverStore.IsProgress = false
		retrieverStore.OriginQuery = queryInput
		iris.UpdateStoreByKey(run, storeKey, retrieverStore)

		if experiencePrompt != "" {
			// 更新 ContextParts 中的经验信息
			a.ContextParts.ExpInsights = experiencePrompt
			logger.Infof("retrieved experience prompt: %s", experiencePrompt)
		} else {
			logger.Infof("no relevant experience found")
		}

		return nil
	})

	// Retrieve tool experience.
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("tool experience retrieval panic: %v", r)
			}
		}()

		if !experience.EnabledExpInsights(run) {
			logger.Infof("experience insights is disabled")
			return nil
		}
		toolNames := lo.Map(a.ProgreAct.Tools, func(t iris.Action, _ int) string {
			return t.Name()
		})
		if len(toolNames) == 0 {
			logger.Warn("no tools input for experience retrieval")
			return nil
		}

		experiencePrompt, _, _ := experienceRetriever.RetrieveInsights(
			run,
			toolNames,
			"",
			experience.MewtwoApplyType,
			[]string{
				experience.InsightsTools,
			},
			1,
			[]string{},
		)

		if experiencePrompt != "" {
			// 更新 ContextParts 中的经验信息
			a.ContextParts.ExpInsightsTool = experiencePrompt
			logger.Infof("retrieved tool experience prompt: %s", experiencePrompt)
		} else {
			logger.Infof("no relevant tool experience found")
		}

		return nil
	})

	// Retrieve history memory
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("experience retrieval panic: %v", r)
			}
		}()

		if memory.DisableMemoryConfig(run) {
			logger.Infof("memory is disabled")
			return nil
		}
		mem := memory.GetAgentMemory(run)
		memoryMessages, _ := mem.WithPreviousContext(run, &memory.MemoryComposeOptions{
			Variant:     a.Variant,
			TaskContext: a.ContextParts.TaskContext,
			Condenser:   a.Condenser,
		})
		iris.UpdateStoreByKey(run, memory.GetMemoryStoreKey(a.ProgreAct.Agent.Name()), memoryMessages)
		return nil
	})

	// Retrieve experience reusable workflows.
	group.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("experience retrieval panic: %v", r)
			}
		}()

		if !experience.EnabledExpReusableWorkflow(run) {
			logger.Infof("experience reusable workflow is disabled")
			return nil
		}

		logger.Info("retrieving experience reusable workflows")
		workflows, err := experienceRetriever.RetreiveReusableWorkflows(run, task.Content)
		if err != nil {
			logger.Errorf("failed to retrieve experience reusable workflows: %v", err)
			return nil
		}

		logger.Infof("retrieved experience reusable workflows: %d", len(workflows))

		expReusableWorkflows, _ := promptset.ExecutePrompt("system_exp_reusable_workflows", a.Variant, map[string]any{
			"ExpReusableWorkflows": workflows,
		})
		logger.Infof("retrieved system exp reusable workflows: %s", expReusableWorkflows)

		// Store reusable workflows to state, so that it can be retrieved in the `ReuseWorkflow` tool.
		expStore := iris.RetrieveStoreByKey[experience.Store](run, experience.StoreKey)
		expStore.ReusableWorkflows = workflows

		iris.UpdateStoreByKey(run, experience.StoreKey, expStore)
		a.ContextParts.ExpReusableWorkflows = expReusableWorkflows

		return nil
	})

	go func() {
		defer func() {
			close(waitChan)
		}()

		group.Wait()
	}()

	select {
	case <-time.After(120 * time.Second):
		run.GetLogger().Errorf("retrieval timeout after 60 seconds")
		// Timeout, use current results.
		// FIXME(cyx): cancel the retrieval goroutines to avoid leaking and potential concurrent access.
		goto retrievalComplete
	case <-waitChan:
		run.GetLogger().Info("retrieval completed")
	}

retrievalComplete:
	err = a.ProgreAct.RunWithOption(run, &agents.ProgreActAgentRunOption{
		Step: a.ExecutionStep.Step,
	})
	if err != nil {
		// the actor need to return a iris.NewFatal(err) with a non-nil error to send a fatal error message to user
		isFatal := iris.IsFatal(err)
		result = controltool.ConclusionOutput{
			Content:    err.Error(),
			Evaluation: lo.Ternary(isFatal, controltool.ConclusionEvaluationFatal, controltool.ConclusionEvaluationFailed),
		}
	}

	lastStep := run.State.LastStep()
	mapstructure.Decode(lastStep.Outputs, &result)
	return result
}
