package mcpcaller

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"strings"
	"sync"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/argos"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/bitsanalysis"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/codebase"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/devmind"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/meego"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/oncall"
	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/mcp/client"
	"code.byted.org/gopkg/logs/v2"
	gomcpclient "github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"

	"github.com/gabriel-vasile/mimetype"
	"github.com/samber/lo"
)

func call(run *iris.AgentRunContext, args agententity.MCPCallArgs) (*agententity.MCPCallOutput, error) {
	_, err := connectThirdPart(args.BaseURL)
	if err != nil {
		return nil, iris.NewRecoverable(fmt.Errorf("failed to connect to server: %v", err))
	}
	cli, ok := clients[args.BaseURL]
	if !ok {
		return nil, iris.NewRecoverable(fmt.Errorf("unknown base url: %s", args.BaseURL))
	}

	log := run.GetLogger()
	var argsJSON map[string]any
	err = json.Unmarshal([]byte(args.Arguments), &argsJSON)
	if err != nil {
		return nil, iris.NewRecoverable(fmt.Errorf("failed to parse arguments: %v", err))
	}
	callRequest := mcp.CallToolRequest{}
	callRequest.Params.Name = args.Name
	callRequest.Params.Arguments = argsJSON
	resp, err := cli.CallTool(run, callRequest)
	if err != nil {
		return nil, iris.NewRecoverable(err)
	}

	images := mcptool.ExtractImageContent(resp.Content)
	for _, image := range images {
		buf := make([]byte, len(image.Data))
		decoded, err := base64.StdEncoding.Decode([]byte(image.Data), buf)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		mime := mimetype.Lookup(image.MIMEType)
		err = os.WriteFile(fmt.Sprintf("%s.%s", args.Name, mime.Extension()), buf[:decoded], os.ModePerm)
		if err != nil {
			log.Errorf("failed to write image to file: %v", err)
			return nil, iris.NewRecoverable(err)
		}
	}
	return &agententity.MCPCallOutput{
		TextContents: mcptool.ExtractTextContent(resp.Content),
		ImageContents: lo.Map(images, func(image mcp.ImageContent, _ int) string {
			return fmt.Sprintf("%s.%s", args.Name, mimetype.Lookup(image.MIMEType).Extension())
		}),
	}, nil
}

var initialized sync.Once
var mu sync.Mutex

func Init() {
	mu.Lock()
	defer mu.Unlock()
	initialized.Do(func() {
		clients = make(map[string]gomcpclient.MCPClient)
		server, err := connectThirdPart("stdio://a_map")
		if err != nil {
			logs.Fatalf("failed to connect to a_map: %v", err)
		} else {
			clients["stdio://a_map"] = server
		}
		server, err = connectThirdPart("stdio://figma")
		if err != nil {
			logs.Fatalf("failed to connect to figma: %v", err)
		} else {
			clients["stdio://figma"] = server
		}
		server, err = connectThirdPart("stdio://arxiv")
		if err != nil {
			logs.Fatalf("failed to connect to arxiv: %v", err)
		} else {
			clients["stdio://arxiv"] = server
		}
		server, err = connectThirdPart("stdio://unsplash")
		if err != nil {
			logs.Fatalf("failed to connect to unsplash: %v", err)
		} else {
			clients["stdio://unsplash"] = server
		}
		server, err = connectThirdPart("stdio://google_maps")
		if err != nil {
			logs.Fatalf("failed to connect to google_maps: %v", err)
		} else {
			clients["stdio://google_maps"] = server
		}
		server, err = connectThirdPart("stdio://yfinance")
		if err != nil {
			logs.Fatalf("failed to connect to yfinance: %v", err)
		} else {
			clients["stdio://yfinance"] = server
		}
		server, err = connectThirdPart("stdio://google_image_search")
		if err != nil {
			logs.Fatalf("failed to connect to google_image_search: %v", err)
		} else {
			clients["stdio://google_image_search"] = server
		}
	})
}

var clients = map[string]gomcpclient.MCPClient{}

func connectThirdPart(baseUrl string) (gomcpclient.MCPClient, error) {
	mu.Lock()
	defer mu.Unlock()
	cli, ok := clients[baseUrl]
	if ok {
		return cli, nil
	}
	var (
		cmd     string
		env     []string
		cliArgs []string
	)
	splitN := strings.Split(baseUrl, "://")
	cliType := client.TransportType(splitN[0])
	server := splitN[1]

	switch server {
	case "a_map":
		cmd = "npx"
		env = []string{
			"AMAP_MAPS_API_KEY=72cbd4e721ca47af30f366ab572d8829",
		}
		cliArgs = []string{
			"-y",
			"@amap/amap-maps-mcp-server",
		}
	case "google_maps":
		cmd = "npx"
		env = []string{
			"GOOGLE_MAPS_API_KEY=AIzaSyDJuZYLj7zx8HlwQ7miGhpjShBTAffxX5k",
		}
		cliArgs = []string{
			"-y",
			"@modelcontextprotocol/server-google-maps",
		}
	case "figma":
		cmd = "node"
		env = []string{
			"FIGMA_ACCESS_TOKEN=*********************************************",
		}
		cliArgs = []string{
			"/opt/mcp_servers/figma-mcp-chunked/build/index.js",
		}
	case "arxiv":
		cmd = "uv"
		cwd, err := os.Getwd()
		if err != nil {
			cwd = "/workspace/arxiv/papers"
		}
		cliArgs = []string{
			"--directory",
			"/opt/mcp_servers/arxiv-mcp-server",
			"run",
			"arxiv-mcp-server",
			"--storage-path", cwd + "/papers",
		}
		env = []string{
			"http_proxy=http://sys-proxy-rd-relay.byted.org:8118",
			"https_proxy=http://sys-proxy-rd-relay.byted.org:8118",
			"use_proxy=yes",
		}
	case "unsplash":
		cmd = "uv"
		cliArgs = []string{
			"--directory",
			"/opt/mcp_servers/unsplash-mcp-server",
			"run",
			"--with",
			"fastmcp",
			"fastmcp",
			"run",
			"/opt/mcp_servers/unsplash-mcp-server/server.py",
		}
		env = []string{
			"UNSPLASH_ACCESS_KEY=JfMu2HOp-jhufz2_7wRjrdf7c29ULRK-7jb6QJtXskY",
		}
	case "google_image_search":
		cmd = "npx"
		cliArgs = []string{
			"-y",
			"@apify/actors-mcp-server",
			"--actors",
			"devisty/google-image-search",
		}
		env = []string{
			"APIFY_TOKEN=**********************************************",
			// "HTTP_PROXY=sys-proxy-rd-relay.byted.org:8118",
			// "HTTPS_PROXY=sys-proxy-rd-relay.byted.org:8118",
			// "NO_PROXY=.byted.org,10.0.0.0/8,172.16.0.0/12,192.168.0.0/16,fc00::/7",
		}
	case "yfinance":
		cmd = "npx"
		cliArgs = []string{
			"-y",
			"@apify/actors-mcp-server",
			"--actors",
			"nmdmnd/yahoo-finance",
		}
		env = []string{
			"APIFY_TOKEN=**********************************************",
		}
	default:
		return nil, fmt.Errorf("unknown server: %s", server)
	}
	var err error

	if cliType == client.TransportTypeStdio {
		cli, err = gomcpclient.NewStdioMCPClient(cmd, env, cliArgs...)
		if err != nil {
			return nil, err
		}
	} else if cliType == client.TransportTypeSSE {
		// todo test/fix this
		scli, err := gomcpclient.NewSSEMCPClient("https://" + server)
		if err != nil {
			return nil, err
		}
		cli = scli
		err = scli.Start(context.Background())
		if err != nil {
			return nil, err
		}
	} else {
		return nil, fmt.Errorf("unknown cli type: %s", cliType)
	}

	_, err = cli.Initialize(context.Background(), mcp.InitializeRequest{})
	if err != nil {
		return nil, iris.NewRecoverable(fmt.Errorf("failed to initialize mcp client: %v", err))
	}

	clients[baseUrl] = cli
	return cli, nil
}

type MCPListArgs struct {
	BaseURL string `json:"base_url" mapstructure:"base_url" description:"Server Base URL for the MCP request"`
}

type MCPListOutput struct {
	Tools []string `json:"tools" mapstructure:"tools" description:"MCP tools you can use from this server"`
}

func list(run *iris.AgentRunContext, args MCPListArgs) (*MCPListOutput, error) {
	// Init()
	_, err := connectThirdPart(args.BaseURL)
	if err != nil {
		return nil, iris.NewRecoverable(fmt.Errorf("failed to connect to server: %v", err))
	}
	cli, ok := clients[args.BaseURL]
	if !ok {
		return nil, iris.NewRecoverable(fmt.Errorf("unknown base url: %s", args.BaseURL))
	}

	resp, err := cli.ListTools(run, mcp.ListToolsRequest{})
	if err != nil {
		return nil, err
	}
	return &MCPListOutput{
		Tools: lo.Map(resp.Tools, func(tool mcp.Tool, _ int) string {
			json, err := json.Marshal(tool)
			if err != nil {
				return fmt.Sprintf("- %s: %s\n%s\n", tool.Name, tool.Description, err)
			}
			return string(json)
		}),
	}, nil
}

type MCPToolkitArgs struct {
	Variant string
}

func NewMCPToolkit(args MCPToolkitArgs) []iris.Action {

	registry := NewInternalMCPRegistry()
	registry.RegisterServer("codebase", InternalMCPServer{
		BasePath:    "codebase",
		Description: `Codebase integration server that provides access to Codebase repository and merge request data.`,
		Tools: []iris.Action{
			codebase.NewGetMergeRequestDetails(),
			codebase.NewListMergeRequest(),
		},
	})
	registry.RegisterServer("bitsanalysis", InternalMCPServer{
		BasePath: "bitsanalysis",
		Description: `Bits Analysis Platform is static code analysis platform (like SonarQube, etc). provides the following features:
- List issues in specific repository on the bits analysis platform based on given conditions.
- Parse a URL from the Bits Analysis platform into useful information for subsequent steps.
Key Concepts:
- Issue: 问题，是在代码库中发现的问题或潜在问题，例如代码质量问题、安全漏洞、语法错误等
- Rule: 规则,是一组预定义的标准，用于静态代码分析过程中检测代码库中的潜在问题。
- Scene: 应用场景，指的是代码分析的不同应用场景，如质量分析(quality_analysis)、合并检查(merge_check)、部署检查(deploy_check)、治理专项检查(resolution_check)
- URL: URL是指bits analysis平台上某个链接地址，可以基于URL解析获取到对应的代码库、场景、问题、规则等信息
`,
		Tools: []iris.Action{
			bitsanalysis.NewListIssues(),
			bitsanalysis.NewParseURL(),
		},
	})

	registry.RegisterServer("lark", InternalMCPServer{
		BasePath: "lark",
		Description: "Can be used to 1.download documents/sheets from Lark 2.create lark documents from local html/md file " +
			"3. Convert csv、xlsx、xls files to Lark/Feishu sheets/table or Lark/Feishu Base. (将csv、xlsx、xls文件转换为飞书表格或多维表格)",
		Tools: []iris.Action{
			lark.NewLarkDownloadTool(),
			lark.NewCreateLarkDoc(),
			lark.NewCreateLarkTable(),
		},
	})

	registry.RegisterServer("oncall", InternalMCPServer{
		BasePath:    "oncall",
		Description: `The OnCall service provides tools to access OnCall platform data, including searching single or multiple OnCall issues by tenant ID, tenant name, time range, or directly from OnCall platform URLs, and retrieving details of a specific OnCall group chat. **IMPORTANT**: When users provide OnCall platform URLs (https://oncall.bytedance.net/admin/review/all), always prioritize using search_oncall_from_url tool to maintain the original search conditions and filters.`,
		Tools: []iris.Action{
			oncall.NewListOnCallTool(),
			oncall.NewGetOnCallChatMessage(),
			oncall.NewListOnCallFromURLTool(),
		},
	})

	registry.RegisterServer("meego", InternalMCPServer{
		BasePath:    "meego",
		Description: "Meego integration server that provides access to meego space and workitem data. If a Meego URL is provided, prioritize using AnalyzeMeeGoURL to process the link directly. After identifying the link type, select the appropriate tool for different types of Meego resources.",
		Tools: []iris.Action{
			meego.NewListProjects(),
			meego.NewSearchProjects(),
			meego.NewGetProjectDetailBySimpleName(),
			meego.NewIdentifyMeeGoURLType(),
			meego.NewListWorkItems(),
			meego.NewGetWorkItemDetailByURL(),
			meego.NewGetCreateWorkItemMetaData(),
			meego.NewCreateWorkItem(),
			meego.NewCreateWorkItemFromFile(),
			meego.NewUpdateWorkItem(),
			meego.NewWorkItemByView(),
			meego.NewWorkItemByRelation(),
			// user
			// meego.NewGetUserInfo(),
		},
	})

	registry.RegisterServer("argos", InternalMCPServer{
		BasePath:    "argos",
		Description: "Argos is an internal APM (Application Performance Monitoring) platform that provides log query services. It allows you to search and retrieve logs using log IDs.",
		Tools: []iris.Action{
			argos.NewLogQueryByIdTool(),
			argos.NewLogQueryByIDAndPsmTool(),
			argos.NewLogQueryByKeywordsTool(),
		},
	})

	registry.RegisterServer("devmind", InternalMCPServer{
		BasePath: "devmind",
		Description: "DevMind is an internal platform that offers tools for querying R&D efficiency metrics, retrieving business and reporting nodes, and managing favorites. These metrics include, but are not limited to, code commit volume, defect rates, and requirement throughput. In addition, it also provides tools for recommending data model, analysis metric, analysis dimension, querying chart url, chart info." +
			"These DevMind tools do not require limitation detection. That is, if a Limitation is triggered, the MCP shall prevail, and the task shall be marked as \"continue\".",
		Tools: []iris.Action{
			devmind.NewQueryStoryMetricsTool(),
			devmind.NewQueryBusinessNodesTool(),
			devmind.NewQueryReportNodesTool(),
			devmind.NewQueryMetricDataTool(),
			devmind.NewManageFavoriteTool(),
			devmind.NewInsightReportsTool(),
			devmind.NewQueryDataModelTool(),
			devmind.NewQueryAnalysisMetricTool(),
			devmind.NewQueryAnalysisDimensionTool(),
			devmind.NewQueryChartUrlTool(),
			devmind.NewQueryChartInfoTool(),
		},
	})

	list := actions.ToTool("mcp_list", "List MCP tools", func(run *iris.AgentRunContext, args MCPListArgs) (*MCPListOutput, error) {
		server, ok := getInternalServer(args.BaseURL)
		if ok {
			return registry.ListTools(server)
		}
		return list(run, args)
	})

	call := actions.ToTool("mcp_call", "Call MCP tool", func(run *iris.AgentRunContext, args agententity.MCPCallArgs) (*agententity.MCPCallOutput, error) {
		server, ok := getInternalServer(args.BaseURL)
		if ok {
			step := run.State.CurrentStep
			var argsJSON map[string]any
			err := json.Unmarshal([]byte(args.Arguments), &argsJSON)
			if err != nil {
				return nil, iris.NewRecoverable(err)
			}
			oldInputs := step.Inputs
			step.Inputs = argsJSON
			res, err := registry.CallTool(run, step, server, args.Name, argsJSON)
			step.Inputs = oldInputs
			if err != nil {
				return nil, iris.NewRecoverable(err)
			}
			return res, nil
		}
		return call(run, args)
	})

	return []iris.Action{
		list,
		call,
	}
}

type InternalMCPServer struct {
	BasePath    string
	Description string
	Tools       []iris.Action
}

type InternalMCPRegistry struct {
	Servers map[string]InternalMCPServer
}

func NewInternalMCPRegistry() *InternalMCPRegistry {
	return &InternalMCPRegistry{
		Servers: make(map[string]InternalMCPServer),
	}
}

func (r *InternalMCPRegistry) RegisterServer(name string, server InternalMCPServer) {
	r.Servers[name] = server
}

func (r *InternalMCPRegistry) ListTools(server string) (*MCPListOutput, error) {
	return &MCPListOutput{
		Tools: lo.FilterMap(r.Servers[server].Tools, func(tool iris.Action, _ int) (string, bool) {
			spec := tool.InputSpec()
			specJSON, err := json.Marshal(spec)
			if err != nil {
				logs.Errorf("failed to marshal tool input spec: %v", err)
				return "", false
			}
			return fmt.Sprintf("%s: %s\n%s\n", tool.Name(), tool.Description(), string(specJSON)), true
		}),
	}, nil
}

func (r *InternalMCPRegistry) CallTool(run *iris.AgentRunContext, step *iris.AgentRunStep, server string, name string, args map[string]any) (*agententity.MCPCallOutput, error) {
	tool, ok := lo.Find(r.Servers[server].Tools, func(tool iris.Action) bool {
		return tool.Name() == name
	})
	if !ok {
		return nil, iris.NewRecoverable(fmt.Errorf("tool %s not found on server %s", name, server))
	}
	err := tool.Execute(run, step)
	if err != nil {
		return nil, iris.NewRecoverable(err)
	}

	// step.output to json string
	outputStr, err := json.Marshal(step.Outputs)
	if err != nil {
		return nil, iris.NewRecoverable(err)
	}
	return &agententity.MCPCallOutput{
		TextContents: []string{string(outputStr)},
	}, nil
}

func getInternalServer(baseURL string) (string, bool) {
	u, err := url.Parse(baseURL)
	if err != nil || u.Host != "internal" {
		return "", false
	}
	return strings.TrimPrefix(baseURL, "http://internal/"), true
}
