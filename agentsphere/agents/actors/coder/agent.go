package coder

import (
	"embed"
	"os"
	"strings"

	"github.com/samber/lo"

	codertools "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/coder/tools"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/codebase"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	coderfix "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/coder/fix"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
)

var (
	Identifier = "repository_developer"
	parameters = []actors.ParameterDescription{
		{
			Name: "task",
			Description: "Detailed description of the task. The task should be focus on \"what to do\" instead of \"how to do it\"	" +
				"For example: \"Add a new feature to the project\" instead of \"Modify the code in file xxx.go to add xxxx\"",
		},
	}
	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")
)

type CoderStore struct {
	CurrentTask string
	CurrentPlan string
	Knowledges  []knowledges.KnowledgeItem
}

// construct using coder.NewAgent()
type Agent struct {
	actors.BaseActor

	// FixAgent 是 Coder 下的 Fix Agent，主要用于修复代码的 Code 子领域
	FixAgent *coderfix.FixAgent
	SWEAgent *coderfix.SWEAgent
	// ReAct 是 Coder 下通用的 ReAct Agent，如果输入不符合任何一种领域Agent的要求，则使用 ReAct 进行处理
	ReAct         *agents.ReActAgent
	Knowledgebase knowledges.Knowledgebase
	Variant       string
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	MaxSteps         int
	Variant          string
	LLMHooks         *prompt.LLMCallHook
	Knowledgebase    knowledges.Knowledgebase
	EnableBytedRepos bool
	ActorStep        *iris.AgentRunStep
}

type WorkspaceRepo struct {
	Directory string
	RepoName  string
	Platform  string
}

func New(opt CreateOption) *Agent {
	info := iris.AgentInfo{
		Identifier: Identifier,
		Desc:       lo.Must(promptset.ExecutePrompt("description", opt.Variant, nil)),
	}
	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: parameters,
		},
		Variant:       opt.Variant,
		Knowledgebase: opt.Knowledgebase,
	}
	// FixAgent 是 Coder 下的 Fix Agent，主要用于修复代码的 Code 子领域
	agent.FixAgent = coderfix.New(coderfix.CreateOption{
		MaxSteps: opt.MaxSteps,
		Variant:  opt.Variant,
		LLMHooks: opt.LLMHooks,
	})
	agent.SWEAgent = coderfix.NewSWEAgent(coderfix.CreateOption{
		MaxSteps: opt.MaxSteps,
		Variant:  opt.Variant,
		LLMHooks: opt.LLMHooks,
	})
	// ReAct 是 Coder 下通用的 ReAct Agent，如果输入不符合任何一种领域Agent的要求，则使用 ReAct 进行处理
	agent.ReAct = agent.newReAct(opt)
	agent.ExecutionStep = &actors.ExecutionStep{
		Step: opt.ActorStep,
	}
	return agent
}

func (a *Agent) newReAct(opt CreateOption) *agents.ReActAgent {
	createFileOpt := workspace.CreateFileOption{RemoveOuterBackquotes: false}
	if opt.Variant == agententity.VariantIntern {
		createFileOpt.RemoveOuterBackquotes = true
	}
	readFileOpt := workspace.ReadFileActionOption{MaxLines: 200}
	if opt.Variant == agententity.VariantExpert {
		readFileOpt.MaxLines = 500
	}

	tools := []iris.Action{
		workspace.NewCreateFileAction(createFileOpt),
		workspace.NewReadFileAction(readFileOpt),
		workspace.NewRawPatchFileAction(),
		workspace.NewGlobSearchAction(),
		//workspace.NewSearchFileNameAction(),
		workspace.NewListDirectoryAction(),
		workspace.NewGrepSearchRangeAction(),
		// allow mv, rm, cp, mkdir, rmdir commands to update project structure
		workspace.NewExecuteCommandAction(workspace.NewExecuteCommandActionOption{
			Variant: opt.Variant,
		}),
		workspace.NewDeployAction(workspace.DeployOption{ProcessHTML: false}),
		controltool.NewSequentialThinkingToolAction(),
		workspace.NewGitCloneAction(opt.EnableBytedRepos),
		//codebaseactor.NewGitPushAction(),
		codebase.NewSubmitMergeRequestAction(opt.EnableBytedRepos),
		controltool.NewConclusionTool(),
	}
	templates := prompt.TemplateSet{
		SystemTmpl:  promptset.GetTemplate("system", opt.Variant),
		UserTmpl:    promptset.GetTemplate("user", opt.Variant),
		ThoughtTmpl: promptset.GetTemplate("thought", opt.Variant),
	}
	info := iris.AgentInfo{
		Identifier: Identifier,
		Desc:       lo.Must(promptset.ExecutePrompt("description", opt.Variant, nil)),
	}

	composer := func(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep) []*framework.ChatMessage {

		ws := workspace.GetWorkspace(run)
		root, err := ws.Editor.ReadDirectory(workspace.ReadDirectory{
			Path:      ".",
			Recursive: true,
			MaxDepth:  1,
		})
		if err != nil {
			run.GetLogger().Errorf("failed to list root directory: %s", err)
		}

		store := iris.RetrieveStoreByKey[CoderStore](run, a.ReAct.UID())
		run.GetLogger().Infof("current task: %v", store.CurrentTask)
		return agents.ComposeMessage(run, agents.ComposerOptions{
			Templates: templates,
			Tools:     tools,
			SystemPromptVariables: map[string]any{
				"Language": agents.GetUserLanguage(run.Parameters),
				"WorkspaceRepositories": lo.MapValues(ws.Repositories, func(repo *workspace.Repository, _ string) WorkspaceRepo {
					return WorkspaceRepo{
						Directory: repo.Directory,
						RepoName:  repo.Meta.Name,
						Platform:  string(repo.Meta.Platform),
					}
				}),
				"Workspace":     ws.Editor.WorkingDirectory,
				"RootStructure": root,
				"Knowledges":    store.Knowledges,
			},
			UserPromptVariables: map[string]any{
				"Content":  store.CurrentTask,
				"Plan":     store.CurrentPlan,
				"Language": agents.GetUserLanguage(run.Parameters),
			},
			Trace: previousSteps,
		})
	}
	reAct := agents.NewReActAgent(agents.ReActAgentConfig{
		Info:     info,
		Tools:    tools,
		Composer: composer,
		MaxSteps: opt.MaxSteps,
		LLMHooks: opt.LLMHooks,
	})
	reAct.GetAction = a.GetAction
	return reAct
}

func (a *Agent) GetAction(run *iris.AgentRunContext, action string, parameters map[string]any) iris.Action {
	if action != string(workspace.ActionExecuteCommand) {
		tool, _ := lo.Find(a.ReAct.Tools, func(tool iris.Action) bool {
			return tool.Name() == action
		})
		return tool
	}
	return workspace.NewExecuteCommandActionWithDefaultEnv(run, workspace.NewExecuteCommandActionOption{
		ScrollbackLines: 300,
		Variant:         a.Variant,
	})
}

func (a *Agent) Run(ctx *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	ctx.GetLogger().Infof("[DEBUG] coder agent run with variant: %s", a.Variant)
	// 获取环境变量 CODER_SWE_ENABLE 的值
	if strings.ToLower(os.Getenv("CODER_SWE_ENABLE")) == "true" {
		return a.SWEAgent.Run(ctx, a.ExecutionStep.Step, input)
	}
	// important: The reason for the comment is to adjust the Fix effect, wait for the effect to align before letting it go
	//// 根据不同的场景进行路由
	//scenario, routeErr := routingTask(ctx, input)
	//if routeErr != nil {
	//	ctx.GetLogger().Errorf("failed to route scenario: %s", routeErr)
	//}
	//if scenario == coderfix.Identifier {
	//	return a.FixAgent.Run(ctx, step, input)
	//}

	if a.Variant == agententity.VariantIntern {
		// TODO 修改Plan Task Prompt，判断plan输出，进行场景路由，例如 Fix 子场景路由
		// eg : if plan == 'fix' then route to fix agent
		plan, planErr := codertools.PlanTask(ctx, codertools.PlannerToolArgs{
			Task: input,
		})
		if planErr != nil {
			ctx.GetLogger().Errorf("failed to plan: %s", planErr)
		}

		knowledges, err := a.Knowledgebase.RetrieveKnowledge(ctx, knowledges.KgRetrieveOption{
			Query: plan, // use plan as query
			Param: knowledges.RetrieveParam{
				Agent:        Identifier,
				Variant:      a.Variant,
				Tools:        lo.Map(a.ReAct.Tools, func(t iris.Action, _ int) string { return t.Name() }),
				WithCitation: false,
			},
			Limit: 20,
		})
		if err != nil {
			ctx.GetLogger().Errorf("failed to recall knowledge: %s", err)
		}
		ctx.GetLogger().Infof("[DEBUG] knowledge: %v", knowledges)

		iris.UpdateStoreByKey(ctx, a.ReAct.UID(), CoderStore{
			CurrentTask: input,
			CurrentPlan: plan,
			Knowledges:  knowledges,
		})
	} else {
		knowledges, err := a.Knowledgebase.RetrieveKnowledge(ctx, knowledges.KgRetrieveOption{
			Query: input, // use task as query
			Param: knowledges.RetrieveParam{
				Agent:        Identifier,
				Variant:      a.Variant,
				Tools:        lo.Map(a.ReAct.Tools, func(t iris.Action, _ int) string { return t.Name() }),
				WithCitation: false,
			},
			Limit: 20,
		})
		if err != nil {
			ctx.GetLogger().Errorf("failed to recall knowledge: %s", err)
		}
		ctx.GetLogger().Infof("[DEBUG] knowledge: %v", knowledges)

		iris.UpdateStoreByKey(ctx, a.ReAct.UID(), CoderStore{
			CurrentTask: input,
			Knowledges:  knowledges,
		})
	}

	err := a.ReAct.RunWithOption(ctx, &agents.ReActAgentRunOption{
		Step: a.ExecutionStep.Step,
	})
	if err != nil {
		result = controltool.ConclusionOutput{
			Content:    err.Error(),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}
		return result
	}

	lastStep := ctx.State.LastStep()
	mapstructure.Decode(lastStep.Conclusion, &result)
	return result
}
