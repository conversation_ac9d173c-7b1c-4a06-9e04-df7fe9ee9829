package websearch

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_combineSearchItems(t *testing.T) {
	items := combineSearchItems([]SearchItem{
		{
			Title:         "title1",
			URL:           "url1",
			NormalizedURL: "url1",
			Description:   "description1",
		},
		{
			Title:         "title1",
			URL:           "url1",
			NormalizedURL: "url1",
			Description:   "description1-1",
		},
		{
			Title:         "title",
			URL:           "url1",
			NormalizedURL: "url1",
			Description:   "description1-2",
		},
		{
			Title:         "title2",
			URL:           "url2",
			NormalizedURL: "url2",
			Description:   "description2",
		},
	})
	assert.True(t, len(items) == 2)
	for _, item := range items {
		if item.URL == "url1" {
			assert.True(t, strings.Contains(item.Description, "description1-1"))
			assert.True(t, strings.Contains(item.Description, "description1-2"))
		}
	}
}

func Test_fixGovURL(t *testing.T) {
	assert.Equal(t, fixGovURL("https://stats.gov.cn/sj/zxfb/202308/t20230816_1942026.html"), "https://www.stats.gov.cn/sj/zxfb/202308/t20230816_1942026.html")
}
