package websearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"
	"unicode/utf8"

	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/gopkg/lang/v2/timex"

	"code.byted.org/devgpt/kiwis/lib/metrics"

	"github.com/hashicorp/go-multierror"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch/jsonschema"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"

	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/sashabaranov/go-openai"
)

type tool string

const (
	evaluatorTool     tool = "evaluator"
	evaluateChunkTool tool = "evaluate_chunk"
	queryRewriterTool tool = "query_rewriter"
	answerTool        tool = "answer"
	backupTool        tool = "backup"
	planTool          tool = "plan"
)

// use a min temperature value. 0 might be not allowed for every llm.
const minTemperature = 0.01

func (a *Agent) getLLMConfig(tool tool) *LLMConfig {
	const defaultScene = "web_searcher"
	model := a.config.Model.Model
	maxToken := a.config.Model.MaxTokens
	temperature := float32(0.1)
	scene := defaultScene
	if len(a.config.ModelScenesConfig.SceneSpecifications) > 0 {
		modelConfig := a.config.GetModelByScene(defaultScene)
		if modelConfig.Model != "" {
			model = modelConfig.Model
			maxToken = modelConfig.MaxTokens
		}
		toolScene := "web_searcher_" + string(tool)
		toolSpecModelConfig, ok := a.config.TryGetModelByScene(toolScene)
		if ok && toolSpecModelConfig.Model != "" {
			model = toolSpecModelConfig.Model
			scene = toolScene
			maxToken = toolSpecModelConfig.MaxTokens
		}
	}
	a.logger.Infof("web search using model %s for tool %s", model, tool)
	switch tool {
	case evaluatorTool, evaluateChunkTool:

		temperature = minTemperature
	case queryRewriterTool, answerTool, backupTool, planTool:
		temperature = 0.1
	default:
		panic("unknown tool " + tool)
	}
	return &LLMConfig{
		Scene:       scene,
		Temperature: temperature,
		MaxToken:    maxToken,
		Model:       model,
	}
}

type LLMConfig struct {
	Scene       string
	Model       string
	Temperature float32
	MaxToken    int32
}

type prompt struct {
	System string
	User   string
}

type Option struct {
	Backoff backoff.BackOff
}

func (a *Agent) generateObject(ctx *iris.AgentRunContext, tool tool, schema *jsonschema.Definition, system string, user string, r interface{}, option ...Option) error {
	schemaPrompt, err := json.MarshalIndent(schema, "", "  ")
	if err != nil {
		return errors.WithStack(err)
	}
	system = fmt.Sprintf(`%s
Respond in valid JSON format matching exact JSON schema.

JSONSchema:
%s`, system, string(schemaPrompt))
	invoke := func() error {
		llmMessage := []*framework.ChatMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: system,
			},
		}
		if len(user) > 0 {
			llmMessage = append(llmMessage, &framework.ChatMessage{
				Role:    openai.ChatMessageRoleUser,
				Content: user,
			})
		}
		llmConfig := a.getLLMConfig(tool)
		tag := &metrics.AgentActorWebSearcherLLMTag{
			Model: llmConfig.Model,
			Tool:  string(tool),
		}
		start := time.Now()
		result, err := agents.Think(ctx, llmConfig.Scene, llmMessage, agents.ThinkOption{})
		if err != nil {
			return err
		}
		content := result.Content
		a.publishLLM(llmConfig, llmMessage, content)
		if err != nil {
			if errors.Is(err, context.Canceled) {
				return backoff.Permanent(err)
			}
			_ = metrics.AR.ActorWebSearcherLLMError.WithTags(tag).Add(1)
			return errors.WithStack(err)
		}
		_ = metrics.AR.ActorWebSearcherLLMCost.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))
		err = a.llmUnmarshal(content, r)
		if err == nil {
			return nil
		}
		return errors.WithMessage(err, "raw output: "+content)
	}
	backOff := backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5)
	if len(option) > 0 && option[0].Backoff != nil {
		backOff = option[0].Backoff
	}
	err = backoff.Retry(invoke, backOff)
	return errors.WithMessagef(err, "tried too many times")
}

// llmUnmarshal trys to unmarshal the content to r.
func (a *Agent) llmUnmarshal(content string, r interface{}) (err error) {
	defer func() {
		// Easy to panic by out of range.
		r := recover()
		if r != nil {
			err = errors.Errorf("%+v", r)
			a.logger.Errorf("panic in llmUnmarshal: %+v", err)
		}
	}()
	content = strings.ToValidUTF8(content, "") // LLM might output invalid UTF-8 just like 'ä'.
	content = strings.TrimSpace(content)
	content = strings.TrimPrefix(content, "```json")
	content = strings.TrimSuffix(content, "```")
	for {
		err = json.Unmarshal([]byte(content), r)
		if err == nil {
			return nil
		}
		var syntaxError *json.SyntaxError
		if errors.As(err, &syntaxError) {
			offset := int(syntaxError.Offset - 1)
			if offset >= len(content) || offset < 0 {
				return err
			}
			content = removeInvalidCharacter(content, offset)
			continue
		}
		return err
	}
}

func removeInvalidCharacter(content string, byteOffset int) string {
	runeStart, runeSize := utf8.DecodeRuneInString(content[byteOffset:])
	if runeStart == utf8.RuneError {
		return content[:byteOffset] + content[byteOffset+1:]
	}
	return content[:byteOffset] + content[byteOffset+runeSize:]
}

func (a *Agent) generate(ctx *iris.AgentRunContext, tools []tool, system string, user string) (string, error) {
	var content string
	invoke := func() error {
		llmMessage := []*framework.ChatMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: system,
			},
		}
		if len(user) > 0 {
			llmMessage = append(llmMessage, &framework.ChatMessage{
				Role:    openai.ChatMessageRoleUser,
				Content: user,
			})
		}
		var (
			errs error
		)
		for _, tool := range tools {
			llmConfig := a.getLLMConfig(tool)
			start := timex.Now()
			var err error
			result, err := agents.Think(ctx, llmConfig.Scene, llmMessage, agents.ThinkOption{})
			if err != nil {
				return err
			}
			content = result.Content
			a.publishLLM(llmConfig, llmMessage, content)
			tag := &metrics.AgentActorWebSearcherLLMTag{
				Model: llmConfig.Model,
				Tool:  string(tool),
			}
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return backoff.Permanent(err)
				}
				_ = metrics.AR.ActorWebSearcherLLMError.WithTags(tag).Add(1)
				ctx.GetLogger().Infof("failed to chat completion with mode %s: %+v", llmConfig.Model, err)
				errs = multierror.Append(errs, errors.WithMessagef(err, "failed to chat completion with mode %s", llmConfig.Model))
				continue // Try next model.
			}
			_ = metrics.AR.ActorWebSearcherLLMCost.WithTags(tag).Observe(float64(time.Since(start).Milliseconds()))
			return nil
		}
		return errs
	}
	err := backoff.Retry(invoke, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 20))
	return content, errors.WithMessagef(err, "tried too many times")
}
