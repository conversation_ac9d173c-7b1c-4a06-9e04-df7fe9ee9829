package websearch

import (
	"fmt"
	"strings"
	"sync"
	"time"

	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/metrics"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"golang.org/x/exp/slices"
	"golang.org/x/sync/errgroup"
)

func (a *Agent) InternalKnowledgeBaseSearch(ctx *iris.AgentRunContext, tasks []string) (*websearchtool.Response, error) {
	a.mcpMode = true
	a.agentCtx = ctx
	a.step = ctx.State.CurrentStep
	a.stepID = a.step.StepID
	a.logger = ctx.GetLogger()
	a.llm = ctx.GetLLM()
	a.cloudToken = ctx.GetEnv(entity.RuntimeEnvironUserCloudJWT)
	a.larkToken = ctx.GetEnv(entity.RunTimeLarkUserAccessToken)
	if err := a.initContext(ctx); err != nil {
		return nil, errors.WithStack(err)
	}
	start := time.Now()

	subTaskGroup := &errgroup.Group{}
	subTaskGroup.SetLimit(4)
	var subTaskLock sync.RWMutex
	resMap := make(map[string]string)
	defer func() {
		_ = metrics.AR.ActorWebSearcherPhraseCost.WithTags(&metrics.AgentActorWebSearcherPhraseTag{
			Phrase: "whole_run",
		}).Observe(float64(time.Since(start).Milliseconds()))
	}()

	var runTasks []string

	// 先直接用 mewtwo 的参数
	runTasks = tasks

	for _, task := range runTasks {
		task := task
		subTaskGroup.Go(func() error {
			subTaskResult, err := a.knowledgeBaseSearch(ctx, task)
			if err != nil {
				a.logger.Errorf("failed to process task: %s, err: %v", task, err)
				return nil
			}
			var allItems []SearchItem
			var reportItems []*SearchItem
			subTaskLock.Lock()
			for _, item := range subTaskResult.Items {
				item.Description = fmt.Sprintf("Title: %v\nContent: %v\n", item.Title, item.Description)
				allItems = append(allItems, item)
				reportItems = append(reportItems, &item)
			}
			subTaskLock.Unlock()
			a.logger.Infof("[InternalKnowledgeBaseSearch] before evaluateSearch length of answer is: %v, task: %v", len(allItems), task)
			rankedItems := a.evaluateSearch(ctx, task, allItems, 99999)

			a.logger.Infof("[InternalKnowledgeBaseSearch] evaluateSearch length of answer is: %v, task: %v", len(rankedItems), task)

			if len(rankedItems) == 0 {
				a.toolCallLock.Lock()
				stepReporter := a.createAndRunningStep(map[string]any{"SearchRequests": task})
				stepReportDesc := fmt.Sprintf("正在搜索：%v", task)
				searchToolCallFinished := stepReporter.ReportSearchToolCall(stepReportDesc, []string{task})
				searchToolCallFinished(selectSearchItems([]*SearchItem{}, []*SearchItem{}, 30)) // Only display fixed number of items.
				a.toolCallLock.Unlock()
				return nil
			}

			var useItems []*SearchItem
			crawlCount := 0
			for _, item := range rankedItems {
				item := item
				if crawlCount < 3 {
					content, err := a.agentCtx.GetAPIClient().GetKnowledgeContent(ctx, cast.ToString(a.agentCtx.Parameters[entity.RuntimeParametersDatasetID]), item.DocumentID)
					if err == nil {
						item.Description = content
						a.logger.Infof("[InternalKnowledgeBaseSearch] GetKnowledgeContent, documentID: %s, content: %v", item.DocumentID, content)
					} else {
						a.logger.Errorf("[InternalKnowledgeBaseSearch] failed to process GetKnowledgeContent, documentID: %s, err: %v", item.DocumentID, err)
					}
					crawlCount++
				}
				useItems = append(useItems, &item)
				if len(useItems) == 8 {
					break
				}
			}
			taskPrompt := fmt.Sprintf("以下是对<%v>的搜索结果\n", task)
			a.logger.Infof("[InternalKnowledgeBaseSearch] useItems length of answer is : %v, task: %v", len(rankedItems), task)
			if len(useItems) > 0 {
				for _, item := range useItems {
					itemPrompt := fmt.Sprintf("URL: %v\n", item.URL)
					if len(item.Owner) > 0 {
						itemPrompt += fmt.Sprintf("Author: %v\n", item.Owner)
					}
					if !item.LastUpdatedTime.IsZero() {
						itemPrompt += fmt.Sprintf("LastUpdatedTime: %v\n", item.LastUpdatedTime.Format(time.RFC1123))
					}
					itemPrompt += fmt.Sprintf("Content: %v\n\n", item.Description)
					taskPrompt += itemPrompt

				}
			}
			a.logger.Infof("[InternalKnowledgeBaseSearch] task prompt is : %v, task: %v", taskPrompt, task)

			if len(taskPrompt) > 24000 {
				taskPrompt = taskPrompt[0:24000]
			}
			subTaskLock.Lock()
			resMap[task] = taskPrompt
			subTaskLock.Unlock()
			a.toolCallLock.Lock()
			stepReporter := a.createAndRunningStep(map[string]any{"SearchRequests": task})
			stepReportDesc := fmt.Sprintf("正在搜索：%v", task)
			searchToolCallFinished := stepReporter.ReportSearchToolCall(stepReportDesc, []string{task})
			searchToolCallFinished(selectSearchItems(useItems, reportItems, 30)) // Only display fixed number of items.
			a.toolCallLock.Unlock()
			return nil
		})
	}
	_ = subTaskGroup.Wait()
	var taskAnswers []string
	for _, res := range resMap {
		taskAnswers = append(taskAnswers, res)
	}
	finalPrompt := fmt.Sprintf("以下是对<%v>的搜索结果\n", strings.Join(tasks, "; "))
	for _, item := range taskAnswers {
		finalPrompt += item
	}
	if len(finalPrompt) > 100000 {
		finalPrompt = finalPrompt[:100000]
	}
	return &websearchtool.Response{
		FinalReport: finalPrompt,
	}, nil
}

func RecallKnowledgebase(ctx *iris.AgentRunContext, query string, maxCount int) ([]string, error) {
	datasetID := cast.ToString(ctx.Parameters[entity.RuntimeParametersDatasetID])
	ctx.GetLogger().Infof("datasetID: %s,query: %s", datasetID, query)
	if len(datasetID) == 0 {
		return nil, nil
	}
	segments, err := ctx.GetAPIClient().RecallKnowledgeBase(ctx, datasetID, query, int64(maxCount)*2)
	if err != nil {
		return nil, err
	}
	rankScore, err := rankByBgeMiniCpm(ctx, query, lo.Map(segments, func(segment *nextagent.RecallSegment, _ int) string {
		return fmt.Sprintf("Title: %s\nContent: %s\n", segment.Title, segment.Content)
	}))
	if err != nil {
		return nil, err
	}
	var threshold = -3.5
	filteredSegments := lo.Filter(segments, func(segment *nextagent.RecallSegment, index int) bool {
		return rankScore[index] > threshold
	})
	pos := make([]int, 0, len(filteredSegments))
	for i := 0; i < len(filteredSegments); i++ {
		pos = append(pos, i)
	}
	slices.SortFunc(pos, func(i, j int) int {
		if rankScore[pos[i]] > rankScore[pos[j]] {
			return -1
		}
		if rankScore[pos[i]] < rankScore[pos[j]] {
			return 1
		}
		return 0
	})
	topSegments := lo.Slice(pos, 0, maxCount)
	result := lo.Map(topSegments, func(segmentIdx int, _ int) string {
		segment := filteredSegments[segmentIdx]
		return fmt.Sprintf("Title: %s\nContent Snippet: %s\nFull Document in URL: %s\n", segment.Title, segment.Content, segment.URL)
	})
	return result, nil
}
