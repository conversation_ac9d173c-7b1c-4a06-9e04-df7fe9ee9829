package websearch

import (
	"fmt"
	"net/url"
	"sort"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

func (a *Agent) publishThink(format string, arg ...any) {
	if a.step == nil {
		return
	}
	step := *a.step
	step.Thought = &iris.Thought{
		Rationale: fmt.Sprintf(format, arg...),
	}
	a.agentCtx.GetPublisher().ReportThought(&step)
}

func (a *Agent) publishLLM(modelConfig *LLMConfig, messages []*framework.ChatMessage, content string) {
	if a.step == nil {
		return
	}
	step := *a.step
	step.Thought = &iris.Thought{
		LLMCall: iris.LLMCall{
			ModelName:   modelConfig.Model,
			Temperature: float64(modelConfig.Temperature),
			Prompt:      messages,
		},
		Content: content,
	}
	a.agentCtx.GetPublisher().ReportThought(&step)
}

type stepReporter struct {
	step     *iris.AgentRunStep
	agentCtx *iris.AgentRunContext
}

func (s *stepReporter) Update(status iris.StepStatus) {
	s.step.Status = status
	s.report(s.step)
}

func (s *stepReporter) Finish(err error) {
	if s.step == nil {
		// Make it easy to run in local evaluation.
		return
	}
	if err != nil {
		s.step.Error = err
		s.step.Status = iris.AgentRunStepStatusFailed
	} else {
		s.step.Status = iris.AgentRunStepStatusSuccess
	}
	s.report(s.step)
}

func (s *stepReporter) summarizeThinking(think string, action *actions.Tool) string {
	if s.step == nil {
		// Make it easy to run in local evaluation.
		return ""
	}
	s.step.Thought = &iris.Thought{
		Rationale: think,
	}
	s.step.Action = action
	return strings.TrimSpace(agents.Summarize(s.agentCtx, s.step))
}

func (s *stepReporter) report(step *iris.AgentRunStep) {
	if s.agentCtx == nil {
		// Make it easy to run in local evaluation.
		return
	}
	s.agentCtx.GetPublisher().ReportStep(step)
}

func (s *stepReporter) reportToolCall(status iris.ToolCallStatus, action iris.Action, desc string,
	inputs map[string]any, outputs map[string]any) {
	if s.agentCtx == nil {
		// Make it easy to run in local evaluation.
		return
	}
	s.step.Action = action
	s.step.Inputs = inputs
	s.step.Outputs = outputs
	s.agentCtx.GetPublisher().ReportToolCall(s.step, status, desc)
}

var searchTool = actions.ToTool[string, string]("web_search", "search from ByteDance knowledge and Internet", func(c *iris.AgentRunContext, input string) (string, error) {
	return "", nil
})

type ReportSearchToolCallFinished func([]*SearchItem)

func (s *stepReporter) ReportSearchToolCall(description string, queries []string) ReportSearchToolCallFinished {
	s.reportToolCall(iris.ToolCallStatusStarted, searchTool, description, map[string]any{"query": strings.Join(queries, ",")}, map[string]any{})
	return func(items []*SearchItem) {
		// 排个序让前端更好看
		sort.SliceStable(items, func(i, j int) bool { return items[i].RankScore > items[j].RankScore })
		var (
			dedupMap       = map[string]bool{}
			toolCallReport []websearchtool.ReportSearchResult
		)
		for _, item := range items {
			normalizedURL := item.NormalizedURL
			if item.Source == larkSearchSource {
				// Remove query parameters and fragment for lark search result.
				parse, _ := url.Parse(normalizedURL)
				if parse != nil {
					parse.RawQuery = ""
					parse.Fragment = ""
					normalizedURL = parse.String()
				}
			}
			if dedupMap[normalizedURL] {
				continue
			}
			dedupMap[normalizedURL] = true
			toolCallReport = append(toolCallReport, websearchtool.ReportSearchResult{
				Icon:        item.Icon,
				Title:       item.Title,
				URL:         normalizedURL,
				Description: replaceSnippetTag(item.Description),
			})
		}
		s.reportToolCall(iris.ToolCallStatusCompleted, searchTool, description, map[string]any{"query": strings.Join(queries, ",")}, map[string]any{"results": toolCallReport})
	}
}
