package websearch

import (
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
)

type SearchItem struct {
	URL               string
	NormalizedURL     string
	Title             string
	Description       string
	LastUpdatedTime   time.Time
	Owner             string
	Snippet           string
	Icon              string
	Query2AnswerScore float64
	RankScore         float64
	Source            string // lark_search, web_search, byte_knowledge.
	// DocumentID is the ID of the document in the DevAI.
	DocumentID          string
	NeedFullContext     bool
	TraceID             string
	WebEngine           string
	IndexInSearchEngine int // Index in the search engine result.
}

func (s *SearchItem) GetTraceTag() agentrace.TagKV {
	return agentrace.NewTagKV("span_id", s.TraceID)
}
