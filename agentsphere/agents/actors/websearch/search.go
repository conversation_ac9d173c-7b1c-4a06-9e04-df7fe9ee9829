package websearch

import (
	"bytes"
	"context"
	_ "embed"
	"encoding/json"
	"io"
	"math"
	"net/http"
	"net/url"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"github.com/cenkalti/backoff/v4"
	"github.com/spf13/cast"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"

	"code.byted.org/gopkg/logid"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/devai"
	devaiutil "code.byted.org/devgpt/kiwis/devai/common/util"
	"code.byted.org/devgpt/kiwis/lib/hertz"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

const (
	larkSearchSource    = "lark_search"
	webSearchSource     = "web_search"
	bytecloudSource     = "bytecloud"
	arcositeSource      = "arcosite"
	toutiaoHybridSource = "toutiao_hybrid"
	knowledgebaseSource = "knowledgebase"
)

type searchResult struct {
	Query string
	Items []SearchItem
}

type larkFileMetadata struct {
	UV             int32
	PV             int32
	LikeCount      int32
	UVToday        int32
	PVToday        int32
	LikeCountToday int32
	LastModifyTime string
	LastModifyUser string
	OwnerID        string
	SecLabelName   string
}

var searchClient *hertz.Client

func init() {
	var err error
	searchClient, err = hertz.NewClient("https://bitsai.bytedance.net", hertz.NewHTTPClientOption{
		Timeout: time.Second * 30,
	})
	if err != nil {
		panic(err)
	}
}

func (a *Agent) retryReadLarkFileMeta(ctx context.Context, url string) (*larkFileMetadata, error) {
	var meta *larkFileMetadata
	err := backoff.Retry(func() error {
		var err error
		meta, err = a.readLarkFileMeta(ctx, url)
		if err != nil {
			_ = metrics.AR.ActorWebSearcherReadLarkDocMetadataError.Add(1)
			return errors.WithStack(err)
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5))
	return meta, err
}

func (a *Agent) readLarkFileMeta(ctx context.Context, url string) (*larkFileMetadata, error) {
	body := &devai.AgentReadLarkMetaRequest{
		LarkToken: a.larkToken,
		URL:       url,
	}
	_ = metrics.AR.ActorWebSearcherReadLarkDocMetadata.Add(1)
	response := &devai.AgentReadLarkMetaResponse{}
	_, err := searchClient.DoJSONReq(ctx, http.MethodPost, "/openapi/knowledge/v1/agent/read_lark_meta", hertz.ReqOption{
		ExpectedCode:    http.StatusOK,
		Body:            body,
		Result:          response,
		Notes:           "devai_search",
		Headers:         map[string]string{"x-jwt-token": a.cloudToken},
		Timeout:         time.Second * 30,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var result larkFileMetadata
	result.UV = response.UV
	result.PV = response.PV
	result.UVToday = response.UVToday
	result.PVToday = response.PVToday
	result.LikeCount = response.LikeCount
	result.LikeCountToday = response.LikeCountToday
	result.LastModifyTime = response.LastModifyTime
	result.OwnerID = response.OnwerID
	result.SecLabelName = response.SecLabelName
	result.LastModifyUser = response.LastModifyUser
	return &result, nil
}

func (a *Agent) agentSearch(ctx context.Context, query string) (_ *searchResult, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "agent_search", agentrace.WithObjectSpanData(map[string]any{"query": query}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	body := &devai.RecallAgentRequest{
		Query: query,
		TopK:  20,
		RecallExtra: &devai.RecallExtra{
			UserLarkToken: lo.Ternary(len(a.larkToken) > 0, &a.larkToken, nil),
		},
		EnableBytedSearch: &a.enableBytedSearch,
		Recallers:         []string{"web_search", "lark_baike", "lark_search"},
	}
	response := &devai.RecallAgentResponse{}
	_, err = searchClient.DoJSONReq(ctx, http.MethodPost, "/openapi/knowledge/v1/agent/recall", hertz.ReqOption{
		ExpectedCode:    http.StatusOK,
		Body:            body,
		Result:          response,
		Notes:           "agent_search",
		Headers:         map[string]string{"x-jwt-token": a.cloudToken},
		Timeout:         time.Second * 30,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	searchEngineIndex := map[string]int{}
	items := lo.Map(response.RecallSegments, func(item *devai.RecallSegment, _ int) SearchItem {
		var s = SearchItem{
			Description: item.Content,
			Icon:        item.Icon,
			Source:      item.SourceType,
		}
		switch {
		case item.DocumentSegment != nil:
			s.Title = item.DocumentSegment.Title
			s.URL = item.DocumentSegment.GetLink()
		case item.QASegment != nil:
			s.Title = item.QASegment.Question
			s.URL = item.QASegment.GetLink()
		case item.OncallSegment != nil:
			s.Title = item.OncallSegment.Title
			s.URL = item.OncallSegment.GetLink()
		case item.SearchSegment != nil:
			s.Title = item.SearchSegment.Title
			s.URL = item.SearchSegment.GetLink()
			s.WebEngine = item.SearchSegment.GetSource()
		case item.PhraseSegment != nil:
			s.Title = item.PhraseSegment.Title
			s.URL = item.PhraseSegment.GetLink()
		}
		if item.DocumentSegment != nil && item.DocumentSegment.DocumentId > 0 {
			s.DocumentID = strconv.FormatInt(item.DocumentSegment.DocumentId, 10)
		}
		s.TraceID = logid.GenLogID()
		normalizedURL, err := normalizeURL(ctx, s.URL)
		if err != nil {
			getLogger(ctx).Errorf("failed to normalize url: %v", err)
			normalizedURL = s.URL
		}
		normalizedURL = fixGovURL(normalizedURL)
		s.NormalizedURL = normalizedURL
		s.IndexInSearchEngine = searchEngineIndex[s.WebEngine]
		searchEngineIndex[s.WebEngine]++
		return s
	})
	items = lo.Filter(items, func(item SearchItem, index int) bool {
		return len(item.URL) > 0
	})
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"items": items}))
	items = lo.Filter(items, func(item SearchItem, _ int) bool {
		// Just for testing. Ignore all items with [no_aime] in the title.
		return !strings.Contains(item.Title, "[no_aime]")
	})
	return &searchResult{
		Query: query,
		Items: items,
	}, nil
}

func (a *Agent) toutiaoHybridSearch(ctx context.Context, query string, needEval bool) (*searchResult, error) {
	topK := 10
	body := &devai.RecallAgentRequest{
		Query: query,
		TopK:  int32(topK),
	}
	response := &devai.RecallAgentResponse{}
	_, err := searchClient.DoJSONReq(ctx, http.MethodPost, "/openapi/knowledge/v1/agent/toutiao_hybrid_recall", hertz.ReqOption{
		ExpectedCode:    http.StatusOK,
		Body:            body,
		Result:          response,
		Notes:           "devai_toutiao_hybrid_search",
		Headers:         map[string]string{"x-jwt-token": a.cloudToken},
		Timeout:         time.Second * 30,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	items := lo.Map(response.RecallSegments, func(item *devai.RecallSegment, idx int) SearchItem {
		var s = SearchItem{
			Description: item.Content,
			Icon:        item.Icon,
			Source:      toutiaoHybridSource,
		}
		switch {
		case item.DocumentSegment != nil:
			s.Title = item.DocumentSegment.Title
			s.URL = item.DocumentSegment.GetLink()
		case item.QASegment != nil:
			s.Title = item.QASegment.Question
			s.URL = item.QASegment.GetLink()
		case item.OncallSegment != nil:
			s.Title = item.OncallSegment.Title
			s.URL = item.OncallSegment.GetLink()
		case item.SearchSegment != nil:
			s.Title = item.SearchSegment.Title
			s.URL = item.SearchSegment.GetLink()
			s.WebEngine = toutiaoHybridSource
		case item.PhraseSegment != nil:
			s.Title = item.PhraseSegment.Title
			s.URL = item.PhraseSegment.GetLink()
		}
		s.TraceID = logid.GenLogID()
		normalizedURL, err := normalizeURL(ctx, s.URL)
		if err != nil {
			getLogger(ctx).Errorf("failed to normalize url: %v", err)
			normalizedURL = s.URL
		}
		normalizedURL = fixGovURL(normalizedURL)
		s.NormalizedURL = normalizedURL
		s.IndexInSearchEngine = idx
		return s
	})
	items = lo.Filter(items, func(item SearchItem, index int) bool {
		return len(item.URL) > 0
	})
	if needEval {
		items = a.evaluateSearch(ctx, query, items, topK)
	}

	return &searchResult{
		Query: query,
		Items: items,
	}, nil
}

func (a *Agent) devAISearch(ctx context.Context, query string) (_ *searchResult, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "devai_search", agentrace.WithObjectSpanData(map[string]any{"query": query}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	body := &devai.RecallAgentRequest{
		Query: query,
		TopK:  20,
		RecallExtra: &devai.RecallExtra{
			UserLarkToken: lo.Ternary(len(a.larkToken) > 0, &a.larkToken, nil),
		},
		EnableBytedSearch: &a.enableBytedSearch,
	}
	response := &devai.RecallAgentResponse{}
	_, err = searchClient.DoJSONReq(ctx, http.MethodPost, "/openapi/knowledge/v1/agent/recall", hertz.ReqOption{
		ExpectedCode:    http.StatusOK,
		Body:            body,
		Result:          response,
		Notes:           "devai_search",
		Headers:         map[string]string{"x-jwt-token": a.cloudToken},
		Timeout:         time.Second * 30,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	searchEngineIndex := map[string]int{}
	items := lo.Map(response.RecallSegments, func(item *devai.RecallSegment, _ int) SearchItem {
		var s = SearchItem{
			Description: item.Content,
			Icon:        item.Icon,
			Source:      item.SourceType,
		}
		switch {
		case item.DocumentSegment != nil:
			s.Title = item.DocumentSegment.Title
			s.URL = item.DocumentSegment.GetLink()
		case item.QASegment != nil:
			s.Title = item.QASegment.Question
			s.URL = item.QASegment.GetLink()
		case item.OncallSegment != nil:
			s.Title = item.OncallSegment.Title
			s.URL = item.OncallSegment.GetLink()
		case item.SearchSegment != nil:
			s.Title = item.SearchSegment.Title
			s.URL = item.SearchSegment.GetLink()
			s.WebEngine = item.SearchSegment.GetSource()
		case item.PhraseSegment != nil:
			s.Title = item.PhraseSegment.Title
			s.URL = item.PhraseSegment.GetLink()
		}
		if item.DocumentSegment != nil && item.DocumentSegment.DocumentId > 0 {
			s.DocumentID = strconv.FormatInt(item.DocumentSegment.DocumentId, 10)
		}
		s.TraceID = logid.GenLogID()
		normalizedURL, err := normalizeURL(ctx, s.URL)
		if err != nil {
			getLogger(ctx).Errorf("failed to normalize url: %v", err)
			normalizedURL = s.URL
		}
		normalizedURL = fixGovURL(normalizedURL)
		s.URL = fixGovURL(s.URL)
		s.NormalizedURL = normalizedURL
		s.IndexInSearchEngine = searchEngineIndex[s.WebEngine]
		searchEngineIndex[s.WebEngine]++
		return s
	})
	items = lo.Filter(items, func(item SearchItem, index int) bool {
		return len(item.URL) > 0
	})
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"items": items}))
	items = lo.Filter(items, func(item SearchItem, _ int) bool {
		// Just for testing. Ignore all items with [no_aime] in the title.
		return !strings.Contains(item.Title, "[no_aime]")
	})
	return &searchResult{
		Query: query,
		Items: items,
	}, nil
}

func fixGovURL(u string) string {
	// 特殊处理 gov.cn 域名：如果没有 www 前缀，则添加 www 前缀
	if parsedURL, err := url.Parse(u); err == nil {
		if strings.HasSuffix(parsedURL.Host, "gov.cn") && !strings.HasPrefix(parsedURL.Host, "www.") {
			parsedURL.Host = "www." + parsedURL.Host
			u = parsedURL.String()
		}
	}
	return u
}

func (a *Agent) knowledgeBaseSearch(ctx context.Context, query string) (_ *searchResult, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "knowledgebase_search", agentrace.WithObjectSpanData(map[string]any{"query": query}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	if a.agentCtx == nil {
		return &searchResult{
			Query: query,
			Items: []SearchItem{},
		}, nil
	}
	datasetID := cast.ToString(a.agentCtx.Parameters[entity.RuntimeParametersDatasetID])
	if len(datasetID) == 0 {
		getLogger(ctx).Infof("knowledgebase_search: datasetID is empty")
		return &searchResult{
			Query: query,
			Items: []SearchItem{},
		}, nil
	}
	getLogger(ctx).Infof("dataset id: %s", datasetID)
	segments, err := a.agentCtx.GetAPIClient().RecallKnowledgeBase(ctx, datasetID, query, 85)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	getLogger(ctx).Infof("recall knowlege base segment: %+v", segments)
	items := lo.Map(segments, func(item *nextagent.RecallSegment, _ int) SearchItem {
		normalizedURL, err := normalizeURL(ctx, item.URL)
		if err != nil {
			getLogger(ctx).Errorf("failed to normalize url: %v", err)
			normalizedURL = item.URL
		}
		parse, _ := time.Parse(time.RFC3339, item.LastUpdatedAt)
		var s = SearchItem{
			URL:             item.URL,
			NormalizedURL:   normalizedURL,
			Title:           item.Title,
			LastUpdatedTime: parse,
			Owner:           item.Owner,
			Icon:            "https://lf-package-cn.feishucdn.com/obj/feishu-static/lark/open/website/favicon-logo.svg",
			Description:     item.Content,
			DocumentID:      item.DocumentID,
			Source:          knowledgebaseSource,
		}
		s.TraceID = logid.GenLogID()
		return s
	})
	items = lo.Filter(items, func(item SearchItem, index int) bool {
		return len(item.URL) > 0
	})
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"items": items}))
	items = lo.Filter(items, func(item SearchItem, _ int) bool {
		// Just for testing. Ignore all items with [no_aime] in the title.
		return !strings.Contains(item.Title, "[no_aime]")
	})
	return &searchResult{
		Query: query,
		Items: items,
	}, nil
}

func (a *Agent) scholarSearch(ctx context.Context, query string) (_ *searchResult, err error) {
	span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "scholar_search", agentrace.WithObjectSpanData(map[string]any{"query": query}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	body := &devai.RecallAgentRequest{
		Query: query,
		TopK:  20,
		RecallExtra: &devai.RecallExtra{
			UserLarkToken: lo.Ternary(len(a.larkToken) > 0, &a.larkToken, nil),
		},
		EnableBytedSearch: &a.enableBytedSearch,
		Recallers:         []string{"agent_x_scholar"},
	}
	response := &devai.RecallAgentResponse{}
	_, err = searchClient.DoJSONReq(ctx, http.MethodPost, "/openapi/knowledge/v1/agent/recall", hertz.ReqOption{
		ExpectedCode:    http.StatusOK,
		Body:            body,
		Result:          response,
		Notes:           "scholar_search",
		Headers:         map[string]string{"x-jwt-token": a.cloudToken},
		Timeout:         time.Second * 30,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	searchEngineIndex := map[string]int{}
	items := lo.Map(response.RecallSegments, func(item *devai.RecallSegment, _ int) SearchItem {
		var s = SearchItem{
			Description: item.Content,
			Icon:        item.Icon,
			Source:      item.SourceType,
		}
		switch {
		case item.DocumentSegment != nil:
			s.Title = item.DocumentSegment.Title
			s.URL = item.DocumentSegment.GetLink()
		case item.QASegment != nil:
			s.Title = item.QASegment.Question
			s.URL = item.QASegment.GetLink()
		case item.OncallSegment != nil:
			s.Title = item.OncallSegment.Title
			s.URL = item.OncallSegment.GetLink()
		case item.SearchSegment != nil:
			s.Title = item.SearchSegment.Title
			s.URL = item.SearchSegment.GetLink()
			s.WebEngine = item.SearchSegment.GetSource()
		case item.PhraseSegment != nil:
			s.Title = item.PhraseSegment.Title
			s.URL = item.PhraseSegment.GetLink()
		}
		if item.DocumentSegment != nil && item.DocumentSegment.DocumentId > 0 {
			s.DocumentID = strconv.FormatInt(item.DocumentSegment.DocumentId, 10)
		}
		s.TraceID = logid.GenLogID()
		normalizedURL, err := normalizeURL(ctx, s.URL)
		if err != nil {
			getLogger(ctx).Errorf("failed to normalize url: %v", err)
			normalizedURL = s.URL
		}
		s.NormalizedURL = normalizedURL
		s.IndexInSearchEngine = searchEngineIndex[s.WebEngine]
		searchEngineIndex[s.WebEngine]++
		return s
	})
	items = lo.Filter(items, func(item SearchItem, index int) bool {
		return len(item.URL) > 0
	})
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"items": items}))
	items = lo.Filter(items, func(item SearchItem, _ int) bool {
		// Just for testing. Ignore all items with [no_aime] in the title.
		return !strings.Contains(item.Title, "[no_aime]")
	})
	return &searchResult{
		Query: query,
		Items: items,
	}, nil
}

func (a *Agent) batchSearch(ctx context.Context, queries []string, needEval bool) ([]*searchResult, error) {
	group := errgroup.Group{}
	resultChan := make(chan *searchResult, len(queries))
	for _, query := range queries {
		ctx := ctx
		a.ctxInfof(ctx, "search query: %s", query)
		group.Go(func() error {
			span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "search", agentrace.WithObjectSpanData(map[string]any{"query": query}))
			defer span.Finish()
			searchResult, err := a.devAISearch(ctx, query)
			if err != nil {
				a.logger.Errorf("failed for query %q: %v", query, err)
				return nil
			}
			lo.ForEach(searchResult.Items, func(item SearchItem, _ int) {
				a.ctxInfof(ctx, "data_trace_id: %s, search result: %s", item.TraceID, indentJSON(item))
			})
			searchResult.Items = a.cherrypickSearch(ctx, query, searchResult.Items)
			lo.ForEach(searchResult.Items, func(item SearchItem, _ int) {
				a.ctxInfof(ctx, "data_trace_id: %s, cherry pick result: %s", item.TraceID, indentJSON(item))
			})
			// There are some same urls in the search result, but we need to combine them.
			searchResult.Items = combineSearchItems(searchResult.Items)
			if needEval {
				searchResult.Items = a.evaluateSearch(ctx, query, searchResult.Items, 10)
				if len(searchResult.Items) == 0 {
					a.logger.Warnf("no results found for query: %s", query)
					return nil
				}
			}
			lo.ForEach(searchResult.Items, func(item SearchItem, _ int) {
				a.ctxInfof(ctx, "data_trace_id: %s, evaluate result: %s", item.TraceID, indentJSON(item))
			})
			resultChan <- searchResult
			return nil
		})
	}
	err := group.Wait()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	close(resultChan)
	result := make([]*searchResult, 0, len(queries))
	for r := range resultChan {
		result = append(result, r)
	}

	return result, nil
}

func (a *Agent) cherrypickSearch(ctx context.Context, query string, answers []SearchItem) []SearchItem {
	group := errgroup.Group{}
	group.SetLimit(len(answers))
	resultChan := make(chan SearchItem, len(answers))
	for _, answer := range answers {
		answer := answer
		group.Go(func() error {
			span, ctx := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "cherry_pick", agentrace.WithObjectSpanData(map[string]any{"before": answer.Description}))
			span.SetTags(answer.GetTraceTag())
			defer span.Finish()
			result, err := a.cherryPick(ctx, query, answer.Description)
			if err != nil {
				agentrace.AddErrorTag(span, err)
				span.Finish()
				a.logger.Errorf("failed to evaluate chunk: %v", err)
				resultChan <- answer
				return nil
			}
			span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"after": result}))
			answer.Description = result
			resultChan <- answer
			return nil
		})
	}
	group.Wait()
	close(resultChan)
	result := make([]SearchItem, 0, len(answers))
	for r := range resultChan {
		result = append(result, r)
	}
	return result
}

func combineSearchItems(answers []SearchItem) []SearchItem {
	combined := make(map[string]map[string]SearchItem)
	for _, answer := range answers {
		url := answer.NormalizedURL
		if existing, ok := combined[url]; ok {
			existing[answer.Description] = answer
		} else {
			combined[url] = map[string]SearchItem{answer.Description: answer}
		}
	}
	result := make([]SearchItem, 0, len(combined))
	for _, items := range combined {
		item := SearchItem{}
		for _, answer := range items {
			if len(item.URL) == 0 {
				item = answer
				continue
			}
			item.Description += "\n... ...\n" + answer.Description
		}
		result = append(result, item)
	}
	return result
}

func callBgeMiniCpm(ctx context.Context, query string, passages []string) ([]float64, error) {
	url := "http://bits-ai-online-embedding.byted.org/bge-reranker-v2-minicpm-layerwise"
	payload := map[string]interface{}{
		"query":    query,
		"passages": passages,
	}
	marshal, err := json.Marshal(payload)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	request, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(marshal))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	request.Header.Set("content-type", "application/json")
	do, err := http.DefaultClient.Do(request)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	traceID := do.Header.Get("x-tt-logid")
	getLogger(ctx).Warnf("callBgeMiniCpm, traceID: %s", traceID)
	if do.StatusCode == http.StatusUnauthorized {
		return nil, errors.Errorf("unauthorized")
	}
	if do.StatusCode != http.StatusOK {
		return nil, errors.Errorf("Bad Status: %s", do.Status)
	}
	respBody, err := io.ReadAll(do.Body)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var response struct {
		Data []float64 `json:"data"`
		Msg  string    `json:"msg"`
	}

	err = json.Unmarshal(respBody, &response)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if response.Msg != "success" {
		return nil, errors.Errorf("Bad Response: %s", response.Msg)
	}
	return response.Data, nil
}

func rankByBgeMiniCpm(ctx context.Context, query string, answers []string) ([]float64, error) {
	numWorker := 3
	batchSize := 5
	numSegments := len(answers)
	rankScores := make([]float64, numSegments)
	numBatches := int(math.Ceil(float64(numSegments) / float64(batchSize)))
	indexChan := make(chan int, numBatches)

	var wg sync.WaitGroup
	wg.Add(numWorker)
	for w := 0; w < numWorker; w++ {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					getLogger(ctx).Errorf("rankByBgeMiniCpm, panic: %v,stack trace: %s", r, string(debug.Stack()))
				}
			}()
			defer wg.Done()
			for index := range indexChan {
				endIndex := index + batchSize
				if endIndex > numSegments {
					endIndex = numSegments
				}
				passages := answers[index:endIndex]

				workerErr := devaiutil.RetryWithBackoffDelay(ctx, time.Duration(0), 2, func() error {
					passageRankScores, rankErr := callBgeMiniCpm(ctx, query, passages)
					if rankErr != nil {
						return rankErr
					}
					for i := index; i < endIndex; i++ {
						// 多个 goroutine 同时写结果数组里面的不同区间，不会有并发冲突
						rankScores[i] = passageRankScores[i-index]
					}
					return nil
				})
				if workerErr != nil {
					getLogger(ctx).Errorf("rankByBgeMiniCpm: error on index %d", index)
				}
			}
		}()
	}

	for index := 0; index < numSegments; index += batchSize {
		indexChan <- index
	}
	close(indexChan)
	wg.Wait()

	return rankScores, nil
}

func (a *Agent) evaluateSearch(ctx context.Context, query string, answers []SearchItem, maxCount int) []SearchItem {
	var descriptions []string
	for _, answer := range answers {
		a.logger.Infof("[evaluateSearch] query: %v, description: %v", query, answer.Description)
		descriptions = append(descriptions, answer.Description)
	}
	rankScores, err := rankByBgeMiniCpm(ctx, query, descriptions)
	if err != nil {
		a.logger.Errorf("failed to evaluate chunk: %v", err)
		return answers
	}
	a.ctxInfof(ctx, "callBgeMiniCpm data trace ids: %+v,result: %+v", lo.Map(answers, func(item SearchItem, index int) string {
		return item.TraceID
	}), rankScores)
	for i := 0; i < len(answers); i++ {
		answers[i].RankScore = rankScores[i]
		answers[i].Query2AnswerScore = rankScores[i]
	}
	sort.SliceStable(answers, func(i, j int) bool {
		return answers[i].RankScore > answers[j].RankScore
	})
	answers = lo.Filter(answers, func(answer SearchItem, idx int) bool {
		pass := answer.RankScore > -2.5
		span, _ := agentrace.GetRuntimeTracerFromContext(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "evaluate",
			agentrace.WithObjectSpanData(map[string]any{"score": answer.RankScore, "pass": pass}))
		span.SetTags(answer.GetTraceTag())
		span.Finish()
		return pass
	})
	answers = lo.Slice(answers, 0, maxCount)
	return answers
}
