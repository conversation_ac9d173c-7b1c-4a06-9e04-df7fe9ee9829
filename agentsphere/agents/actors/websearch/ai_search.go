package websearch

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/lang/v2/mathx"
	"golang.org/x/sync/errgroup"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logid"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc"

	runtimeentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
)

func (a *Agent) InternalAISearch(ctx *iris.AgentRunContext, tasks []string) (*websearchtool.Response, error) {
	a.mcpMode = true
	a.agentCtx = ctx
	a.step = ctx.State.CurrentStep
	a.stepID = a.step.StepID
	a.logger = ctx.GetLogger()
	a.llm = ctx.GetLLM()
	a.cloudToken = ctx.GetEnv(runtimeentity.RuntimeEnvironUserCloudJWT)
	a.larkToken = ctx.GetEnv(runtimeentity.RunTimeLarkUserAccessToken)
	if err := a.initContext(ctx); err != nil {
		return nil, errors.WithStack(err)
	}
	start := time.Now()
	subTaskGroup := &errgroup.Group{}
	subTaskGroup.SetLimit(4)
	var subTaskLock sync.RWMutex
	var items []SearchItem
	seen := make(map[string]bool)
	defer func() {
		_ = metrics.AR.ActorWebSearcherPhraseCost.WithTags(&metrics.AgentActorWebSearcherPhraseTag{
			Phrase: "whole_run",
		}).Observe(float64(time.Since(start).Milliseconds()))
	}()
	for _, task := range tasks {
		task := task
		subTaskGroup.Go(func() error {
			subTaskResult, err := a.agentSearch(ctx, task)
			if err != nil {
				a.logger.Errorf("failed to process task: %s, err: %v", task, err)
				return nil
			}
			subTaskLock.Lock()
			var braveItem []SearchItem
			var youItem []SearchItem
			var toutiaoItem []SearchItem
			var larkItem []SearchItem
			var allItems []*SearchItem
			for _, item := range subTaskResult.Items {
				if item.Source == webSearchSource && item.WebEngine == "toutiao" && len(toutiaoItem) < 3 {
					toutiaoItem = append(toutiaoItem, item)
					allItems = append(allItems, &item)
					_, exist := seen[item.URL]
					if exist {
						continue
					}
					items = append(items, item)
					seen[item.URL] = true
				}
				if item.Source == webSearchSource && item.WebEngine == "you" && len(youItem) < 8 {
					youItem = append(youItem, item)
					allItems = append(allItems, &item)
					_, exist := seen[item.URL]
					if exist {
						continue
					}
					items = append(items, item)
					seen[item.URL] = true
				}
				if item.Source == webSearchSource && item.WebEngine == "brave" && len(braveItem) < 8 {
					braveItem = append(braveItem, item)
					allItems = append(allItems, &item)
					_, exist := seen[item.URL]
					if exist {
						continue
					}
					items = append(items, item)
					seen[item.URL] = true
				}
				if item.Source == larkSearchSource && len(larkItem) < 5 {
					larkItem = append(larkItem, item)
					allItems = append(allItems, &item)
					_, exist := seen[item.URL]
					if exist {
						continue
					}
					items = append(items, item)
					seen[item.URL] = true
				}
			}
			subTaskLock.Unlock()
			a.toolCallLock.Lock()
			stepReporter := a.createAndRunningStep(map[string]any{"SearchRequests": task})
			stepReportDesc := fmt.Sprintf("正在搜索：%v", task)
			searchToolCallFinished := stepReporter.ReportSearchToolCall(stepReportDesc, []string{task})
			searchToolCallFinished(selectSearchItems(allItems, allItems, 30)) // Only display fixed number of items.
			a.toolCallLock.Unlock()
			return nil
		})
	}
	_ = subTaskGroup.Wait()
	return &websearchtool.Response{
		SearchResults: lo.Map(items, func(item SearchItem, _ int) *websearchtool.SearchItem {
			return &websearchtool.SearchItem{
				Title:       item.Title,
				URL:         item.URL,
				Description: item.Description,
			}
		}),
	}, nil
}

func (a *Agent) InternalSimpleAIResearch(ctx *iris.AgentRunContext, task string, subtasks []string) (response *websearchtool.Response, err error) {
	a.mcpMode = true
	a.agentCtx = ctx
	a.step = ctx.State.CurrentStep
	a.stepID = a.step.StepID
	a.logger = ctx.GetLogger()
	a.llm = ctx.GetLLM()
	a.cloudToken = ctx.GetEnv(runtimeentity.RuntimeEnvironUserCloudJWT)
	a.larkToken = ctx.GetEnv(runtimeentity.RunTimeLarkUserAccessToken)
	if err = a.initContext(ctx); err != nil {
		return nil, errors.WithStack(err)
	}
	span, c := ctx.GetTracer(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "run",
		agentrace.WithSpanID(a.stepID), agentrace.WithObjectSpanData(map[string]any{"inputs": subtasks}))
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	ctx = ctx.WithContext(c)
	answer, err := a.internalAIResearch(ctx, task, subtasks)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"answer": answer.FinalReport}))
	return answer, err
}

func (a *Agent) runSingleAIResearchTask(ctx *iris.AgentRunContext, task string) (response *websearchtool.Response, err error) {
	var think string
	isOnlyInternal, think, err := a.IsOnlyInternal(ctx, task)
	if err != nil {
		isOnlyInternal = false
		a.logger.Errorf("[InternalRunWithPlanFirst] isOnlyInternal err: %v", err)
	}
	a.logger.Infof("[InternalRunWithPlanFirst] internal source detect: %v, think %v", isOnlyInternal, think)

	return
}

func (a *Agent) internalAIResearch(ctx *iris.AgentRunContext, task string, subtasks []string) (*websearchtool.Response, error) {
	stepTaskMaxToken := mathx.MinInteger(a.maxToken, 50_000)
	subTaskGroup := &errgroup.Group{}
	subTaskGroup.SetLimit(2)
	var subTaskResults []SubTaskResult
	var subTaskLock sync.RWMutex
	start := time.Now()
	defer func() {
		_ = metrics.AR.ActorWebSearcherPhraseCost.WithTags(&metrics.AgentActorWebSearcherPhraseTag{
			Phrase: "whole_run",
		}).Observe(float64(time.Since(start).Milliseconds()))
	}()
	for _, task := range subtasks {
		task := task
		subTaskGroup.Go(func() error {
			ctx := ctx
			span, c := ctx.GetTracer(ctx).StartCustomSpan(ctx, agentrace.SpanTypeStep, "sub_task", agentrace.WithObjectSpanData(map[string]any{"sub_task": task}))
			a.ctxInfof(ctx, "start to process task: %s", task)
			ctx = ctx.WithContext(c)
			isOnlyInternal, _, err := a.IsOnlyInternal(ctx, task)
			if err != nil {
				isOnlyInternal = false
				a.logger.Errorf("[InternalRunWithPlanFirst] isOnlyInternal err: %v", err)
			}
			subTaskResult, err := a.subTask(ctx, task, "", stepTaskMaxToken, isOnlyInternal, false)
			if err != nil {
				a.logger.Errorf("failed to process task: %s, err: %v", task, err)
				return nil
			}
			span.UpdateData(agentrace.NewObjectSpanData(map[string]any{"answer": subTaskResult.Answer, "no_answer": subTaskResult.NoAnswer}))
			span.Finish()
			subTaskLock.Lock()
			subTaskResults = append(subTaskResults, subTaskResult)
			subTaskLock.Unlock()
			return nil
		})
	}
	_ = subTaskGroup.Wait()
	composeTask := fmt.Sprintf("Task: \n%s\nBased on the above task, the divided subtasks are\n%v", task, strings.Join(subtasks, "\n"))

	var knowledges []SearchItem
	seen := make(map[string]bool)

	for _, subTaskResult := range subTaskResults {
		if len(subTaskResult.Knowledge) == 0 {
			subTaskResult.NoAnswer = true
		}
		for _, knowledge := range subTaskResult.Knowledge {
			_, exist := seen[knowledge.Description]
			if exist {
				continue
			}
			seen[knowledge.Description] = true
			knowledges = append(knowledges, knowledge)
		}
	}
	masker := NewURLMasker()
	for i := 0; i < len(knowledges); i++ {
		knowledges[i].Description = masker.maskURLs(knowledges[i].Description)
	}
	system := a.getAnswerWithPlanSystemPrompt(a.languageCode)
	user := getAnswerWithPlanUserPrompt(composeTask, knowledges, "")

	answer, err := a.generate(ctx, []tool{answerTool, backupTool}, system, user)
	if err != nil {
		a.logger.Errorf("failed to generate answer: %+v", err)
		return nil, errors.WithMessage(err, "failed to generate answer")
	}
	answer = masker.unmaskURLs(answer)
	answer, knowledges = processBrackets(ctx, answer, knowledges)

	var response websearchtool.Response

	for idx, _ := range subTaskResults {
		response.TaskResults = append(response.TaskResults, websearchtool.TaskResult{
			Task:     subTaskResults[idx].Task,
			Finished: !subTaskResults[idx].NoAnswer,
			Reference: lo.Map(subTaskResults[idx].Knowledge, func(item SearchItem, _ int) websearchtool.SearchItem {
				return websearchtool.SearchItem{
					Icon:        item.Icon,
					Title:       item.Title,
					URL:         item.URL,
					Description: item.Description,
				}
			}),
		})
	}
	response.FinalReport = answer
	response.Reference = lo.Map(knowledges, func(item SearchItem, _ int) websearchtool.SearchItem {
		return websearchtool.SearchItem{
			Icon:        item.Icon,
			Title:       item.Title,
			URL:         item.URL,
			Description: item.Description,
		}
	})
	return &response, nil
}

func (a *Agent) internalAISearch(ctx context.Context, input string) (response *websearchtool.Response, err error) {
	logID, ok := ctxvalues.LogID(ctx)
	if !ok {
		logID = logid.GenLogID()
		ctx = ctxvalues.SetLogID(ctx, logID)
	}

	ctx = setLogger(ctx, a.logger)
	a.logger.Infof("websearch: %s, log_id: %s", input, logID)

	group := conc.NewWaitGroup()

	var searchResults []*searchResult
	var toutiaoSearchResult *searchResult
	// Normal Search
	group.Go(func() {
		searchResults, err = a.batchSearch(ctx, []string{input}, false)
		if err != nil {
			a.logger.Errorf("[InternalRunWithPlanFirst] batchSearch err: %v", err)
		}
	})

	// Toutiao Hybrid Search
	group.Go(func() {
		var (
			err error
		)
		err = backoff.Retry(func() error {
			toutiaoSearchResult, err = a.toutiaoHybridSearch(ctx, input, false)
			if err != nil {
				return err
			}
			return nil
		}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
		if err != nil {
			a.logger.Errorf("[InternalRunWithPlanFirst toutiaoHybridSearch] err: %v", err)
			return
		}
		lo.ForEach(toutiaoSearchResult.Items, func(item SearchItem, index int) {
			a.ctxInfof(ctx, "data_trace_id: %s, toutiao search result: %s", item.TraceID, indentJSON(item))
		})
	})
	group.Wait()

	var items []*SearchItem
	for _, res := range searchResults {
		if len(res.Items) == 0 {
			continue
		}
		for _, item := range res.Items {
			item := item
			items = append(items, &item)
		}
	}
	if toutiaoSearchResult != nil {
		for _, item := range toutiaoSearchResult.Items {
			item := item
			items = append(items, &item)
		}
	}
	var results []*SearchItem

	var internalItems []SearchItem
	var externalItems []*SearchItem

	for _, item := range items {
		if item.Source == webSearchSource {
			externalItems = append(externalItems, item)
		} else {
			internalItems = append(internalItems, *item)
		}
	}

	youItems := make([]*SearchItem, 0, len(items))
	braveItems := make([]*SearchItem, 0, len(items))
	toutiaoItems := make([]*SearchItem, 0, len(items))
	googleItems := make([]*SearchItem, 0, len(items))

	for _, i := range externalItems {
		switch i.WebEngine {
		case "you":
			youItems = append(youItems, i)
		case "brave":
			braveItems = append(braveItems, i)
		case "google":
			googleItems = append(googleItems, i)
		case "toutiao":
			toutiaoItems = append(toutiaoItems, i)
		}
	}

	internalRankItems := a.evaluateSearch(ctx, input, internalItems, 5)
	var internalRankPItems []*SearchItem

	for _, internalItem := range internalRankItems {
		item := internalItem
		internalRankPItems = append(internalRankPItems, &item)
	}

	youItems = lo.Slice(youItems, 0, 5)
	googleItems = lo.Slice(googleItems, 0, 5)
	braveItems = lo.Slice(braveItems, 0, 5)
	toutiaoItems = lo.Slice(toutiaoItems, 0, 5)

	results = append(results, braveItems...)
	results = append(results, youItems...)
	results = append(results, googleItems...)
	results = append(results, toutiaoItems...)
	results = append(results, internalRankPItems...)

	return &websearchtool.Response{
		SearchResults: lo.Map(results, func(item *SearchItem, index int) *websearchtool.SearchItem {
			return &websearchtool.SearchItem{
				Icon:            item.Icon,
				Title:           item.Title,
				URL:             item.NormalizedURL,
				Description:     replaceSnippetTag(item.Description),
				RankScore:       item.RankScore,
				Source:          item.Source,
				DocumentID:      item.DocumentID,
				NeedFullContext: item.NeedFullContext,
				LastUpdatedTime: item.LastUpdatedTime,
				TraceID:         item.TraceID,
				WebEngine:       item.WebEngine,
			}
		}),
	}, nil

}
