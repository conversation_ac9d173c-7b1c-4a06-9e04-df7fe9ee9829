package htmlreporter

import (
	"embed"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	agentutil "code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"code.byted.org/devgpt/kiwis/lib/util"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

var (
	Identifier = "html_reporter"
	parameters = []actors.ParameterDescription{
		{
			Name:        "user_request",
			Description: "exactly copy the user's requirements",
		},
		{
			Name:        "task",
			Description: "the task to complete",
		},
	}
	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")
)

type ReporterStore struct {
	CurrentTask     string
	UserRequirement string
	AgentStepID     string
	ParentStepID    string
}

type DeployStrategy string

const (
	DeployStrategySingleFile DeployStrategy = "single_file"
	DeployStrategyWholeDir   DeployStrategy = "whole_dir"
)

type Agent struct {
	actors.BaseActor
	Reporter *Reporter
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	MaxSteps       int
	Variant        string
	DeployStrategy DeployStrategy
}

func New(opt CreateOption) *Agent {
	info := iris.AgentInfo{
		Identifier: Identifier,
		Desc:       lo.Must(promptset.ExecutePrompt("description", opt.Variant, nil)),
	}

	reporter := &Reporter{
		AgentInfo:      info,
		Variant:        opt.Variant,
		DeployStrategy: opt.DeployStrategy,
	}
	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: parameters,
		},
		Reporter: reporter,
	}

	return agent
}

type CreateReportOption struct {
	UserRequirement string
	CurrentTask     string
}

func (a *Agent) CreateReport(run *iris.AgentRunContext, step *iris.AgentRunStep, opt CreateReportOption) (result controltool.ConclusionOutput) {
	run.GetLogger().Infof("html reporter run with input: %s", opt.UserRequirement)

	attachReg := regexp.MustCompile(`\[(.*)\]\((.*)\)`)
	matches := attachReg.FindAllStringSubmatch(opt.UserRequirement, -1)

	fileNames := make(map[string]bool)
	for _, match := range matches {
		if len(match) < 3 {
			run.GetLogger().Warnf("invalid match: %v", match)
			continue
		}

		fileName := filepath.Base(match[2])
		if !strings.HasSuffix(fileName, ".md") {
			continue
		}
		fileNames[fileName] = true
	}
	// upload attachments
	task := ""
	editor := workspace.GetEditor(run)
	for fileName, exist := range fileNames {
		if !exist {
			continue
		}

		output, err := editor.SearchFileName(workspace.SearchFileNameArgs{
			Directory: ".",
			FileName:  fileName,
		})
		if err != nil || len(output.Files) == 0 {
			run.GetLogger().Error("filename: %s, not found", fileName)
			continue
		}
		for _, file := range output.Files {
			fileName = file.Path
			fileContent, err := readFileIfExists(fileName)
			if err != nil {
				run.GetLogger().Errorf("filename: %s, read err", fileName)
				continue
			}
			task += "\n" + fileName + "\n" + string(fileContent)
		}
	}

	iris.UpdateStoreByKey(run, a.Reporter.UID(), ReporterStore{
		CurrentTask:     lo.Ternary(len(task) > 0, task, opt.CurrentTask),
		UserRequirement: opt.UserRequirement,
	})
	err := a.Reporter.RunWithOption(run, &agents.ReActAgentRunOption{
		Step: step,
	})
	if err != nil {
		result = controltool.ConclusionOutput{
			Content:    err.Error(),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}
		return result
	}

	run.GetLogger().Debugf("htmlreporter conclusion: %+v", run.State.LastStep().Outputs)
	result = controltool.ConclusionOutput{
		Content:    "success",
		Evaluation: controltool.ConclusionEvaluationSuccess,
		Reference: iris.Reference{
			iris.ReferenceItem{
				Title: "index.html",
				URI:   conv.DefaultAny[string](run.State.LastStep().Outputs["url"]),
			},
		},
	}
	return result
}

type Reporter struct {
	iris.AgentInfo
	uid            string
	Variant        string
	DeployStrategy DeployStrategy
}

func (r *Reporter) UID() string {
	if r.uid == "" {
		r.uid = fmt.Sprintf("%s-agent-%s", r.Identifier, util.RandomString(8))
	}
	return r.uid
}

var _ iris.RunnableAgent = &Reporter{}

func (r *Reporter) Run(run *iris.AgentRunContext) error {
	logger := run.GetLogger()
	publisher := run.GetPublisher()
	store := iris.RetrieveStoreByKey[ReporterStore](run, r.UID())
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: r.Name(),
		Parent:        lo.Ternary(store.AgentStepID != "", run.GetStep(store.AgentStepID), nil),
	})

	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(step))

	thought, err := r.think(run)
	if err != nil {
		return err
	}

	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)

	logger.Infof("agent %s current step: generate html, step_id: %s", r.uid, step.StepID)
	step.Action = workspace.NewCreateFileAction(workspace.CreateFileOption{RemoveOuterBackquotes: false})

	ws := workspace.GetWorkspace(run)
	_, err = ws.Editor.CreateFile(run, workspace.CreateFileArgs{
		Path:    "index.html",
		Content: thought.Content,
	}, workspace.CreateFileOption{RemoveOuterBackquotes: false})
	if err != nil {
		return err
	}

	logger.Infof("agent %s current step: deploy html, step_id: %s", r.uid, step.StepID)
	step.Thought.Tool = "deploy"
	step.Action = workspace.NewDeployAction(workspace.DeployOption{ProcessHTML: false})

	var output *workspace.DeployOutput
	var deployErr error
	switch r.DeployStrategy {
	case DeployStrategySingleFile:
		step.Thought.Parameters = map[string]any{
			"directory": "index.html",
		}
		publisher.ReportToolCall(step, iris.ToolCallStatusStarted, "生成最终报告")
		output, deployErr = workspace.DeploySingleFile(run, "index.html", workspace.DeployOption{ProcessHTML: false})
	case DeployStrategyWholeDir:
		step.Thought.Parameters = map[string]any{
			"directory": ".",
		}
		publisher.ReportToolCall(step, iris.ToolCallStatusStarted, "生成最终报告")
		output, deployErr = workspace.DeployDirectory(run, workspace.DeployArgs{Directory: "."}, workspace.DeployOption{ProcessHTML: false})
	}

	if deployErr != nil {
		run.GetLogger().Errorf("agent %s current step: deploy html, step_id: %s, err: %+v", r.uid, step.StepID, deployErr)
		return err
	}

	step.Finish(agentutil.ValueToMap(lo.FromPtr(output)), nil)
	run.GetLogger().Debugf("agent %s current step: deploy html, step_id: %s, result: %+v", r.uid, step.StepID, step.Outputs)
	publisher.ReportStep(step)
	publisher.ReportToolCall(step, iris.ToolCallStatusCompleted, "生成最终报告")
	return nil
}

func (r *Reporter) think(run *iris.AgentRunContext) (*iris.Thought, error) {
	messages := r.Compose(run)

	content, err := agents.Think(run, r.Identifier, messages,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
		},
	)
	if err != nil {
		return nil, errors.WithMessage(err, "llm api not available")
	}
	htmlReg := regexp.MustCompile("```(.?)*html([\\S\\s]*)```")
	htmlContent := content.Content
	htmlContents := htmlReg.FindStringSubmatch(content.Content)
	run.GetLogger().Debugf("html content: %s, extracted length: %d", htmlContent, len(htmlContents))
	if len(htmlContents) > 2 {
		htmlContent = htmlContents[2]
	} else {
		htmlReg2 := regexp.MustCompile("```(.?)*html([\\S\\s]*)")
		htmlContents := htmlReg2.FindStringSubmatch(content.Content)
		if len(htmlContents) > 2 {
			htmlContent = htmlContents[2]
		}
	}

	thought := &iris.Thought{
		Content: htmlContent,
		Tool:    "create_file",
		Parameters: map[string]any{
			"file_name": "index.html",
			"content":   content.Content,
		},
		LLMCall: iris.LLMCall{
			ModelName:    content.Model,
			Temperature:  content.Temperature,
			Usage:        content.Usage,
			Prompt:       messages,
			FinishReason: content.FinishReason,
			TraceID:      content.TraceID,
		},
	}
	run.UpdateThought(thought)
	return thought, nil
}

func (r *Reporter) RunWithOption(run *iris.AgentRunContext, opt *agents.ReActAgentRunOption) error {
	publisher := run.GetPublisher()
	store := iris.RetrieveStoreByKey[ReporterStore](run, r.UID())
	// the step represents the agent's task
	// it will be used as the parent step for all the steps created by the agent
	step := opt.Step
	if step == nil {
		return errors.New("step is nil")
	}
	store.AgentStepID = step.StepID
	iris.UpdateStoreByKey(run, r.UID(), store)

	// push current step to steps
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(step))

	originalStep := run.State.CurrentStep
	run.State.CurrentStep = nil
	defer func() {
		last := run.State.LastStep()
		step.Finish(last.Conclusion, last.Error)
		publisher.ReportStep(step)
		run.State.CurrentStep = originalStep
	}()

	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)
	return r.Run(run)
}

func (r *Reporter) Compose(run *iris.AgentRunContext) []*framework.ChatMessage {
	store := iris.RetrieveStoreByKey[ReporterStore](run, r.UID())

	templates := prompt.TemplateSet{
		SystemTmpl: promptset.GetTemplate("system_brief", r.Variant),
		UserTmpl:   promptset.GetTemplate("user", r.Variant),
	}

	messages := agents.ComposeMessage(run, agents.ComposerOptions{
		Templates: templates,
		UserPromptVariables: map[string]any{
			"UserRequirement": store.UserRequirement,
			"Task":            store.CurrentTask,
		},
		MemoryRounds: 20,
	})
	return messages
}

func readFileIfExists(filename string) ([]byte, error) {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return nil, err
	}
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	return io.ReadAll(file)
}
