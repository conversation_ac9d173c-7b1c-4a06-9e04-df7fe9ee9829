package codebaseactor

import (
	"embed"

	"github.com/samber/lo"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/codebase"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
)

var (
	Identifier = "codebase_manager"
	parameters = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "Detailed description of the task, including repository name, branch/commit/tag name, commit message and any other related information",
		},
	}
	//go:embed prompts
	prompts        embed.FS
	promptset      = prompt.MustNewPromptSet(prompts, "prompts")
	enabledCommand = []string{"git", "ls", "cat", "echo", "cd", "rm"}
)

type CodebaseAgentStore struct {
	CurrentTask string
}

type Agent struct {
	actors.BaseActor
	ReAct   *agents.ReActAgent
	Variant string
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	MaxSteps         int
	Variant          string
	EnableBytedRepos bool
	LLMHooks         *prompt.LLMCallHook
	ActorStep        *iris.AgentRunStep
}

func New(opt CreateOption) *Agent {
	tools := []iris.Action{
		workspace.NewGitCloneAction(opt.EnableBytedRepos),
		codebase.NewSubmitMergeRequestAction(opt.EnableBytedRepos),
		workspace.NewReadFileAction(),
		workspace.NewExecuteCommandAction(workspace.NewExecuteCommandActionOption{
			EnabledCommand: enabledCommand,
		}),
		controltool.NewConclusionTool(),
	}
	info := iris.AgentInfo{
		Identifier: Identifier,
		Desc:       lo.Must(promptset.ExecutePrompt("description", opt.Variant, nil)),
	}
	reactor := agents.NewReActAgent(agents.ReActAgentConfig{
		Info:     info,
		Tools:    tools,
		MaxSteps: opt.MaxSteps,
		LLMHooks: opt.LLMHooks,
	})
	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: parameters,
		},
		Variant: opt.Variant,
	}
	reactor.Composer = agent.Compose
	reactor.GetAction = agent.GetAction
	agent.ReAct = reactor
	agent.ExecutionStep = &actors.ExecutionStep{
		Step: opt.ActorStep,
	}

	return agent
}

type WorkspaceRepo struct {
	Directory string
	RepoName  string
	Platform  string
}

func (a *Agent) Compose(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep) []*framework.ChatMessage {
	store := iris.RetrieveStoreByKey[CodebaseAgentStore](run, a.ReAct.UID())
	ws := workspace.GetWorkspace(run)

	templates := prompt.TemplateSet{
		SystemTmpl:  promptset.GetTemplate("system", a.Variant),
		UserTmpl:    promptset.GetTemplate("user", a.Variant),
		ThoughtTmpl: promptset.GetTemplate("thought", a.Variant),
	}

	root, err := ws.Editor.ReadDirectory(workspace.ReadDirectory{
		Path:      ".",
		Recursive: true,
		MaxDepth:  1,
	})
	if err != nil {
		run.GetLogger().Errorf("failed to list root directory: %s", err)
	}

	messages := agents.ComposeMessage(run, agents.ComposerOptions{
		Templates: templates,
		Tools:     a.ReAct.Tools,
		SystemPromptVariables: map[string]any{
			"WorkspaceRepositories": lo.MapValues(ws.Repositories, func(repo *workspace.Repository, _ string) WorkspaceRepo {
				return WorkspaceRepo{
					Directory: repo.Directory,
					RepoName:  repo.Meta.Name,
					Platform:  string(repo.Meta.Platform),
				}
			}),
			"Workspace":     ws.Editor.WorkingDirectory,
			"RootStructure": root,
		},
		UserPromptVariables: map[string]any{
			"Content": store.CurrentTask,
		},
		Trace: previousSteps,
	})
	return messages
}

func (a *Agent) GetAction(run *iris.AgentRunContext, action string, parameters map[string]any) iris.Action {
	if action != string(workspace.ActionExecuteCommand) {
		tool, _ := lo.Find(a.ReAct.Tools, func(tool iris.Action) bool {
			return tool.Name() == action
		})
		return tool
	}

	return workspace.NewExecuteCommandActionWithDefaultEnv(run, workspace.NewExecuteCommandActionOption{
		ScrollbackLines: 300,
		EnabledCommand:  enabledCommand,
		Variant:         a.Variant,
	})
}

func (a *Agent) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	run.GetLogger().Infof("run with input: %s", input)
	if options.ReuseTrace {
		mem := memory.GetAgentMemory(run)
		previousSteps := lo.Filter(mem.ActionMemory, func(step *memory.ActionMemoryItem, _ int) bool {
			return step.StepID == a.ReAct.Name()
		})
		lastStep, exist := lo.Last(previousSteps)
		if exist {
			lastStep.Outputs = map[string]any{"new_user_requirements": "The previous task is finished. Here is the new requirements of user:\n" + input}
		}
	} else {
		iris.UpdateStoreByKey(run, a.ReAct.UID(), CodebaseAgentStore{
			CurrentTask: input,
		})
	}
	err := a.ReAct.RunWithOption(run, &agents.ReActAgentRunOption{
		Step: a.ExecutionStep.Step,
	})
	if err != nil {
		result = controltool.ConclusionOutput{
			Content:    err.Error(),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}
	}

	lastStep := run.State.LastStep()
	mapstructure.Decode(lastStep.Conclusion, &result)
	return result
}
