package genexp

import (
	_ "embed"
	"errors"
	"fmt"
	"strings"
	"text/template"

	action_prompts "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"

	"github.com/sashabaranov/go-openai"
)

var (
	//go:embed prompts/gen_tmpl_exp_sop_system.go.tmpl
	promptGenTmplExpSOPSystem string
	//go:embed prompts/gen_tmpl_exp_sop_user_instruct.go.tmpl
	promptGenTmplExpSOPUserInstruct string
	//go:embed prompts/gen_tmpl_exp_sop_plan.go.tmpl
	promptGenTmplExpTrajPlan string
	//go:embed prompts/gen_tmpl_exp_sop_exec.go.tmpl
	promptGenTmplExpTrajExec string
	//go:embed prompts/gen_tmpl_exp_sop_user.go.tmpl
	promptGenTmplExpSOPUser string

	//go:embed prompts/sum_actor_traj_system.go.tmpl
	promptSumActorTrajSystem string
	//go:embed prompts/sum_actor_traj_user.go.tmpl
	promptSumActorTrajUser string
	//go:embed prompts/sum_actor_traj_think.go.tmpl
	promptSumActorTrajThinkThink string

	//go:embed prompts/gen_tmpl_exp_plan_system.go.tmpl
	promptGenTmplExpPlanSystem string
	//go:embed prompts/gen_tmpl_exp_plan_user.go.tmpl
	promptGenTmplExpPlanUser string

	//go:embed prompts/gen_tmpl_user_query_system.go.tmpl
	promptGenTmplUserQuerySystem string
	//go:embed prompts/gen_tmpl_user_query_user.go.tmpl
	promptGenTmplUserQueryUser string

	//go:embed prompts/gen_lark_tmpl_sop_system.go.tmpl
	promptGenLarkTemplateSOP string
	//go:embed prompts/gen_lark_tmpl_sop_user.go.tmpl
	promptGenLarkTemplateSOPUser string

	//go:embed prompts/eval_tmpl_exp_sop_system.go.tmpl
	promptEvalTmplExpSOPSystem string
	//go:embed prompts/eval_tmpl_exp_sop_user.go.tmpl
	promptEvalExpSOPUser string

	//go:embed prompts/logical_steps_description.go.tmpl
	promptLogicalStepsDescription string
)

func formatParam(v any) string {
	m, ok := v.(map[string]any)
	if !ok {
		return conv.JSONFormatString(v)
	}
	sb := strings.Builder{}
	for k, v := range m {
		sb.WriteString(fmt.Sprintf("```param=\"%s\"\n%v\n```\n\n", k, v))
	}
	return sb.String()
}

func mustGetPromptTemplate(s string) *template.Template {
	tmpl, err := template.New("").
		Funcs(template.FuncMap{
			"json":        conv.JSONFormatString,
			"formatParam": formatParam,
			"add":         func(a, b int) int { return a + b },
			"observeTool": func(tool string, errMsg string, input, output map[string]any) string {
				var err error
				if len(errMsg) > 0 {
					err = errors.New(errMsg)
				}
				obs := action_prompts.ToolObservation(nil, "", tool, input, output, err)
				if len(obs) == 0 {
					obs = "Tool invoked with no result returned."
				}
				return obs
			},
		}).Parse(s)
	if err != nil {
		panic(err)
	}
	return tmpl
}

func composeActorTrajectorySummarizingPrompt(item TaskTrajectoryNode) []*framework.ChatMessage {
	if item.Round == nil || item.Round.Execution.Detail == nil {
		return nil
	}
	opts := []prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(mustGetPromptTemplate(promptSumActorTrajSystem), map[string]any{}),
		prompt.WithUserMessage(mustGetPromptTemplate(promptSumActorTrajUser), map[string]any{
			"Task":           item.Round.Execution.Detail.Task,
			"ExecutionTrace": item.Round.Execution.Detail.ExecutionTrace,
			"Context":        item.Round.Execution.Detail.Context,
		}),
	}
	for _, item := range item.Round.Execution.Detail.Rounds {
		if len(item.Think.Rationale) == 0 {
			continue
		}
		opts = append(opts,
			prompt.WithAssistantMessage(mustGetPromptTemplate(promptSumActorTrajThinkThink), map[string]any{
				"Think":  item.Think,
				"Action": item.Action,
			}),
			func() ([]*framework.ChatMessage, error) {
				var err error
				if len(item.Action.Error) != 0 {
					err = errors.New(item.Action.Error)
				}
				obs := action_prompts.ToolObservation(nil, "", item.Action.Tool, item.Action.Parameters, item.Action.Results, err)
				if len(obs) == 0 {
					obs = "Tool invoked with no result returned."
				}
				return []*framework.ChatMessage{
					{
						Role:    openai.ChatMessageRoleUser,
						Content: obs,
					},
				}, nil
			},
		)
	}
	opts = append(opts,
		prompt.WithAssistantMessage(mustGetPromptTemplate("Agent has ended the task."), map[string]any{}),
		prompt.WithUserMessage(mustGetPromptTemplate("The above is the agent's execution history, please summarize it according to the output format."), map[string]any{}),
	)
	msgs, err := prompt.ComposeVaryMessages(opts)
	if err != nil {
		return nil
	}
	return msgs
}

func composeSOPGenPrompt(tr []TaskTrajectoryNode, userQueryTemplate *entity.ExpUserQueryTemplate, wsSum string) []*framework.ChatMessage {
	opts := []prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(mustGetPromptTemplate(promptGenTmplExpSOPSystem), map[string]any{
			"UserQueryTemplate":         userQueryTemplate,
			"WorkspaceStructureSummary": wsSum,
		}),
	}
	for _, item := range tr {
		switch {
		case item.UserMessage != nil:
			opts = append(opts, prompt.WithUserMessage(mustGetPromptTemplate(promptGenTmplExpSOPUserInstruct), map[string]any{
				"Message": item.UserMessage,
			}))
		case item.Round != nil:
			opts = append(opts, prompt.WithAssistantMessage(mustGetPromptTemplate(promptGenTmplExpTrajPlan), map[string]any{
				"Plan": item.Round.Plan,
			}))
			opts = append(opts, prompt.WithUserMessage(mustGetPromptTemplate(promptGenTmplExpTrajExec), map[string]any{
				"Execution": item.Round.Execution,
			}))
		}
	}
	opts = append(opts,
		prompt.WithAssistantMessage(mustGetPromptTemplate("Agent has ended the task."), map[string]any{}),
		prompt.WithUserMessage(mustGetPromptTemplate(promptGenTmplExpSOPUser), map[string]any{}),
	)
	msgs, err := prompt.ComposeVaryMessages(opts)
	if err != nil {
		return nil
	}
	return msgs
}

func composeSOPEvalPrompt(sop *entity.ExpSOP) []*framework.ChatMessage {
	opts := []prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(
			mustGetPromptTemplate(promptEvalTmplExpSOPSystem),
			map[string]any{},
		),
		prompt.WithUserMessage(
			mustGetPromptTemplate(promptEvalExpSOPUser),
			map[string]any{
				"SOP": sop.ToXMLString(),
			},
		),
	}
	msgs, err := prompt.ComposeVaryMessages(opts)
	if err != nil {
		return nil
	}
	return msgs
}
