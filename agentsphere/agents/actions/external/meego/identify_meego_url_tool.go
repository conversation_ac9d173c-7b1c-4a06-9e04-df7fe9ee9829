package meego

import (
	"context"
	"fmt"
	"net/url"
	"regexp"
	"strings"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/meegosdk"
)

type IdentifyMeeGoURLTypeArgs struct {
	URL string `json:"url" mapstructure:"url" description:"MeeGo URL，参数示例：\"https://meego.larkoffice.com/bitsdevops/sprint/detail/6092166577?parentUrl=%2Fbitsdevops%2Fsprint%2Fhomepage&openScene=2\""`
}

const (
	ToolIdentifyMeeGoURLType            = "identify_meego_url_type"
	ToolIdentifyMeeGoURLTypeDescription = "该接口用于识别MeeGo的URL类型。根据不同的URL类型，返回不同的信息：1.如果是视图链接，返回视图类型、视图ID、筛选器ID和视图模式；2.如果是图表链接，返回图表ID及其对应的视图信息；3.如果是工作项详情链接，返回工作项类型和ID；4.如果只能提取出simpleName，则返回项目详情信息，注意：该工具禁止重复调用"
)

// URLInfo 包含对URL解析后的信息
type URLInfo struct {
	Type            string `json:"type,omitempty"`               // URL类型: "视图", "工作项", "空间"
	SimpleName      string `json:"simple_name,omitempty"`        // 空间简称
	ViewType        string `json:"view_type,omitempty"`          // 视图类型: "multiProjectView", "storyView", "issueView", "workObjectView", "chart"
	ViewID          string `json:"view_id,omitempty"`            // 视图ID
	ChartID         string `json:"chart_id,omitempty"`           // 图表ID (当ViewType为chart时)
	FilterID        string `json:"filter_id,omitempty"`          // 快速筛选器ID
	ViewMode        string `json:"view_mode,omitempty"`          // 视图模式
	WorkItemType    string `json:"work_item_type,omitempty"`     // 工作项类型
	WorkItemAPIName string `json:"work_item_api_name,omitempty"` // 工作项API名称
	WorkItemID      string `json:"work_item_id,omitempty"`       // 工作项ID
	Description     string `json:"description,omitempty"`        // 描述
	IsSupported     bool   `json:"is_supported,omitempty"`       // 工作项类型是否支持
	NextStep        string `json:"next_step,omitempty"`          // 下一步操作提示
}

func NewIdentifyMeeGoURLType() iris.Action {
	return actions.ToTool(ToolIdentifyMeeGoURLType, ToolIdentifyMeeGoURLTypeDescription, IdentifyMeeGoURLType)
}

func IdentifyMeeGoURLType(run *iris.AgentRunContext, args IdentifyMeeGoURLTypeArgs) (*URLInfo, error) {
	ctx := context.Background()

	// 1. 检查是否是视图链接
	viewInfo, isView := tryMatchViewURL(run, args.URL)
	if isView {
		if viewInfo.ChartID != "" {
			run.GetLogger().Infof("识别到图表链接: %s, 图表ID: %s, 原视图类型: %s, 视图ID: %s, 筛选器ID: %s, 视图模式: %s",
				args.URL, viewInfo.ChartID, viewInfo.ViewType, viewInfo.ViewID, viewInfo.FilterID, viewInfo.ViewMode)
		} else {
			run.GetLogger().Infof("识别到视图链接: %s, 视图类型: %s, 视图ID: %s, 筛选器ID: %s, 视图模式: %s",
				args.URL, viewInfo.ViewType, viewInfo.ViewID, viewInfo.FilterID, viewInfo.ViewMode)
		}
		// 检查 larkoffice.com 域名的 cookie 是否存在且未过期
		_, isValid := isLarkOfficeCookieValid(run, viewInfo.ViewID)
		if !isValid {
			viewInfo.NextStep = "使用浏览器登录meego，重新授权"
			viewInfo.Description = "获取失败，larkoffice.com域名的cookie已过期或不存在，请使用浏览器访问 https://meego.larkoffice.com 重新登录授权后再重试本工具"
		}
		return viewInfo, nil
	}

	// 2. 检查是否是工作项详情链接
	workItemInfo, isWorkItem := tryMatchWorkItemURL(args.URL)
	if isWorkItem {
		run.GetLogger().Infof("识别到工作项详情链接: %s, 工作项类型: %s, 工作项ID: %s",
			args.URL, workItemInfo.WorkItemAPIName, workItemInfo.WorkItemID)

		// 获取当前用户信息
		user, err := getCurrentUserInfo(run, GetUserInfoArgs{})
		if err != nil {
			run.GetLogger().Errorf("获取当前用户信息失败: %v", err)
			return workItemInfo, nil // 继续返回工作项信息，但不检查支持状态
		}

		// 创建MeeGo客户端
		client, err := meegosdk.New()
		if err != nil {
			run.GetLogger().Errorf("创建MeeGo客户端失败: %v", err)
			return workItemInfo, nil // 继续返回工作项信息，但不检查支持状态
		}

		// 获取项目所有工作项类型
		workItemTypes, err := client.ListWorkItemAllTypes(ctx, user.UserKey, workItemInfo.SimpleName)
		if err != nil {
			run.GetLogger().Errorf("获取项目工作项类型失败: %v", err)
			return workItemInfo, nil // 继续返回工作项信息，但不检查支持状态
		}

		// 检查工作项类型是否支持
		for _, itemType := range workItemTypes {
			if itemType.APIName == workItemInfo.WorkItemAPIName && itemType.IsDisable != 1 {
				workItemInfo.IsSupported = true
				workItemInfo.WorkItemType = itemType.TypeKey
				break
			}
		}

		// 如果不支持，在描述中说明
		if !workItemInfo.IsSupported {
			workItemInfo.Description = fmt.Sprintf("这是一个Meego工作项详情链接: %s，但当前不支持该工作项类型: %s", args.URL, workItemInfo.WorkItemAPIName)
		}

		return workItemInfo, nil
	}

	// 3. 提取simpleName并获取项目详情
	simpleName, ok := extractSimpleName(args.URL)
	if !ok {
		run.GetLogger().Errorf("无法从URL提取simpleName: %s", args.URL)
		return nil, iris.NewRecoverable(errors.Errorf("无法从URL提取simpleName: %s", args.URL))
	}

	run.GetLogger().Infof("提取到simpleName: %s 来自URL: %s", simpleName, args.URL)

	// 封装结果
	return &URLInfo{
		Type:        "MeegoSpace",
		SimpleName:  simpleName,
		Description: fmt.Sprintf("这是一个Meego空间链接: %s", args.URL),
	}, nil
}

// tryMatchViewURL 检查URL是否是视图链接，如果是则解析出相关信息
func tryMatchViewURL(run *iris.AgentRunContext, urlStr string) (*URLInfo, bool) {
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return nil, false
	}

	path := parsedURL.Path
	params := parsedURL.Query()

	// 解析路径段
	pathSegments := strings.Split(strings.Trim(path, "/"), "/")
	if len(pathSegments) < 3 {
		return nil, false
	}

	project := pathSegments[0]
	viewType := pathSegments[1]

	info := &URLInfo{
		Type:       "View",
		SimpleName: project,
		ViewType:   viewType,
	}

	var chartID, filterID, viewID string
	originalViewType := viewType // 保存原始的视图类型

	// 处理chart类型
	if viewType == "chart" {
		if len(pathSegments) >= 3 {
			chartID = pathSegments[len(pathSegments)-1] // 获取最后一段作为chartId
		}

		// 从parentUrl参数中解析实际的视图类型和视图ID
		if parentURL := params.Get("parentUrl"); parentURL != "" {
			parentParsed, err := url.Parse(parentURL)
			if err == nil {
				parentPath := strings.Split(strings.Trim(parentParsed.Path, "/"), "/")
				if len(parentPath) >= 2 {
					viewType = parentPath[1] // 获取真实的viewType
					if len(parentPath) >= 3 {
						viewID = parentPath[2]
					}
				}
			}
		}

		info.ViewType = viewType
		info.ChartID = chartID
	} else {
		// 非chart类型的处理
		if viewType == "workObjectView" {
			if len(pathSegments) >= 4 {
				info.WorkItemType = pathSegments[2]
				viewID = pathSegments[3]
			}
		} else {
			if len(pathSegments) >= 3 {
				viewID = pathSegments[2]
			}
		}
	}

	info.ViewID = viewID
	info.FilterID = filterID

	// 设置描述
	if originalViewType == "chart" {
		info.Description = fmt.Sprintf("这是一个Meego图表链接: %s", urlStr)
	} else {
		info.Description = fmt.Sprintf("这是一个Meego视图链接: %s", urlStr)
	}

	// 验证是否是有效的视图URL
	validViewTypes := map[string]bool{
		"multiProjectView": true,
		"storyView":        true,
		"issueView":        true,
		"workObjectView":   true,
		"chart":            true,
	}

	if !validViewTypes[originalViewType] {
		return nil, false
	}

	return info, true
}

// tryMatchWorkItemURL 检查URL是否是工作项详情链接，如果是则解析出相关信息
func tryMatchWorkItemURL(url string) (*URLInfo, bool) {
	// 工作项详情URL模式
	regex := regexp.MustCompile(`https?://meego\.larkoffice\.com/([^/]+)/([^/]+)/detail/(\d+)`)
	matches := regex.FindStringSubmatch(url)

	if len(matches) >= 4 {
		return &URLInfo{
			Type:            "WorkItem",
			SimpleName:      matches[1],
			WorkItemAPIName: matches[2],
			WorkItemID:      matches[3],
			Description:     fmt.Sprintf("这是一个Meego工作项详情链接: %s", url),
		}, true
	}

	return nil, false
}

// extractSimpleName 从URL中提取项目简称
func extractSimpleName(url string) (string, bool) {
	pattern := regexp.MustCompile(`https?://meego\.larkoffice\.com/([^/]+)`)
	matches := pattern.FindStringSubmatch(url)

	if len(matches) >= 2 {
		return matches[1], true
	}

	return "", false
}
