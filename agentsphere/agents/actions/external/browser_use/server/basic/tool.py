from global_vars import llm_high, markdownify_user_message_format, network_data, extract_content_system_prompt
from langchain_core.prompts import PromptTemplate
import markdownify
import base64
import json
import os
import uuid
from datetime import datetime
from pathlib import Path
import asyncio

from langchain_core.messages import SystemMessage, HumanMessage
from urllib.parse import urlparse
from mcp import types
from global_vars import get_browser_session, screenshot_dirs, reviewer_prompt, vlm_reviewer
from log import logger
from util import extract_result, download


async def browser_hover(index: int, goal: str, expectation: str) -> list[
    types.TextContent | types.ImageContent | types.EmbeddedResource]:
    try:
        page = await get_browser_session().get_current_page()
        image_id = uuid.uuid4()
        image_path = os.path.join(f'{screenshot_dirs}/screenshot-before-{image_id}.png')
        await get_browser_session().remove_highlights()
        screenshot_before_action_bytes = await page.screenshot(
            full_page=False, type="png", path=image_path
        )
        screenshot_before_action_base64 = base64.b64encode(
            screenshot_before_action_bytes
        ).decode("utf-8")

        # do hover
        selector_map = await get_browser_session().get_selector_map()
        logger.info(f"browser_hover called with selector_map: {selector_map}")
        if index not in selector_map:
            raise Exception(f"Element with index {index} does not exist")
        logger.info(f"browser_hover called with index: {index}, goal: {goal}, expectation: {expectation}")
        element_node = await get_browser_session().get_dom_element_by_index(index)
        element_handler = await get_browser_session().get_locate_element(element_node)
        await element_handler.hover()

        image_path = os.path.join(f'{screenshot_dirs}/screenshot-after-{image_id}.png')
        await get_browser_session().remove_highlights()
        screenshot_after_action_bytes = await page.screenshot(
            full_page=False, type="png", path=image_path
        )
        screenshot_after_action_base64 = base64.b64encode(
            screenshot_after_action_bytes
        ).decode("utf-8")

        # review
        review_message_content = [
            {
                "type": "text",
                "text": "This is screenshot 1, the screenshot before the hover operation"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot_before_action_base64}"
                }
            },
            {
                "type": "text",
                "text": "This is screenshot 2, the screenshot after the hover operation"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot_after_action_base64}"
                }
            },
            {
                "type": "text",
                "text": f"The task is:\n {goal}\n\n The expectation is:\n{expectation}"
            }
        ]

        system_message = SystemMessage(
            content=reviewer_prompt
        )

        message = HumanMessage(
            content=review_message_content
        )
        start_review_time = datetime.now()
        resp = await vlm_reviewer.ainvoke([system_message, message])
        end_review_time = datetime.now()
        review_duration = (end_review_time - start_review_time).total_seconds()
        logger.info(f"review duration: {review_duration} seconds")
        logger.info(f"review result full: {resp}")
        review_result = extract_result(resp.content)
        logger.info(f"review result: {review_result}")

        if review_result != "success":
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": True,
                            "message": f"Hover the element with index: {index}.",
                            "result": f"The hover operation fails. review result: {resp.content}.",
                        },
                        indent=2
                    )
                )
            ]

        result_list = [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {
                        "success": True,
                        "message": f"Hover the element with index {index} successfully.",
                        "result": f"Hover the element with index {index} successfully.",
                    },
                    indent=2
                )
            )
        ]
        return result_list
    except Exception as e:
        logger.error(f"Error executing hover: {str(e)}")
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {"error": f"Failed to hover: {str(e)}"}, indent=2
                ),
            )
        ]

async def scroll(direction:str,amount:int, goal: str, expectation: str):
    try:
        page = await get_browser_session().get_current_page()
        image_id = uuid.uuid4()
        image_path = os.path.join(f'{screenshot_dirs}/screenshot-before-{image_id}.png')
        await get_browser_session().remove_highlights()
        screenshot_before_action_bytes = await page.screenshot(
            full_page=False, type="png", path=image_path
        )
        screenshot_before_action_base64 = base64.b64encode(
            screenshot_before_action_bytes
        ).decode("utf-8")
        if direction == "down":
            await page.evaluate(f'window.scrollBy(0, {amount});')
        elif direction == "up":
            await page.evaluate(f'window.scrollBy(0, -{amount});')

        image_path = os.path.join(f'{screenshot_dirs}/screenshot-after-{image_id}.png')
        await get_browser_session().remove_highlights()
        screenshot_after_action_bytes = await page.screenshot(
            full_page=False, type="png", path=image_path
        )
        screenshot_after_action_base64 = base64.b64encode(
            screenshot_after_action_bytes
        ).decode("utf-8")

        review_message_content = [
            {
                "type": "text",
                "text": "This is screenshot 1, the screenshot before the input operation"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot_before_action_base64}"
                }
            },
            {
                "type": "text",
                "text": "This is screenshot 2, the screenshot after the input operation"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot_after_action_base64}"
                }
            },
            {
                "type": "text",
                "text": f"The task is:\n {goal}\n\n The expectation is:\n{expectation}"
            }
        ]

        system_message = SystemMessage(
            content=reviewer_prompt
        )

        message = HumanMessage(
            content=review_message_content
        )
        start_review_time = datetime.now()
        resp = await vlm_reviewer.ainvoke([system_message, message])
        end_review_time = datetime.now()
        review_duration = (end_review_time - start_review_time).total_seconds()
        logger.info(f"review duration: {review_duration} seconds")
        logger.info(f"review result full: {resp}")
        review_result = extract_result(resp.content)
        logger.info(f"review result: {review_result}")

        if review_result != "success":
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": True,
                            "message": f"Scroll {direction} {amount} failed. Use browser_handover_to_vlm to scroll in next step",
                            "result": f"The scroll {direction} operation fails. review result: {resp.content}.",
                        },
                        indent=2
                    )
                )
            ]

        scroll_msg = f'🔍 Scrolled {direction} the page by {amount} pixels'

        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {
                        "success": True,
                        "message": f"Scrolled {direction} {scroll_msg}",
                        "result": f"Scrolled {direction} {scroll_msg}",
                    },
                    indent=2
                )
            )
        ]
    except Exception as e:
        logger.error(f"Error executing scroll {direction}: {str(e)}")
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {"error": f"Failed to scroll {direction}: {str(e)}"}, indent=2
                ),
            )
        ]


class AriaLabelConverter(markdownify.MarkdownConverter):
    def convert_tr(self, el, text, parent_tags):
        """处理<tr>标签：保留原有表格逻辑，同时添加aria-label"""
        aria_label = el.get("aria-label", "").strip()

        # 执行原有表格处理逻辑
        cells = el.find_all(['td', 'th'])
        is_first_row = el.find_previous_sibling() is None
        is_headrow = (
            all([cell.name == 'th' for cell in cells])
            or (el.parent.name == 'thead' and len(el.parent.find_all('tr')) == 1)
        )
        is_head_row_missing = (
            (is_first_row and not el.parent.name == 'tbody')
            or (is_first_row and el.parent.name == 'tbody' and len(el.parent.parent.find_all(['thead'])) < 1)
        )
        overline = ''
        underline = ''
        full_colspan = 0
        for cell in cells:
            if 'colspan' in cell.attrs and cell['colspan'].isdigit():
                full_colspan += int(cell["colspan"])
            else:
                full_colspan += 1
        if ((is_headrow or (is_head_row_missing and self.options['table_infer_header'])) and is_first_row):
            underline += '| ' + \
                ' | '.join(['---'] * full_colspan) + ' |' + '\n'
        elif ((is_head_row_missing and not self.options['table_infer_header'])
              or (is_first_row and (el.parent.name == 'table'
                                    or (el.parent.name == 'tbody' and not el.parent.find_previous_sibling())))):
            overline += '| ' + ' | '.join([''] * full_colspan) + ' |' + '\n'
            overline += '| ' + ' | '.join(['---'] * full_colspan) + ' |' + '\n'

        original_markdown = overline + '|' + text + '\n' + underline

        # 将aria-label作为前缀添加
        return f"{aria_label}\n{original_markdown}" if aria_label else original_markdown

    def convert_span(self, el, text, parent_tags):
        """处理<span>标签：在原有内容前添加aria-label（如果有）"""
        aria_label = el.get("aria-label", "").strip()
        if aria_label:
            return f"{aria_label} {text}"
        return text

    def convert_a(self, el, text, parent_tags):
        aria_label = el.get("aria-label", "").strip()

        if '_noformat' in parent_tags:
            return text
        prefix, suffix, text = markdownify.chomp(text)
        if not text:
            return ''
        href = el.get('href')
        title = el.get('title')
        if (self.options['autolinks']
                and text.replace(r'\_', '_') == href
                and not title
                and not self.options['default_title']):
            default_markdown = '<%s>' % href
        else:
            if self.options['default_title'] and not title:
                title = href
            title_part = ' "%s"' % title.replace('"', r'\"') if title else ''
            default_markdown = '%s[%s](%s%s)%s' % (
                prefix, text, href, title_part, suffix) if href else text

        if aria_label:
            return f"{aria_label} {default_markdown}"
        return default_markdown

def custom_markdownify(html):
    return AriaLabelConverter().convert(html)


async def goto_url(url: str):
    try:
        page = await get_browser_session().get_current_page()

        try:
            if page:
                await page.goto(url, wait_until="networkidle", timeout=30000)
            else:
                page = await get_browser_session().create_new_tab(url)

            pdf = page.locator('css=[type="application/pdf"]')

            if await pdf.count() != 0:

                await asyncio.sleep(3)
        except:
            pass

    except Exception as e:
        raise e

async def browser_markdownify_content(goal: str, language: str, file_name:str):

    page = await get_browser_session().get_current_page()

    pdf = page.locator('css=[type="application/pdf"]')

    if await pdf.count() != 0:
        # pdf viewer
        pdf_path = download(url=page.url)

        import shutil

        count = 0
        file_name = os.path.join(os.getenv("IRIS_WORKSPACE_PATH"), f"{count}_{file_name}.pdf")
        while os.path.exists(file_name):
            count += 1
            file_name = os.path.join(os.getenv("IRIS_WORKSPACE_PATH"), f"{count}_{file_name}.pdf")

        logger.info(f"move from {pdf_path} to {file_name}")
        shutil.move(pdf_path, file_name)

        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {
                        "success": True,
                        "message": f"Browser cannot parse pdf content and just download the pdf.",
                        "result": f"Pdf download at {file_name}.",
                        "pdf": True,
                        "file_name": file_name,
                    },
                    indent=2
                )
            )
        ]
    logger.info(f"page title {await page.title()}")
    content = custom_markdownify(await page.content())

    for iframe in page.frames:
        if iframe.url != page.url and not iframe.url.startswith('data:'):
            content += f'\n\nIFRAME {iframe.url}:\n'
            content += custom_markdownify(await iframe.content())
    simple_content = content

    if network_data.get(page.url) is not None:
        content += f"PAGE NETWORK DATA: {network_data.get(page.url)}"

    template = PromptTemplate(input_variables=['url','language', 'output_format', 'goal', 'page'], template=markdownify_user_message_format)

    system_message = SystemMessage(content=extract_content_system_prompt)

    output_format = """
<content>
{Extracted content from website page}
</content>
<reference>
- [description_1](website_url_1)
    Authors: <AUTHORS>
    Date: [submission_date_1]  
- [description_2](website_url_2)
    Authors: <AUTHORS>
    Date: [submission_date_2]  
...
</reference>
"""

    try:
        user_prompt = template.format(
            url=page.url, language=language, output_format=output_format, goal=goal, page=content)
        user_message = HumanMessage(
            content=[
                {
                    "type": "text",
                    "text": user_prompt,
                },
            ]
        )

        output = llm_high.invoke([system_message, user_message])
        msg = f'{output.content}\n'
        logger.info(msg)
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {
                        "success": True,
                        "message": f"Extracted content with goal: {goal}",
                        "result": f"{msg}",
                        "file_name": file_name,
                    },
                    indent=2
                )
            )
        ]
    except Exception as e1:
        logger.info(f'Error extracting content: {e1}')
        if "exceeds the maximum number of tokens" in str(e1):
            logger.info("try invoke llm with simple content")
            try:
                user_prompt = template.format(
                    url=page.url, language=language, output_format=output_format, goal=goal, page=simple_content)
                user_message = HumanMessage(
                    content=[
                        {
                            "type": "text",
                            "text": user_prompt,
                        },
                    ]
                )
                output = llm_high.invoke(
                    [system_message, user_message])
                msg = f'{output.content}\n'
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps(
                            {
                                "success": True,
                                "message": f"Extracted content with goal: {goal}.",
                                "result": f"{msg}",
                            },
                            indent=2
                        )
                    )
                ]
            except Exception as e2:
                raise e2
        else:
            raise e1