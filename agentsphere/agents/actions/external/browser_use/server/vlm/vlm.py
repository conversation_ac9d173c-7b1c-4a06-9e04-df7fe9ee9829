import asyncio
import base64
import json
import os
import platform
import re
import uuid
import urllib.parse
from typing import Tuple

from PIL import Image
from langchain_core.messages import SystemMessage, HumanMessage

from global_vars import get_browser_session, screenshot_dirs, vlm, gif_dir, step_reviewer_prompt, vlm_reviewer
from knowledge.knowledge import fetch_knowledge
from log import logger
from util import *
from vlm.planner import VlmPlanner
from vlm.prompt import vlm_with_planner_prompt, vlm_prompt
from vlm.screenshot import ScreenshotManager

system = platform.system()

if system == "Darwin":
    hotkey_map = {
        "ctrl a": "Meta+a",
        "ctrl c": "Meta+c",
        "ctrl v": "Meta+v",
        "delete": "Delete",
        "esc": "Escape",
        "backspace": "Backspace",
        "enter": "Enter",
        "space": "Space",
        "tab": "Tab",
    }
elif system == "Linux":
    hotkey_map = {
        "ctrl a": "Control+a",
        "ctrl c": "Control+c",
        "ctrl v": "Control+v",
        "delete": "Delete",
        "esc": "Escape",
        "backspace": "Backspace",
        "enter": "Enter",
        "space": "Space",
        "tab": "Tab",
    }
else:
    hotkey_map = {}


def parse_action_output(output_text):
    # 提取Thought部分
    thought_match = re.search(r'Thought:(.*?)\nAction:', output_text, re.DOTALL)
    thought = thought_match.group(1).strip() if thought_match else ""

    # 提取Action部分
    action_match = re.search(r'Action:(.*?)(?:\n|$)', output_text, re.DOTALL)
    action_text = action_match.group(1).strip() if action_match else ""

    # 初始化结果字典
    result = {
        "thought": thought,
        "action": "",
        "key": None,
        "content": None,
        "start_box": None,
        "end_box": None,
        "direction": None
    }

    if not action_text:
        return json.dumps(result, ensure_ascii=False)

    # 解析action类型
    action_parts = action_text.split('(')
    action_type = action_parts[0]
    result["action"] = action_type

    # 解析参数
    if len(action_parts) > 1:
        params_text = action_parts[1].rstrip(')')

        # 处理键值对参数
        for param in params_text.split(','):
            param = param.strip()
            if '=' in param:
                key, value = param.split('=', 1)
                key = key.strip()
                value = value.strip().strip('\'"')

                # 处理bbox格式
                if 'box' in key:
                    # 提取坐标数字
                    numbers = re.findall(r'\d+', value)
                    if numbers:
                        coords = [int(num) for num in numbers]
                        if len(coords) == 4:
                            if key == 'start_box':
                                result["start_box"] = coords
                            elif key == 'end_box':
                                result["end_box"] = coords
                elif key == 'key':
                    result["key"] = value
                elif key == 'content':
                    # 处理转义字符
                    value = value.replace('\\n', '\n').replace('\\"', '"').replace("\\'", "'")
                    result["content"] = value
                elif key == 'direction':
                    result["direction"] = value

    return json.dumps(result, ensure_ascii=False, indent=2)


def coordinates_convert(relative_bbox, img_size):
    """
       将相对坐标[0,1000]转换为图片上的绝对像素坐标

       参数:
           relative_bbox: 相对坐标列表/元组 [x1, y1, x2, y2] (范围0-1000)
           img_size: 图片尺寸元组 (width, height)

       返回:
           绝对坐标列表 [x1, y1, x2, y2] (单位:像素)

       示例:
           >>> coordinates_convert([500, 500, 600, 600], (1000, 2000))
           [500, 1000, 600, 1200]  # 对于2000高度的图片，y坐标×2
       """
    # 参数校验
    if len(relative_bbox) != 4 or len(img_size) != 2:
        raise ValueError("输入参数格式应为: relative_bbox=[x1,y1,x2,y2], img_size=(width,height)")

    # 解包图片尺寸
    img_width, img_height = img_size

    # 计算绝对坐标
    abs_x1 = int(relative_bbox[0] * img_width / 1000)
    abs_y1 = int(relative_bbox[1] * img_height / 1000)
    abs_x2 = int(relative_bbox[2] * img_width / 1000)
    abs_y2 = int(relative_bbox[3] * img_height / 1000)

    return [abs_x1, abs_y1, abs_x2, abs_y2]


async def vlm_execute(goal: str, expectation: str) -> Tuple[bool, str, str]:
    planner = VlmPlanner(goal, expectation)
    await planner.construct_message_prefix()
    while True:
        task = await planner.plan()
        logger.info(f"vlm task: {task.id}, goal: {task.goal}, status: {task.status}")
        if task.id == "finished":
            return True, "Finished all tasks", planner.gif_path
        elif task.id == "exceed":
            return False, "Exceeded maximum step count", planner.gif_path
        task.status = "running"
        page = await get_browser_session().get_current_page()
        await get_browser_session().remove_highlights()
        date = await page.evaluate("new Date().toLocaleString()")
        history = [
            SystemMessage(content=vlm_with_planner_prompt),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": f"Current date is: {date}\n"
                },
                {
                    "type": "text",
                    "text": f"The planner already gives what to do for this task.\n"
                            f"Here is the output of planner:\n{task.goal}\n"
                            f"Here is the initial screenshot:\n"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{task.screenshot_str}",
                    }
                },
                {
                    "type": "text",
                    "text": f"### Step History Begin ###\n"
                }
            ])
        ]

        step_count = 0
        max_step_count = 10
        while step_count < max_step_count:
            logger.info(f"inner step {step_count}/{max_step_count}")
            step_count += 1
            current_host = urllib.parse.urlparse(page.url).hostname
            image_path = os.path.join(screenshot_dirs, f"screenshots-step-{step_count}-before-{task.id}.png")
            step_screenshot = await page.screenshot(full_page=False, type="png", path=image_path)
            step_screenshot_str = base64.b64encode(step_screenshot).decode("utf-8")
            step_message = [
                HumanMessage(
                    content=[
                        {
                            "type": "text",
                            "text": f"### Step History End ###\n"
                        },
                        {
                            "type": "text",
                            "text": f"Here is the screenshot before step {step_count}.\n"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{step_screenshot_str}"
                            }
                        }
                    ]
                )
            ]
            temp_message = history + step_message
            resp = await vlm.ainvoke(temp_message)
            logger.info(f"vlm result: {resp.content}")
            action_output_str = parse_action_output(resp.content)
            logger.info(action_output_str)
            action_output = json.loads(action_output_str)
            action = action_output.get("action")
            action_success = True
            action_failed_reason = ""
            thought = action_output.get("thought", "")
            manager = planner.screenshot_manager

            if action == "click":
                if is_limited_host(current_host, page.url):
                    return False, "You must tell the user that Aime, as a llm agent, is prohibited from clicking on the page to prevent uncontrollable damage to the bytedance intranet", ""
                start_box = action_output.get("start_box", None)
                logger.info(f"start_box: {start_box}")
                with Image.open(image_path) as image:
                    start_box = coordinates_convert(start_box, image.size)
                    logger.info(f"start_box after convert: {start_box}")
                    device_pixel_ratio = await page.evaluate("window.devicePixelRatio")
                    x = start_box[0] / device_pixel_ratio
                    y = start_box[1] / device_pixel_ratio
                    await add_visual_marker(page, x, y)
                    manager.add_annotations(step_screenshot, annotations={
                        "markers": [{
                            "position": (start_box[0], start_box[1]),
                            "color": "#FF0000",
                            "size": 15
                        }],
                        "subtitle": {
                            "text": f"{planner.step_count}.{step_count}. {thought}",
                            "color": "#FFFFFF",
                            "font_size": 40
                        }
                    })
                    await page.mouse.click(x, y)
                    await page.wait_for_load_state()
                    await asyncio.sleep(0.5)
            elif action == "type":
                if is_limited_host(current_host, page.url):
                    return False, "You must tell the user that Aime, as a llm agent, is prohibited from typing on the page to prevent uncontrollable damage to the bytedance intranet", ""
                content = action_output.get("content", "")
                logger.info(f"type {content}")
                manager.add_annotations(step_screenshot, annotations={
                    "subtitle": {
                        "text": f"{planner.step_count}.{step_count}. {thought}",
                        "color": "#FFFFFF",
                        "font_size": 40
                    }
                })
                await page.keyboard.type(content)
                await page.wait_for_load_state()
                await asyncio.sleep(0.5)

            elif action == "wait":
                manager.add_annotations(step_screenshot, annotations={
                    "subtitle": {
                        "text": f"{planner.step_count}.{step_count}. {thought}",
                        "color": "#FFFFFF",
                        "font_size": 40
                    }
                })
                await asyncio.sleep(5)
            elif action == "scroll":
                start_box = action_output.get("start_box", None)
                logger.info(f"start_box: {start_box}")
                with Image.open(image_path) as image:
                    start_box = coordinates_convert(start_box, image.size)
                    logger.info(f"start_box after convert: {start_box}")
                    device_pixel_ratio = await page.evaluate("window.devicePixelRatio")
                    x = start_box[0] / device_pixel_ratio
                    y = start_box[1] / device_pixel_ratio
                    await add_visual_marker(page, x, y)
                    manager.add_annotations(step_screenshot, annotations={
                        "markers": [{
                            "position": (start_box[0], start_box[1]),
                            "color": "#FF0000",
                            "size": 15
                        }],
                        "subtitle": {
                            "text": f"{planner.step_count}.{step_count}. {thought}",
                            "color": "#FFFFFF",
                            "font_size": 40
                        }
                    })
                    await page.mouse.move(x, y)
                    direction = action_output.get("direction", "down")
                    if direction == "up":
                        await page.mouse.wheel(0, -800)
                    elif direction == "down":
                        await page.mouse.wheel(0, 800)
                    elif direction == "left":
                        await page.mouse.wheel(-100, 0)
                    elif direction == "right":
                        await page.mouse.wheel(100, 0)
                    else:
                        action_success = False
                        action_failed_reason = "vlm unknown scroll direction"
                    await page.wait_for_load_state()
                    await asyncio.sleep(0.5)
            elif action == "drag":
                start_box = action_output.get("start_box", None)
                end_box = action_output.get("end_box", None)
                logger.info(f"start_box: {start_box}, end_box: {end_box}")
                with Image.open(image_path) as image:
                    start_box = coordinates_convert(start_box, image.size)
                    end_box = coordinates_convert(end_box, image.size)
                    logger.info(f"start_box after convert: {start_box}, end_box after convert: {end_box}")
                    device_pixel_ratio = await page.evaluate("window.devicePixelRatio")
                    x1 = start_box[0] / device_pixel_ratio
                    y1 = start_box[1] / device_pixel_ratio
                    x2 = end_box[0] / device_pixel_ratio
                    y2 = end_box[1] / device_pixel_ratio
                    await add_visual_marker(page, x1, y1)
                    await add_visual_marker(page, x2, y2)
                    manager.add_annotations(step_screenshot, annotations={
                        "markers": [{
                            "position": (start_box[0], start_box[1]),
                            "color": "#FF0000",
                            "size": 15
                        }, {
                            "position": (end_box[0], end_box[1]),
                            "color": "#FF0000",
                            "size": 15
                        }],
                        "subtitle": {
                            "text": f"{planner.step_count}.{step_count}. {thought}",
                            "color": "#FFFFFF",
                            "font_size": 40
                        }
                    })
                    await page.mouse.move(start_box[0] / device_pixel_ratio, start_box[1] / device_pixel_ratio)
                    await asyncio.sleep(0.5)
                    await page.mouse.down()
                    await asyncio.sleep(0.5)
                    await page.mouse.move(end_box[0] / device_pixel_ratio, end_box[1] / device_pixel_ratio)
                    await asyncio.sleep(0.5)
                    await page.mouse.up()
                    await asyncio.sleep(0.5)
                    await page.wait_for_load_state()
            elif action == "hotkey":
                if is_limited_host(current_host, page.url):
                    return False, "You are Forbidden to type in this page. Use browser_markdownify_content to extract this page", ""
                key = action_output.get("key", None)
                mapped_key = hotkey_map.get(key, None)
                manager.add_annotations(step_screenshot, annotations={
                    "subtitle": {
                        "text": f"{planner.step_count}.{step_count}. {thought}",
                        "color": "#FFFFFF",
                        "font_size": 40
                    }
                })
                if mapped_key:
                    logger.info(f"press hotkey {mapped_key}")
                    await page.keyboard.press(mapped_key)
                    await page.wait_for_load_state()
                    await asyncio.sleep(0.5)
                else:
                    action_success = False
                    action_failed_reason = f"vlm unknown hotkey {key}"

            elif action == "finished":
                # logger.info(f"finished with resp: {resp.content}")
                manager.add_annotations(step_screenshot, annotations={
                    "subtitle": {
                        "text": f"{planner.step_count}.{step_count}. {thought}",
                        "color": "#FFFFFF",
                        "font_size": 40
                    }
                })
                task.status = "finished"
                break

                # manager.save_gif(gif_path)
                # manager.cleanup()
                # return True, f"vlm finished with resp: {resp.content}", gif_path
            else:
                logger.error(f"Unknown action: {action}")
                action_success = False
                action_failed_reason = f"vlm unknown action {action}"
                manager.add_annotations(step_screenshot, annotations={
                    "subtitle": {
                        "text": f"{planner.step_count}.{step_count}. {thought}",
                        "color": "#FFFFFF",
                        "font_size": 40
                    }
                })
            history.append(
                HumanMessage(
                    content=[
                        {
                            "type": "text",
                            "text": f"#### Step {step_count} Action ####\n{resp.content}\n"
                        }
                    ]
                )
            )
            if not action_success:
                history.append(
                    HumanMessage(
                        content=[
                            {
                                "type": "text",
                                "text": f"This action failed because {action_failed_reason}\n"
                            }
                        ]
                    )
                )
        if step_count == max_step_count:
            logger.warning(f"Exceeded maximum step count: {max_step_count}.")
            task.status = "failed, reason: Exceeded maximum step count"


async def vlm_execute_fast(goal: str, expectation: str) -> Tuple[bool, str, str]:
    step_count = 0
    max_step_count = 50
    history = [SystemMessage(content=vlm_prompt)]
    page = await get_browser_session().get_current_page()
    await get_browser_session().remove_highlights()
    first_screenshot = await page.screenshot(full_page=False, type="png")
    first_screenshot_str = base64.b64encode(first_screenshot).decode("utf-8")
    date = await page.evaluate("new Date().toLocaleString()")
    content_list = [
        {
            "type": "text",
            "text": f"Current date is: {date}\n"
        },
        {
            "type": "text",
            "text": f"The goal is: {goal}, the expectation is: {expectation}.\nHere is the initial screenshot.\n"
        },
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{first_screenshot_str}"
            }
        },
    ]
    knowledge = await fetch_knowledge(page.url)
    if knowledge:
        content_list.append(
            {
                "type": "text",
                "text": f"Here is some knowledge on this task:\n{knowledge}\n"
            }
        )
    content_list.append(
        {
            "type": "text",
            "text": f"### Step History Begin ###\n"
        }
    )
    history.append(
        HumanMessage(
            content=content_list
        )
    )
    task_id = str(uuid.uuid4())
    manager = ScreenshotManager()
    gif_id = uuid.uuid4()
    gif_path = os.path.join(f'{gif_dir}/gif-{gif_id}.gif')
    while step_count < max_step_count:
        logger.info(f"step {step_count}/{max_step_count}")
        step_count += 1
        current_host = urllib.parse.urlparse(page.url).hostname
        image_path = os.path.join(screenshot_dirs, f"screenshots-step-{step_count}-before-{task_id}.png")
        step_screenshot = await page.screenshot(full_page=False, type="png", path=image_path)
        step_screenshot_str = base64.b64encode(step_screenshot).decode("utf-8")

        step_message = [
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": f"### Step History End ###\n"
                    },
                    {
                        "type": "text",
                        "text": f"Here is the screenshot before step {step_count}.\n"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{step_screenshot_str}"
                        }
                    }
                ]
            )
        ]
        temp_message = history + step_message
        resp = await vlm.ainvoke(temp_message)
        logger.info(f"uitars result: \n{resp.content}")
        action_output_str = parse_action_output(resp.content)
        logger.info(action_output_str)
        action_output = json.loads(action_output_str)
        action = action_output.get("action")
        action_success = True
        action_failed_reason = ""
        thought = action_output.get("thought", "")

        if action == "click":
            if is_limited_host(current_host, page.url):
                return False, "You are Forbidden to click in this page. Use browser_markdownify_content to extract this page", ""
            start_box = action_output.get("start_box", None)
            logger.info(f"start_box: {start_box}")
            with Image.open(image_path) as image:
                start_box = coordinates_convert(start_box, image.size)
                logger.info(f"start_box after convert: {start_box}")
                device_pixel_ratio = await page.evaluate("window.devicePixelRatio")
                x = start_box[0] / device_pixel_ratio
                y = start_box[1] / device_pixel_ratio
                await add_visual_marker(page, x, y)
                manager.add_annotations(step_screenshot, annotations={
                    "markers": [{
                        "position": (start_box[0], start_box[1]),
                        "color": "#FF0000",
                        "size": 15
                    }],
                    "subtitle": {
                        "text": f"{step_count}. {thought}",
                        "color": "#FFFFFF",
                        "font_size": 40
                    }
                })
                await page.mouse.click(x, y)
                await page.wait_for_load_state()
                await asyncio.sleep(0.5)
        elif action == "type":
            if is_limited_host(current_host, page.url):
                return False, "You are Forbidden to type in this page. Use browser_markdownify_content to extract this page", ""
            content = action_output.get("content", "")
            logger.info(f"type {content}")
            manager.add_annotations(step_screenshot, annotations={
                "subtitle": {
                    "text": f"{step_count}. {thought}",
                    "color": "#FFFFFF",
                    "font_size": 40
                }
            })
            await page.keyboard.type(content)
            await page.wait_for_load_state()
            await asyncio.sleep(0.5)

        elif action == "wait":
            manager.add_annotations(step_screenshot, annotations={
                "subtitle": {
                    "text": f"{step_count}. {thought}",
                    "color": "#FFFFFF",
                    "font_size": 40
                }
            })
            await asyncio.sleep(5)
        elif action == "scroll":
            start_box = action_output.get("start_box", None)
            logger.info(f"start_box: {start_box}")
            with Image.open(image_path) as image:
                start_box = coordinates_convert(start_box, image.size)
                logger.info(f"start_box after convert: {start_box}")
                device_pixel_ratio = await page.evaluate("window.devicePixelRatio")
                x = start_box[0] / device_pixel_ratio
                y = start_box[1] / device_pixel_ratio
                await add_visual_marker(page, x, y)
                manager.add_annotations(step_screenshot, annotations={
                    "markers": [{
                        "position": (start_box[0], start_box[1]),
                        "color": "#FF0000",
                        "size": 15
                    }],
                    "subtitle": {
                        "text": f"{step_count}. {thought}",
                        "color": "#FFFFFF",
                        "font_size": 40
                    }
                })
                await page.mouse.move(x, y)
                direction = action_output.get("direction", "down")
                if direction == "up":
                    await page.mouse.wheel(0, -500)
                elif direction == "down":
                    await page.mouse.wheel(0, 500)
                elif direction == "left":
                    await page.mouse.wheel(-100, 0)
                elif direction == "right":
                    await page.mouse.wheel(100, 0)
                else:
                    action_success = False
                    action_failed_reason = "vlm unknown scroll direction"
                await page.wait_for_load_state()
                await asyncio.sleep(0.5)
        elif action == "drag":
            start_box = action_output.get("start_box", None)
            end_box = action_output.get("end_box", None)
            logger.info(f"start_box: {start_box}, end_box: {end_box}")
            with Image.open(image_path) as image:
                start_box = coordinates_convert(start_box, image.size)
                end_box = coordinates_convert(end_box, image.size)
                logger.info(f"start_box after convert: {start_box}, end_box after convert: {end_box}")
                device_pixel_ratio = await page.evaluate("window.devicePixelRatio")
                x1 = start_box[0] / device_pixel_ratio
                y1 = start_box[1] / device_pixel_ratio
                x2 = end_box[0] / device_pixel_ratio
                y2 = end_box[1] / device_pixel_ratio
                await add_visual_marker(page, x1, y1)
                await add_visual_marker(page, x2, y2)
                manager.add_annotations(step_screenshot, annotations={
                    "markers": [{
                        "position": (start_box[0], start_box[1]),
                        "color": "#FF0000",
                        "size": 15
                    }, {
                        "position": (end_box[0], end_box[1]),
                        "color": "#FF0000",
                        "size": 15
                    }],
                    "subtitle": {
                        "text": f"{step_count}. {thought}",
                        "color": "#FFFFFF",
                        "font_size": 40
                    }
                })
                await page.mouse.move(x1, y1)
                await asyncio.sleep(0.5)
                await page.mouse.down()
                await asyncio.sleep(0.5)
                await page.mouse.move(x2, y2)
                await asyncio.sleep(0.5)
                await page.mouse.up()
                await asyncio.sleep(0.5)
                await page.wait_for_load_state()

        elif action == "hotkey":
            if is_limited_host(current_host, page.url):
                return False, "You are Forbidden to type in this page. Use browser_markdownify_content to extract this page", ""
            key = action_output.get("key", None)
            mapped_key = hotkey_map.get(key, None)
            manager.add_annotations(step_screenshot, annotations={
                "subtitle": {
                    "text": f"{step_count}. {thought}",
                    "color": "#FFFFFF",
                    "font_size": 40
                }
            })
            if mapped_key:
                logger.info(f"press hotkey {mapped_key}")
                await page.keyboard.press(mapped_key)
                await page.wait_for_load_state()
                await asyncio.sleep(0.5)
            else:
                action_success = False
                action_failed_reason = f"vlm unknown hotkey {key}"

        elif action == "finished":
            logger.info(f"finished with resp: {resp.content}")
            manager.add_annotations(step_screenshot, annotations={
                "subtitle": {
                    "text": f"{step_count}. {thought}",
                    "color": "#FFFFFF",
                    "font_size": 40
                }
            })

            manager.save_gif(gif_path)
            manager.cleanup()
            return True, f"vlm finished with resp: {resp.content}", gif_path
        else:
            logger.error(f"Unknown action: {action}")
            action_success = False
            action_failed_reason = f"vlm unknown action {action}"
            manager.add_annotations(step_screenshot, annotations={
                "subtitle": {
                    "text": f"{step_count}. {thought}",
                    "color": "#FFFFFF",
                    "font_size": 40
                }
            })
        logger.info(f"screenshot path: {image_path}")
        # image_path = os.path.join(screenshot_dirs, f"screenshots-step-{step_count}-after-{task_id}.png")
        # step_screenshot_after_str = await take_original_screenshot(page, image_path)
        # step_screenshot_after_str = base64.b64encode(step_screenshot_after).decode("utf-8")
        # logger.info(f"screenshot path: {image_path}")
        # step_review_result = await step_review(step_screenshot_str, step_screenshot_after_str, action_output_str)
        history.append(
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": f"#### Step {step_count} Action ####\n{resp.content}\n"
                    }
                ]
            )
        )
        if not action_success:
            history.append(
                HumanMessage(
                    content=[
                        {
                            "type": "text",
                            "text": f"This action failed because {action_failed_reason}\n"
                        }
                    ]
                )
            )
        # history.append(
        #     HumanMessage(
        #         content=[
        #             {
        #                 "type": "text",
        #                 "text": f"#### Step {step_count} Review ####\n{step_review_result}\n"
        #             }
        #         ]
        #     )
        # )
    manager.save_gif(gif_path)
    manager.cleanup()
    return False, "vlm reach max step count", gif_path


async def add_visual_marker(page, x, y, color="red", size=10, duration=1):
    """在指定坐标添加可视化标记（圆圈）"""
    js_code = f"""
    () => {{
        // 创建标记元素
        const marker = document.createElement('div');
        marker.style.position = 'absolute';
        marker.style.left = '{x}px';
        marker.style.top = '{y}px';
        marker.style.width = '{size}px';
        marker.style.height = '{size}px';
        marker.style.border = '2px solid {color}';
        marker.style.borderRadius = '50%';
        marker.style.transform = 'translate(-50%, -50%)';
        marker.style.zIndex = '999999';
        marker.style.pointerEvents = 'none';  // 防止遮挡点击

        // 添加临时动画效果
        marker.style.animation = 'pulse 1s infinite';
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {{
                0% {{ transform: translate(-50%, -50%) scale(1); opacity: 1; }}
                100% {{ transform: translate(-50%, -50%) scale(3); opacity: 0; }}
            }}
        `;

        document.body.appendChild(style);
        document.body.appendChild(marker);

        // 自动移除标记
        setTimeout(() => {{
            marker.remove();
            style.remove();
        }}, {duration * 200});
    }}
    """
    await page.evaluate(js_code)


async def step_review(screenshot_str1, screenshot_str2, step_info: str) -> str:
    system_message = SystemMessage(
        content=step_reviewer_prompt
    )
    message = HumanMessage(
        content=[
            {
                "type": "text",
                "text": "This is screenshot 1, the screenshot before the action"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot_str1}"
                }
            },
            {
                "type": "text",
                "text": "This is screenshot 2, the screenshot after the action"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{screenshot_str2}"
                }
            },
            {
                "type": "text",
                "text": f"The step info is: {step_info}"
            }
        ]
    )
    resp = await vlm_reviewer.ainvoke([system_message, message])
    logger.info(f"step reviewer result full: {resp.content}")
    review_result = extract_result(resp.content)
    logger.info(f"step reviewer result: {review_result}")
    return resp.content
