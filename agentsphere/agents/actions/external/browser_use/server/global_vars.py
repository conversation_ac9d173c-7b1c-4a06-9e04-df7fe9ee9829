# Browser context configuration
import json
import os
import uuid

import requests
from browser_use.browser.browser import <PERSON><PERSON><PERSON><PERSON><PERSON>ion, BrowserProfile
from browser_use.controller.service import Controller
from langchain_openai import ChatOpenAI
from playwright._impl._api_structures import ProxySettings, ViewportSize

from log import logger

profile = {
    "wait_for_network_idle_page_load_time": 0.6,
    "maximum_wait_page_load_time": 1.2,
    "minimum_wait_page_load_time": 0.2,
    # change window_size to viewport seems that viewport is the content given to the LLM
    "viewport": ViewportSize(width=1920, height=1650),
    "window_size": ViewportSize(width=1920, height=1650),
    "locale": "zh-cn",
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "highlight_elements": True,
    "viewport_expansion": 0,
    "save_downloads_path": os.getenv("IRIS_WORKSPACE_PATH"),
    "downloads_path": os.getenv("IRIS_WORKSPACE_PATH"),
    "executable_path": os.environ.get("CHROME_BIN"),
    "args": [
        "--no-sandbox",
        "--disable-gpu",
        "--disable-software-rasterizer",
        "--disable-dev-shm-usage",
        "--enable-features=GlobalMediaControls",
    ],
    "proxy": ProxySettings(
        server="http://strato-proxy-rd-relay.byted.org:8118",
        bypass=".larkoffice.com,.zijieapi.com,.byteimg.com,baidu.com,.baidu.com,byted.org,bytedance.net,.byted.org,.bytedance.net,localhost,127.0.0.1,::1,10.0.0.0/8,*********/8,fd00::/8,**********/10,fe80::/10,**********/12,***********/16,***********/16"
    )
}

profile_sandbox = {
    "wait_for_network_idle_page_load_time": 0.6,
    "maximum_wait_page_load_time": 1.2,
    "minimum_wait_page_load_time": 0.2,
    # change window_size to viewport seems that viewport is the content given to the LLM
    "viewport": ViewportSize(width=1920, height=1650),
    "window_size": ViewportSize(width=1920, height=1650),
    "locale": "zh-cn",
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "highlight_elements": True,
    "viewport_expansion": 0,
    "executable_path": os.environ.get("CHROME_BIN"),
    "args": [
        "--disable-gpu",
        "--disable-software-rasterizer",
        "--disable-dev-shm-usage",
        "--enable-features=GlobalMediaControls",
    ],
    "proxy": ProxySettings(
        server="http://strato-proxy-rd-relay.byted.org:8118",
        bypass=".larkoffice.com,.zijieapi.com,.byteimg.com,baidu.com,.baidu.com,byted.org,bytedance.net,.byted.org,.bytedance.net,localhost,127.0.0.1,::1,10.0.0.0/8,*********/8,fd00::/8,**********/10,fe80::/10,**********/12,***********/16,***********/16"
    )
}

from dotenv import load_dotenv
load_dotenv()

# Initialize browser and context
use_sidecar_browser = True
if os.getenv("IRIS_DOCKER_ARCH"):
    use_sidecar_browser = False  # 本地用docker调试，没有browser sidecar
if os.getenv("BROWSER_USE_SSE_PORT") == "9226":  # browserUseMCPPortForWebSearch
    use_sidecar_browser = False  # 给websearch用的本地浏览器，防止影响browser-use
if not os.getenv("BROWSER_USE_CDP_URL"):
    use_sidecar_browser = False
if use_sidecar_browser:
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(**profile, headless=True),
        cdp_url=os.getenv("BROWSER_USE_CDP_URL")
    )
else:
    browser_session = BrowserSession(browser_profile=BrowserProfile(**profile_sandbox))

try:
    doubao_cookies_url = "https://tosv.byted.org/obj/dolphin-inner/sailfish/doubao/cookies.json"
    response = requests.get(doubao_cookies_url)
    response.raise_for_status()
    doubao_cookies_str = response.content
    doubao_cookies = json.loads(doubao_cookies_str)
except:
    doubao_cookies = {}

session_id = os.getenv("SESSION_ID")
if not session_id:
    session_id = str(uuid.uuid4())
# Initialize LLM
llm = ChatOpenAI(
    model=os.environ.get("BROWSER_USE_SUMMARIZE_MODEL"),
    temperature=0.1,
    api_key=os.environ.get("OPENAI_API_KEY"),
    base_url=os.environ.get("OPENAI_API_BASE_URL"),
    default_headers={
        "X-Session-ID": session_id,
        "X-LLM-TAG": "browser_use_mcp",
    }
)

llm_high = ChatOpenAI(
    model=os.environ.get("BROWSER_USE_SUMMARIZE_MODEL"),
    temperature=0.6,
    api_key=os.environ.get("OPENAI_API_KEY"),
    base_url=os.environ.get("OPENAI_API_BASE_URL"),
    default_headers={
        "X-Session-ID": session_id,
        "X-LLM-TAG": "browser_use_mcp",
    }
)

vlm_model_name = os.environ.get("BROWSER_USE_VLM_MODEL")
if not vlm_model_name:
    vlm_model_name = "doubao-1.5-think-vision-pro-250428"
vlm = ChatOpenAI(
    model=vlm_model_name,
    temperature=0,
    # top_p=0.7,
    api_key=os.environ.get("OPENAI_API_KEY"),
    base_url=os.environ.get("OPENAI_API_BASE_URL"),
    default_headers={
        "X-Session-ID": session_id,
        "X-LLM-TAG": "browser_use_mcp",
    }
)

vlm_reviewer = ChatOpenAI(
    model=os.environ.get("BROWSER_USE_SUMMARIZE_MODEL"),
    temperature=0,
    # top_p=0.7,
    api_key=os.environ.get("OPENAI_API_KEY"),
    base_url=os.environ.get("OPENAI_API_BASE_URL"),
    default_headers={
        "X-Session-ID": session_id,
        "X-LLM-TAG": "browser_use_mcp",
    }
)

cookie_store_url = "https://strato-keystore.bytedance.net/api/v1/cookie"
if os.getenv("CUBE_WILDCARD_DOMAIN") is not None and os.getenv("CUBE_WILDCARD_DOMAIN").find("my") != -1:
    cookie_store_url = "https://strato-keystore.byteintl.net/api/v1/cookie"

async def restore_cookies():
    user_cloud_jwt = os.getenv("JWT_TOKEN")
    aime_user_name = os.getenv("USERNAME")
    headers = {"x-jwt-token": user_cloud_jwt}
    resp = requests.get("%s?user_name=%s" % (cookie_store_url, aime_user_name), headers=headers)
    resp.raise_for_status()
    resp_json = json.loads(resp.content)
    if resp_json["code"] != 0:
        raise Exception(f"Error restore cookies: {resp_json['message']}")
    cookies = resp_json["data"]
    await get_browser_session().browser_context.add_cookies(cookies)


async def save_cookies():
    user_cloud_jwt = os.getenv("JWT_TOKEN")
    aime_user_name = os.getenv("USERNAME")
    headers = {"x-jwt-token": user_cloud_jwt, "Content-Type": "application/json"}
    cookies = await get_browser_session().browser_context.cookies()
    
    # 保存cookies到本地文件
    try:
        sso_dir = "/tmp/sso"
        if not os.path.exists(sso_dir):
            os.makedirs(sso_dir)

        # 获取当前页面的URL来生成文件名
        current_page = await get_browser_session().get_current_page()
        if current_page:
            from urllib.parse import urlparse
            parsed_url = urlparse(current_page.url)
            domain = parsed_url.netloc.replace(".", "_")
            cookie_file = os.path.join(sso_dir, f"cookies_{domain}.json")

            # 保存cookies到文件
            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, indent=2, ensure_ascii=False)

            logger.info(f"Cookies saved to: {cookie_file}")
    except Exception as e:
        logger.warning(f"Failed to save cookies to /tmp/sso: {str(e)}")
    
    # 保存cookies到远程keystore
    data = {"user_name": aime_user_name, "cookies": cookies}
    resp = requests.post(cookie_store_url, json=data, headers=headers)
    resp.raise_for_status()
    resp_json = json.loads(resp.content)
    if resp_json["code"] != 0:
        raise Exception(f"Error save cookies: {resp_json['message']}")


# Initialize Controller for direct browser actions
browser_controller = Controller()

# Flag to track browser context health
browser_context_healthy = True

# Task storage for async operations
task_store = {}
workspace = os.getenv("IRIS_WORKSPACE_PATH")
workspace_screenshot_dirs = f"{workspace}/browser_screenshots"
download_dom_dirs = "/tmp/tmp-dom"
screenshot_dirs = "/tmp/screenshots"
gif_dir = "/tmp/gif"
aeolus_debug_dir = "/tmp/aeolus-debug"

if not os.path.exists(download_dom_dirs):
    os.makedirs(download_dom_dirs)

if not os.path.exists(screenshot_dirs):
    os.makedirs(screenshot_dirs)

if not os.path.exists(gif_dir):
    os.makedirs(gif_dir)

network_data = {}

find_element_by_sample_prompt = """
Image 1 is the target image you need to find. Image 2 is a clean screenshot. You need to find the target image in the clean screenshot.
Make sure the target image is EXACTLY the same as image 1.
Image 3 is a screenshot with colored rectangles numbered by element index with the same color.
After you find the target image in the clean screenshot, check the image 3 and return the element index of where the target image is found.
Also return the color of the rectangle in #hex format.
The output format MUST be:
{The reason of your answer}
{color of the rectangle}
<result>
index_of_the_element or None
</result>
"""

find_element_by_text_prompt = """
You are given a list of elements with their index and text. The user query will ask you to find an element and return its index.
You should return the index only when there is obvious evidence in element text.
The output format MUST be:
{The reason of your answer}
<result>
index_of_the_element or None
</result>
"""

find_file_name_prompt = """
You are given a file name list of png files and a user query. Find the matching file name according to user query.
The output format MUST be:
{The reason of your answer}
<result>
file_name or None
</result>
"""

reviewer_prompt = """
You are a task reviewer. You are given a task and two screenshots. Screenshot 1 is before the task, and screenshot 2 is after the task.
You need to check if the task is completed successfully.
Not only check if the task is completed, but also verify if the post-operation state meets the given goal and expectation.
The output format MUST be:
{The reason of your answer}
<result>
{success or failed}
</result>
"""

step_reviewer_prompt = """
You are a step reviewer to check if another llm achieve its step goal. You are given a step info and two screenshots.
The step info shows what the llm think and what action it wants to take.
Screenshot 1 is before the step, and screenshot 2 is after the step.
You need to check if the step is completed successfully according to the step info.
Note that if the step is to click an input box, the screenshot after the action may not show any change because the cursor blinks.
Consider this case as success.
The output format MUST be:
{The reason of your answer}
<result>
{success or failed}
</result>
"""

extract_content_system_prompt="""
Your task is to extract the content of the page.
Extraction Rules:
* You will be given a page and a goal and you should extract all relevant information around this goal from the page.
* If the goal is vague, summarize the page but MUST NOT return raw content.
* If pagination exists ("Next Page", "Load More", "See All" etc.), you MUST mention the point.
* You can use "PAGE NETWORK DATA" to help you to achieve goal.
* The URLs you infer or concatenate need to be marked.
* Important viewpoints, brief and keypoint MUST be cited from reference.
* Citations format MUST be ::cite[index] (e.g. ::cite[50] ::cite[51]). INDEX MUST BE INTEGER
* Review the output to ensure it is relevant to the goal.
* Unable to obtain API json data.
* If the page contains elements that require filtering, you MUST summarize what the current filtering conditions are.
* You can only use the URL you actually obtained as a reference. Fabricating reference sources is prohibited.
"""

markdownify_user_message_format = """
You are browsing {url}
Output Rules:
* Output language: {language}.
* Output format: {output_format}.
Extraction goal: {goal}.
Page: {page}
"""

generate_libra_report_system_prompt = """
You are a data analysis report generator. You will be given a set of data in markdown, along with a template and user instruction. Your task is to generate a report based on the template and the data provided.
Generation Rules:
* You MUST strictly follow the template structure and write analysis according to the data provided.
* Whenever data demonstration is needed, you MUST cite the data screenshot image provided in corresponding <screenshot></screenshot> instead of text.
* Make sure to format the report in Markdown.
Output format:
```markdown
{report_content}
```
"""

generate_libra_report_user_message_format = """
Template content: 
{template_content}

Data: 
{data}
"""


async def reset_browser_session():
    """Reset the browser context to a clean state."""
    global browser_session, browser_context_healthy

    logger.info("Resetting browser session")
    # v2.0里Browser和BrowserContext合并为BrowserSession，所以直接reset browser了
    try:
        # Try to close the existing context
        await browser_session.close()
        if use_sidecar_browser:
            browser_session = BrowserSession(
                browser_profile=BrowserProfile(**profile),
                cdp_url=os.getenv("BROWSER_USE_CDP_URL")
            )
            await browser_session.start()
            try:
                await restore_cookies()
            except Exception as e:
                logger.info(f"Failed to restore cookies: {str(e)}")
        else:
            browser_session = BrowserSession(
                browser_profile=BrowserProfile(**profile_sandbox))
            await browser_session.start()
        browser_context_healthy = True
        logger.info("Browser reset successfully")
    except Exception as e:
        logger.error(f"Failed to reset browser: {str(e)}")
        browser_context_healthy = False


async def check_browser_health():
    """Check if the browser context is healthy."""
    global browser_context_healthy

    if not browser_context_healthy:
        await reset_browser_session()
        return browser_context_healthy

    try:
        # Simple health check - try to get the current page
        await browser_session.get_current_page()
        return True
    except Exception as e:
        logger.warning(f"Browser health check failed: {str(e)}")
        browser_context_healthy = False
        await reset_browser_session()
        return browser_context_healthy


def get_browser_session():
    return browser_session


def get_browser_context_healthy():
    return browser_context_healthy


def set_browser_context_healthy(healthy: bool):
    global browser_context_healthy
    browser_context_healthy = healthy


def get_browser_controller():
    return browser_controller
