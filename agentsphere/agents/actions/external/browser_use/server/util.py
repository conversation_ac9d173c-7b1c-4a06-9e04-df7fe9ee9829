import urllib.parse
from pathlib import Path
import re
from log import logger

def extract_result(text: str) -> str:
    start_tag = "<result>"
    end_tag = "</result>"
    start = text.find(start_tag)
    if start == -1:
        return "None"
    start += len(start_tag)
    end = text.find(end_tag, start)
    return text[start:end].strip() if end != -1 else "None"


limited_hosts = ["byted.org", "bytedance.org", "bytedance.net", "bytedance.larkoffice.com"]

white_hosts = ["bitsai.bytedance.net",
               "libra-staging.maat.bytedance.net", "test-union.bytedance.net",
               "aime.bytedance.net", "aime-boe.bytedance.net", "rdk.bytedance.net",
               "voc.bytedance.net", "vcloud-admin.bytedance.net", "vqos.bytedance.net"]

white_urls = ["https://cloud.bytedance.net/docs","https://data.bytedance.net/libra", "https://ecop.bytedance.net/molly/experimental-report"]

white_url_patterns = ["https://meego.larkoffice.com/.+?/story/detail", "https://meego.larkoffice.com/.+?/issue/detail"]

white_wildcard_hosts = ["aime-app.bytedance.net"]


def is_limited_host(current_host: str | None, current_url: str | None):
    scheme = urllib.parse.urlparse(current_url).scheme
    if scheme == "file":
        return False

    for wildcard_host in white_wildcard_hosts:

        if wildcard_host in current_host:
            return False

    if current_host in white_hosts:
        return False

    for url in white_urls:

        if current_url.startswith(url):
            return False

    for url_re in white_url_patterns:
        if len(re.compile(url_re).findall(current_url)) != 0:
            return False

    for limited_host in limited_hosts:

        if limited_host in current_host:
            return True

    return False


def download(url: str) -> str:
    if url.startswith(("http://", "https://")):
        import tempfile
        import requests

        # Create temp file with .pdf extension
        temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)

        try:
            response = requests.get(url, stream=True, timeout=(20, 60 * 10), proxies={
                "http": "http://strato-proxy-rd-relay.byted.org:8118",
                "https": "http://strato-proxy-rd-relay.byted.org:8118",
            })
            response.raise_for_status()
            downloaded = 0
            total_size = int(response.headers.get('content-length', 0))

            # Write to temp file
            with open(temp_file.name, 'wb') as f:

                for chunk in response.iter_content(chunk_size=8192):

                    f.write(chunk)

                    downloaded += len(chunk)

                    progress = (downloaded / total_size) * 100

                    if progress % 10 < 0.05:
                        logger.info(f"download progress: {progress:.0f}%")

            return temp_file.name

        except requests.exceptions.Timeout as e:
            Path(temp_file.name).unlink(missing_ok=True)
            raise RuntimeError(f"Timeout downloading from {url}: {str(e)}")
        except Exception as e:
            Path(temp_file.name).unlink(missing_ok=True)
            raise RuntimeError(f"Failed to download from {url}: {str(e)}")

    elif url.startswith("file://"):
        return url[7:]  # Strip file:// prefix

    return url  # Assume local path