package devai

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/devai"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/middleware/hertz/pkg/protocol"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
)

var devaiClient *hertz.Client

func init() {
	var err error
	devaiClient, err = hertz.NewClient("https://bitsai.bytedance.net", hertz.NewHTTPClientOption{
		Timeout: time.Second * 10,
	})
	if err != nil {
		panic(err)
	}

}
func SaveLarkDocument(ctx context.Context, documentID string, creator string) error {
	var resp *protocol.Response
	return backoff.Retry(func() error {
		var err error
		resp, err = devaiClient.DoJSONReq(ctx, http.MethodPost, "/openapi/knowledge/v1/agent/save_lark_document", hertz.ReqOption{
			ExpectedCode: 0,
			Body: devai.SaveAgentLarkDocumentRequest{
				DocumentID:   documentID,
				DocumentType: "docx",
				Creator:      creator,
			},
			SetTTEnvHeaders: true,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to save lark document")
		}
		if resp.StatusCode() != http.StatusOK {
			return errors.WithMessagef(err, "failed to save lark document, status code: %d", resp.StatusCode())
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Millisecond*100), 10))

}
