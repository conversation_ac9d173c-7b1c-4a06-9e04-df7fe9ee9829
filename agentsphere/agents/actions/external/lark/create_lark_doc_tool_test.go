package lark

// import (
// 	"testing"

// 	"code.byted.org/gopkg/jsonx"
// 	"github.com/bytedance/mockey"
// 	"github.com/sirupsen/logrus"

// 	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
// 	planentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
// 	"code.byted.org/devgpt/kiwis/agentsphere/entity"
// 	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
// )

//	func TestLarkDocTool_Markdown(t *testing.T) {
//		c := &iris.AgentRunContext{
//			Environ: map[string]string{
//				entity.RuntimeEnvironUserCloudJWT: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//				entity.RunTimeLarkUserAccessToken: "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//			},
//		}
//
//		llm, err := framework.NewOpenAILLM(
//			"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",
//			"https://openrouter.ai/api/v1")
//		if err != nil {
//			t.Errorf("NewAzureOpenAILLM() error = %v", err)
//		}
//		defer mockey.UnPatchAll()
//
//		mockey.Mock(iris.Logger.Infof).Return().Build()
//		mockey.Mock(iris.Logger.Errorf).Return().Build()
//		mockey.Mock((*iris.AgentRunContext).GetLogger).Return(&logrus.Logger{}).Build()
//
//		mockey.Mock((*iris.AgentRunContext).GetLLM).Return(llm).Build()
//
//		doc, err := CreateLarkDoc(c, CreateLarkDocArgs{
//			FilePath: "/Users/<USER>/Downloads/iris_e9ceda5f-4b3e-4c31-be8d-4e523f46cb7e/output/langmanus_report.md",
//			Title:    "0403",
//		})
//		if err != nil {
//			t.Errorf("ListWorkItems() error = %v", err)
//			return
//		}
//		t.Logf("ListWorkItems() got = %s", jsonx.ToString(doc))
//	}
//func TestLarkDocTool_Html(t *testing.T) {
//	c := &iris.AgentRunContext{
//		Environ: map[string]string{
//			entity.RuntimeEnvironUserCloudJWT: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//			entity.RunTimeLarkUserAccessToken: "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//		},
//		User: entity.User{
//			Type:     "user",
//			Username: "shixiaoxue.111",
//		},
//	}
//
//	llm, err := framework.NewOpenAILLM(
//		"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",
//		"https://openrouter.ai/api/v1")
//	if err != nil {
//		t.Errorf("NewAzureOpenAILLM() error = %v", err)
//	}
//	defer mockey.UnPatchAll()
//
//	mockey.Mock(iris.Logger.Infof).Return().Build()
//	mockey.Mock(iris.Logger.Errorf).Return().Build()
//	mockey.Mock((*iris.AgentRunContext).GetLogger).Return(&logrus.Logger{}).Build()
//
//	mockey.Mock((*iris.AgentRunContext).GetLLM).Return(llm).Build()
//
//	mockey.Mock((*iris.AgentRunConfig).GetModelByScene).Return(iris.SceneModelConfig{
//		Model:       "anthropic/claude-3.7-sonnet",
//		Temperature: 0.2,
//		MaxTokens:   8192,
//	}).Build()
//	mockey.Mock((*iris.AgentRunContext).GetConfig).Return(&iris.AgentRunConfig{}).Build()
//
//	//args := CreateLarkDocArgs{
//	//	FilePath: "/Users/<USER>/Downloads/workspace 7/iris_03fe07de-a818-4a03-8213-2940a9b7635a/output/index.html",
//	//	Title:    "png",
//	//}
//
//	args := CreateLarkDocArgs{
//		FilePath: "/Users/<USER>/Downloads/workspace 25/iris_5cc99ca6-9f01-439f-9a85-04a88860be2a/output/index.html",
//		Title:    "multi html",
//	}
//
//	// inner chart
//	//args := CreateLarkDocArgs{
//	//	FilePath: "/Users/<USER>/Downloads/iris_73e2bf12-6d98-4b8d-84a2-10e3b57d5f31/index.html",
//	//	Title:    "nio",
//	//}
//
//	// contains json file
//	//args := CreateLarkDocArgs{
//	//	FilePath: "/Users/<USER>/OrbStack/docker/containers/************************************/workspace/iris_************************************/index_rendered_rendered.html",
//	//	Title:    "words",
//	//}
//
//	//args := CreateLarkDocArgs{
//	//	FilePath: "/Users/<USER>/Downloads/iris_dc56972f-0bea-40fb-ba73-e4f9aa7c68b3/index.html",
//	//	Title:    "etf",
//	//}
//	//
//	//args := CreateLarkDocArgs{
//	//	FilePath: "/Users/<USER>/Downloads/iris_11b58170-6e7e-40b8-b54e-9a8750aa1348/output/index.html",
//	//	Title:    "中间件对比",
//	//}
//
//	//
//	//svg := CreateLarkDocArgs{
//	//	FilePath: "/Users/<USER>/Downloads/iris_e9ceda5f-4b3e-4c31-be8d-4e523f46cb7e/output/index.html",
//	//	Title:    "langmanus",
//	//}
//
//	doc, err := CreateLarkDoc(c, args)
//	if err != nil {
//		t.Errorf("ListWorkItems() error = %v", err)
//		return
//	}
//	t.Logf("ListWorkItems() got = %s", jsonx.ToString(doc))
//}
//func TestRenderHTMLToImages(t *testing.T) {
//	c := &iris.AgentRunContext{
//		Environ: map[string]string{
//			entity.RuntimeEnvironUserCloudJWT: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//			entity.RunTimeLarkUserAccessToken: "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//		},
//		User: entity.User{
//			Type:     "user",
//			Username: "shixiaoxue.111",
//		},
//	}
//	defer mockey.UnPatchAll()
//
//	mockey.Mock(iris.Logger.Infof).Return().Build()
//	mockey.Mock(iris.Logger.Errorf).Return().Build()
//	mockey.Mock((*iris.AgentRunContext).GetLogger).Return(&logrus.Logger{}).Build()
//
//	//Run the RenderHTMLToImages function
//	outputFiles, err := RenderHTMLToImages(c, map[string]*DocxBlockImage{
//		"1111": {
//			FilePath: "/Users/<USER>/Downloads/workspace 25/iris_5cc99ca6-9f01-439f-9a85-04a88860be2a/output/index.html",
//			ChartID:  "functionCompleteness",
//		},
//		"2": {
//			FilePath: "/Users/<USER>/Downloads/workspace 25/iris_5cc99ca6-9f01-439f-9a85-04a88860be2a/output/index.html",
//			ChartID:  "elementCompleteness",
//		},
//		"3": {
//			FilePath: "/Users/<USER>/Downloads/workspace 25/iris_5cc99ca6-9f01-439f-9a85-04a88860be2a/output/index.html",
//			ChartID:  "optimizationPath",
//		},
//		//"4": {
//		//	FilePath: "/Users/<USER>/Downloads/workspace 25/iris_5cc99ca6-9f01-439f-9a85-04a88860be2a/output/index.html",
//		//	ChartID:  "price-comparison-chart",
//		//},
//		//"5": {
//		//	FilePath: "/Users/<USER>/Downloads/workspace 25/iris_5cc99ca6-9f01-439f-9a85-04a88860be2a/output/index.html",
//		//	ChartID:  "user-stats-chart",
//		//},
//		//"6": {
//		//	FilePath: "/Users/<USER>/Downloads/workspace 25/iris_5cc99ca6-9f01-439f-9a85-04a88860be2a/output/index.html",
//		//	ChartID:  "user-satisfaction-chart",
//		//},
//		//"7": {
//		//	FilePath: "/Users/<USER>/Downloads/workspace 25/iris_5cc99ca6-9f01-439f-9a85-04a88860be2a/output/index.html",
//		//	ChartID:  "user-retention-chart",
//		//},
//	})
//
//	//Check if there was an error
//	if err != nil {
//		t.Logf("RenderHTMLToImages returned error: %v", err)
//		// This might fail in CI environments without a display
//		t.Skip("Skipping test as it requires a display to render HTML")
//		return
//	}
//
//	// Verify that the output files were created
//	//assert.Equal(t, len(htmlFiles), len(outputFiles))
//	t.Logf("RenderHTMLToImages returned output files: %v", outputFiles)
//}

// func TestLarkDocTool_ProcessLarkMarkdownFile(t *testing.T) {
// 	c := &iris.AgentRunContext{
// 		Environ: map[string]string{
// 			entity.RuntimeEnvironUserCloudJWT: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
// 			entity.RunTimeLarkUserAccessToken: "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
// 		},
// 		User: entity.User{
// 			Type:     "user",
// 			Username: "shixiaoxue.111",
// 		},
// 	}

// 	llm, err := framework.NewOpenAILLM(
// 		"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",
// 		"https://openrouter.ai/api/v1")
// 	if err != nil {
// 		t.Errorf("NewAzureOpenAILLM() error = %v", err)
// 	}
// 	defer mockey.UnPatchAll()

// 	mockey.Mock(iris.Logger.Infof).Return().Build()
// 	mockey.Mock(iris.Logger.Errorf).Return().Build()
// 	mockey.Mock((*iris.AgentRunContext).GetLogger).Return(&logrus.Logger{}).Build()
// 	mockey.MockGeneric(iris.RetrieveStoreByKey[planentity.ReferenceStore]).Return(planentity.ReferenceStore{}).Build()

// 	mockey.Mock((*iris.AgentRunContext).GetLLM).Return(llm).Build()

// 	mockey.Mock((*iris.AgentRunConfig).GetModelByScene).Return(iris.SceneModelConfig{
// 		Model:       "anthropic/claude-3.7-sonnet",
// 		Temperature: 0.2,
// 		MaxTokens:   8192,
// 	}).Build()
// 	mockey.Mock((*iris.AgentRunContext).GetConfig).Return(&iris.AgentRunConfig{}).Build()

// 	args := CreateLarkDocArgs{
// 		FilePath: "larkmd/output_markdown.lark.md",
// 		Title:    "test lark markdown",
// 	}

// 	doc, err := CreateLarkDoc(c, args)
// 	if err != nil {
// 		t.Errorf("ListWorkItems() error = %v", err)
// 		return
// 	}
// 	t.Logf("ListWorkItems() got = %s", jsonx.ToString(doc))
// }
