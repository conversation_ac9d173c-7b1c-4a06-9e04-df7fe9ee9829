package lark

import (
	"context"
	"encoding/csv"
	"fmt"
	"html"
	"net/url"
	"os"
	"strings"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"github.com/cenkalti/backoff/v4"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/port/lark"
)

type LarkToolArgs struct {
	DocumentURL string `json:"document_url" mapstructure:"document_url" description:"required, lark document/sheet url, example: https://domain.larkoffice.com/docx/CCzFdEVGXoyLpmxxxxxx, https://bytedance.larkoffice.com/sheets/xxxxx?sheet=xxxxx"`
}

const (
	ToolLarkDownload            = "lark_download"
	ToolLarkDownloadDescription = "download file from the lark url, save locally, and return file path list, example: [\"doc_title_xxx.docx\", \"sheet_title_xxx.xlsx\"], this is first priority tool to download content from lark urls"
)

func NewLarkDownloadTool() iris.Action {
	return actions.ToTool(ToolLarkDownload, ToolLarkDownloadDescription, DownloadContentFromLark)
}

func download(ctx context.Context, client lark.Client, fileExtension, token, fileType, subID, userAccessToken string) (string, error) {
	ticket, err := client.CreateExportTasks(ctx, fileExtension, token, fileType, subID, userAccessToken)
	// 降级策略，如果是权限不足，从只读接口获取文档内容
	if errors.Is(err, lark.ErrPermissionDenied) && fileExtension == "docx" {
		return downloadFileByGetDocContent(ctx, client, token, userAccessToken)
	}
	if err != nil {
		return "", iris.NewRecoverable(err)
	}

	if ticket == nil {
		return "", iris.NewRecoverable(errors.New("export task ticket is nil"))
	}

	var downloadToken, fileName string

	err = backoff.Retry(func() error {
		success, exportToken, exportName, err := client.GetExportTask(ctx, token, *ticket, userAccessToken)
		if err != nil {
			return err
		}
		if !success {
			return errors.New("export task not ready")
		}
		if exportToken == "" {
			return errors.New("empty export token")
		}

		downloadToken = exportToken
		fileName = strings.ReplaceAll(exportName, "/", "_") + "." + fileExtension
		return nil
	}, backoff.NewExponentialBackOff(backoff.WithMaxElapsedTime(time.Second*120)))
	if err != nil {
		return "", iris.NewRecoverable(errors.Wrap(err, "failed to get export result"))
	}

	if downloadToken == "" {
		return "", iris.NewRecoverable(errors.New("failed to get download token"))
	}

	// 下载文件
	resp, err := client.DownLoadExportFile(ctx, downloadToken, userAccessToken)
	if err != nil {
		return "", iris.NewRecoverable(err)
	}
	if resp.StatusCode != 200 {
		return "", iris.NewRecoverable(errors.Errorf("download failed with status code: %d", resp.StatusCode))
	}

	if fileExtension == "xlsx" {
		fileName = fmt.Sprintf("%s.xlsx", subID)
		excel, err := excelize.OpenReader(resp.File)
		if err != nil {
			return "", iris.NewRecoverable(err)
		}
		defer excel.Close()
		list := excel.GetSheetList()
		// 删除sheet name不等于subID的sheet
		for _, sheet := range list {
			if sheet != subID {
				if err := excel.DeleteSheet(sheet); err != nil {
					return "", iris.NewRecoverable(err)
				}
			}
		}
		// 保存修改后的文件
		if err := excel.SaveAs(fileName); err != nil {
			return "", iris.NewRecoverable(err)
		}
	} else {
		// 写入文件
		err = resp.WriteFile(fileName)
		if err != nil {
			return "", iris.NewRecoverable(err)
		}
	}

	// 下载@aime的评论
	err = saveComments(ctx, client, token, fileType, userAccessToken, fileName)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to save comments %v", err)
	}

	return fileName, nil
}

func saveComments(ctx context.Context, client lark.Client, token string, fileType string, userAccessToken string, fileName string) error {
	comments, err := client.GetLarkDocComments(ctx, false, token, fileType, userAccessToken)

	if err != nil {
		logs.V1.CtxError(ctx, "failed to get comments", err)
	} else {
		atAime := make(map[string]string)
		for _, comment := range comments {
			for _, reply := range comment.ReplyList.Replies {
				for _, element := range reply.Content.Elements {
					if element.TextRun != nil && element.TextRun.Text != nil {
						text := *element.TextRun.Text
						if strings.Contains(strings.ToLower(text), "@aime") {
							if _, exists := atAime[*comment.Quote]; !exists {
								atAime[*comment.Quote] = html.UnescapeString(text)
							} else {
								atAime[*comment.Quote] += " " + html.UnescapeString(text)
							}
						}
					}
				}
			}
		}
		if len(atAime) > 0 {
			commentFileName := fileName + "_comment.csv"
			commentFile, err := os.Create(commentFileName)
			if err != nil {
				return err
			}
			defer commentFile.Close()

			writer := csv.NewWriter(commentFile)
			defer writer.Flush()
			header := []string{"Quote", "Content"}
			if err := writer.Write(header); err != nil {
				logs.V1.CtxError(ctx, "failed to write header %v", err)
			}
			for k, v := range atAime {
				row := []string{k, v}
				if err := writer.Write(row); err != nil {
					logs.V1.CtxError(ctx, "failed to write row %v", err)
				}
			}
		}
	}
	return nil
}

func downloadFileByGetDocContent(ctx context.Context, client lark.Client, token, userAccessToken string) (fileName string, err error) {
	resp, err := client.GetLarkDocxRawContent(ctx, token, userAccessToken)
	if err != nil {
		return "", err
	}
	// 写入文件
	fileName = resp.Title + ".docx"
	// 创建文件
	file, err := os.Create(fileName)
	if err != nil {
		return "", err
	}
	defer file.Close()
	// 将 data 写入 file
	_, err = file.Write([]byte(resp.Body))
	if err != nil {
		return "", err
	}
	return fileName, nil
}

func writeFile(fileName string, data []byte) error {
	// 创建文件
	file, err := os.Create(fileName)
	if err != nil {
		return err
	}
	defer file.Close()
	// 将 data 写入 file
	_, err = file.Write(data)
	if err != nil {
		return err
	}
	return nil
}

func GetLarkClient() lark.Client {
	client := lark.NewClient("********************", "E98UDT0US21Cz4UNlvWycdDBiOpLwuaz", "dfee5cdb7c86fbbd0ae7c0d6d28f6d91", "https://aime.bytedance.net/api/agents/v2/lark/auth")
	if env.IsBoe() {
		client = lark.NewClient("********************", "QRW7OJ6RI9qihsS8OfZTDbKjSuq0B2sN", "dfee5cdb7c86fbbd0ae7c0d6d28f6d91", "https://aime-boe.bytedance.net/api/agents/v2/lark/auth")
	}
	return client
}

type DownloadLarkContentResult struct {
	FilePath  string   `json:"file_path,omitempty" mapstructure:"file_path"`
	FilePaths []string `json:"file_paths,omitempty" mapstructure:"file_paths"`
}

// DownloadContentFromLark doc: https://open.larkoffice.com/document/server-docs/docs/drive-v1/export_task/create?appId=cli_a29e059c17fa900d
func DownloadContentFromLark(c *iris.AgentRunContext, args LarkToolArgs) (*DownloadLarkContentResult, error) {
	client := GetLarkClient()

	return downloadLarkDocument(c, client, args.DocumentURL, c.GetEnv(entity.RunTimeLarkUserAccessToken), true)
}

func DownloadLarkComments(c *iris.AgentRunContext, args LarkToolArgs) (map[string]any, error) {
	ctx := context.Background()

	client := GetLarkClient()
	userAccessToken := c.GetEnv(entity.RunTimeLarkUserAccessToken)
	_, fileType, fileToken, _, err := PrepareLarkURL(client, args.DocumentURL, userAccessToken)
	if err != nil {
		return nil, err
	}

	larkParser := NewLarkParser(c, client)

	comments, err := client.GetLarkDocComments(ctx, false, fileToken, fileType, userAccessToken)
	if err != nil {
		return nil, iris.NewRecoverable(err)
	}

	larkParser.userComments = comments

	res := make(map[string]any)
	if fileType == "docx" {
		// 获取docx类型的文档块
		blocks, err := client.GetLarkDocxBlock(ctx, fileToken, userAccessToken, lark.Option{
			UserAccessToken: userAccessToken,
		})
		if err != nil {
			return nil, iris.NewRecoverable(errors.Wrap(err, "failed to get document blocks"))
		}
		content := larkParser.ParseDocxContent(ctx, fileToken, blocks)
		res["content"] = content
		aimeTasks := larkParser.ParseAimeTasks(ctx, blocks)
		res["aime_tasks"] = aimeTasks
	} else {
		aimeTasks := make([]AimeTask, 0)
		for _, comment := range comments {
			text := larkParser.getAtAimeCommentText(comment)
			if text != "" {
				aimeTasks = append(aimeTasks, AimeTask{
					Quote:   *comment.Quote,
					Content: text,
				})
			}
		}
		res["aime_tasks"] = aimeTasks
	}
	return res, nil
}

// downloadLarkDocument 核心文档下载逻辑，可以被复用
func downloadLarkDocument(c *iris.AgentRunContext, client lark.Client, docURL, userAccessToken string, enableDownloadRefDoc bool) (*DownloadLarkContentResult, error) {
	ctx := context.Background()

	fileExtension, fileType, fileToken, subID, err := PrepareLarkURL(client, docURL, userAccessToken)
	if err != nil {
		return nil, err
	}

	// 对doc和docx文件进行特殊处理，转换为markdown
	if fileType == "doc" || fileType == "docx" {
		// 调用转换方法
		mdFileName, err := convertLarkDocToMarkdownWithOptions(c, client, fileToken, fileType, userAccessToken, enableDownloadRefDoc)
		if err != nil {
			return nil, iris.NewRecoverable(errors.Wrap(err, "failed to get document info"))
		}

		return &DownloadLarkContentResult{
			FilePath: mdFileName,
		}, nil
	}

	if fileType == "sheet" {
		var filePaths []string
		var downloadErrs *multierror.Error

		subIDResp, err := client.GetSheetsSubIDs(ctx, fileToken, userAccessToken)
		if err != nil {
			return nil, err
		}
		if subIDResp.Data == nil {
			return nil, iris.NewRecoverable(errors.New("sub id resp date is nil"))
		}
		if subIDResp.Data.Sheets == nil {
			return nil, iris.NewRecoverable(errors.New("sub id resp data sheets is nil"))
		}

		for _, sheet := range subIDResp.Data.Sheets {
			if sheet.SheetId == nil {
				continue
			}
			filePath, err := download(ctx, client, fileExtension, fileToken, fileType, *sheet.SheetId, userAccessToken)
			if err != nil {
				downloadErrs = multierror.Append(downloadErrs, errors.WithMessagef(err, "failed to download sheet %s", *sheet.SheetId))
				continue
			}
			filePaths = append(filePaths, filePath)
		}

		// if filepaths is empty, return error with detailed info
		if len(filePaths) == 0 {
			if downloadErrs != nil {
				return nil, iris.NewRecoverable(errors.Errorf("no sheet found, download errors: %v", downloadErrs.ErrorOrNil()))
			}
			return nil, iris.NewRecoverable(errors.New("no sheet found"))
		}

		return &DownloadLarkContentResult{
			FilePaths: filePaths,
		}, nil
	}

	filePath, err := download(ctx, client, fileExtension, fileToken, fileType, subID, userAccessToken)
	if err != nil {
		return nil, err
	}
	return &DownloadLarkContentResult{
		FilePaths: []string{filePath},
	}, nil
}

func PrepareLarkURL(client lark.Client, docURL string, userAccessToken string) (fileExtension string, fileType string, token string, subID string, err error) {
	if docURL == "" {
		return "", "", "", "", iris.NewRecoverable(errors.New("empty document url"))
	}
	ctx := context.Background()

	parsedURL, err := url.Parse(docURL)
	if err != nil {
		return "", "", "", "", iris.NewRecoverable(err)
	}

	// 获取 path 部分并提取所需的 ID
	pathSegments := strings.Split(strings.Trim(parsedURL.Path, "/"), "/")
	if len(pathSegments) < 2 {
		return "", "", "", "", iris.NewRecoverable(errors.New("invalid base url"))
	}
	token = pathSegments[len(pathSegments)-1]

	// 获取 query 参数中的 table 值，subID 仅支持 csv 格式。https://open.larkoffice.com/document/server-docs/docs/drive-v1/export_task/create?appId=********************
	subID = parsedURL.Query().Get("table")

	// 确定文件类型和扩展名
	switch {
	case strings.Contains(docURL, "/sheet/") || strings.Contains(docURL, "/sheets/"):
		fileExtension, fileType = "csv", "sheet"
	case strings.Contains(docURL, "/docs/"):
		fileExtension, fileType = "docx", "doc"
	case strings.Contains(docURL, "/docx/"):
		fileExtension, fileType = "docx", "docx"
	case strings.Contains(docURL, "/wiki/"):
		fileInfo, err := client.GetWikiNodeInfo(ctx, token, userAccessToken)
		if err != nil {
			return "", "", "", "", err
		}
		if fileInfo.Data == nil {
			return "", "", "", "", iris.NewRecoverable(errors.New("file info data is nil"))
		}
		if fileInfo.Data.Node == nil {
			return "", "", "", "", iris.NewRecoverable(errors.New("file info data node is nil"))
		}
		if fileInfo.Data.Node.ObjType == nil {
			return "", "", "", "", iris.NewRecoverable(errors.New("file info data node obj type is nil"))
		}
		if fileInfo.Data.Node.ObjToken == nil {
			return "", "", "", "", iris.NewRecoverable(errors.New("file info data node obj token is nil"))
		}
		switch {
		case *fileInfo.Data.Node.ObjType == "doc":
			fileExtension, fileType = "docx", "doc"
		case *fileInfo.Data.Node.ObjType == "docx":
			fileExtension, fileType = "docx", "docx"
		case *fileInfo.Data.Node.ObjType == "sheet":
			fileExtension, fileType = "csv", "sheet"
		case *fileInfo.Data.Node.ObjType == "bitable":
			fileExtension, fileType = "csv", "bitable"
		default:
			return "", "", "", "", iris.NewRecoverable(errors.Errorf("exporting %s is not supported. only /sheet/, /docs/, /docx/ are supported", docURL))
		}
		token = *fileInfo.Data.Node.ObjToken
	case strings.Contains(docURL, "/base/"):
		parsedURL, err := url.Parse(docURL)
		if err != nil {
			return "", "", "", "", iris.NewRecoverable(err)
		}

		// 获取 path 部分并提取所需的 ID
		pathSegments := strings.Split(strings.Trim(parsedURL.Path, "/"), "/")
		if len(pathSegments) < 2 {
			return "", "", "", "", iris.NewRecoverable(errors.New("invalid base url"))
		}
		token = pathSegments[len(pathSegments)-1]

		// 获取 query 参数中的 table 值，subID 仅支持 csv 格式。https://open.larkoffice.com/document/server-docs/docs/drive-v1/export_task/create?appId=********************
		subID = parsedURL.Query().Get("table")
		// todo view是拿不到的，只支持导出数据表。如果要保留视图信息，需要按xlsx导出。后续可以改造成xlsx进行完整文件的导出，然后通过代码来处理
		fileExtension, fileType = "csv", "bitable"
	default:
		return "", "", "", "", iris.NewRecoverable(errors.Errorf("exporting %s is not supported. only /sheet/, /docs/, /docx/ are supported", docURL))
	}

	return fileExtension, fileType, token, subID, nil
}

// convertLarkDocToMarkdownWithOptions 将Lark文档转换为Markdown并保存到本地，支持配置是否下载引用文档
func convertLarkDocToMarkdownWithOptions(c *iris.AgentRunContext, client lark.Client, token, fileType, userAccessToken string, enableDownloadRefDoc bool) (mdFileName string, err error) {

	ctx := context.Background()
	// 创建LarkParser实例
	larkOption := lark.Option{
		UserAccessToken: userAccessToken,
	}

	var larkParser *LarkParser
	if enableDownloadRefDoc {
		larkParser = NewLarkParser(c, client, WithEnableImage(true), WithEnableUserOpenID(true), WithEnableDownloadRefDoc(true), WithLarkOption(larkOption))
	} else {
		larkParser = NewLarkParser(c, client, WithEnableImage(true), WithEnableUserOpenID(true), WithLarkOption(larkOption))
	}

	comments, err := client.GetLarkDocComments(ctx, false, token, fileType, userAccessToken)

	if err != nil {
		if !strings.Contains(err.Error(), "429") {
			c.GetLogger().Errorf("failed to get comments: %v", err)
		}
	} else {
		larkParser.userComments = comments
	}

	var (
		content string
		title   string
	)

	if fileType == "docx" {
		docxMeta, err := client.GetLarkDocx(ctx, token, "", larkOption)
		if err != nil {
			return "", iris.NewRecoverable(errors.Wrap(err, "failed to get document info"))
		}
		if docxMeta != nil && docxMeta.Document != nil && docxMeta.Document.Title != nil {
			title = *docxMeta.Document.Title
		}
		// 获取docx类型的文档块
		blocks, err := client.GetLarkDocxBlock(ctx, token, userAccessToken, larkOption)
		if err != nil {
			return "", iris.NewRecoverable(errors.Wrap(err, "failed to get document blocks"))
		}

		// 转换成markdown
		content = larkParser.ParseDocxContent(ctx, token, blocks)
	} else {
		docMeta, err := client.GetLarkDoc(ctx, token, "", larkOption)
		if err != nil {
			return "", iris.NewRecoverable(errors.Wrap(err, "failed to get document info"))
		}
		if docMeta != nil {
			title = docMeta.Data.Title
		}
		// 获取doc类型的文档块
		docContent, err := client.GetLarkDocBlock(ctx, token, userAccessToken, larkOption)
		if err != nil {
			return "", iris.NewRecoverable(errors.Wrap(err, "failed to get document content"))
		}
		// 转换成markdown
		content = larkParser.ParseDocContent(ctx, docContent)
	}

	if title == "" {
		title = fmt.Sprintf("%s_%s", fileType, token)
	}

	mdFileName = title + ".lark.md"
	illegalChars := []string{
		"/", "\\", "?", "%", "*", ":", "|", "\"", "<", ">", " ",
		// 可以根据需要添加更多非法字符
	}
	// 替换所有非法字符为下划线
	for _, char := range illegalChars {
		mdFileName = strings.ReplaceAll(mdFileName, char, "_")
	}
	// 保存到markdown文件

	err = os.WriteFile(mdFileName, []byte(content), 0644)
	if err != nil {
		mdFileName = fmt.Sprintf("%s_%s.lark.md", fileType, token)
		err = os.WriteFile(mdFileName, []byte(content), 0644)
		if err != nil {
			return "", iris.NewRecoverable(errors.Wrap(err, "failed to write markdown file"))
		}
	}

	return mdFileName, nil
}
