package lark

import (
	"bytes"
	"context"
	_ "embed"
	"fmt"
	"image"
	_ "image/jpeg" // 支持 JPEG 格式
	_ "image/png"  // 支持 PNG 格式
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/devai"
	"code.byted.org/gopkg/jsonx"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/lang/gg/gslice"
	"github.com/PuerkitoBio/goquery"
	json "github.com/bytedance/sonic"
	"github.com/chromedp/chromedp"
	"github.com/google/uuid"
	larksdk "github.com/larksuite/oapi-sdk-go/v3"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
	"github.com/samber/lo"

	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"

	"github.com/xuri/excelize/v2"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark/larkmd"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	repowiki_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/repowiki/agent"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/copilotstack/prompt"
	"code.byted.org/devgpt/kiwis/port/lark"
)

var (
	//go:embed lark_doc_prompt.tmpl
	larkDocPrompt string

	// ChromedpBaseOpts defines the base options for chromedp
	chromedpBaseOpts = append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("disable-web-security", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("allow-file-access-from-files", true),
		chromedp.Flag("ignore-certificate-errors", true),
		chromedp.Flag("autoplay-policy", "no-user-gesture-required"),
		chromedp.Flag("enable-automation", false),
		chromedp.Flag("load-images", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("disable-software-rasterizer", true),
		chromedp.Flag("disable-dev-shm-usage", true),
	)

	// ChromedpProxyOpts defines proxy options for chromedp
	chromedpProxyOpts = []chromedp.ExecAllocatorOption{
		chromedp.ProxyServer("http://sys-proxy-rd-relay.byted.org:8118"),
		chromedp.Flag("proxy-bypass-list", "<-loopback>"),
	}
)

// CreateLarkDocArgs defines the arguments for creating a Lark document
type CreateLarkDocArgs struct {
	FilePath string `json:"file_path" mapstructure:"file_path" description:"必填，文件绝对路径，支持HTML文件和Markdown文件（.md、.markdown、.lark.md），比如：/workspace/iris_e7c707a5-ae78-42d0-b045-1882a9f0a4d7/index.html，注意：1. 禁止填url 2. 文件路径必须真实存在（必须提前使用 pwd && ls 确认文件路径） 3. 如果是html文件，必须是完整的html文件，不能是片段，禁止index_part.html这种形式"`
	Title    string `json:"title" mapstructure:"title" description:"必填，文档标题'"`
}

const (
	ToolCreateLarkDoc            = "create_lark_doc"
	ToolCreateLarkDocDescription = "Create a Lark document based on an HTML or Markdown file (.md, .markdown, .lark.md). Do not call this tool multiple times in a single task. Must Use pwd && ls to confirm the file path before use."
)

// NewCreateLarkDoc creates a new tool for generating Lark documents
func NewCreateLarkDoc() iris.Action {
	return actions.ToTool(ToolCreateLarkDoc, ToolCreateLarkDocDescription, CreateLarkDoc)
}

func larkClient(run *iris.AgentRunContext) lark.Client {
	tceHostEnv := run.Environ.Get(entity.RuntimeTCEHostEnv)
	return lo.Ternary(tceHostEnv == "boe",
		lark.NewClient("********************", "QRW7OJ6RI9qihsS8OfZTDbKjSuq0B2sN", "dfee5cdb7c86fbbd0ae7c0d6d28f6d91", "https://aime-boe.bytedance.net/api/agents/v2/lark/auth", larksdk.WithReqTimeout(time.Second*20)),
		lark.NewClient("********************", "E98UDT0US21Cz4UNlvWycdDBiOpLwuaz", "dfee5cdb7c86fbbd0ae7c0d6d28f6d91", "https://aime.bytedance.net/api/agents/v2/lark/auth", larksdk.WithReqTimeout(time.Second*20)))
}

// CreateLarkDoc creates a Lark document from an HTML or Markdown file
func CreateLarkDoc(run *iris.AgentRunContext, args CreateLarkDocArgs) (map[string]any, error) {
	// Validate input parameters
	if args.FilePath == "" || args.Title == "" {
		return nil, fmt.Errorf("file_path and title are required")
	}

	// Check if file exists
	if _, err := os.Stat(args.FilePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file does not exist: %s", args.FilePath)
	}

	// Check the file extension to determine the processing method
	fileExt := strings.ToLower(filepath.Ext(args.FilePath))

	var (
		documentID   string
		relatedFiles []string
		err          error
	)
	switch fileExt {
	case ".html", ".htm":
		documentID, err = processHTMLFile(run, args.FilePath, args.Title)
		if err != nil {
			return nil, err
		}
	case ".lark.md", ".md", ".markdown":
		documentID, relatedFiles, err = ProcessLarkMarkdownFile(run, args.FilePath, args.Title, &ProcessLarkMarkdownFileOptions{})
		if err != nil {
			return nil, err
		}
		//upload lark.md related files
		err = uploadRelatedFiles(run, relatedFiles, args.Title)
		if err != nil {
			run.GetLogger().Warnf("failed to upload lark.md related files: %v", err)
		}
		err = devai.SaveLarkDocument(run, documentID, run.UserInfo.Email)
		if err != nil {
			run.GetLogger().Errorf("failed to save lark document metadata: %v", err)
			return nil, err
		}
	default:
		return nil, fmt.Errorf("unsupported file type: %s. Only HTML (.html, .htm) and Markdown (.md, .markdown, .lark.md) files are supported", fileExt)
	}

	// Add edit permission for the user after document creation
	if err = addEditPermission(run, documentID, "docx"); err != nil {
		run.GetLogger().Errorf("Failed to add edit permission for user: %v", err)
		// Continue even if permission addition fails
	}

	return map[string]any{
		"document_id":  documentID,
		"document_url": fmt.Sprintf("https://bytedance.larkoffice.com/docx/%s", documentID),
		"description":  fmt.Sprintf("飞书文档创建成功，请在%s查看", fmt.Sprintf("https://bytedance.larkoffice.com/docx/%s", documentID)),
		"title":        args.Title,
		"reference":    fmt.Sprintf("[%s]%s", args.Title, fmt.Sprintf("https://bytedance.larkoffice.com/docx/%s", documentID)),
	}, nil
}

// getOrCreateDateFolder finds or creates a date-named folder within the "aime" folder
func getOrCreateDateFolder(run *iris.AgentRunContext, folderToken string) (string, error) {
	ctx := context.Background()

	var aimeFolderToken string
	if folderToken != "" {
		aimeFolderToken = folderToken
	} else {
		// Use hardcoded aime folder tokens based on environment
		if os.Getenv(entity.RuntimeTCEHostEnv) == "boe" {
			// Offline environment token
			aimeFolderToken = "YKgSfkdhslvTdhdcqWVcFKRqn3d"
			run.GetLogger().Infof("Using offline aime folder token: %s", aimeFolderToken)
		} else {
			// Online environment token
			aimeFolderToken = "D889fEgb6lAJERdoZpucTdTenTb"
			run.GetLogger().Infof("Using online aime folder token: %s", aimeFolderToken)
		}
	}

	// Step 2: List files in the aime folder to check for today's date folder
	aimerFolderResp, err := larkClient(run).ListFiles(ctx, aimeFolderToken, "", 100, "", "")
	if err != nil {
		return "", fmt.Errorf("failed to list aime folder contents: %v", err)
	}

	// Get today's date in YYYYMMDD format
	todayFolder := time.Now().Format("2006-01-02")
	var dateFolderToken string

	// Check if today's date folder already exists
	for _, file := range aimerFolderResp.Files {
		if file.Name != nil && *file.Name == todayFolder && file.Type != nil && *file.Type == "folder" {
			dateFolderToken = *file.Token
			run.GetLogger().Infof("Found existing date folder: %s with token: %s", todayFolder, dateFolderToken)
			break
		}
	}

	// Step 3: Create today's date folder if it doesn't exist
	if dateFolderToken == "" {
		createResp, err := larkClient(run).CreateFolder(ctx, todayFolder, aimeFolderToken)
		if err != nil {
			return "", fmt.Errorf("failed to create date folder: %v", err)
		}
		dateFolderToken = *createResp.Token
		run.GetLogger().Infof("Created new date folder: %s with token: %s", todayFolder, dateFolderToken)
	}

	// Step 4: List files in the date folder to check for hour-level folder
	dateFolderResp, err := larkClient(run).ListFiles(ctx, dateFolderToken, "", 100, "", "")
	if err != nil {
		run.GetLogger().Errorf("Failed to list date folder contents: %v", err)
		// Continue with date folder if listing hour folders fails
		return dateFolderToken, nil
	}

	// Get current hour in HH format
	hourFolder := time.Now().Format("15")
	// 在目录里拼上当前角色，防止目录下写入文档达到上限
	hourFolder = fmt.Sprintf("%s_%s", run.Config.AgentVariantConfig.Default.Variant, hourFolder)
	var hourFolderToken string

	// Check if hour folder already exists
	for _, file := range dateFolderResp.Files {
		if file.Name != nil && *file.Name == hourFolder && file.Type != nil && *file.Type == "folder" {
			hourFolderToken = *file.Token
			run.GetLogger().Infof("Found existing hour folder: %s with token: %s", hourFolder, hourFolderToken)
			break
		}
	}

	// Step 5: Create hour folder if it doesn't exist
	if hourFolderToken == "" {
		createResp, err := larkClient(run).CreateFolder(ctx, hourFolder, dateFolderToken)
		if err != nil {
			run.GetLogger().Errorf("Failed to create hour folder: %v", err)
			// Return date folder token if hour folder creation fails
			return dateFolderToken, nil
		}
		hourFolderToken = *createResp.Token
		run.GetLogger().Infof("Created new hour folder: %s with token: %s", hourFolder, hourFolderToken)
	}

	return hourFolderToken, nil
}

// addEditPermission adds edit permission to the document for the user
func addEditPermission(run *iris.AgentRunContext, documentID string, fileType string) error {
	// 此方法只支持这几种飞书文档格式授权
	if !gslice.Contains([]string{"doc", "docx", "sheet", "bitable"}, fileType) {
		return fmt.Errorf("file type %s not supported", fileType)
	}

	ctx := context.Background()

	var userEmail = run.UserInfo.Email
	if userEmail == "" {
		userEmail = run.User.Username + "@bytedance.com"
	}

	// Add edit permission for the user
	err := larkClient(run).AddLarkFilePermission(ctx, documentID, userEmail, "edit", fileType)

	return err
}

// processHTMLFile handles the HTML file conversion to Lark document
func processHTMLFile(run *iris.AgentRunContext, htmlFilePath string, title string) (string, error) {
	promptTemplate := prompt.NewPromptTemplate(prompt.Template{
		Template: larkDocPrompt,
	})
	res, pErr := promptTemplate.ComposePrompt(nil)
	if pErr != nil {
		run.GetLogger().Errorf("failed to compose html to doc prompt", pErr)
	}

	// Get the target folder token for document creation
	folderToken, err := getOrCreateDateFolder(run, "")
	if err != nil {
		run.GetLogger().Errorf("Failed to get or create date folder: %v", err)
		// Continue with empty folder token (will create in root)
		folderToken = ""
	}

	// First, create a new document
	docxCreateResponse, err := larkClient(run).CreateLarkDocx(context.Background(), folderToken, title, "")
	if err != nil {
		run.GetLogger().Errorf("Failed to create Lark document: %v", err)
		return "", err
	}
	documentID := *docxCreateResponse.Document.DocumentId
	run.GetLogger().Infof("Created empty Lark document with ID: %s", documentID)

	// Then, convert HTML to blocks and add them to the document
	err = convertAndAddBlocks(run, res.String(), htmlFilePath, documentID)
	if err != nil {
		run.GetLogger().Errorf("Failed to add blocks to document: %v", err)
		// We still return the document ID even if adding blocks failed
		return documentID, err
	}

	return documentID, nil
}

// renderHTMLToFile renders an HTML file using chromedp and saves the rendered content
func renderHTMLToFile(run *iris.AgentRunContext, htmlFilePath string) (string, error) {
	// Create output file path
	ext := filepath.Ext(htmlFilePath)
	baseName := strings.TrimSuffix(htmlFilePath, ext)
	renderedFilePath := fmt.Sprintf("%s_rendered%s", baseName, ext)

	var renderedHTML string
	var renderErr error

	// Try up to 3 times to render the HTML
	for attempt := 0; attempt < 2; attempt++ {
		run.GetLogger().Infof("Retrying HTML rendering with proxy for %s on attempt %d", htmlFilePath, attempt+1)
		opts := append(chromedpBaseOpts, chromedpProxyOpts...)

		renderedHTML, renderErr = func() (string, error) {
			allocCtx, allocCancel := chromedp.NewExecAllocator(context.Background(), opts...)
			defer allocCancel()

			ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithDebugf(run.GetLogger().Infof))
			defer cancel()

			timeoutCtx, timeoutCancel := context.WithTimeout(ctx, time.Second*60)
			defer timeoutCancel()

			fileURL := fmt.Sprintf("file://%s", htmlFilePath)
			var htmlContent string

			// Navigate to the file and capture its rendered HTML
			err := chromedp.Run(timeoutCtx,
				chromedp.Navigate(fileURL),
				chromedp.WaitReady("body", chromedp.ByQuery),
				chromedp.Sleep(10*time.Second), // Allow time for any JavaScript to execute
				chromedp.OuterHTML("html", &htmlContent, chromedp.ByQuery),
			)
			if err != nil {
				run.GetLogger().Errorf("Attempt %d failed to render HTML: %v", attempt+1, err)
				return "", err
			}

			return htmlContent, nil
		}()

		// If successful, save the rendered HTML to a file and return
		if renderErr == nil && renderedHTML != "" {
			if err := os.WriteFile(renderedFilePath, []byte(renderedHTML), 0644); err != nil {
				run.GetLogger().Errorf("Failed to save rendered HTML: %v", err)
				return htmlFilePath, err // Fall back to original file
			}
			run.GetLogger().Infof("Successfully rendered HTML to %s on attempt %d", renderedFilePath, attempt+1)
			return renderedFilePath, nil
		}
	}

	// If we've reached here, all attempts failed
	run.GetLogger().Errorf("All attempts to render HTML failed, falling back to original file: %v", renderErr)
	return htmlFilePath, renderErr
}

// convertAndAddBlocks converts HTML to Lark blocks and adds them to an existing document
func convertAndAddBlocks(run *iris.AgentRunContext, prompt string, htmlFilePath string, documentID string) error {
	// Try up to 3 times to generate and add blocks
	maxRetries := 3
	var lastError error

	// Initialize context for LLM messages
	messages := []*framework.ChatMessage{
		{
			Role:    "system",
			Content: prompt,
		},
	}

	// Render the HTML file using chromedp
	renderedFilePath, renderErr := renderHTMLToFile(run, htmlFilePath)
	defer func() {
		if renderErr == nil {
			err := os.Remove(renderedFilePath)
			if err != nil {
				run.GetLogger().Errorf("Failed to remove rendered HTML file: %v", err)
			}
		}
	}()
	if renderErr != nil {
		run.GetLogger().Warnf("HTML rendering had issues, using original file: %v", renderErr)
		// Continue with original file if rendering failed
	}
	htmlContentBytes, readErr := os.ReadFile(renderedFilePath)
	if readErr != nil {
		run.GetLogger().Errorf("Failed to read rendered HTML file: %v", readErr)
		return readErr
	}
	htmlContent := string(htmlContentBytes)

	// Parse the rendered HTML content
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return err
	}

	bodyHTML, err := doc.Find("body").Html()
	if err != nil {
		return err
	}

	// Add HTML content to messages
	messages = append(messages, &framework.ChatMessage{
		Role:    "user",
		Content: fmt.Sprintf(`HTML content:\n\n%s\n`, bodyHTML),
	})

	// Get model configuration
	model := run.GetConfig().GetModelByScene("mcp_caller")

	for retry := 1; retry <= maxRetries; retry++ {
		run.GetLogger().Infof("Attempt %d/%d to convert HTML and add blocks to document", retry, maxRetries)

		// Call the LLM for block generation
		completion, err := run.GetLLM().ChatCompletion(context.Background(), messages, framework.LLMCompletionOption{
			Model:       model.Model,
			Temperature: model.Temperature,
			MaxTokens:   model.MaxTokens,
		})
		if err != nil {
			run.GetLogger().Errorf("Failed to get chat completion: %v", err)
			lastError = err
			continue
		}

		// Extract Python code from the response
		re := regexp.MustCompile("\\x60\\x60\\x60python\\n((?s).*?)\\x60\\x60\\x60")
		matches := re.FindStringSubmatch(completion.Content)
		if len(matches) < 2 {
			// Try alternative regex if first one doesn't match
			re = regexp.MustCompile("\\x60\\x60\\x60python\\n([\\s\\S]*)")
			matches = re.FindStringSubmatch(completion.Content)
			if len(matches) < 2 {
				run.GetLogger().Errorf("Failed to extract Python code from response")
				// Ask for properly formatted code in next attempt
				messages = append(messages, &framework.ChatMessage{
					Role:    "assistant",
					Content: completion.Content,
				})
				messages = append(messages, &framework.ChatMessage{
					Role:    "user",
					Content: "Your response doesn't contain valid Python code. Please provide your solution inside ```python and ``` blocks.",
				})
				lastError = fmt.Errorf("failed to extract Python code")
				continue
			}
		}

		// Create temporary Python file
		pythonCode := matches[1]
		tempFileName := fmt.Sprintf("lark_doc_python_executor_%s_%d.py", strings.ReplaceAll(time.Now().Format(time.RFC3339), ":", "-"), retry)
		fullCode := generatePythonCode(pythonCode, renderedFilePath)

		if err := os.WriteFile(tempFileName, []byte(fullCode), 0644); err != nil {
			run.GetLogger().Errorf("Failed to create temporary Python file: %v", err)
			lastError = err
			continue
		}

		// Install dependencies
		installCmd := exec.CommandContext(context.Background(), "pip3", "install", "beautifulsoup4")
		if err := installCmd.Run(); err != nil {
			run.GetLogger().Errorf("Failed to install BeautifulSoup: %v", err)
			lastError = err
			continue
		}

		// Execute Python script
		cmd := exec.CommandContext(context.Background(), "python3", tempFileName)
		var stdout, stderr bytes.Buffer
		cmd.Stdout = &stdout
		cmd.Stderr = &stderr

		if err := cmd.Run(); err != nil {
			run.GetLogger().Errorf("Python execution failed: %v\nError: %s", err, stderr.String())
			// Add context for next attempt
			messages = append(messages, &framework.ChatMessage{
				Role:    "assistant",
				Content: completion.Content,
			})
			messages = append(messages, &framework.ChatMessage{
				Role:    "user",
				Content: fmt.Sprintf("The Python code couldn't be executed. Error: %v\n%s\nPlease fix the code.", err, stderr.String()),
			})
			lastError = fmt.Errorf("python execution failed: %v", err)
			continue
		}

		// Parse JSON output
		jsonData := stdout.Bytes()
		var blocks []*DocxBlock
		if err := json.Unmarshal(jsonData, &blocks); err != nil {
			run.GetLogger().Errorf("Invalid JSON format: %v", err)
			messages = append(messages, &framework.ChatMessage{
				Role:    "assistant",
				Content: completion.Content,
			})
			messages = append(messages, &framework.ChatMessage{
				Role:    "user",
				Content: fmt.Sprintf("The Python code generated invalid JSON. Error: %v\nPlease fix the code to generate valid JSON.", err),
			})
			lastError = fmt.Errorf("invalid JSON format: %v", err)
			continue
		}

		// Validate block structure
		validationErrors := validateBlocks(blocks)
		if len(validationErrors) > 0 {
			errorMsg := strings.Join(validationErrors, "\n")
			run.GetLogger().Errorf("Block validation failed:\n%s", errorMsg)
			messages = append(messages, &framework.ChatMessage{
				Role:    "assistant",
				Content: completion.Content,
			})
			messages = append(messages, &framework.ChatMessage{
				Role:    "user",
				Content: fmt.Sprintf("The generated blocks have structural issues:\n%s\nPlease fix the code to generate valid blocks. All table_cell and grid_column blocks must have children, and all children IDs must reference existing blocks.", errorMsg),
			})
			lastError = fmt.Errorf("block validation failed: %s", errorMsg)
			continue
		}

		// Try to add blocks to the document
		err = addBlocksToDocument(run, blocks, renderedFilePath, documentID)
		if err == nil {
			run.GetLogger().Infof("Successfully added blocks to document on attempt %d", retry)
			return nil
		}

		run.GetLogger().Errorf("Failed to add blocks to document: %v", err)
		messages = append(messages, &framework.ChatMessage{
			Role:    "assistant",
			Content: completion.Content,
		})
		messages = append(messages, &framework.ChatMessage{
			Role:    "user",
			Content: fmt.Sprintf("Failed to add blocks to the document: %v\nPlease generate simpler blocks with less complex nesting.", err),
		})
		lastError = err
	}

	return fmt.Errorf("failed to add blocks after %d attempts: %v", maxRetries, lastError)
}

// generatePythonCode creates the full Python script with the model-generated code
func generatePythonCode(modelCode string, htmlFilePath string) string {
	return fmt.Sprintf(`#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uuid
import json

def generate_block_id():
    """生成唯一的block_id"""
    return str(uuid.uuid4()).replace("-", "")[:20]

def create_text_block(block_type, elements, block_id=None):
    """创建文本类型的块
    
    参数:
        block_type: 块的类型
        elements: 元素列表，每个元素都应该是一个包含文本内容的字典
        block_id: 可选的块ID，如果不提供则自动生成
    """
    if block_id is None:
        block_id = generate_block_id()

    return {
        "block_id": block_id,
        "block_type": block_type,
        block_type: {
            "elements": elements
        }
    }

def create_image_block(file_path, chart_id, width=800, height=600, block_id=None):
    if file_path is None and chart_id is None:
        raise ValueError("file_path and chart_id cannot both be None")
    """创建图片块"""
    if block_id is None:
        block_id = generate_block_id()

    return {
        "block_id": block_id,
        "block_type": "image",
        "image": {
            "file_path": file_path,
            "width": width,
            "height": height,
            "chart_id": chart_id,
        }
    }

def create_grid_blocks(column_size, children_ids):
    """创建网格块和网格列块"""
    grid_id = generate_block_id()
    if column_size != len(children_ids):
        return error("分栏列数量和分栏列子项数量不一致")
    
    grid_block = {
        "block_id": grid_id,
        "block_type": "grid",
        "grid": {
            "column_size": column_size
        },
        "children": children_ids
    }
    
    return grid_block

def create_grid_column_block(width_ratio=1, children=None, block_id=None):
    """创建网格列块"""
    if block_id is None:
        block_id = generate_block_id()
    
    column_block = {
        "block_id": block_id,
        "block_type": "grid_column",
        "grid_column": {
            "width_ratio": width_ratio
        }
    }
    
    if children:
        column_block["children"] = children
    
    return column_block

def create_table_blocks(row_size, column_size, children_ids, column_width=None, header_row=None, header_column=None):
    """创建表格块
    
    参数:
        row_size: 行数
        column_size: 列数
        children_ids: 子块ID列表
        column_width: 列宽，单位px，例如 [100, 200, 300]
        header_row: 设置首行为标题行
        header_column: 设置首列为标题列
    """
    table_id = generate_block_id()
    
    # 构建基本属性
    property_dict = {
        "row_size": row_size,
        "column_size": column_size
    }
    
    # 添加可选属性
    if column_width is not None:
        property_dict["column_width"] = column_width
    
    if header_row is not None:
        property_dict["header_row"] = header_row
    
    if header_column is not None:
        property_dict["header_column"] = header_column
    
    table_block = {
        "block_id": table_id,
        "block_type": "table",
        "table": {
            "property": property_dict
        },
        "children": children_ids
    }

    return table_block

def create_table_cell_block(children=None, block_id=None):
    """创建表格单元格块"""
    if block_id is None:
        block_id = generate_block_id()
    
    cell_block = {
        "block_id": block_id,
        "block_type": "table_cell",
        "table_cell": {}
    }
    
    if children:
        cell_block["children"] = children
    
    return cell_block

def create_callout_block(children=None, emoji_id="gift", background_color=1, block_id=None):
    """创建高亮块"""
    if block_id is None:
        block_id = generate_block_id()
    
    callout_block =  {
        "block_id": block_id,
        "block_type": "callout",
        "callout": {
            "background_color": background_color,
        },
    }
    if emoji_id:
        callout_block["callout"]["emoji_id"] = emoji_id
    if children:
        callout_block["children"] = children

    return callout_block

def create_code_block(content, language=1, block_id=None):
    """创建代码块"""
    if block_id is None:
        block_id = generate_block_id()
    
    return {
        "block_id": block_id,
        "block_type": "code",
        "code": {
            "elements": [
                {
                    "text_run": {
                        "content": content
                    }
                }
            ],
            "style": {
                "language": language
            }
        }
    }

# 模型生成代码
%s

from bs4 import BeautifulSoup
import re
def process_html(file_path):
    """从HTML文件中提取内容并转换为飞书文档块"""
    with open(file_path, 'r', encoding='utf-8') as file:
        html_content = file.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = []
    
    # 处理body部分
    body = soup.find('body')
    if body:
        # 寻找body下的所有直接容器元素
        containers = body.find_all(['div', 'main', 'section', 'article'], recursive=False)
        
        # 如果找到了直接的容器元素，先处理它们
        if containers:
            for container in containers:
                # 处理每个容器
                container_results = extract_content(container)
                result.extend(container_results)
        else:
            # 如果没有直接的容器元素，处理body的所有子元素
            for child in body.children:
                result.extend(extract_content(child))
        
        # 如果结果太少，说明可能有深层嵌套，尝试查找所有main标签
        if len(result) < 10:  # 使用一个阈值判断结果是否太少
            # 找到所有main标签，无论嵌套多深
            all_mains = soup.find_all('main')
            if all_mains:
                for main in all_mains:
                    # 处理每个main标签
                    result.extend(extract_content(main))
    
    # 过滤掉没有block_type的非法块
    result = [block for block in result if 'block_type' in block]
    
    return result

# 调用函数并打印结果
result = process_html("%s")
print(json.dumps(result))
`, modelCode, htmlFilePath)
}

// validateBlocks checks blocks for structural validity
func validateBlocks(blocks []*DocxBlock) []string {
	blockIDMap := make(map[string]bool)
	for _, block := range blocks {
		blockIDMap[block.BlockID] = true
	}

	validationErrors := []string{}

	for _, block := range blocks {
		// Check if table_cell or grid_column blocks have children
		if (block.BlockType == "table_cell" || block.BlockType == "grid_column") &&
			(block.Children == nil || len(block.Children) == 0) {
			validationErrors = append(
				validationErrors,
				fmt.Sprintf("Block %s of type %s must have children", block.BlockID, block.BlockType),
			)
		}

		// Check if all children IDs reference existing blocks
		if block.Children != nil {
			for _, childID := range block.Children {
				if !blockIDMap[childID] {
					validationErrors = append(
						validationErrors,
						fmt.Sprintf("Block %s references non-existent child block ID: %s", block.BlockID, childID),
					)
				}
			}
		}

		// Check if grid column_size doesn't exceed 5
		if block.BlockType == "grid" && block.Grid != nil && *block.Grid.ColumnSize > 5 {
			validationErrors = append(
				validationErrors,
				fmt.Sprintf("Block %s of type grid has column_size %d which exceeds maximum of 5", block.BlockID, *block.Grid.ColumnSize),
			)
		}

		// Check if table has exactly row_size * column_size children
		if block.BlockType == "table" && block.Table != nil && block.Table.Property != nil {
			expectedChildren := *block.Table.Property.RowSize * *block.Table.Property.ColumnSize
			if block.Children == nil || len(block.Children) != expectedChildren {
				actualCount := 0
				if block.Children != nil {
					actualCount = len(block.Children)
				}
				validationErrors = append(
					validationErrors,
					fmt.Sprintf("Block %s of type table must have exactly %d children (rows %d × columns %d), but has %d",
						block.BlockID, expectedChildren, block.Table.Property.RowSize, block.Table.Property.ColumnSize, actualCount),
				)
			}
		}
	}

	return validationErrors
}

type ProcessLarkMarkdownFileOptions struct {
	FolderToken      string
	SkipFixMermaid   bool
	SkipFixPlantUml  bool
	CheckMentionUser bool
}

// ProcessLarkMarkdownFile handles the .lark.md file conversion to Lark document
func ProcessLarkMarkdownFile(run *iris.AgentRunContext, markdownFilePath string, title string, opt *ProcessLarkMarkdownFileOptions) (string, []string, error) {
	relatedFiles := make([]string, 0)
	relatedFiles = append(relatedFiles, markdownFilePath)

	// 替换掉文件语法里面放着html的情况, 把html部署之后按[preview](url)的形式引用
	err := UpdateMarkdownWithDeployedHTML(run, markdownFilePath)
	if err != nil {
		run.GetLogger().Errorf("Failed to update markdown with deployed HTML: %v", err)
		return "", relatedFiles, err
	}
	// 替换掉文件里面citation的情况，改成[num](url)的形式
	err = ProcessMarkdownCitations(run, markdownFilePath)
	if err != nil {
		run.GetLogger().Errorf("Failed to process markdown citations: %v", err)
		return "", relatedFiles, err
	}
	// Read .lark.md file content
	content, err := os.ReadFile(markdownFilePath)
	if err != nil {
		run.GetLogger().Errorf("Failed to read markdown file: %v", err)
		return "", relatedFiles, err
	}

	// Check and fix Mermaid code
	if !opt.SkipFixMermaid {
		contentStr := repowiki_agent.CheckAndFixMermaid(run, string(content))
		if contentStr != string(content) {
			content = []byte(contentStr)
			// write back to file
			if err := os.WriteFile(markdownFilePath, content, 0644); err != nil {
				run.GetLogger().Errorf("Failed to write markdown file: %v", err)
			}
		}
	}

	// Use LarkMarkdownParser to parse content
	parser := larkmd.NewLarkMarkdownParser()
	var buf bytes.Buffer
	if err := parser.Convert(content, &buf); err != nil {
		run.GetLogger().Errorf("Failed to parse lark markdown: %v", err)
		return "", relatedFiles, err
	}

	// 直接将buf解析为Lark blocks和媒体tokens
	larkBlocks, mediaTokens, sheetMap, boardMap, err := parseLarkBlocks(run, buf.Bytes(), markdownFilePath)
	if err != nil {
		run.GetLogger().Errorf("Failed to parse lark blocks: %v", err)
		return "", relatedFiles, err // Return document ID even if parsing fails
	}

	// 记录sheet blocks信息
	if len(sheetMap) > 0 {
		run.GetLogger().Infof("Found %d sheet blocks with tokens", len(sheetMap))
	}

	// 记录board blocks信息
	if len(boardMap) > 0 {
		run.GetLogger().Infof("Found %d board blocks with tokens", len(boardMap))
	}

	// 获取文本元素内容的通用方法
	getTextElementContent := func(block *larkdocx.Block) string {
		if block == nil || block.BlockType == nil {
			return ""
		}

		var elements []*larkdocx.TextElement
		switch *block.BlockType {
		case lark.DocxBlockTypeText:
			if block.Text != nil && block.Text.Elements != nil {
				elements = block.Text.Elements
			}
		case lark.DocxBlockTypeHeading1, lark.DocxBlockTypeHeading2, lark.DocxBlockTypeHeading3,
			lark.DocxBlockTypeHeading4, lark.DocxBlockTypeHeading5, lark.DocxBlockTypeHeading6,
			lark.DocxBlockTypeHeading7, lark.DocxBlockTypeHeading8, lark.DocxBlockTypeHeading9:
			// 获取对应的heading元素
			var headingElements []*larkdocx.TextElement
			switch *block.BlockType {
			case lark.DocxBlockTypeHeading1:
				if block.Heading1 != nil && block.Heading1.Elements != nil {
					headingElements = block.Heading1.Elements
				}
			case lark.DocxBlockTypeHeading2:
				if block.Heading2 != nil && block.Heading2.Elements != nil {
					headingElements = block.Heading2.Elements
				}
			case lark.DocxBlockTypeHeading3:
				if block.Heading3 != nil && block.Heading3.Elements != nil {
					headingElements = block.Heading3.Elements
				}
			case lark.DocxBlockTypeHeading4:
				if block.Heading4 != nil && block.Heading4.Elements != nil {
					headingElements = block.Heading4.Elements
				}
			case lark.DocxBlockTypeHeading5:
				if block.Heading5 != nil && block.Heading5.Elements != nil {
					headingElements = block.Heading5.Elements
				}
			case lark.DocxBlockTypeHeading6:
				if block.Heading6 != nil && block.Heading6.Elements != nil {
					headingElements = block.Heading6.Elements
				}
			case lark.DocxBlockTypeHeading7:
				if block.Heading7 != nil && block.Heading7.Elements != nil {
					headingElements = block.Heading7.Elements
				}
			case lark.DocxBlockTypeHeading8:
				if block.Heading8 != nil && block.Heading8.Elements != nil {
					headingElements = block.Heading8.Elements
				}
			case lark.DocxBlockTypeHeading9:
				if block.Heading9 != nil && block.Heading9.Elements != nil {
					headingElements = block.Heading9.Elements
				}
			}
			elements = headingElements
		}

		// 提取所有文本内容
		var content string
		for _, element := range elements {
			if element.TextRun != nil && element.TextRun.Content != nil {
				content += *element.TextRun.Content
			}
		}
		return content
	}

	// 检查第一个块是否与标题相同，如果是则移除
	if len(larkBlocks) > 0 {
		firstBlock := larkBlocks[0]
		if firstBlock.BlockType != nil && (*firstBlock.BlockType == lark.DocxBlockTypeText ||
			(*firstBlock.BlockType >= lark.DocxBlockTypeHeading1 && *firstBlock.BlockType <= lark.DocxBlockTypeHeading9)) {

			content := getTextElementContent(firstBlock)
			// 去除所有空格后比较（包括中间的空格）
			trimmedContent := strings.Join(strings.Fields(content), "")
			trimmedTitle := strings.Join(strings.Fields(title), "")

			if trimmedContent == trimmedTitle {
				run.GetLogger().Infof("Removing first block as it duplicates the title: %s", trimmedTitle)
				larkBlocks = larkBlocks[1:]
			}
		}
	}

	for _, block := range larkBlocks {
		// 检查是否是表格
		// 处理表格宽度
		if block.BlockType != nil && *block.BlockType == lark.DocxBlockTypeTable {
			colSize := block.Table.Property.ColumnSize

			columnWidths := make([]int, *colSize)
			baseWidth := 1000 / *colSize
			if baseWidth < 200 {
				baseWidth = 200
			}
			for i := range columnWidths {
				columnWidths[i] = baseWidth
			}
			block.Table.Property.ColumnWidth = columnWidths
		}
	}

	larkBlocks = processTableCellLinks(run, larkBlocks)

	// 识别根块和子块关系
	var rootBlockIDs []string
	childrenBlockIDs := make(map[string]bool)

	// 收集所有子块ID
	for _, block := range larkBlocks {
		for _, childID := range block.Children {
			childrenBlockIDs[childID] = true
		}
	}

	// 找出根块（不是任何块的子块）
	for _, block := range larkBlocks {
		if !childrenBlockIDs[*block.BlockId] {
			rootBlockIDs = append(rootBlockIDs, *block.BlockId)
		}
	}
	if opt.CheckMentionUser {
		// mentionUser open_id should not cross app, use aime instead of aime-boe for online env
		checkToResetEnv(run, larkBlocks)
	}

	// Get the target folder token for document creation
	folderToken, err := getOrCreateDateFolder(run, opt.FolderToken)
	if err != nil {
		run.GetLogger().Errorf("Failed to get or create date folder: %v", err)
		// Continue with empty folder token (will create in root)
	}

	// First create an empty document
	docxCreateResponse, err := larkClient(run).CreateLarkDocx(context.Background(), folderToken, title, "")
	if err != nil {
		run.GetLogger().Errorf("Failed to create Lark document: %v", err)
		return "", relatedFiles, err
	}
	documentID := *docxCreateResponse.Document.DocumentId
	run.GetLogger().Infof("Created empty Lark document with ID: %s", documentID)

	// 将块插入文档
	insertResult, err := insertBlocksInChunks(
		run,
		documentID,
		rootBlockIDs,
		larkBlocks,
	)
	if err != nil {
		run.GetLogger().Errorf("Failed to insert blocks: %v", err)
		return documentID, relatedFiles, err
	}

	// 处理媒体文件（图片和文件）
	if len(mediaTokens) > 0 {
		mediaRelatedFiles, err := processMediaTokens(run, mediaTokens, insertResult, documentID, markdownFilePath)
		if err != nil {
			run.GetLogger().Errorf("Failed to process media tokens: %v", err)
			// Continue even if media processing fails
		}
		relatedFiles = append(relatedFiles, mediaRelatedFiles...)
	}

	// 处理sheet块
	if len(sheetMap) > 0 {
		err := processSheetTokens(run, sheetMap, insertResult, documentID, markdownFilePath, folderToken)
		if err != nil {
			run.GetLogger().Errorf("Failed to process sheet tokens: %v", err)
			// Continue even if sheet processing fails
		}
	}

	// 处理board块
	if len(boardMap) > 0 {
		err := processBoardTokens(run, boardMap, insertResult, markdownFilePath, opt.SkipFixPlantUml)
		if err != nil {
			run.GetLogger().Errorf("Failed to process board tokens: %v", err)
			// Continue even if board processing fails
		}
	}

	return documentID, relatedFiles, nil
}

// 将 image block 按需升级为 VisActor block
func updatedToVisActorBlock(block *larkdocx.Block, filePath string) bool {
	blockType := *block.BlockType
	if blockType != BlockTypeMap["image"] {
		return false
	}

	// 如果说 file 存在对应的 .lark_visactor_readonly.json 后缀文件，说明是 visactor 图表，采用图表助手
	visActorFilePath := filePath + ".lark_visactor_readonly.json"

	visActorFileInfo, err := os.Stat(visActorFilePath)
	if err != nil {
		return false
	}
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return false
	}
	// If image file is newer than visactor file, that means image file is updated later so
	// we should ignore the out dated visactor file
	if visActorFileInfo.ModTime().Before(fileInfo.ModTime()) {
		return false
	}

	content, err := os.ReadFile(visActorFilePath)
	if err != nil {
		return false
	}

	visActorComponentTypeID := "blk_64df3b277a87c002dafdc52b"
	addOnsBlockType := BlockTypeMap["add_ons"]
	record := string(content)

	block.BlockType = &addOnsBlockType
	block.AddOns = &larkdocx.AddOns{
		ComponentTypeId: &visActorComponentTypeID,
		Record:          &record,
	}
	block.Image = nil
	return true
}

// parseLarkBlocks 直接从解析后的内容中提取Lark文档块和媒体token
func parseLarkBlocks(run *iris.AgentRunContext, parsedContent []byte, markdownFilePath string) ([]*larkdocx.Block, []*MediaToken, map[string]*SheetInfo, map[string]string, error) {
	content := string(parsedContent)
	content = strings.ReplaceAll(content, "<!-- raw HTML omitted -->", "")
	content = strings.TrimSuffix(content, "\n")
	content = strings.TrimSuffix(content, ",")
	content = "[" + content + "]"
	// 尝试直接解析为larkdocx.Block数组
	var blocks []*larkdocx.Block
	if err := json.UnmarshalString(content, &blocks); err != nil {
		run.GetLogger().Errorf("Failed to unmarshal blocks directly: %v", err)
		return nil, nil, nil, nil, err
	}
	var blockMap = make(map[string]*larkdocx.Block)
	for _, block := range blocks {
		blockMap[*block.BlockId] = block
	}

	var (
		addedBlocks   []*larkdocx.Block
		deletedBlocks []*larkdocx.Block
		cellParser    = larkmd.NewTableCellMarkdownParser()
	)
	for i, block := range blocks {
		blockType := *block.BlockType
		if blockType != BlockTypeMap["table_cell"] {
			continue
		}
		// 处理table cell块，重新解析其中的processed_content
		if len(block.Children) > 0 {
			childBlock := blockMap[block.Children[0]]
			if childBlock != nil && childBlock.Text != nil && childBlock.Text.Elements != nil {
				cellMarkdown := *childBlock.Text.Elements[0].TextRun.Content
				var buf bytes.Buffer
				if err := cellParser.Convert([]byte(cellMarkdown), &buf); err != nil {
					run.GetLogger().Errorf("Failed to parse table cell markdown: %v", err)
					continue
				}
				var cellBlocks []*larkdocx.Block
				s := buf.String()
				s = strings.TrimSuffix(s, "\n")
				s = strings.TrimSuffix(s, ",")
				s = strings.ReplaceAll(s, "<!-- raw HTML omitted -->", "")
				s = "[" + s + "]"
				if err := json.UnmarshalString(s, &cellBlocks); err != nil {
					run.GetLogger().Errorf("Failed to unmarshal table cell blocks: %v", err)
					continue
				}
				block.Children = []string{}
				var cellBlockChildrenIDs []string
				for _, cellBlock := range cellBlocks {
					cellBlockChildrenIDs = append(cellBlockChildrenIDs, *&cellBlock.Children...)
				}
				for _, cellBlock := range cellBlocks {
					if !lo.Contains(cellBlockChildrenIDs, *cellBlock.BlockId) {
						block.Children = append(block.Children, *cellBlock.BlockId)
					}
				}
				blocks[i] = block
				addedBlocks = append(addedBlocks, cellBlocks...)
				deletedBlocks = append(deletedBlocks, childBlock)
			}
		} else {
			// 如果子块为空，则添加空文本块
			emptyBlock := larkdocx.NewBlockBuilder().
				BlockId(uuid.New().String()).
				BlockType(2).
				Text(larkdocx.NewTextBuilder().
					Elements([]*larkdocx.TextElement{
						larkdocx.NewTextElementBuilder().
							TextRun(larkdocx.NewTextRunBuilder().
								Content("").
								Build()).
							Build(),
					}).
					Build()).
				Build()
			block.Children = []string{*emptyBlock.BlockId}
			blocks[i] = block
			addedBlocks = append(addedBlocks, emptyBlock)
		}
	}

	blocks = append(blocks, addedBlocks...)
	blocks = lo.Filter(blocks, func(block *larkdocx.Block, _ int) bool {
		return !lo.ContainsBy(deletedBlocks, func(b *larkdocx.Block) bool {
			return *b.BlockId == *block.BlockId
		})
	})

	toString := jsonx.ToString(blocks)
	run.GetLogger().Debugf("Parsed blocks: %s", toString)

	// 收集媒体tokens
	mediaTokens := make([]*MediaToken, 0)
	// 收集sheet tokens
	sheetMap := make(map[string]*SheetInfo)
	// 收集board tokens
	boardMap := make(map[string]string)
	// 收集需要删除的blockID
	blocksToDelete := make(map[string]bool)

	// 辅助函数：检查和搜索文件
	checkAndSearchFile := func(originalPath, blockID, fileType string) (string, bool) {
		filePath := originalPath

		// 如果是相对路径，转换为绝对路径
		if !filepath.IsAbs(filePath) {
			filePath = filepath.Join(filepath.Dir(markdownFilePath), filePath)
		}

		// 检查文件是否存在，如果不存在则搜索
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			fileName := filepath.Base(filePath)
			searchedPath := searchFileInDirectories(run, fileName, markdownFilePath)
			if searchedPath != "" {
				filePath = searchedPath
				run.GetLogger().Infof("Found %s file %s at %s", fileType, fileName, searchedPath)
			} else {
				run.GetLogger().Warnf("%s file not found: %s, marking block %s for deletion", fileType, originalPath, blockID)
				blocksToDelete[blockID] = true
				return "", false
			}
		}
		return filePath, true
	}

	// 遍历Blocks查找需要上传的媒体文件
	for i, block := range blocks {
		if block == nil || block.BlockType == nil || block.BlockId == nil {
			continue
		}
		blockType := *block.BlockType
		blockID := *block.BlockId

		// 处理图片块
		if blockType == BlockTypeMap["image"] && block.Image != nil && block.Image.Token != nil {
			if filePath, found := checkAndSearchFile(*block.Image.Token, blockID, "image"); found {
				// 处理visactor图表，非 visActor 图表才做图片处理
				if !updatedToVisActorBlock(block, filePath) {
					mediaTokens = append(mediaTokens, &MediaToken{
						BlockID:   blockID,
						FilePath:  filePath,
						MediaType: "image",
					})
					blocks[i].Image.Token = nil
				}

			}
		}

		// 处理文件块
		if blockType == BlockTypeMap["file"] && block.File != nil && block.File.Token != nil {
			if filePath, found := checkAndSearchFile(*block.File.Token, blockID, "file"); found {
				mediaTokens = append(mediaTokens, &MediaToken{
					BlockID:   blockID,
					FilePath:  filePath,
					MediaType: "file",
				})
				blocks[i].File.Token = nil
			}
		}

		// 处理sheet块
		if blockType == BlockTypeMap["sheet"] && block.Sheet != nil && block.Sheet.Token != nil {
			if filePath, found := checkAndSearchFile(*block.Sheet.Token, blockID, "sheet"); found {
				// 读取Excel文件获取行列数量
				rowCount, colCount, err := getExcelDimensions(filePath)
				if err != nil || rowCount == 0 || colCount == 0 {
					run.GetLogger().Warnf("Unable to read Excel file %s, marking block %s for deletion: %v", filePath, blockID, err)
					blocksToDelete[blockID] = true
					continue
				}

				// 飞书API限制，创建时行列最大为9
				if rowCount > 9 {
					rowCount = 9
				}
				if colCount > 9 {
					colCount = 9
				}

				sheetMap[blockID] = &SheetInfo{
					FilePath:    filePath,
					RowCount:    rowCount,
					ColumnCount: colCount,
				}

				// 设置block的行列数量
				blocks[i].Sheet = larkdocx.NewSheetBuilder().
					RowSize(rowCount).
					ColumnSize(colCount).
					Build()
			}
		}

		// 处理board块
		if blockType == BlockTypeMap["board"] && block.Board != nil && block.Board.Token != nil {
			boardMap[blockID] = *block.Board.Token
			blocks[i].Board.Token = nil
		}
	}

	// 删除标记的blocks和更新父block的children
	if len(blocksToDelete) > 0 {
		// 从父block的children中删除这些blockID
		for _, block := range blocks {
			if block == nil || block.BlockId == nil {
				continue
			}

			// 过滤掉要删除的children
			newChildren := lo.Filter(block.Children, func(childID string, _ int) bool {
				return !blocksToDelete[childID]
			})
			block.Children = newChildren
		}

		// 从blocks数组中删除这些block
		filteredBlocks := lo.Filter(blocks, func(block *larkdocx.Block, _ int) bool {
			return block != nil && block.BlockId != nil && !blocksToDelete[*block.BlockId]
		})
		blocks = filteredBlocks

		run.GetLogger().Infof("Deleted %d blocks due to missing files", len(blocksToDelete))
	}

	return blocks, mediaTokens, sheetMap, boardMap, nil
}

// MediaToken represents a token for an image or file block
type MediaToken struct {
	BlockID   string
	FilePath  string
	MediaType string // "image" or "file"
}

// SheetInfo represents information about a sheet file
type SheetInfo struct {
	FilePath    string
	RowCount    int
	ColumnCount int
}

// processMediaTokens uploads media (images/files) for the given tokens and updates the blocks
func processMediaTokens(run *iris.AgentRunContext, mediaTokens []*MediaToken, insertResult *lark.DocxBlockDescendantsResponse, documentID, markdownFilePath string) ([]string, error) {
	ctx := context.Background()
	updateRequests := make([]*larkdocx.UpdateBlockRequest, 0)
	temporaryBlockIDToBlockIDMap := makeTemporaryBlockIDMap(insertResult)
	blockMap := make(map[string]*larkdocx.Block)
	for _, block := range insertResult.Children {
		blockMap[*block.BlockId] = block
	}
	failedBlockIDs := make([]string, 0)
	relatedFiles := make([]string, 0)

	for _, mediaToken := range mediaTokens {
		tempBlockID := mediaToken.BlockID
		filePath := mediaToken.FilePath
		mediaType := mediaToken.MediaType

		actualBlockID, ok := temporaryBlockIDToBlockIDMap[tempBlockID]
		if !ok {
			run.GetLogger().Warnf("Could not find actual block ID for temporary block ID %s", tempBlockID)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		// 文件已经在parseLarkBlocks中检查过了，直接使用filePath
		// It's a file path, upload it
		file, err := os.Open(filePath)
		if err != nil {
			run.GetLogger().Errorf("Failed to open media file %s: %v", filePath, err)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		// Get file info for size
		fileInfo, err := file.Stat()
		if err != nil {
			file.Close()
			run.GetLogger().Errorf("Failed to get file info for %s: %v", filePath, err)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		// Upload the media to Lark and get token
		fileName := filepath.Base(filePath)
		size := int(fileInfo.Size())

		// Create upload body
		bodyBuilder := larkdrive.NewUploadAllMediaReqBodyBuilder().
			FileName(fileName).
			Size(size).
			File(file)

		if mediaType == "image" {
			bodyBuilder = bodyBuilder.
				ParentType("docx_image").
				ParentNode(actualBlockID).
				Extra(fmt.Sprintf(`{"drive_route_token":"%s"}`, documentID))
		} else {
			bodyBuilder = bodyBuilder.
				ParentType("docx_file").
				ParentNode(actualBlockID).
				Extra(fmt.Sprintf(`{"drive_route_token":"%s"}`, documentID))
		}

		body := bodyBuilder.Build()

		token, err := larkClient(run).UploadLarkMedia(ctx, body, "")
		file.Close() // Close the file after upload
		if err != nil {
			run.GetLogger().Errorf("Failed to upload media %s: %v", filePath, err)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		if token == nil {
			run.GetLogger().Errorf("Received nil token for media %s", filePath)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		// Create update request based on media type
		if mediaType == "image" {
			width, height, err := getImageResolution(filePath)
			if err != nil {
				run.GetLogger().Warnf("Failed to get image resolution for %s: %v", filePath, err)
				width, height = 800, 600
			}
			updateRequests = append(updateRequests, larkdocx.NewUpdateBlockRequestBuilder().
				BlockId(actualBlockID).
				ReplaceImage(larkdocx.NewReplaceImageRequestBuilder().
					Token(*token).
					Width(width).
					Height(height).
					Build()).
				Build())
		} else if mediaType == "file" {
			updateRequests = append(updateRequests, larkdocx.NewUpdateBlockRequestBuilder().
				BlockId(actualBlockID).
				ReplaceFile(larkdocx.NewReplaceFileRequestBuilder().
					Token(*token).
					Build()).
				Build())
		}
		relatedFiles = append(relatedFiles, filePath)
	}

	// Batch update the blocks
	if len(updateRequests) > 0 {
		// Split updates into batches of 20 to avoid errors with large batch sizes
		batchSize := 20
		for i := 0; i < len(updateRequests); i += batchSize {
			end := i + batchSize
			if end > len(updateRequests) {
				end = len(updateRequests)
			}

			batch := updateRequests[i:end]
			_, err := larkClient(run).BatchUpdateDocxBlocks(ctx, documentID, batch, "")
			if err != nil {
				run.GetLogger().Errorf("Failed to update media blocks (batch %d-%d): %v", i, end-1, err)
				failedBlockIDs = append(failedBlockIDs, lo.Map(batch, func(req *larkdocx.UpdateBlockRequest, _ int) string {
					return *req.BlockId
				})...)
			} else {
				run.GetLogger().Infof("Successfully updated media blocks batch %d-%d", i, end-1)
			}
		}
	}

	// Handle failed blocks by deleting them
	if len(failedBlockIDs) > 0 {
		// 定义失败块信息结构
		type FailedBlockInfo struct {
			blockID  string
			parentID string
			index    int
		}

		var failedBlocks []FailedBlockInfo

		// 1. 从后往前遍历每个块，如果遇到失败的图片块，记录图片块ID和父块ID
		var traverseBlocks func(parentID string, children []*larkdocx.Block, startIndex int)
		traverseBlocks = func(parentID string, children []*larkdocx.Block, startIndex int) {
			// 从后往前遍历
			for i := len(children) - 1; i >= 0; i-- {
				child := children[i]
				if child.BlockId == nil {
					continue
				}

				childBlockID := *child.BlockId
				actualIndex := startIndex + i

				// 检查是否是失败的块
				if lo.Contains(failedBlockIDs, childBlockID) {
					failedBlocks = append(failedBlocks, FailedBlockInfo{
						blockID:  childBlockID,
						parentID: parentID,
						index:    actualIndex,
					})
					run.GetLogger().Infof("Found failed block %s in parent %s at index %d", childBlockID, parentID, actualIndex)
				}

				// 递归处理子块（如果有的话）
				if len(child.Children) > 0 {
					// 查找子块的实际对象
					var childBlocks []*larkdocx.Block
					// 从后往前遍历子块ID
					for i := len(child.Children) - 1; i >= 0; i-- {
						grandChildID := child.Children[i]
						if childBlock, ok := blockMap[grandChildID]; ok {
							childBlocks = append(childBlocks, childBlock)
						}
					}
					if len(childBlocks) > 0 {
						traverseBlocks(childBlockID, childBlocks, 0)
					}
				}
			}
		}

		// 开始遍历文档的直接子块
		traverseBlocks(documentID, insertResult.Children, 0)

		// 2. 遍历失败块列表，删除每个块
		for _, failedBlock := range failedBlocks {
			_, deleteErr := larkClient(run).BatchDeleteDocxBlockChildren(
				ctx,
				documentID,
				failedBlock.parentID,
				failedBlock.index,
				failedBlock.index+1,
				"",
			)
			if deleteErr != nil {
				run.GetLogger().Errorf("Failed to delete failed block %s from parent %s at index %d: %v",
					failedBlock.blockID, failedBlock.parentID, failedBlock.index, deleteErr)
			} else {
				run.GetLogger().Infof("Successfully cleaned up failed block %s from parent %s at index %d",
					failedBlock.blockID, failedBlock.parentID, failedBlock.index)
			}
		}
	}

	return relatedFiles, nil
}

// processSheetTokens processes sheet tokens and updates the corresponding blocks
func processSheetTokens(run *iris.AgentRunContext, sheetMap map[string]*SheetInfo, insertResult *lark.DocxBlockDescendantsResponse, documentID, markdownFilePath, folderToken string) error {
	run.GetLogger().Infof("Processing %d sheet tokens", len(sheetMap))

	// 结果存储map，以blockID为key
	resultMap := make(map[string]string)
	errorMap := make(map[string]error)

	// 不要改并发，可能出现空表
	for blockID, sheetInfo := range sheetMap {
		run.GetLogger().Infof("Processing sheet for block %s: %s (rows: %d, cols: %d)",
			blockID, sheetInfo.FilePath, sheetInfo.RowCount, sheetInfo.ColumnCount)

		// 调用processOfficeFile方法
		title := fmt.Sprintf("Sheet_%s", blockID)
		result, err := processOfficeFile(run, sheetInfo.FilePath, title, "sheet", folderToken)

		if err != nil {
			errorMap[blockID] = err
			run.GetLogger().Errorf("Failed to process sheet for block %s: %v", blockID, err)
		} else {
			resultMap[blockID] = result
			run.GetLogger().Infof("Successfully processed sheet for block %s, result: %s", blockID, result)
		}
	}

	// 记录处理结果
	run.GetLogger().Infof("Sheet processing completed. Success: %d, Errors: %d", len(resultMap), len(errorMap))

	// 如果有错误，返回第一个错误
	if len(errorMap) > 0 {
		for blockID, err := range errorMap {
			run.GetLogger().Errorf("Failed to process sheet for block %s: %v", blockID, err)
		}
	}

	// 新增逻辑：处理sheet数据填充
	if len(resultMap) > 0 {
		// 1. 根据sheetmap里的blockID，在insertResult的relation里，找到actual blockID
		tempToActualBlockIDMap := make(map[string]string)
		for _, relation := range insertResult.BlockIDRelations {
			tempToActualBlockIDMap[relation.TemporaryBlockID] = relation.BlockID
		}

		// 2. 根据insert Result的children，建立actual block ID 和block（blocktype为sheet）的map
		actualBlockIDToSheetBlockMap := make(map[string]*larkdocx.Block)
		for _, block := range insertResult.Children {
			if block.BlockType != nil && *block.BlockType == BlockTypeMap["sheet"] {
				actualBlockIDToSheetBlockMap[*block.BlockId] = block
			}
		}

		// 3. 遍历resultMap，处理每个成功创建的sheet
		for tempBlockID, sheetToken := range resultMap {
			actualBlockID, exists := tempToActualBlockIDMap[tempBlockID]
			if !exists {
				run.GetLogger().Errorf("Could not find actual block ID for temporary block ID %s", tempBlockID)
				continue
			}

			sheetBlock, exists := actualBlockIDToSheetBlockMap[actualBlockID]
			if !exists {
				run.GetLogger().Errorf("Could not find sheet block for actual block ID %s", actualBlockID)
				continue
			}

			// 先调用GetSheetsSubIDs，获取第一个sheetID
			ctx := context.Background()
			subsResp, err := larkClient(run).GetSheetsSubIDs(ctx, sheetToken, "")
			if err != nil {
				run.GetLogger().Errorf("Failed to get sheet sub IDs for token %s: %v", sheetToken, err)
				continue
			}

			if subsResp.Data == nil || len(subsResp.Data.Sheets) == 0 {
				run.GetLogger().Errorf("No sheets found for token %s", sheetToken)
				continue
			}

			// 获取第一个sheetID
			firstSheetID := *subsResp.Data.Sheets[0].SheetId
			run.GetLogger().Infof("Found first sheet ID %s for token %s", firstSheetID, sheetToken)

			// 根据sheetInfo获取读取范围
			sheetInfo := sheetMap[tempBlockID]
			if sheetInfo == nil {
				run.GetLogger().Errorf("Could not find sheet info for block ID %s", tempBlockID)
				continue
			}

			// 调用ReadBatchLarkSheet，获取数据
			sheetData, err := larkClient(run).GetSheetMarkdownContent(ctx, sheetToken, firstSheetID, []string{firstSheetID}, "")
			if err != nil {
				run.GetLogger().Errorf("Failed to read sheet data for token %s: %v", sheetToken, err)
				continue
			}

			if sheetData == nil || len(sheetData.ValueRanges) == 0 {
				run.GetLogger().Errorf("No data found in sheet for token %s", sheetToken)
				continue
			}

			// 获取要填充的sheet block的sheetID
			var (
				targetSheetFileToken string
				targetSheetID        string
			)
			if sheetBlock.Sheet != nil && sheetBlock.Sheet.Token != nil {
				targetSheetFileToken = strings.Split(*sheetBlock.Sheet.Token, "_")[0]
				targetSheetID = strings.Split(*sheetBlock.Sheet.Token, "_")[1]
			}

			if targetSheetFileToken == "" || targetSheetID == "" {
				run.GetLogger().Errorf("No target sheet ID found for block %s", actualBlockID)
				continue
			}

			// 准备写入数据
			var writeValueRanges []*lark.ValueRange
			for _, valueRange := range sheetData.ValueRanges {
				writeValueRanges = append(writeValueRanges, &lark.ValueRange{
					Range:  strings.ReplaceAll(valueRange.Range, firstSheetID, targetSheetID),
					Values: valueRange.Values,
				})
			}

			// 调用WriteBatchLarkSheet方法把数据写进去
			_, err = larkClient(run).BatchUpdateSheetValues(ctx, targetSheetFileToken, targetSheetID, writeValueRanges, "", "")
			if err != nil {
				run.GetLogger().Errorf("Failed to write sheet data for block %s: %v", actualBlockID, err)
				continue
			}

			run.GetLogger().Infof("Successfully filled sheet data for block %s", actualBlockID)

			// 1. 调用QuerySheet方法，获取firstSheetID的信息
			firstSheetInfo, err := larkClient(run).QuerySheet(ctx, sheetToken, "")
			if err != nil {
				run.GetLogger().Errorf("Failed to query first sheet info for block %s: %v", actualBlockID, err)
				continue
			}

			// 2. 判断是否有合并单元格信息，如果有，调用MergeLarkSheetCells方法进行合并
			if firstSheetInfo != nil && len(firstSheetInfo.Sheets) > 0 {
				// 查找对应的sheet
				var sourceSheet *larksheets.Sheet
				for _, sheet := range firstSheetInfo.Sheets {
					if sheet.SheetId != nil && *sheet.SheetId == firstSheetID {
						sourceSheet = sheet
						break
					}
				}

				if sourceSheet != nil && len(sourceSheet.Merges) > 0 {
					// 将MergeRange转换为MergeCell
					mergeCells := make([]*larksheets.MergeCell, 0, len(sourceSheet.Merges))
					for _, mergeRange := range sourceSheet.Merges {
						mergeCell := &larksheets.MergeCell{
							Range: mergeRange,
						}
						mergeCells = append(mergeCells, mergeCell)
					}

					// 调用MergeLarkSheetCells方法对目标sheet进行合并单元格操作
					_, err = larkClient(run).MergeLarkSheetCells(ctx, targetSheetFileToken, targetSheetID, mergeCells, "")
					if err != nil {
						run.GetLogger().Errorf("Failed to merge cells for target sheet %s: %v", targetSheetID, err)
					} else {
						run.GetLogger().Infof("Successfully merged cells for target sheet %s", targetSheetID)
					}
				}
			}
		}
	}

	return nil
}

// makeTemporaryBlockIDMap creates a map of temporary block IDs to actual block IDs
func makeTemporaryBlockIDMap(insertResult *lark.DocxBlockDescendantsResponse) map[string]string {
	result := make(map[string]string)
	for _, relation := range insertResult.BlockIDRelations {
		result[relation.TemporaryBlockID] = relation.BlockID
	}
	return result
}

// RenderHTMLToImages renders multiple HTML files or SVG files using chromedp and saves screenshots
func RenderHTMLToImages(run *iris.AgentRunContext, blockImagesMap map[string]*DocxBlockImage) (map[string]string, error) {
	outputFiles := make(map[string]string)

	// Group blocks by FilePath for chartID cases
	filePathToBlockIDs := make(map[string]map[string]*DocxBlockImage)
	regularBlocks := make(map[string]*DocxBlockImage)

	// Separate chartID blocks (grouped by FilePath) from regular blocks
	for blockID, blockImage := range blockImagesMap {
		if blockImage.ChartID != "" {
			if _, exists := filePathToBlockIDs[blockImage.FilePath]; !exists {
				filePathToBlockIDs[blockImage.FilePath] = make(map[string]*DocxBlockImage)
			}
			filePathToBlockIDs[blockImage.FilePath][blockID] = blockImage
		} else {
			regularBlocks[blockID] = blockImage
		}
	}

	// Process regular blocks (without chartID)
	for blockID, blockImage := range regularBlocks {
		allocCtx, allocCancel := chromedp.NewExecAllocator(context.Background(), chromedpBaseOpts...)
		ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithDebugf(run.GetLogger().Infof))

		func() {
			defer allocCancel()
			defer cancel()

			fileURL := fmt.Sprintf("file://%s", blockImage.FilePath)
			outputPath := fmt.Sprintf("%s.png", blockImage.FilePath)

			var buf []byte
			tasks := []chromedp.Action{
				chromedp.Navigate(fileURL),
			}

			if strings.HasSuffix(strings.ToLower(blockImage.FilePath), ".svg") {
				// For SVG files
				tasks = append(tasks,
					chromedp.WaitReady("svg", chromedp.ByQuery),
					chromedp.Sleep(2*time.Second),
					chromedp.FullScreenshot(&buf, 100),
				)
			} else {
				// For regular HTML files
				tasks = append(tasks,
					chromedp.WaitReady("body", chromedp.ByQuery),
					chromedp.Sleep(10*time.Second),
					chromedp.FullScreenshot(&buf, 100),
				)
			}

			taskCtx, taskCancel := context.WithTimeout(ctx, 20*time.Second)
			defer taskCancel()

			if err := chromedp.Run(taskCtx, tasks...); err != nil {
				run.GetLogger().Errorf("Failed to capture screenshot for %s: %v",
					blockImage.FilePath, err)
				return
			}

			if err := os.WriteFile(outputPath, buf, 0644); err != nil {
				run.GetLogger().Errorf("Failed to save screenshot for %s: %v",
					blockImage.FilePath, err)
				return
			}

			outputFiles[blockID] = outputPath
			run.GetLogger().Infof("Successfully captured screenshot for %s", blockImage.FilePath)
		}()
	}

	// Process chart blocks grouped by FilePath
	for filePath, blocks := range filePathToBlockIDs {
		// Try up to 3 times with proxy
		for attempt := 0; attempt < 3; attempt++ {
			opts := append(chromedpBaseOpts, chromedpProxyOpts...)
			run.GetLogger().Infof("Retrying with proxy for %s on attempt %d", filePath, attempt)

			allocCtx, allocCancel := chromedp.NewExecAllocator(context.Background(), opts...)
			ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithDebugf(run.GetLogger().Infof))

			func() {
				defer allocCancel()
				defer cancel()

				fileURL := fmt.Sprintf("file://%s", filePath)

				// Navigate only once for all charts in this file
				if err := chromedp.Run(ctx, chromedp.Navigate(fileURL)); err != nil {
					return
				}

				// Wait for body to be ready
				if err := chromedp.Run(ctx, chromedp.WaitReady("body", chromedp.ByQuery)); err != nil {
					run.GetLogger().Errorf("Attempt %d failed to wait for body in %s: %v",
						attempt+1, filePath, err)
					return
				}

				for blockID, blockImage := range blocks {
					// 检查当前blockID是否已经成功处理过
					if _, exists := outputFiles[blockID]; exists {
						run.GetLogger().Infof("Skipping already processed chart for blockID %s", blockID)
						continue
					}

					var buf []byte
					selectorID := fmt.Sprintf("#%s", blockImage.ChartID)

					// Create actions for this specific chart
					actions := []chromedp.Action{
						chromedp.WaitVisible(selectorID, chromedp.ByID),
						chromedp.Sleep(5 * time.Second),
						chromedp.ScrollIntoView(selectorID, chromedp.ByID),
						chromedp.Screenshot(selectorID, &buf, chromedp.NodeVisible),
					}

					taskCtx, taskCancel := context.WithTimeout(ctx, 10*time.Second)
					if err := chromedp.Run(taskCtx, actions...); err != nil {
						run.GetLogger().Errorf("Attempt %d failed to capture chart %s in %s: %v",
							attempt+1, blockImage.ChartID, filePath, err)
						taskCancel()
						continue
					}

					outputPath := fmt.Sprintf("%s.png", blockImage.ChartID)
					if err := os.WriteFile(outputPath, buf, 0644); err != nil {
						run.GetLogger().Errorf("Attempt %d failed to save chart %s from %s: %v",
							attempt+1, blockImage.ChartID, filePath, err)
						taskCancel()
						continue
					}

					outputFiles[blockID] = outputPath
					run.GetLogger().Infof("Successfully captured chart %s from %s on attempt %d",
						blockImage.ChartID, filePath, attempt+1)

					taskCancel()
				}

				// If all blocks have been processed, we can break the retry loop
				if allBlocksProcessed(blocks, outputFiles) {
					run.GetLogger().Infof("All charts in %s have been successfully processed, no need for more attempts", filePath)
					return
				}
			}()

			// If all blocks for this file have been processed, we can move to the next file
			if allBlocksProcessed(blocks, outputFiles) {
				break
			}
		}

		// Log any unprocessed blocks after all attempts
		for blockID := range blocks {
			if _, processed := outputFiles[blockID]; !processed {
				run.GetLogger().Errorf("Failed to process chart for blockID %s after all attempts", blockID)
			}
		}
	}

	return outputFiles, nil
}

// allBlocksProcessed checks if all blocks in the given map have been processed
func allBlocksProcessed(blocks map[string]*DocxBlockImage, outputFiles map[string]string) bool {
	for blockID := range blocks {
		if _, exists := outputFiles[blockID]; !exists {
			return false
		}
	}
	return true
}

// processBlockImages updates Lark document image blocks with new images
func processBlockImages(run *iris.AgentRunContext, blockImagesMap map[string]*DocxBlockImage, insertBlockDescendantsResponse *lark.DocxBlockDescendantsResponse, documentID string) error {
	// Create context for all operations
	ctx := context.Background()
	request := make([]*larkdocx.UpdateBlockRequest, 0, len(blockImagesMap))
	failedBlockIDs := make([]string, 0)
	temporaryBlockIDToBlockIDMap := make(map[string]string)
	for _, relation := range insertBlockDescendantsResponse.BlockIDRelations {
		temporaryBlockIDToBlockIDMap[relation.TemporaryBlockID] = relation.BlockID
	}

	needRenderHTML := func(blockImage *DocxBlockImage) bool {
		return strings.HasSuffix(blockImage.FilePath, ".html") || strings.HasSuffix(blockImage.FilePath, ".svg") || blockImage.ChartID != ""
	}

	// process html files to images
	htmlFilesMap := make(map[string]*DocxBlockImage)
	for blockID, blockImage := range blockImagesMap {
		if needRenderHTML(blockImage) {
			htmlFilesMap[blockID] = blockImage
		}
	}

	var (
		htmlToImageFiles map[string]string
		err              error
	)
	if len(htmlFilesMap) > 0 {
		htmlToImageFiles, err = RenderHTMLToImages(run, htmlFilesMap)
		if err != nil {
			run.GetLogger().Errorf("failed to render html to images: %v", err)
		}
	}

	// Process each block and image pair
	for blockID, blockImage := range blockImagesMap {
		actualBlockID := temporaryBlockIDToBlockIDMap[blockID]
		// Open the image file
		var imagePath string
		if needRenderHTML(blockImage) {
			imagePath = htmlToImageFiles[blockID]
		} else {
			imagePath = blockImage.FilePath
		}

		if imagePath == "" {
			run.GetLogger().Errorf("Image path is empty for blockID %s", blockID)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		file, err := os.Open(imagePath)
		if err != nil {
			run.GetLogger().Errorf("Failed to open image file %s: %v", imagePath, err)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		// Get file info for size
		fileInfo, err := file.Stat()
		if err != nil {
			file.Close()
			run.GetLogger().Errorf("Failed to get file info for %s: %v", imagePath, err)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		// Upload the image to Lark and get token
		fileName := filepath.Base(imagePath)
		size := int(fileInfo.Size())

		// Create upload body
		body := larkdrive.NewUploadAllMediaReqBodyBuilder().
			FileName(fileName).
			ParentType("docx_image").
			Size(size).
			File(file).
			ParentNode(actualBlockID).
			Extra(fmt.Sprintf(`{"drive_route_token":"%s"}`, documentID)).
			Build()

		token, err := larkClient(run).UploadLarkMedia(ctx, body, "")
		file.Close() // Close the file after upload
		if err != nil {
			run.GetLogger().Errorf("Failed to upload image %s: %v", imagePath, err)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		if token == nil {
			run.GetLogger().Errorf("Received nil token for image %s", imagePath)
			failedBlockIDs = append(failedBlockIDs, actualBlockID)
			continue
		}

		request = append(request, larkdocx.NewUpdateBlockRequestBuilder().
			BlockId(actualBlockID).
			ReplaceImage(larkdocx.NewReplaceImageRequestBuilder().
				Token(*token).
				Build()).
			Build())
	}

	_, err = larkClient(run).BatchUpdateDocxBlocks(ctx, documentID, request, "")
	if err != nil {
		run.GetLogger().Errorf("Failed to update block images: %v", err)
		return err
	}
	if len(failedBlockIDs) > 0 {
		documentChildren := insertBlockDescendantsResponse.Children
		// Create a map of block IDs to their indices
		childrenIndices := make(map[string]int)
		for i, child := range documentChildren {
			childrenIndices[*child.BlockId] = i
		}

		// Sort failedBlockIDs by their indices in descending order
		sort.Slice(failedBlockIDs, func(i, j int) bool {
			idxI, existsI := childrenIndices[failedBlockIDs[i]]
			idxJ, existsJ := childrenIndices[failedBlockIDs[j]]
			if !existsI {
				return false
			}
			if !existsJ {
				return true
			}
			return idxI > idxJ
		})

		for _, failedBlockID := range failedBlockIDs {
			idx, exists := childrenIndices[failedBlockID]
			if !exists {
				run.GetLogger().Warnf("Failed block ID %s not found in document children", failedBlockID)
				continue
			}

			// Delete the failed block
			_, deleteErr := larkClient(run).BatchDeleteDocxBlockChildren(ctx, documentID, documentID, idx, idx+1, "")
			if deleteErr != nil {
				run.GetLogger().Errorf("Failed to delete failed block %s: %v", failedBlockID, deleteErr)
				// Continue with other blocks even if this one fails
			} else {
				run.GetLogger().Infof("Successfully cleaned up failed block %s", failedBlockID)
			}
		}
	}
	return nil
}

// insertBlocksInChunks inserts blocks into the document in chunks of 500 blocks.
// Each chunk must contain a block and all of its recursive children.
func insertBlocksInChunks(run *iris.AgentRunContext, documentID string, rootBlockIDs []string, allBlocks []*larkdocx.Block) (*lark.DocxBlockDescendantsResponse, error) {
	//当前请求中资源的数目超限，请拆分成多次请求。各类资源上限为：ChatCard 200 张，File 200 个，MentionDoc 200 个，MentionUser 200 个，Image 20 张，ISV 20 个，Sheet 5 篇，Bitable 5 篇。
	const maxBlocksPerRequest = 100
	const maxBoardBlocksPerRequest = 5 // 同一个chunk里最多包含5个board类型的block
	const maxImageBlocksPerRequest = 20
	const maxSheetBlocksPerRequest = 5
	ctx := context.Background()

	// Helper function to count board blocks
	countBlocks := func(blocks []*larkdocx.Block, bType string) int {
		count := 0
		for _, block := range blocks {
			if block != nil && block.BlockType != nil && *block.BlockType == BlockTypeMap[bType] {
				count++
			}
		}
		return count
	}

	// Count total board blocks first
	totalBoardBlocks := countBlocks(allBlocks, "board")
	totalImageBlocks := countBlocks(allBlocks, "image")
	totalSheetBlocks := countBlocks(allBlocks, "sheet")
	run.GetLogger().Infof("Total board blocks: %d, total image blocks: %d, total sheet blocks: %d", totalBoardBlocks, totalImageBlocks, totalSheetBlocks)

	// If total blocks are fewer than limit AND board blocks are within limit, insert them all at once
	if len(allBlocks) <= maxBlocksPerRequest &&
		totalBoardBlocks <= maxBoardBlocksPerRequest &&
		totalImageBlocks <= maxImageBlocksPerRequest &&
		totalSheetBlocks <= maxSheetBlocksPerRequest {
		return larkClient(run).InsertDocxBlockDescendants(
			ctx,
			documentID,
			documentID,
			-1, // Set index to -1 as required
			rootBlockIDs,
			allBlocks,
			"",
		)
	}

	// Build a map of block IDs to their blocks
	blockMap := make(map[string]*larkdocx.Block)
	for _, block := range allBlocks {
		blockMap[*block.BlockId] = block
	}

	// Build a map of block IDs to their children
	childrenMap := make(map[string][]string)
	for _, block := range allBlocks {
		if len(block.Children) > 0 {
			childrenMap[*block.BlockId] = block.Children
		}
	}

	// Get all descendants of a block recursively
	var getAllDescendants func(blockID string, visited map[string]bool) []string
	getAllDescendants = func(blockID string, visited map[string]bool) []string {
		if visited[blockID] {
			return nil
		}
		visited[blockID] = true

		descendants := []string{blockID}
		children, hasChildren := childrenMap[blockID]
		if !hasChildren {
			return descendants
		}

		for _, childID := range children {
			childDescendants := getAllDescendants(childID, visited)
			descendants = append(descendants, childDescendants...)
		}
		return descendants
	}

	// Process root blocks in batches
	// Create a merged result to collect all insertion results
	mergedResult := &lark.DocxBlockDescendantsResponse{
		BlockIDRelations: []struct {
			BlockID          string `json:"block_id"`
			TemporaryBlockID string `json:"temporary_block_id"`
		}{},
		Children:           []*larkdocx.Block{},
		DocumentRevisionID: 0,
		ClientToken:        "",
	}
	run.GetLogger().Infof("Beginning block insertion process with %d blocks in chunks of %d (max %d board blocks per chunk)", len(allBlocks), maxBlocksPerRequest, maxBoardBlocksPerRequest)

	currentRootIDs := make([]string, 0)
	currentBlockIDs := make([]string, 0)
	currentBlocks := make([]*larkdocx.Block, 0)

	for _, rootID := range rootBlockIDs {
		// Get all descendants of this root block
		visited := make(map[string]bool)
		blockIDsToAdd := getAllDescendants(rootID, visited)

		// Build blocks to add for this root
		blocksToAdd := make([]*larkdocx.Block, 0, len(blockIDsToAdd))
		for _, blockID := range blockIDsToAdd {
			if block, exists := blockMap[blockID]; exists {
				blocksToAdd = append(blocksToAdd, block)
			}
		}

		// Calculate board blocks in blocks to add
		boardBlocksToAdd := countBlocks(blocksToAdd, "board")
		currentBoardBlocks := countBlocks(currentBlocks, "board")
		imageBlocksToAdd := countBlocks(blocksToAdd, "image")
		currentImageBlocks := countBlocks(currentBlocks, "image")
		sheetBlocksToAdd := countBlocks(blocksToAdd, "sheet")
		currentSheetBlocks := countBlocks(currentBlocks, "sheet")

		// Check if adding this root and its descendants would exceed either limit
		exceedsBlockLimit := len(currentBlocks)+len(blockIDsToAdd) > maxBlocksPerRequest
		exceedsBoardLimit := currentBoardBlocks+boardBlocksToAdd > maxBoardBlocksPerRequest
		exceedsImageLimit := currentImageBlocks+imageBlocksToAdd > maxImageBlocksPerRequest
		exceedsSheetLimit := currentSheetBlocks+sheetBlocksToAdd > maxSheetBlocksPerRequest

		if (exceedsBlockLimit || exceedsBoardLimit || exceedsImageLimit || exceedsSheetLimit) && len(currentBlocks) > 0 {
			// Submit the current batch
			result, err := larkClient(run).InsertDocxBlockDescendants(
				ctx,
				documentID,
				documentID,
				-1, // Set index to -1 as required
				currentRootIDs,
				currentBlocks,
				"",
			)
			if err != nil {
				return nil, fmt.Errorf("failed to insert block chunk: %v", err)
			}

			// Merge the result with our merged result
			mergedResult.BlockIDRelations = append(mergedResult.BlockIDRelations, result.BlockIDRelations...)
			mergedResult.Children = append(mergedResult.Children, result.Children...)
			mergedResult.DocumentRevisionID = result.DocumentRevisionID // Use the latest revision ID
			if result.ClientToken != "" {
				mergedResult.ClientToken = result.ClientToken
			}

			// Reset current collections
			currentRootIDs = make([]string, 0)
			currentBlockIDs = make([]string, 0)
			currentBlocks = make([]*larkdocx.Block, 0)
		}

		// Add this root and its descendants to the current batch
		currentRootIDs = append(currentRootIDs, rootID)
		for _, blockID := range blockIDsToAdd {
			// Avoid duplicates
			if !gslice.Contains(currentBlockIDs, blockID) {
				currentBlockIDs = append(currentBlockIDs, blockID)
				currentBlocks = append(currentBlocks, blockMap[blockID])
			}
		}
	}

	// Insert any remaining blocks
	if len(currentBlocks) > 0 {
		finalBoardBlocks := countBlocks(currentBlocks, "board")
		finalImageBlocks := countBlocks(currentBlocks, "image")
		finalSheetBlocks := countBlocks(currentBlocks, "sheet")
		run.GetLogger().Infof("Final batch has %d blocks (%d board blocks, %d image blocks, %d sheet blocks)",
			len(currentBlocks), finalBoardBlocks, finalImageBlocks, finalSheetBlocks)

		result, err := larkClient(run).InsertDocxBlockDescendants(
			ctx,
			documentID,
			documentID,
			-1, // Set index to -1 as required
			currentRootIDs,
			currentBlocks,
			"",
		)
		if err != nil {
			return nil, fmt.Errorf("failed to insert final block chunk: %v", err)
		}

		// Merge the final result with our merged result
		run.GetLogger().Infof("Merging final insertion result with %d block ID relations and %d children",
			len(result.BlockIDRelations), len(result.Children))
		mergedResult.BlockIDRelations = append(mergedResult.BlockIDRelations, result.BlockIDRelations...)
		mergedResult.Children = append(mergedResult.Children, result.Children...)
		mergedResult.DocumentRevisionID = result.DocumentRevisionID // Use the latest revision ID
		if result.ClientToken != "" {
			mergedResult.ClientToken = result.ClientToken
		}
	}

	run.GetLogger().Infof("Finished merging all insertion results. Final result has %d block ID relations and %d children",
		len(mergedResult.BlockIDRelations), len(mergedResult.Children))

	return mergedResult, nil
}

// addBlocksToDocument adds blocks to an existing Lark document
func addBlocksToDocument(run *iris.AgentRunContext, blocks []*DocxBlock, htmlFilePath string, documentID string) error {
	imageBlocks := make(map[string]*DocxBlockImage) // Map of blockID to image file path
	allBlocks := make(map[string]*larkdocx.Block)
	childrenBlockIDs := make(map[string]bool)

	for _, block := range blocks {
		// Convert string block type to int
		blockTypeInt, ok := BlockTypeMap[block.BlockType]
		if !ok {
			blockTypeInt = BlockTypeMap["undefined"]
		}

		// Track all children blockIDs
		if block.Children != nil {
			for _, childID := range block.Children {
				childrenBlockIDs[childID] = true
			}
		}

		larkBlock := &larkdocx.Block{
			BlockId:   &block.BlockID,
			Children:  block.Children,
			BlockType: &blockTypeInt,
		}

		// Copy the appropriate field based on block type
		switch block.BlockType {
		case "page":
			larkBlock.Page = block.Page
		case "text":
			larkBlock.Text = block.Text
		case "heading1":
			larkBlock.Heading1 = block.Heading1
		case "heading2":
			larkBlock.Heading2 = block.Heading2
		case "heading3":
			larkBlock.Heading3 = block.Heading3
		case "heading4":
			larkBlock.Heading4 = block.Heading4
		case "heading5":
			larkBlock.Heading5 = block.Heading5
		case "heading6":
			larkBlock.Heading6 = block.Heading6
		case "heading7":
			larkBlock.Heading7 = block.Heading7
		case "heading8":
			larkBlock.Heading8 = block.Heading8
		case "heading9":
			larkBlock.Heading9 = block.Heading9
		case "bullet":
			larkBlock.Bullet = block.Bullet
		case "ordered":
			larkBlock.Ordered = block.Ordered
		case "code":
			larkBlock.Code = block.Code
		case "quote":
			larkBlock.Quote = block.Quote
		case "equation":
			larkBlock.Equation = block.Equation
		case "todo":
			larkBlock.Todo = block.Todo
		case "bitable":
			larkBlock.Bitable = block.Bitable
		case "callout":
			larkBlock.Callout = block.Callout
		case "image":
			// Adjust image file path to be relative to the HTML file directory
			if block.Image != nil && block.Image.FilePath != "" && !filepath.IsAbs(block.Image.FilePath) {
				htmlDir := filepath.Dir(htmlFilePath)
				// Join the HTML directory with the relative image path
				block.Image.FilePath = filepath.Join(htmlDir, block.Image.FilePath)
			}
			if block.Image != nil && block.Image.ChartID != "" {
				block.Image.FilePath = htmlFilePath
			}
			// Store image block for later processing (both FilePath and ChartID cases)
			imageBlocks[block.BlockID] = block.Image
			larkBlock.Image = &larkdocx.Image{}
		case "grid":
			larkBlock.Grid = block.Grid
		case "grid_column":
			larkBlock.GridColumn = block.GridColumn
		case "table":
			larkBlock.Table = block.Table
		case "table_cell":
			larkBlock.TableCell = block.TableCell
		case "divider":
			larkBlock.Divider = block.Divider
		}

		allBlocks[block.BlockID] = larkBlock
	}

	nonChildrenBlockIDs := make([]string, 0)
	for _, blk := range blocks {
		if !childrenBlockIDs[blk.BlockID] {
			nonChildrenBlockIDs = append(nonChildrenBlockIDs, blk.BlockID)
		}
	}

	// Insert blocks as descendants of the document
	insertBlockDescendantsResponse, err := insertBlocksInChunks(
		run,
		documentID,
		nonChildrenBlockIDs,
		lo.Values(allBlocks),
	)
	if err != nil {
		return fmt.Errorf("failed to insert block descendants: %v", err)
	}

	if len(imageBlocks) > 0 {
		err := processBlockImages(run, imageBlocks, insertBlockDescendantsResponse, documentID)
		if err != nil {
			run.GetLogger().Errorf("failed to process block images: %v", err)
			// Continue even if image processing fails
		}
	}

	return nil
}

// BlockTypeMap maps JSON type keywords to their enum values
var BlockTypeMap = map[string]int{
	"page":                1,
	"text":                2,
	"heading1":            3,
	"heading2":            4,
	"heading3":            5,
	"heading4":            6,
	"heading5":            7,
	"heading6":            8,
	"heading7":            9,
	"heading8":            10,
	"heading9":            11,
	"bullet":              12,
	"ordered":             13,
	"code":                14,
	"quote":               15,
	"todo":                17,
	"bitable":             18,
	"callout":             19,
	"chat_card":           20,
	"diagram":             21,
	"divider":             22,
	"file":                23,
	"grid":                24,
	"grid_column":         25,
	"iframe":              26,
	"image":               27,
	"isv":                 28,
	"mindnote":            29,
	"sheet":               30,
	"table":               31,
	"table_cell":          32,
	"view":                33,
	"quote_container":     34,
	"task":                35,
	"okr":                 36,
	"okr_objective":       37,
	"okr_key_result":      38,
	"okr_progress":        39,
	"add_ons":             40,
	"jira_issue":          41,
	"wiki_catalog":        42,
	"board":               43,
	"agenda":              44,
	"agenda_item":         45,
	"agenda_item_title":   46,
	"agenda_item_content": 47,
	"link_preview":        48,
	"undefined":           999,
}

// 获取图像的分辨率
func getImageResolution(filePath string) (int, int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, 0, err
	}
	defer file.Close()

	// 只读取图片头部信息，不解码像素数据，避免大图片的内存占用问题
	config, _, err := image.DecodeConfig(file)
	if err != nil {
		return 0, 0, err
	}
	return config.Width, config.Height, nil
}

// processTableCellLinks merges link blocks at the end of table cells into preceding text blocks
func processTableCellLinks(run *iris.AgentRunContext, blocks []*larkdocx.Block) []*larkdocx.Block {
	// Build a map of block IDs to blocks for quick lookup
	blockMap := make(map[string]*larkdocx.Block)
	for _, block := range blocks {
		if block.BlockId != nil {
			blockMap[*block.BlockId] = block
		}
	}

	needRemovedBlockIDs := make([]string, 0)
	newTextBlocks := make([]*larkdocx.Block, 0)

	// Only process table cell blocks
	for _, cellBlock := range blocks {
		if cellBlock.BlockType == nil || *cellBlock.BlockType != lark.DocxBlockTypeTableCell {
			continue
		}

		// Table cell must have at least two children to process
		if len(cellBlock.Children) < 2 {
			continue
		}

		// Collect all [number] link blocks and their elements
		var linkElements []*larkdocx.TextElement
		var linkBlockIDs []string

		for _, childID := range cellBlock.Children {
			childBlock, ok := blockMap[childID]
			if !ok || childBlock.BlockType == nil || *childBlock.BlockType != lark.DocxBlockTypeText ||
				childBlock.Text == nil || childBlock.Text.Elements == nil {
				continue
			}

			// Check if this block contains [number] format links
			if areAllElementsLinks(childBlock.Text.Elements) && isNumberInBrackets(childBlock.Text.Elements) {
				linkElements = append(linkElements, childBlock.Text.Elements...)
				linkBlockIDs = append(linkBlockIDs, childID)
			}
		}

		// If no link blocks found, continue to next cell
		if len(linkBlockIDs) == 0 {
			continue
		}

		// Remove duplicate links by URL
		deduplicatedElements := deduplicateLinkElements(linkElements)

		// Mark all collected link blocks for removal
		needRemovedBlockIDs = append(needRemovedBlockIDs, linkBlockIDs...)

		// Find all consecutive blocks in the cell's children
		type BlockGroup struct {
			TextBefore string   // ID of text block before link group
			LinkIDs    []string // IDs of consecutive link blocks
			TextAfter  string   // ID of text block after link group
		}

		var blockGroups []BlockGroup
		currentGroup := BlockGroup{}

		// 预处理：标记所有的链接块（为了快速查找）
		linkBlockIDsMap := make(map[string]bool)
		for _, id := range linkBlockIDs {
			linkBlockIDsMap[id] = true
		}

		// 遍历所有子块，识别链接块组和它们前后的文本块
		inLinkGroup := false
		for i, childID := range cellBlock.Children {
			isLinkBlock := linkBlockIDsMap[childID]

			// 判断当前块是否是文本块
			childBlock, ok := blockMap[childID]
			isTextBlock := ok && childBlock.BlockType != nil && *childBlock.BlockType == lark.DocxBlockTypeText

			// 处理链接块组的开始
			if isLinkBlock && !inLinkGroup {
				inLinkGroup = true
				// 设置链接组前的文本块（如果有）
				if i > 0 {
					prevID := cellBlock.Children[i-1]
					prevBlock, ok := blockMap[prevID]
					if ok && prevBlock.BlockType != nil && *prevBlock.BlockType == lark.DocxBlockTypeText {
						currentGroup.TextBefore = prevID
					}
				}
				currentGroup.LinkIDs = append(currentGroup.LinkIDs, childID)
			} else if isLinkBlock && inLinkGroup {
				// 处理链接块组内的链接块
				currentGroup.LinkIDs = append(currentGroup.LinkIDs, childID)
			} else if !isLinkBlock && inLinkGroup {
				// 处理链接块组的结束
				inLinkGroup = false
				// 设置链接组后的文本块（如果有）
				if isTextBlock {
					currentGroup.TextAfter = childID
				}
				// 保存当前组并开始新组
				blockGroups = append(blockGroups, currentGroup)
				currentGroup = BlockGroup{}
			}
		}

		// 处理最后一个可能未完成的组
		if inLinkGroup && len(currentGroup.LinkIDs) > 0 {
			blockGroups = append(blockGroups, currentGroup)
		}

		// 处理每个识别出的组
		for _, group := range blockGroups {
			// 如果组前后都有文本块，检查它们是否可以合并
			if group.TextBefore != "" && group.TextAfter != "" {
				prevBlock := blockMap[group.TextBefore]
				nextBlock := blockMap[group.TextAfter]

				// 确保两个块都是有效的文本块
				if prevBlock != nil && nextBlock != nil &&
					prevBlock.Text != nil && nextBlock.Text != nil &&
					prevBlock.Text.Elements != nil && nextBlock.Text.Elements != nil {

					// 检查文本块的样式是否相同
					if haveEqualStyles(prevBlock.Text.Elements, nextBlock.Text.Elements) {
						// 合并文本块
						prevBlock.Text.Elements = append(prevBlock.Text.Elements, nextBlock.Text.Elements...)

						// 标记后一个文本块为需要移除
						needRemovedBlockIDs = append(needRemovedBlockIDs, group.TextAfter)

						run.GetLogger().Infof("Merged text blocks %s and %s around link blocks",
							group.TextBefore, group.TextAfter)
					}
				}
			}
		}

		// Remove link blocks and merged blocks from the cell's children
		var newChildren []string
		for _, childID := range cellBlock.Children {
			if !lo.Contains(needRemovedBlockIDs, childID) {
				newChildren = append(newChildren, childID)
			}
		}
		cellBlock.Children = newChildren

		// Find the last text block in the cell
		var lastTextBlockID string
		for i := len(cellBlock.Children) - 1; i >= 0; i-- {
			childID := cellBlock.Children[i]
			childBlock, ok := blockMap[childID]
			if ok && childBlock.BlockType != nil && *childBlock.BlockType == lark.DocxBlockTypeText {
				lastTextBlockID = childID
				break
			}
		}

		if lastTextBlockID != "" {
			// If found a last text block, append links to it
			lastTextBlock := blockMap[lastTextBlockID]
			lastTextBlock.Text.Elements = append(lastTextBlock.Text.Elements, deduplicatedElements...)
			run.GetLogger().Infof("Appended %d deduplicated link elements to existing text block", len(deduplicatedElements))
		} else {
			// Create a new text block with the links
			newBlockID := uuid.New().String()
			newTextBlock := larkdocx.NewBlockBuilder().
				BlockId(newBlockID).
				BlockType(lark.DocxBlockTypeText).
				Text(larkdocx.NewTextBuilder().
					Elements(deduplicatedElements).
					Build()).
				Build()

			// Add the new block to our list of blocks to add
			newTextBlocks = append(newTextBlocks, newTextBlock)

			// Add reference to the new block in the cell's children
			cellBlock.Children = append(cellBlock.Children, newBlockID)
			run.GetLogger().Infof("Created new text block with %d deduplicated link elements", len(deduplicatedElements))
		}
	}

	// Remove blocks marked for deletion
	blocks = lo.Filter(blocks, func(item *larkdocx.Block, index int) bool {
		return !lo.Contains(needRemovedBlockIDs, *item.BlockId)
	})

	// Add new text blocks
	blocks = append(blocks, newTextBlocks...)

	return blocks
}

// haveEqualStyles checks if two sets of text elements have the same style
func haveEqualStyles(elements1, elements2 []*larkdocx.TextElement) bool {
	if len(elements1) == 0 || len(elements2) == 0 {
		return false
	}

	// Compare styles of the first element in each array
	// This is a simplified approach that could be enhanced for more complex cases
	elem1 := elements1[0]
	elem2 := elements2[0]

	if elem1.TextRun == nil || elem2.TextRun == nil {
		return false
	}

	if elem1.TextRun.TextElementStyle == nil && elem2.TextRun.TextElementStyle == nil {
		return true
	}

	if (elem1.TextRun.TextElementStyle == nil && elem2.TextRun.TextElementStyle != nil) ||
		(elem1.TextRun.TextElementStyle != nil && elem2.TextRun.TextElementStyle == nil) {
		return false
	}

	style1 := elem1.TextRun.TextElementStyle
	style2 := elem2.TextRun.TextElementStyle

	// Check basic style attributes
	if (style1.Bold != nil && style2.Bold == nil) || (style1.Bold == nil && style2.Bold != nil) ||
		(style1.Bold != nil && style2.Bold != nil && *style1.Bold != *style2.Bold) {
		return false
	}

	if (style1.Italic != nil && style2.Italic == nil) || (style1.Italic == nil && style2.Italic != nil) ||
		(style1.Italic != nil && style2.Italic != nil && *style1.Italic != *style2.Italic) {
		return false
	}

	if (style1.Strikethrough != nil && style2.Strikethrough == nil) || (style1.Strikethrough == nil && style2.Strikethrough != nil) ||
		(style1.Strikethrough != nil && style2.Strikethrough != nil && *style1.Strikethrough != *style2.Strikethrough) {
		return false
	}

	if (style1.Underline != nil && style2.Underline == nil) || (style1.Underline == nil && style2.Underline != nil) ||
		(style1.Underline != nil && style2.Underline != nil && *style1.Underline != *style2.Underline) {
		return false
	}

	// Add more style checks as needed

	return true
}

// deduplicateLinkElements removes duplicate link elements based on URL
func deduplicateLinkElements(elements []*larkdocx.TextElement) []*larkdocx.TextElement {
	// Map to track unique URLs
	seenURLs := make(map[string]bool)
	var uniqueElements []*larkdocx.TextElement

	for _, element := range elements {
		if element.TextRun == nil ||
			element.TextRun.TextElementStyle == nil ||
			element.TextRun.TextElementStyle.Link == nil ||
			element.TextRun.TextElementStyle.Link.Url == nil {
			continue
		}

		url := *element.TextRun.TextElementStyle.Link.Url
		// Skip if we've seen this URL before
		if seenURLs[url] {
			continue
		}

		seenURLs[url] = true
		uniqueElements = append(uniqueElements, element)
	}

	return uniqueElements
}

// isNumberInBrackets checks if all text elements in a list are links with [number] format
func isNumberInBrackets(elements []*larkdocx.TextElement) bool {
	if len(elements) == 0 {
		return false
	}

	for _, element := range elements {
		if element.TextRun == nil || element.TextRun.Content == nil {
			return false
		}

		content := *element.TextRun.Content
		// Check if content matches [number] pattern using regex
		matched, _ := regexp.MatchString(`^\[\d+\]$`, content)
		if !matched {
			return false
		}
	}

	// All elements are links with [number] format
	return true
}

// areAllElementsLinks checks if all text elements in a list are links
func areAllElementsLinks(elements []*larkdocx.TextElement) bool {
	if len(elements) == 0 {
		return false
	}

	for _, element := range elements {
		if element.TextRun == nil ||
			element.TextRun.TextElementStyle == nil ||
			element.TextRun.TextElementStyle.Link == nil ||
			element.TextRun.TextElementStyle.Link.Url == nil ||
			*element.TextRun.TextElementStyle.Link.Url == "" {
			// Found a non-link element
			return false
		}
	}

	// All elements are links
	return true
}

// searchFileInDirectories searches for a file in workspace and markdownFilePath directories up to 2 levels deep
func searchFileInDirectories(run *iris.AgentRunContext, fileName, markdownFilePath string) string {
	// 获取workspace目录
	workspaceDir := os.Getenv(entity.RuntimeEnvironWorkspacePath)

	// 获取markdownFilePath的目录
	markdownDir := filepath.Dir(markdownFilePath)

	// 搜索目录列表
	searchDirs := []string{}

	// 添加workspace目录（如果存在且不为空）
	if workspaceDir != "" {
		if _, err := os.Stat(workspaceDir); err == nil {
			searchDirs = append(searchDirs, workspaceDir)
		}
	}

	// 添加markdown文件目录（如果存在且不同于workspace目录）
	if markdownDir != "" && markdownDir != workspaceDir {
		if _, err := os.Stat(markdownDir); err == nil {
			searchDirs = append(searchDirs, markdownDir)
		}
	}

	// 在每个目录中搜索文件（最多2层深度）
	for _, dir := range searchDirs {
		if foundPath := searchFileInDirectory(dir, fileName, 2); foundPath != "" {
			return foundPath
		}
	}

	return ""
}

// searchFileInDirectory recursively searches for a file in a directory up to maxDepth levels
func searchFileInDirectory(dir, fileName string, maxDepth int) string {
	if maxDepth < 0 {
		return ""
	}

	// 检查当前目录中是否存在目标文件
	targetPath := filepath.Join(dir, fileName)
	if _, err := os.Stat(targetPath); err == nil {
		return targetPath
	}

	// 如果还有剩余深度，递归搜索子目录
	if maxDepth > 0 {
		entries, err := os.ReadDir(dir)
		if err != nil {
			return ""
		}

		for _, entry := range entries {
			if entry.IsDir() {
				subDir := filepath.Join(dir, entry.Name())
				if foundPath := searchFileInDirectory(subDir, fileName, maxDepth-1); foundPath != "" {
					return foundPath
				}
			}
		}
	}

	return ""
}

func uploadRelatedFiles(run *iris.AgentRunContext, relatedFiles []string, title string) error {
	if len(relatedFiles) > 0 {
		run.GetLogger().Infof("Upload markdown related files: %v", relatedFiles)
		artifactKey := fmt.Sprintf("%s-%s", nextentity.ArtifactKeyFileLarkMD, uuid.NewString())
		artifactService := run.GetArtifactService()
		artifact, err := artifactService.NewFileArtifactWithKey(run, artifactKey, nextentity.FileArtifactTypeMetadata{
			Title: title,
		})
		if err != nil {
			return err
		}

		fileReaders := make([]iris.ArtifactFileReader, 0, len(relatedFiles))
		for _, filePath := range relatedFiles {
			fileInfo, err := os.Stat(filePath)
			if err != nil {
				run.GetLogger().Warnf("File does not exist, skipping: %s", filePath)
				continue
			}

			file, err := os.Open(filePath)
			if err != nil {
				run.GetLogger().Warnf("Failed to open file %s: %v", filePath, err)
				continue
			}

			fileReaders = append(fileReaders, iris.ArtifactFileReader{
				Path:   filePath,
				Reader: file,
				Size:   fileInfo.Size(),
			})
		}

		if len(fileReaders) > 0 {
			err = artifactService.UploadFilesStream(run, artifact, fileReaders)

			for _, fr := range fileReaders {
				if closer, ok := fr.Reader.(io.Closer); ok {
					closer.Close()
				}
			}

			if err != nil {
				run.GetLogger().Errorf("Failed to upload artifact files: %v", err)
			} else {
				run.GetLogger().Infof("Successfully uploaded %d related files to artifact", len(fileReaders))
			}
			return err
		}
	}
	return nil
}

// getExcelDimensions reads an Excel file and returns its row and column count
func getExcelDimensions(filePath string) (int, int, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return 0, 0, err
	}
	defer f.Close()

	// Get the list of sheet names
	sheetNames := f.GetSheetList()
	if len(sheetNames) == 0 {
		return 0, 0, fmt.Errorf("no sheets found in Excel file")
	}

	// Use the first sheet
	sheetName := sheetNames[0]

	// Get all rows
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return 0, 0, err
	}

	if len(rows) == 0 {
		return 0, 0, nil
	}

	// Find the maximum number of columns
	maxCols := 0
	for _, row := range rows {
		if len(row) > maxCols {
			maxCols = len(row)
		}
	}

	return len(rows), maxCols, nil
}

// processBoardTokens processes board tokens and updates the corresponding blocks with PlantUML nodes
func processBoardTokens(run *iris.AgentRunContext, boardMap map[string]string, insertResult *lark.DocxBlockDescendantsResponse, markdownFilePath string, skipFixPlantUml bool) error {
	if len(boardMap) == 0 {
		return nil
	}

	run.GetLogger().Infof("Processing %d board tokens", len(boardMap))
	ctx := context.Background()

	// 创建临时块ID到真实块ID的映射
	tempToActualBlockIDMap := makeTemporaryBlockIDMap(insertResult)

	// 创建真实块ID到board块的映射
	actualBlockIDToBoardBlockMap := make(map[string]*larkdocx.Block)
	for _, block := range insertResult.Children {
		if block.BlockType != nil && *block.BlockType == BlockTypeMap["board"] {
			actualBlockIDToBoardBlockMap[*block.BlockId] = block
		}
	}

	// 处理每个board token
	for tempBlockID, plantUMLCode := range boardMap {
		// 去掉PlantUML代码结尾的换行符
		plantUMLCode = strings.TrimSpace(plantUMLCode)
		// 获取真实块ID
		actualBlockID, exists := tempToActualBlockIDMap[tempBlockID]
		if !exists {
			run.GetLogger().Errorf("Could not find actual block ID for temporary block ID %s", tempBlockID)
			continue
		}

		// 获取board块
		boardBlock, exists := actualBlockIDToBoardBlockMap[actualBlockID]
		if !exists {
			run.GetLogger().Errorf("Could not find board block for actual block ID %s", actualBlockID)
			continue
		}

		// 获取画板ID
		if boardBlock.Board == nil || boardBlock.Board.Token == nil {
			run.GetLogger().Errorf("Board block %s has no token", actualBlockID)
			continue
		}

		whiteboardID := *boardBlock.Board.Token
		run.GetLogger().Infof("Processing PlantUML for board block %s, whiteboard ID: %s", actualBlockID, whiteboardID)

		// 尝试创建PlantUML节点
		var lastError error
		success := false

		// 先尝试StyleType=1 (Board)
		req := &lark.CreateWhiteboardPlantUMLNodeRequest{
			PlantUMLCode: plantUMLCode,
			StyleType:    1, // Board
			SyntaxType:   1,
			DiagramType:  0,
		}

		run.GetLogger().Infof("Attempting to create PlantUML node for block %s with StyleType=1, DiagramType=0",
			actualBlockID)

		_, err := larkClient(run).CreateWhiteboardPlantUMLNode(ctx, whiteboardID, req)
		if err == nil {
			run.GetLogger().Infof("Successfully created PlantUML node for block %s with StyleType=1, DiagramType=0",
				actualBlockID)
			success = true
		} else {
			run.GetLogger().Warnf("Failed to create PlantUML node for block %s with StyleType=1, DiagramType=0: %v",
				actualBlockID, err)
			lastError = err

			// 如果StyleType=1失败了，先尝试使用LLM修复PlantUML代码（如果允许的话）
			if !skipFixPlantUml {
				const maxLLMRetries = 3

				// 最多重试3次LLM修复，只有接口调用成功才算修复成功
				for retry := 1; retry <= maxLLMRetries; retry++ {
					run.GetLogger().Infof("Attempting to fix PlantUML code using LLM for block %s (attempt %d/%d)",
						actualBlockID, retry, maxLLMRetries)

					fixedPlantUMLCode, fixErr := fixPlantUMLForBoard(run, plantUMLCode, err.Error())
					if fixErr != nil {
						run.GetLogger().Warnf("Failed to generate fixed PlantUML code for block %s on attempt %d: %v",
							actualBlockID, retry, fixErr)
						continue
					}

					// 使用修复后的代码尝试StyleType=1
					req.PlantUMLCode = fixedPlantUMLCode
					run.GetLogger().Infof("Testing fixed PlantUML code for block %s (attempt %d/%d)",
						actualBlockID, retry, maxLLMRetries)

					_, err = larkClient(run).CreateWhiteboardPlantUMLNode(ctx, whiteboardID, req)
					if err == nil {
						run.GetLogger().Infof("Successfully created PlantUML node for block %s with StyleType=1 after LLM fix on attempt %d",
							actualBlockID, retry)
						success = true
						// 将修复后的代码回填到lark.md文件
						if err := replacePlantUMLInMarkdown(markdownFilePath, plantUMLCode, fixedPlantUMLCode); err != nil {
							run.GetLogger().Warnf("Failed to replace PlantUML code in markdown file %s: %v", markdownFilePath, err)
						} else {
							run.GetLogger().Infof("Successfully replaced PlantUML code in markdown file %s", markdownFilePath)
						}
						break // 成功了，跳出重试循环
					} else {
						run.GetLogger().Warnf("Fixed PlantUML code still failed for block %s on attempt %d: %v",
							actualBlockID, retry, err)
						lastError = err
						// 继续下一次重试
					}
				}

				if !success {
					run.GetLogger().Errorf("Failed to fix PlantUML code for block %s after %d attempts",
						actualBlockID, maxLLMRetries)
				}
			} else {
				run.GetLogger().Infof("Skipping PlantUML fix for block %s due to SkipFixPlantUml option", actualBlockID)
			}

			// 如果LLM修复后StyleType=1仍然失败，再尝试StyleType=2 (画板中嵌入图片，作为兜底)
			if !success {
				req = &lark.CreateWhiteboardPlantUMLNodeRequest{
					PlantUMLCode: plantUMLCode, // 使用原始代码
					StyleType:    2,            // 图片
					SyntaxType:   1,
					DiagramType:  0,
				}

				run.GetLogger().Infof("Attempting to create PlantUML node for block %s with StyleType=2, DiagramType=0",
					actualBlockID)

				_, err = larkClient(run).CreateWhiteboardPlantUMLNode(ctx, whiteboardID, req)
				if err == nil {
					run.GetLogger().Infof("Successfully created PlantUML node for block %s with StyleType=2, DiagramType=0",
						actualBlockID)
					success = true
				} else {
					run.GetLogger().Warnf("Failed to create PlantUML node for block %s with StyleType=2, DiagramType=0: %v",
						actualBlockID, err)
					lastError = err
				}
			}
		}

		if !success {
			run.GetLogger().Errorf("Failed to create PlantUML node for block %s after trying all diagram types and style types. Last error: %v",
				actualBlockID, lastError)
			// 继续处理其他blocks，不返回错误
		}
		telemetry.EmitLarkBoardUsage(run, req.StyleType, !success)
	}

	run.GetLogger().Infof("Completed processing board tokens")
	return nil
}

// fixPlantUMLForBoard fixes PlantUML code for board usage using LLM
func fixPlantUMLForBoard(run *iris.AgentRunContext, code, errMsg string) (string, error) {
	// 复用plantuml_helper.go中的fixPlantUML函数
	return fixPlantUML(run, code, errMsg)
}

// replacePlantUMLInMarkdown replaces PlantUML code in markdown file
func replacePlantUMLInMarkdown(markdownFilePath, originalCode, fixedCode string) error {
	// 读取markdown文件内容
	content, err := os.ReadFile(markdownFilePath)
	if err != nil {
		return errors.WithMessagef(err, "failed to read markdown file: %s", markdownFilePath)
	}

	// 替换PlantUML代码
	originalContent := string(content)
	newContent := strings.Replace(originalContent, originalCode, fixedCode, 1)

	// 如果没有发生替换，说明原始代码在文件中不存在
	if newContent == originalContent {
		return errors.Errorf("original PlantUML code not found in markdown file: %s", markdownFilePath)
	}

	// 写回文件
	if err := os.WriteFile(markdownFilePath, []byte(newContent), 0644); err != nil {
		return errors.WithMessagef(err, "failed to write markdown file: %s", markdownFilePath)
	}

	return nil
}

func checkToResetEnv(run *iris.AgentRunContext, larkBlocks []*larkdocx.Block) {
	tceHostEnv := os.Getenv(entity.RuntimeTCEHostEnv)
	if tceHostEnv == "boe" {
		return
	}
	hasMentionUser := lo.SomeBy(larkBlocks, func(block *larkdocx.Block) bool {
		if block.BlockType != nil && *block.BlockType == lark.DocxBlockTypeText && block.Text != nil {
			for _, element := range block.Text.Elements {
				if element.MentionUser != nil {
					return true
				}
			}
		}
		return false
	})

	if hasMentionUser {
		run.GetLogger().Infof("Found mentionUser block, resetting env")
		run.Environ.Set(entity.RuntimeTCEHostEnv, tceHostEnv)
	}

}
