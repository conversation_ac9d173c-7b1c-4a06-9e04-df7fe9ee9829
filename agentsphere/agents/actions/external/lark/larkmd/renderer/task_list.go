package renderer

import (
	"encoding/json"

	"github.com/yuin/goldmark/ast"
	extast "github.com/yuin/goldmark/extension/ast"
	"github.com/yuin/goldmark/renderer"
	"github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/util"

	"code.byted.org/devgpt/kiwis/port/lark"
)

// NewTaskListHTMLRenderer 返回一个新的任务列表渲染器
func NewTaskListHTMLRenderer(opts ...html.Option) renderer.NodeRenderer {
	r := &TextRenderer{
		Config: html.NewConfig(),
	}
	for _, opt := range opts {
		opt.SetHTMLOption(&r.Config)
	}
	return r
}

// renderTaskCheckBox 渲染任务列表复选框
func (r *TextRenderer) renderTaskCheckBox(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}

	n := node.(*extast.TaskCheckBox)

	// 创建任务列表块
	todoBlock := r.createTodoBlock(node, source, n.IsChecked)

	// 将任务列表块序列化为JSON并写入输出
	jsonData, err := json.Marshal(todoBlock)
	if err == nil {
		w.Write(jsonData)
	}
	w.WriteString(",")
	node.SetAttributeString("block_id", todoBlock.BlockID)

	// 跳过子节点处理
	return ast.WalkSkipChildren, nil
}

// createTodoBlock 创建任务列表块
func (r *TextRenderer) createTodoBlock(node ast.Node, source []byte, isChecked bool) *lark.DocxBlock {
	// 获取父节点文本内容
	var elements []*lark.DocxTextElement
	parentNode := node.Parent()
	if parentNode != nil && (parentNode.Kind() == ast.KindParagraph || parentNode.Kind() == ast.KindTextBlock) {
		// 跳过第一个子节点（即复选框节点本身）
		for c := parentNode.FirstChild().NextSibling(); c != nil; c = c.NextSibling() {
			elements = append(elements, r.NodeToTextElements(c, source)...)
		}
	}

	if len(elements) == 0 {
		elements = append(elements, &lark.DocxTextElement{
			TextRun: &lark.DocxTextElementTextRun{
				Content: "",
			},
		})
	}

	// 创建任务列表块
	return &lark.DocxBlock{
		BlockID:   generateBlockID(),
		BlockType: 17, // 任务列表类型
		Todo: &lark.DocxBlockText{
			Elements: elements,
			Style: &lark.DocxTextStyle{
				Align: 1,         // 左对齐
				Done:  isChecked, // 根据任务复选框状态设置完成状态
			},
		},
	}
}

func (r *TextRenderer) createTodoBlockFromTextBlock(node ast.Node, source []byte) *lark.DocxBlock {
	// 获取父节点文本内容
	var elements []*lark.DocxTextElement
	for c := node.FirstChild().NextSibling(); c != nil; c = c.NextSibling() {
		elements = append(elements, r.NodeToTextElements(c, source)...)
	}

	if len(elements) == 0 {
		elements = append(elements, &lark.DocxTextElement{
			TextRun: &lark.DocxTextElementTextRun{
				Content: "",
			},
		})
	}

	child := node.FirstChild()
	checkBox := child.(*extast.TaskCheckBox)

	// 创建任务列表块
	return &lark.DocxBlock{
		BlockID:   generateBlockID(),
		BlockType: 17, // 任务列表类型
		Todo: &lark.DocxBlockText{
			Elements: elements,
			Style: &lark.DocxTextStyle{
				Align: 1,                  // 左对齐
				Done:  checkBox.IsChecked, // 根据任务复选框状态设置完成状态
			},
		},
	}
}
