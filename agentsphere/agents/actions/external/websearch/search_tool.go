package websearch

import (
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

type QuickResearchToolArgs struct {
	ResearchTask string   `json:"task" mapstructure:"task" description:"required, A detailed description of the complex research task, example: Analyze the current state of the global electric vehicle market, including key players, market share, and future trends."`
	SubTasks     []string `json:"subtasks" mapstructure:"subtasks" description:"required, **MAX SUBTASKS LENGTH IS FIVE**, research subtasks, each subtask should be ** INDEPENDENT WITH OTHERS ** and very clear, example: []{\"key players of current global electric vehicle market\", \"market share of current global electric vehicle market\", \"future trends of current global electric vehicle market\"}"`
}

type QuickSearchToolArgs struct {
	SearchTasks []string `json:"search_tasks" mapstructure:"search_tasks" description:"required, **MAX SEARCH TASKS LENGTH IS EIGHT**, search tasks, each task should be independent with others(no dependency), example: []{\"2008 年奥运会火炬手是谁\", \"字节跳动 24 年 Q3 营收\", \"Microsoft 现任 CEO 是谁\"}"`
}

type QuickKnowledgeBaseSearchToolArgs struct {
	SearchTasks []string `json:"search_tasks" mapstructure:"search_tasks" description:"required, **MAX LENGTH IS EIGHT**, search tasks, each task should be high-quality, semantically rich, and focus on one topic. Each task must be a complete, natural-language question or a descriptive phrase, example: []{\"关于豆包语音对话产品说明中对 Audio 技术的描述\", \"Node.js 22 最新更新的特性都有哪些？\"}"`
}

const (
	ToolQuickResearch            = "research"
	ToolQuickResearchDescription = "Conduct a comprehensive investigation on a complex topic by exploring multiple facets concurrently and generating a single, synthesized report. Use this for broad, open-ended questions that can be broken down into parallel, independent sub-tasks (e.g., 'impact of AI on healthcare'). This tool is slow but delivers a high-density, accurate report. Do NOT use this for tasks with sequential dependencies where one step requires the output from a previous step. For such tasks, use the `search` tool multiple times in sequence. Do NOT use this for simple or urgent queries where a quick answer is sufficient."

	ToolSearch            = "search"
	ToolSearchDescription = "Perform a quick, real-time search using search engines(Web Online&Bytedance Internal). It is ideal for two main scenarios: (1) Finding a single, specific, and time-sensitive piece of information (e.g., a fact, a number, a date, latest news). (2) Executing one step in a multi-step query that has sequential dependencies. If a task requires finding information A before you can search for information B, you must use this tool for each step. This tool is very fast (low latency) but returns a list of raw, unprocessed search snippets. The output can be verbose, noisy, and may have a high token count. Do NOT use this for complex questions that require analysis, synthesis, or a deep understanding of a subject. **MAX SEARCH TASKS LENGTH IS EIGHT**"

	ToolKnowledgeBaseSearch      = "project_space_search"
	TookKnowledgeBaseDescription = "Exclusively for retrieving documents from a user's personal, private project space. This tool is NOT for general company-wide searches or public information. It should ONLY be used when the user explicitly requests to search their own, private, or team-specific knowledge base using keywords like \"my,\" \"our,\" or specific project names. For any other type of internal search, use the `search` tool."
)

var SearchAgentCreator func(run *iris.AgentRunContext) InternalAISearcher = nil

type TaskResult struct {
	Task      string
	Answer    string
	Finished  bool
	Reference []SearchItem
}
type Response struct {
	FinalReport   string
	TaskResults   []TaskResult
	SearchResults []*SearchItem
	Reference     []SearchItem
}
type SearchItem struct {
	// URL is the already normalized URL.
	URL         string
	Title       string
	Description string
	Icon        string
	RankScore   float64
	Source      string // lark_search, web_search, byte_knowledge.
	// DocumentID is the ID of the document in the DevAI.
	DocumentID      string
	NeedFullContext bool
	LastUpdatedTime time.Time
	TraceID         string
	WebEngine       string
}

// InternalAISearcher is to solve cycle import problem.
type InternalAISearcher interface {
	InternalSimpleAIResearch(ctx *iris.AgentRunContext, task string, subtasks []string) (*Response, error)
	InternalAISearch(ctx *iris.AgentRunContext, query []string) (*Response, error)
	InternalKnowledgeBaseSearch(ctx *iris.AgentRunContext, query []string) (*Response, error)
}

// ReportSearchResult is consumed by frontend to display search result.
// Modify it carefully.
type ReportSearchResult struct {
	URL         string `json:"url"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
}

type ReportResult struct {
	Task     string `json:"task"`
	Answer   string `json:"answer"`
	Finished bool   `json:"finished"`
}

type LoadResult struct {
	Info string `json:"info"`
}

func NewSearchTool() iris.Action {
	return actions.ToTool(ToolSearch, ToolSearchDescription, AgentSearch)
}

func NewQuickResearchTool() iris.Action {
	return actions.ToTool(ToolQuickResearch, ToolQuickResearchDescription, QuickResearch)
}

func NewKnowledgeBaseSearchTool() iris.Action {
	return actions.ToTool(ToolKnowledgeBaseSearch, TookKnowledgeBaseDescription, KnowledgebaseSearch)
}

func AgentSearch(c *iris.AgentRunContext, args QuickSearchToolArgs) (map[string]any, error) {
	agent := SearchAgentCreator(c)
	resp, err := agent.InternalAISearch(c, args.SearchTasks)
	if err != nil {
		err = iris.NewRecoverable(err)
		return nil, err
	}
	searchReference := GetReference(c, entity.ReferenceTypeSearched)
	finalAnswer := fmt.Sprintf("以下是对<%v>的搜索结果\n", strings.Join(args.SearchTasks, "; "))
	var knowledgeInfos string
	oldSearchReference := len(searchReference.List)
	for _, value := range resp.SearchResults {
		referenceItem := iris.ReferenceItem{
			Title:    value.Title,
			URI:      value.URL,
			MetaData: util.ValueToMap(value),
		}
		infoID := searchReference.SaveOrGetIndex(referenceItem)
		knowledgeInfo := fmt.Sprintf(`
<info-%d>
<title>%s</title>
<url>%s</url>
<content>%s</content>
</info-%d>
`, infoID, value.Title, value.URL, value.Description, infoID)
		if len(knowledgeInfos)+len(knowledgeInfo) > 100000 {
			break
		}
		knowledgeInfos += knowledgeInfo
	}
	SetReference(c, entity.ReferenceTypeSearched, searchReference)
	c.GetPublisher().ReportReferenceUpdated(searchReference.List[oldSearchReference:])
	finalAnswer += fmt.Sprintf(`<search-info>
	%v
	<search-info>
	`, knowledgeInfos)
	return map[string]any{
		"answer": finalAnswer,
	}, nil
}

func QuickResearch(c *iris.AgentRunContext, args QuickResearchToolArgs) (map[string]any, error) {
	agent := SearchAgentCreator(c)
	resp, err := agent.InternalSimpleAIResearch(c, args.ResearchTask, args.SubTasks)
	if err != nil {
		err = iris.NewRecoverable(err)
		return nil, err
	}
	c.GetLogger().Infof("QuickResearchTool response final report: %v", resp.FinalReport)

	answer, citations := UpdateReference(c,
		resp.FinalReport,
		lo.Map(resp.Reference, func(item SearchItem, index int) iris.ReferenceItem {
			return iris.ReferenceItem{
				URI:      item.URL,
				Title:    item.Title,
				MetaData: util.ValueToMap(item),
			}
		}),
		entity.ReferenceTypeSearched)

	c.GetLogger().Infof("QuickResearchTool after update reference final report: %v", answer)

	return map[string]any{
		"answer": answer,
		"tasks": lo.Map(resp.TaskResults, func(item TaskResult, _ int) ReportResult {
			return ReportResult{
				Task:     item.Task,
				Finished: item.Finished,
			}
		}),
		"results": lo.Map(citations, func(item iris.ReferenceItem, _ int) ReportSearchResult {
			return ReportSearchResult{
				Icon:        conv.DefaultAny[string](item.MetaData["Icon"]),
				Title:       item.Title,
				URL:         item.URI,
				Description: conv.DefaultAny[string](item.MetaData["Description"]),
			}
		}),
	}, nil
}

func KnowledgebaseSearch(c *iris.AgentRunContext, args QuickKnowledgeBaseSearchToolArgs) (map[string]any, error) {
	agent := SearchAgentCreator(c)
	resp, err := agent.InternalKnowledgeBaseSearch(c, args.SearchTasks)
	if err != nil {
		err = iris.NewRecoverable(err)
		return nil, err
	}
	return map[string]any{
		"answer": resp.FinalReport,
	}, nil
}
