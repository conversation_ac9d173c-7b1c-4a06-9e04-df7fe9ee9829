package websearch

import (
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
)

func GetReference(run *iris.AgentRunContext, refType entity.ReferenceType) entity.Reference {
	store := iris.RetrieveStoreByKey[entity.ReferenceStore](run, entity.ReferenceStoreKey)
	var ref entity.Reference
	switch refType {
	case entity.ReferenceTypeSearched:
		ref = store.SearchedRef
	case entity.ReferenceTypeCreated:
		ref = store.CreatedRef
	default:
		ref = entity.Reference{}
	}
	if ref.IndexOf == nil {
		ref.IndexOf = map[string]int{}
	}
	return ref
}

func SetReference(run *iris.AgentRunContext, refType entity.ReferenceType, ref entity.Reference) {
	store := iris.RetrieveStoreByKey[entity.ReferenceStore](run, entity.ReferenceStoreKey)
	switch refType {
	case entity.ReferenceTypeSearched:
		store.SearchedRef = ref
	case entity.ReferenceTypeCreated:
		store.CreatedRef = ref
	}
	iris.UpdateStoreByKey(run, entity.ReferenceStoreKey, store)
}

func UpdateReference(run *iris.AgentRunContext, answer string, citations iris.Reference, refType entity.ReferenceType) (newAnswer string, newCitations iris.Reference) {

	store := iris.RetrieveStoreByKey[entity.ReferenceStore](run, entity.ReferenceStoreKey)
	var (
		ref    entity.Reference
		refNum int
	)

	switch refType {
	case entity.ReferenceTypeSearched:
		ref = store.SearchedRef
		refNum = len(ref.List)
	case entity.ReferenceTypeCreated:
		ref = store.CreatedRef
	}
	if ref.IndexOf == nil {
		ref.IndexOf = map[string]int{}
	}

	defer func() {
		switch refType {
		case entity.ReferenceTypeSearched:
			// Must report new appended references
			run.GetPublisher().ReportReferenceUpdated(ref.List[refNum:])
			store.SearchedRef = ref
		case entity.ReferenceTypeCreated:
			store.CreatedRef = ref
		}
		iris.UpdateStoreByKey(run, entity.ReferenceStoreKey, store)
	}()

	citationReg := regexp.MustCompile(`::cite\[(\d+)\]`)
	matches := citationReg.FindAllStringSubmatchIndex(answer, -1)

	var builder strings.Builder
	lastIndex := 0
	inNew := map[string]bool{}
	for _, match := range matches {
		citationNum, _ := strconv.Atoi(answer[match[2]:match[3]])
		index := citationNum - 1

		builder.WriteString(answer[lastIndex:match[0]])
		if index >= 0 && index < len(citations) {
			citation := citations[index]
			existingIndex, ok := ref.IndexOf[citation.URI]
			if ok {
				builder.WriteString(fmt.Sprintf("::cite[%d]", existingIndex))
				if !inNew[citation.URI] {
					newCitations = append(newCitations, ref.List[existingIndex-1])
					inNew[citation.URI] = true
				}
			} else {
				citation.ID = len(ref.List) + 1
				ref.List = append(ref.List, citation)
				ref.IndexOf[citation.URI] = citation.ID
				builder.WriteString(fmt.Sprintf("::cite[%d]", citation.ID))
				newCitations = append(newCitations, citation)
				inNew[citation.URI] = true
			}
		}
		lastIndex = match[1]
	}
	for _, citation := range citations {
		if _, ok := inNew[citation.URI]; !ok {
			citation.ID = len(ref.List) + 1
			ref.List = append(ref.List, citation)
			ref.IndexOf[citation.URI] = citation.ID
			newCitations = append(newCitations, citation)
		}
	}
	builder.WriteString(answer[lastIndex:])
	newAnswer = builder.String()

	sort.Slice(newCitations, func(i, j int) bool {
		return newCitations[i].ID < newCitations[j].ID
	})

	return
}
