package codebase

import (
	"net/url"
	"strconv"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/port/codebase"
)

var (
	ToolGitClone           = "git_clone"
	ToolGitCommit          = "git_commit"
	ToolGitPush            = "git_push"
	ToolSubmitMergeRequest = "submit_merge_request"

	ToolGitCloneDescription           = "Clone a git repository from codebase/github with given name and branch/tag/commit. Only this tool has access to user's ssh key."
	ToolGitCommitDescription          = "Commit changes to a git repository"
	ToolSubmitMergeRequestDescription = "Submit a merge request to codebase"
	ToolGitPushDescription            = "Push changes to a git repository"

	GitHubProxy = lo.Must(url.Parse("http://sys-proxy-rd-relay.byted.org:3128"))
)

type SubmitMergeRequestArgs struct {
	RepoName     string `json:"repo_name" mapstructure:"repo_name" description:"name of the repository to submit merge request"`
	SourceBranch string `json:"source_branch" mapstructure:"source_branch" description:"source branch to submit merge request"`
	TargetBranch string `json:"target_branch" mapstructure:"target_branch" description:"target branch to submit merge request"`
	Title        string `json:"title" mapstructure:"title" description:"title of the merge request"`
	Description  string `json:"description" mapstructure:"description" description:"description of the merge request"`
	Draft        bool   `json:"draft" mapstructure:"draft" description:"whether to create a draft merge request, defaults to true"`
}

type SubmitMergeRequestOutputs struct {
	MergeRequestURL string `json:"merge_request_url" mapstructure:"merge_request_url" description:"url of the merge request"`
}

func NewSubmitMergeRequestAction(enabledBytedRepos bool) iris.Action {
	return actions.ToTool(ToolSubmitMergeRequest, ToolSubmitMergeRequestDescription, func(run *iris.AgentRunContext, args SubmitMergeRequestArgs) (*SubmitMergeRequestOutputs, error) {
		if !enabledBytedRepos {
			return nil, iris.NewRecoverable(errors.New("codebase repos are not supported"))
		}
		isDraft := true
		if !args.Draft {
			isDraft = false
		}
		cli, err := NewCodebaseClient(run)
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to create codebase client"))
		}
		mr, err := cli.CreateMergeRequest(run, args.RepoName, codebase.CreateMergeRequestOption{
			SourceBranch: args.SourceBranch,
			TargetBranch: args.TargetBranch,
			Title:        args.Title,
			Description:  args.Description + "\n\n\n Suggested-By: [Aime](https://aime.bytedance.net)",
			Draft:        isDraft,
		})
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to submit merge request to %s", args.RepoName))
		}
		telemetry.EmitSubmitMergeRequest(run, args.RepoName, telemetry.MergeRequestTypeCodebase, strconv.FormatInt(mr.ID, 10))
		return &SubmitMergeRequestOutputs{
			MergeRequestURL: mr.ExternalURL,
		}, nil
	})
}
