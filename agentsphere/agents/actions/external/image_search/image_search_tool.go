package imagessearch

import (
	"crypto/md5"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"code.byted.org/lang/gg/choose"
	"github.com/cenkalti/backoff/v4"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/toutiaosearch"
)

var (
	toutiaoSearchClient *toutiaosearch.RPCClient
	initOnce            sync.Once
)

const (
	ToolImageSearch            = "image_search"
	ToolImageSearchDescription = "image_search is a tool that finds existing web images and downloading them locally. Use when user's requirement is related to images or creating HTML with images, content development, or needs visual materials with local file access."
	searchRetryMaxTimes        = 3
	maxResultCount             = 15 // 限制返回结果数量
	downloadTimeout            = 30 * time.Second
	maxConcurrentDownloads     = 5
	searchRetryInitInterval    = 300 * time.Millisecond

	// 存储目录配置
	imageStorageDir    = "images"
	maxFilenameLength  = 100
	defaultImageFormat = "jpg"
)

type ImageResult struct {
	Title      string `json:"image_title"`
	URL        string `json:"image_url"`
	LocalPath  string `json:"local_path"`
	Format     string `json:"image_format"`
	Height     int    `json:"image_height"`
	Width      int    `json:"image_width"`
	FileSize   int64  `json:"file_size"`
	Downloaded bool   `json:"downloaded"`
}

type ImageSearchResponse struct {
	Results []*ImageResult `json:"results"`
	Guide   string         `json:"guide"`
}

type SearchImagesArgs struct {
	Query string `json:"query" mapstructure:"query" description:"The image search keyword or phrase. Can be in English, Chinese"`
}

type downloadTask struct {
	result *ImageResult
	index  int
}

func NewImagesSearchToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		NewImagesSearchTool(),
	}, nil
}

func NewImagesSearchTool() iris.Action {
	return actions.ToTool(ToolImageSearch, ToolImageSearchDescription, SearchImages)
}

func SearchImages(run *iris.AgentRunContext, args SearchImagesArgs) (*ImageSearchResponse, error) {
	initOnce.Do(func() {
		toutiaoSearchClient = toutiaosearch.NewToutiaoSearchRpcClientByLocalConfig(nil, toutiaosearch.ToutiaoSearchConfig{
			TrafficGroup: "aime",
			TrafficId:    "aime_images",
		})
	})

	var (
		originImagesResp *toutiaosearch.ImageSearchObservation
		err              error
		resp             ImageSearchResponse
	)

	// 搜索图片
	backoffConf := backoff.NewExponentialBackOff()
	backoffConf.InitialInterval = searchRetryInitInterval
	err = backoff.Retry(func() error {
		originImagesResp, err = toutiaoSearchClient.ImageSearch(run, args.Query)
		if err != nil {
			return err
		}
		return nil
	}, backoff.WithMaxRetries(backoffConf, searchRetryMaxTimes))

	if err != nil {
		return nil, fmt.Errorf("图片搜索失败: %w", err)
	}

	// 确保存储目录存在
	if err := ensureImageDir(); err != nil {
		return nil, fmt.Errorf("创建图片存储目录失败: %w", err)
	}

	// 对结果做加工
	for _, rr := range originImagesResp.ImageResults {
		for _, card := range rr.SingleSearchResult.SearchResult.ImageCard.CardList {
			if len(resp.Results) >= maxResultCount {
				break
			}
			resp.Results = append(resp.Results, &ImageResult{
				Title: card.AbstractInfo.TitleOriginal,
				URL: choose.IfLazyL(len(card.ImageInfo.URLList) > 0, func() string {
					return card.ImageInfo.URLList[0]
				}, card.ImageInfo.ThumbnailURL),
				Format: card.ImageInfo.Format,
				Height: card.ImageInfo.Height,
				Width:  card.ImageInfo.Width,
			})
		}
	}

	// 强制下载所有图片
	if len(resp.Results) > 0 {
		downloadImages(resp.Results, args.Query)
	}

	// 生成指导信息
	resultCount := len(resp.Results)
	downloadedCount := 0
	for _, result := range resp.Results {
		if result.Downloaded {
			downloadedCount++
		}
	}

	if resultCount == 0 {
		resp.Guide = "未找到相关图片"
	} else {
		resp.Guide = fmt.Sprintf("找到 %d 张相关图片，成功下载 %d 张到本地 '%s' 目录。可直接使用本地路径进行展示或处理。",
			resultCount, downloadedCount, imageStorageDir)
	}

	return &resp, nil
}

// ensureImageDir 确保图片存储目录存在
func ensureImageDir() error {
	return os.MkdirAll(imageStorageDir, 0755)
}

// downloadImages 并发下载图片
func downloadImages(results []*ImageResult, query string) {
	taskChan := make(chan downloadTask, len(results))
	var wg sync.WaitGroup

	// 启动下载工作器
	for i := 0; i < maxConcurrentDownloads; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for task := range taskChan {
				downloadSingleImage(task.result, task.index, query)
			}
		}()
	}

	// 提交下载任务
	for i, result := range results {
		taskChan <- downloadTask{result: result, index: i}
	}
	close(taskChan)

	// 等待所有下载完成
	wg.Wait()
}

// downloadSingleImage 下载单个图片
func downloadSingleImage(result *ImageResult, index int, query string) {
	if result.URL == "" {
		return
	}

	// 生成安全的文件名
	filename := generateSafeFilename(result.Title, result.URL, index, query, result.Format)
	localPath := filepath.Join(imageStorageDir, filename)

	// 检查文件是否已存在
	if _, err := os.Stat(localPath); err == nil {
		// 文件已存在，生成新的文件名
		filename = generateUniqueFilename(filename)
		localPath = filepath.Join(imageStorageDir, filename)
	}

	// 下载图片
	client := &http.Client{
		Timeout: downloadTimeout,
	}

	resp, err := client.Get(result.URL)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return
	}

	// 创建本地文件
	file, err := os.Create(localPath)
	if err != nil {
		return
	}
	defer file.Close()

	// 复制数据
	size, err := io.Copy(file, resp.Body)
	if err != nil {
		os.Remove(localPath) // 清理失败的文件
		return
	}

	// 更新结果
	result.LocalPath = localPath
	result.FileSize = size
	result.Downloaded = true
}

// generateSafeFilename 生成安全的文件名
func generateSafeFilename(title, url string, index int, query string, format string) string {
	// 清理标题，移除非法字符
	safeTitle := sanitizeFilename(title)
	if safeTitle == "" {
		safeTitle = sanitizeFilename(query)
	}
	if safeTitle == "" {
		safeTitle = "image"
	}

	// 限制长度（按UTF-8字符安全截断）
	if len(safeTitle) > maxFilenameLength {
		safeTitle = truncateUTF8Safe(safeTitle, maxFilenameLength)
	}

	// 生成时间戳
	timestamp := time.Now().Format("20060102_150405")

	// 生成URL哈希（用于唯一性）
	hash := fmt.Sprintf("%x", md5.Sum([]byte(url)))[:8]

	// 确定文件扩展名
	ext := strings.ToLower(format)
	if ext == "" {
		ext = defaultImageFormat
	}
	if !strings.HasPrefix(ext, ".") {
		ext = "." + ext
	}

	// 组合文件名: 标题_索引_时间戳_哈希.扩展名
	filename := fmt.Sprintf("%s_%d_%s_%s%s", safeTitle, index, timestamp, hash, ext)

	return filename
}

// sanitizeFilename 清理文件名，移除非法字符
func sanitizeFilename(filename string) string {
	// 移除或替换非法字符
	reg := regexp.MustCompile(`[<>:"/\\|?*\x00-\x1f]`)
	safe := reg.ReplaceAllString(filename, "_")

	// 移除不可见字符和非打印字符（包括控制字符）
	safe = strings.Map(func(r rune) rune {
		if r == utf8.RuneError || !strconv.IsPrint(r) {
			return -1 // 丢弃不可打印字符
		}
		return r
	}, safe)

	// 移除多余的空格和点
	safe = strings.TrimSpace(safe)
	safe = strings.Trim(safe, ".")

	// 替换多个连续的下划线或空格
	reg2 := regexp.MustCompile(`[_\s]+`)
	safe = reg2.ReplaceAllString(safe, "_")

	return safe
}

// generateUniqueFilename 生成唯一文件名（当文件已存在时）
func generateUniqueFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	nameWithoutExt := strings.TrimSuffix(originalFilename, ext)

	// 添加新的时间戳和随机哈希
	timestamp := time.Now().Format("150405_000")
	hash := fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s_%d", originalFilename, time.Now().UnixNano()))))[:6]

	return fmt.Sprintf("%s_%s_%s%s", nameWithoutExt, timestamp, hash, ext)
}

// truncateUTF8Safe 安全地截断UTF-8字符串，确保不会在多字节字符中间截断
// 优化版本：时间复杂度O(n)，避免重复验证整个字符串
func truncateUTF8Safe(s string, maxBytes int) string {
	if len(s) <= maxBytes {
		return s
	}

	// 从maxBytes位置向前查找UTF-8字符边界
	// 一个UTF-8字符最多4个字节，所以最多向前查找4个位置
	for i := maxBytes; i >= 0 && maxBytes-i < 4; i-- {
		if utf8.RuneStart(s[i]) {
			// 检查这个位置开始的字符是否完整
			r, size := utf8.DecodeRuneInString(s[i:])
			if r != utf8.RuneError && i+size <= len(s) {
				return s[:i]
			}
		}
	}

	// 如果在4字节范围内找不到有效边界，继续向前查找
	for i := maxBytes - 4; i >= 0; i-- {
		if utf8.RuneStart(s[i]) {
			return s[:i]
		}
	}

	// 如果还是找不到，返回空字符串
	return ""
}
