package imagessearch

import (
	"testing"
	"unicode/utf8"
)

func Test_sanitizeFilename(t *testing.T) {
	println(sanitizeFilename("测试  中文"))
}

func TestTruncateUTF8Safe(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		maxBytes int
		expected string
	}{
		{
			name:     "英文字符串不需要截断",
			input:    "hello world",
			maxBytes: 20,
			expected: "hello world",
		},
		{
			name:     "英文字符串正常截断",
			input:    "hello world",
			maxBytes: 5,
			expected: "hello",
		},
		{
			name:     "中文字符串不需要截断",
			input:    "你好世界",
			maxBytes: 20,
			expected: "你好世界",
		},
		{
			name:     "中文字符串安全截断",
			input:    "你好世界", // 每个中文字符3字节
			maxBytes: 6,      // 只能容纳2个中文字符
			expected: "你好",
		},
		{
			name:     "中文字符串避免在字符中间截断",
			input:    "你好世界",
			maxBytes: 7, // 7字节会在第3个字符中间，应该截断到6字节
			expected: "你好",
		},
		{
			name:     "混合字符串截断",
			input:    "hello你好world",
			maxBytes: 10, // hello(5) + 你(3) + 好(3) = 11字节，应该截断到hello你
			expected: "hello你",
		},
		{
			name:     "空字符串",
			input:    "",
			maxBytes: 5,
			expected: "",
		},
		{
			name:     "maxBytes为0",
			input:    "hello",
			maxBytes: 0,
			expected: "",
		},
		{
			name:     "包含emoji的字符串",
			input:    "hello😀world", // 😀是4字节的emoji
			maxBytes: 8,             // hello(5) + 😀(4) = 9字节，应该截断到hello
			expected: "hello",
		},
		{
			name:     "日文字符串",
			input:    "こんにちは", // 每个日文字符3字节
			maxBytes: 9,       // 只能容纳3个字符
			expected: "こんに",
		},
		{
			name:     "特殊字符混合",
			input:    "test测试🎉end",
			maxBytes: 10, // test(4) + 测(3) + 试(3) = 10字节
			expected: "test测试",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := truncateUTF8Safe(tt.input, tt.maxBytes)

			// 检查结果是否符合预期
			if result != tt.expected {
				t.Errorf("truncateUTF8Safe(%q, %d) = %q, expected %q",
					tt.input, tt.maxBytes, result, tt.expected)
			}

			// 检查结果是否是有效的UTF-8字符串
			if !utf8.ValidString(result) {
				t.Errorf("truncateUTF8Safe(%q, %d) returned invalid UTF-8: %q",
					tt.input, tt.maxBytes, result)
			}

			// 检查结果长度不超过maxBytes
			if len(result) > tt.maxBytes {
				t.Errorf("truncateUTF8Safe(%q, %d) returned %d bytes, exceeds maxBytes %d",
					tt.input, tt.maxBytes, len(result), tt.maxBytes)
			}
		})
	}
}
