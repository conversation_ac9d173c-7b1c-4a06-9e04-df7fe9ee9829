package devmind

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/devmind"
	"code.byted.org/lang/gg/gptr"
)

//import (
//	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
//	"code.byted.org/devgpt/kiwis/agentsphere/entity"
//	"testing"
//)
//
//var c = &iris.AgentRunContext{
//	Environ: &iris.RunEnviron{
//		Map: map[string]string{
//			entity.RuntimeEnvironUserCloudJWT: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//		},
//	},
//}
//
//func TestQueryStoryMetrics(t *testing.T) {
//	type args struct {
//		run  *iris.AgentRunContext
//		args QueryStoryMetricsArgs
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    map[string]any
//		wantErr bool
//	}{
//		{
//			name: "test",
//			args: args{
//				run:  c,
//				args: QueryStoryMetricsArgs{},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := QueryStoryMetrics(tt.args.run, tt.args.args)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("QueryStoryMetrics() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			t.Logf("QueryStoryMetrics() got = %v", got)
//		})
//	}
//}
//
//func TestQueryMetricData(t *testing.T) {
//	type args struct {
//		run  *iris.AgentRunContext
//		args QueryMetricDataArgs
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    map[string]any
//		wantErr bool
//	}{
//		{
//			name: "test",
//			args: args{
//				run: c,
//				args: QueryMetricDataArgs{
//					StoryID:         "7473032311174481970",
//					TimeGranularity: "月",
//					QueryDate:       "2025-01-23",
//					NodeID:          "7348862719380555802",
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := QueryMetricData(tt.args.run, tt.args.args)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("QueryMetricData() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			t.Logf("QueryMetricData() got = %v", got)
//		})
//	}
//}

func TestWriteReportMetricsToExcel(t *testing.T) {
	// 假设创建一个 AgentRunContext 实例
	run := &iris.AgentRunContext{}
	reportID := "1234"
	//api, err := devmind.NewTestClient()
	//ctx := context.Background()
	//
	//jwtToken := "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	//result, _ := api.QueryInsightReportData(ctx, jwtToken, "7506170295528310821", "3", "2025-05")

	// 构造 dataList
	dataList := []devmind.AimeMetricQueryInfo{
		{
			MetricName: "示例指标1",
			MetricId:   "12345",
			MetricQueryDataList: []devmind.AimeMetricQueryData{
				{
					GraphType: "card_graph",
					CardGraph: &devmind.AimeChartData{
						XAxis: []string{"2024-01-01", "2024-01-02"},
						DataList: []devmind.AimeChartSubData{
							{
								Name:              "系列1",
								SubDataList:       []string{"10", "20"},
								BaselineValueList: []string{"8", "18"},
								Avg:               "15",
								Sum:               "30",
							},
						},
						BaselineConclusion: "达标",
						HistoryConclusion:  "趋势良好",
					},
				},
				{
					GraphType: "line_graph",
					LineGraph: &devmind.AimeChartData{
						XAxis: []string{"2024-01-01", "2024-01-02"},
						DataList: []devmind.AimeChartSubData{
							{
								Name:              "系列1",
								SubDataList:       []string{"2", "4"},
								BaselineValueList: []string{"1", "8"},
								Avg:               "10",
								Sum:               "20",
							},
						},
						BaselineConclusion: "达标",
						HistoryConclusion:  "趋势良好",
					},
				},
				{
					GraphType: "chart_sheet",
					ChartSheet: &devmind.AimeSheetData{
						Titles: []devmind.AimeSheetTitle{
							{
								Title:    "业务线",
								IsMetric: false,
							},
							{
								Title:    "合入有效代码行",
								IsMetric: true,
							},
							{
								Title:    "责任事故数",
								IsMetric: true,
							},
						},
						DataList: [][]devmind.AimeSheetSubData{
							{
								{
									Value: "一级业务线",
								},
								{
									Value:           "19",
									YearDiffValue:   gptr.Of(2.0),
									YearDiffRatio:   gptr.Of(0.1),
									PeriodDiffValue: gptr.Of(2.0),
									PeriodDiffRatio: gptr.Of(0.2),
									ProportionRatio: gptr.Of(0.3),
									BaselineValue:   gptr.Of(2.0),
								},
								{
									Value:           "1",
									YearDiffValue:   gptr.Of(2.0),
									YearDiffRatio:   gptr.Of(0.1),
									PeriodDiffValue: gptr.Of(2.0),
									PeriodDiffRatio: gptr.Of(0.2),
									ProportionRatio: gptr.Of(0.3),
									BaselineValue:   gptr.Of(2.0),
								},
							},
							{
								{
									Value: "二级业务线",
								},
								{
									Value:           "10",
									YearDiffValue:   gptr.Of(2.0),
									YearDiffRatio:   gptr.Of(0.1),
									PeriodDiffValue: gptr.Of(2.0),
									PeriodDiffRatio: gptr.Of(0.2),
									ProportionRatio: gptr.Of(0.3),
									BaselineValue:   gptr.Of(2.0),
								},
								{
									Value:           "0",
									YearDiffValue:   gptr.Of(2.0),
									YearDiffRatio:   gptr.Of(0.1),
									PeriodDiffValue: gptr.Of(2.0),
									PeriodDiffRatio: gptr.Of(0.2),
									ProportionRatio: gptr.Of(0.3),
									BaselineValue:   gptr.Of(2.0),
								},
							},
						},
					},
				},
			},
		},
	}

	//dataList = result.ReportMetricQueryInfo
	// 调用 writeReportMetricsToExcel 函数
	filePath, err := writeReportMetricsToExcel(run, dataList, reportID)
	if err != nil {
		fmt.Printf("写入 Excel 文件时出错: %v\n", err)
	} else {
		fmt.Printf("Excel 文件已保存至: %s\n", filePath)
	}

}

func TestQueryAnalysisDimension(t *testing.T) {
	api, _ := devmind.NewTestClient()
	ctx := context.Background()
	jwtToken := "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	request := &devmind.GetChartByUser{
		ChartType: "line",
		DimensionConditionList: []devmind.AimeQueryChartDimension{
			{
				DimensionID: "7174308821253441573",
				Operator:    gptr.Of("BETWEEN"),
				Value:       gptr.Of([]string{"2024-01-01 00:00:00", "2024-06-30 23:59:59"}),
			},
			{
				DimensionID: "7132379192532568108",
				Operator:    gptr.Of("IN"),
				Value:       gptr.Of([]string{"chengxin.leo"}),
			},
		},
		DimensionList: []devmind.AimeQueryChartDimension{
			{
				DimensionID: "7056743258990987295",
			},
			{
				DimensionID:   "7174308821253441573",
				GranularityId: gptr.Of(3),
			},
		},
		MetricList: []devmind.AimeQueryChartMetric{
			{
				MetricID: "7162132084713424927",
			},
		},
		ModelID: "7055999808511379463",
	}
	request1 := &devmind.AimeChartQueryByUser{
		SessionID:         "test-01",
		AnalysisMetric:    "7162132084713424927",
		AnalysisDimension: "7056743258990987295",
		FilterCondition:   "7174308821253441573",
		GetChartByUser:    *request,
	}
	result, _ := api.QueryChartUrl(ctx, jwtToken, request1)
	result1, _ := api.QueryChartInfo(ctx, jwtToken, "test-01", "https://bits.bytedance.net/data/visual-analysis/create?q=%28%27queryId%21%27405bd97b7e0fe62f3bd75eb801ea7765%27%29_")
	fmt.Println(result)
	fmt.Println(result1)
}

func TestQueryChartUrl(t *testing.T) {
	type args struct {
		run  *iris.AgentRunContext
		args QueryChartUrlArgs
	}

}
