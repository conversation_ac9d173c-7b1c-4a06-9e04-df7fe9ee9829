package jwt_manager

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/golang-jwt/jwt/v5"
	"github.com/sourcegraph/conc"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/client"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/cloud_oauth"
	"code.byted.org/devgpt/kiwis/lib/hertz"
)

type JWTManager struct {
	run        *iris.AgentRunContext
	cli        *client.RuntimeAPIClient
	cloudOAuth *cloud_oauth.OAuthClient
	logger     iris.Logger
	stop       chan struct{}
}

var (
	_ iris.Disposable = (*JWTManager)(nil)
)

func NewJWTManager(run *iris.AgentRunContext, cli *client.RuntimeAPIClient, oauthCli *cloud_oauth.OAuthClient, logger iris.Logger) *JWTManager {
	jwtManager := &JWTManager{
		run:        run,
		cli:        cli,
		logger:     logger,
		cloudOAuth: oauthCli,
		stop:       make(chan struct{}),
	}
	go func() {
		defer func() {
			recover()
		}()
		ticker := time.NewTicker(10 * time.Minute)
		defer ticker.Stop()

		// at startup, only refresh the jwt file to avoid first response latency
		wg := conc.WaitGroup{}
		wg.Go(func() {
			jwtManager.RefreshCodebaseJWTFile()
			jwtManager.RefreshCodebaseTokens()
			jwtManager.RefreshGoModuleProxyToken()
		})
		wg.Go(jwtManager.RefreshNextCodeTokens)
		recovered := wg.WaitAndRecover()
		if recovered != nil {
			jwtManager.logger.Errorf("failed to refresh jwt: %s", recovered.AsError())
		}

		for {
			select {
			case <-ticker.C:
				wg = conc.WaitGroup{}
				wg.Go(jwtManager.RefreshCodebaseTokens)
				wg.Go(jwtManager.RefreshNextCodeTokens)
				wg.Go(jwtManager.RefreshCloudJwtToken)
				wg.Go(jwtManager.RefreshGoModuleProxyToken)
				recovered = wg.WaitAndRecover()
				if recovered != nil {
					jwtManager.logger.Errorf("failed to refresh jwt: %s", recovered.AsError())
				}
			case <-jwtManager.stop:
				return
			}
		}
	}()
	return jwtManager
}

func (m *JWTManager) Dispose() {
	close(m.stop)
}

func (m *JWTManager) RefreshCodebaseTokens() {
	if m == nil || m.run == nil || m.cli == nil {
		return
	}

	m.logger.Infof("start refreshing codebase user jwt...")
	// we don't use RunContext as the user may continue to use IDE after the agent run is finished
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	token, err := m.cli.RefreshToken(ctx, entity.RefreshTokenRequest{
		TokenType: entity.TokenTypeCodebase,
		Token:     m.run.GetEnv(entity.RuntimeEnvironUserCodebaseJWT),
	})
	if err != nil {
		m.logger.Errorf("failed to refresh codebase user jwt: %s", err)
		return
	}
	m.logger.Infof("refreshed codebase user jwt")
	m.run.Environ.Set(entity.RuntimeEnvironUserCodebaseJWT, token.Token)
	m.refreshCodebaseUserJWTFile(entity.PathUserCodebaseJWT)
}

func (m *JWTManager) RefreshGoModuleProxyToken() {
	if m == nil || m.run == nil || m.cli == nil {
		return
	}

	m.logger.Infof("start refreshing go module proxy token...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	baseURL := "https://ftoxcnyw.fn.bytedance.net"
	if iris.CurrentRegion() == iris.RegionI18n {
		baseURL = "https://a0bw10u9.fn-boei18n.bytedance.net"
	}
	paths := fmt.Sprintf("/?user=%s", m.run.User.Username)

	hertzClient, err := hertz.NewClient(baseURL, hertz.NewHTTPClientOption{
		Timeout: 10 * time.Second,
	})
	if err != nil {
		m.logger.Errorf("failed to new hertz client: %+v", err)
		return
	}

	resp, err := hertzClient.DoJSONReq(ctx, http.MethodGet, paths, hertz.ReqOption{
		Body:    nil,
		Headers: nil,
	})
	if err != nil || resp == nil || resp.StatusCode() != 200 {
		m.logger.Errorf("failed to refresh go module proxy token: err = %+v, status code = %d", err, resp.StatusCode())
		return
	}

	token := resp.Header.Get("x-go-mod-proxy-token")
	if token == "" {
		m.logger.Errorf("go module proxy token is empty")
		return
	}

	data := fmt.Sprintf("machine goproxy.byted.org login x-auth-token password %s", token)
	// bash容器中定时删除~/.netrc中旧的token，添加/tmp/.netrc中的新的token
	err = os.WriteFile("/tmp/.netrc", []byte(data), 0600)
	if err != nil {
		m.logger.Errorf("failed to write /tmp/.netrc: %+v", err)
		return
	}
}

func (m *JWTManager) RefreshCodebaseJWTFile() {
	if m == nil || m.run == nil {
		return
	}
	m.refreshCodebaseUserJWTFile(entity.PathUserCodebaseJWT)
}

func (m *JWTManager) refreshCodebaseUserJWTFile(filename string) {
	userCodebaseJwt := m.run.GetEnv(entity.RuntimeEnvironUserCodebaseJWT)
	if userCodebaseJwt == "" {
		m.logger.Errorf("refreshUserCodebaseJwt: user codebase jwt is empty")
		return
	}

	tempFilename := filename + ".tmp"
	err := os.WriteFile(tempFilename, []byte(userCodebaseJwt), 0600)
	if err != nil {
		m.logger.Errorf("failed to refresh user codebase jwt in %s: %s", filename, err)
		return
	}
	os.Rename(tempFilename, filename)
}

func (m *JWTManager) RefreshNextCodeTokens() {
	if m == nil || m.run == nil || m.cli == nil {
		return
	}
	nextCodeToken := m.run.GetEnv(entity.RuntimeNextCodeUserJWT)
	// 解析受信任 jwt(不需要验证签名)， 降低 next code 刷新频率
	jwtParser := &jwt.Parser{}
	claims := &jwt.MapClaims{}
	_, _, err := jwtParser.ParseUnverified(nextCodeToken, claims)
	if err == nil {
		issuedTime, getErr := claims.GetIssuedAt()
		if getErr == nil && time.Since(issuedTime.Time) < time.Hour {
			return
		}
	}

	m.logger.Infof("start refreshing nextcode user jwt...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	// we don't use RunContext as the user may continue to use IDE after the agent run is finished
	retryPolicy := backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3)
	if err = backoff.Retry(func() error {
		// 这里要用字节云 token，而不是 nextcode token
		token, tempErr := m.cli.RefreshToken(ctx, entity.RefreshTokenRequest{
			TokenType: entity.TokenTypeNextCode,
			Token:     m.run.GetEnv(entity.RuntimeEnvironUserCloudJWT),
		})
		if tempErr != nil {
			return tempErr
		}
		m.run.Environ.Set(entity.RuntimeNextCodeUserJWT, token.Token)
		return nil
	}, retryPolicy); err != nil {
		m.logger.Errorf("failed to refresh nextcode user jwt: %s", err)
		return
	}
	m.logger.Infof("refreshed nextcode user jwt")
}

func (m *JWTManager) RefreshCloudJwtToken() {
	if m.cloudOAuth == nil {
		m.logger.Warnf("has no cloudOAuth cli, wont refresh token")
		return
	}
	token := m.run.GetEnv(entity.RuntimeEnvironUserCloudJWT)
	jwtToken, err := m.cloudOAuth.RefreshJwtToken(m.run, token)
	if err != nil {
		m.logger.Errorf("failed to refresh cloud jwt token: %s", err)
		return
	}
	m.logger.Infof("refreshed cloud jwt token")
	m.run.Environ.Set(entity.RuntimeEnvironUserCloudJWT, jwtToken)
}
