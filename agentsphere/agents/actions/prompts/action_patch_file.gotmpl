{{ if .error -}}
We encountered an error while applying the patch(es) you provided: {{ .error }}
{{ else if and (.output.failed) (gt (len .output.failed) 0) -}}
We encountered error when applying the following patch you provided. 
The following is the patch(es) that failed to apply:
{{ range .output.failed -}}
* FailedReason: {{.error}}
FilePath: `{{.file_path}}`
{{ if .diff }}
{{ .diff }}
{{ else }}
<search>
{{ .search }}
</search>
<replace>
{{ .replace }}
</replace>
{{ end -}}
{{ end -}}
You should carefully review related code and fix your patch. Stop providing the same patches over and over again!

Note:
* All patches must be based on the original code without the line number (such as `【12】`). Make sure it or try to provide more sufficient and unique old line(s) from snippet to facilitate matching.
* Make sure your patch has right indentation(remember to distinguish spaces and tabs) and spaces(don't miss or add extra) which is exact same with the original lines.
* The paths of modified files in diff are relative paths. Make sure your file path is correct.
* The modified file must be in the project directory. Files outside the project directory cannot be modified.
* Previously successfully applied patches will modify the code, and new patches must be applied based on the current code. Please review the relevant code again then provide new patches.
{{ else -}}
Patch(es) apply successfully. The differences between the modified code and the original code are as following:
{{ range .output.succeed -}}
<file_path>{{.file_path}}</file_path>
{{ .patch }}
{{- end -}}
{{ end -}}