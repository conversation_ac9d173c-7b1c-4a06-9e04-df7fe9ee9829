{{ if .error -}}
Tool <deploy> returns error: 
{{ .error }}
{{- else -}}
Files has been deployed into a static site.
Static Site URL: {{.output.url}}
Files:
{{ range .output.files }}
- {{ . }}
{{- end }}
{{- if .output.console }}
Console Output of the browser: (REMINDER: You should make sure all charts/images/maps/diagrams are rendered correctly. Fix any issues with them.)
{{ range .output.console }}
  {{ . }}
{{- end }}
{{- end }}
{{- end }}