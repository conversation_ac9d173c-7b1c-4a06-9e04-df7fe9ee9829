package action_prompts

import (
	"bytes"
	_ "embed"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"text/template"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/lib/mapstructure"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	agentprompt "code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/memory/condenser"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/copilotstack/prompt"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

var (
	//go:embed action_find_identifier.gotmpl
	ActionPromptFindIdentifier string
	//go:embed action_patch_file.gotmpl
	ActionPromptPatchFile string
	//go:embed action_read_directory.gotmpl
	ActionPromptReadDirectory string
	//go:embed action_read_file.gotmpl
	ActionPromptReadFile string
	//go:embed action_read_markdown_files.gotmpl
	ActionPromptReadMarkDownFiles string
	//go:embed action_create_file.gotmpl
	ActionPromptCreateFile string
	//go:embed action_append_file.gotmpl
	ActionPromptAppendFile string
	//go:embed action_write_file.gotmpl
	ActionPromptWriteFile string
	//go:embed action_edit_file.gotmpl
	ActionPromptEditFile string
	//go:embed action_review_definition.gotmpl
	ActionPromptReviewDefinition string
	//go:embed action_search_entity.gotmpl
	ActionPromptSearchEntity string
	//go:embed action_search_class.gotmpl
	ActionPromptSearchClass string
	//go:embed action_search_function.gotmpl
	ActionPromptSearchFunction string
	//go:embed action_grep_search.gotmpl
	ActionPromptGrepSearch string
	//go:embed action_search_filename.gotmpl
	ActionPromptSearchFileName string
	//go:embed action_glob_search.gotmpl
	ActionPromptGlobSearch string
	//go:embed action_bash.gotmpl
	ActionPromptBash string
	//go:embed action_deploy.gotmpl
	ActionPromptDeploy string
	//go:embed action_deploy_backend.gotmpl
	ActionPromptDeployBackend string
	//go:embed action_sequential_thinking.gotmpl
	ActionPromptSequentialThinking string
	//go:embed action_thinking.gotmpl
	ActionPromptThinking string
	//go:embed action_str_replace_editor.gotmpl
	ActionPromptStrReplaceEditor string
	//go:embed action_recall_knowledge.gotmpl
	ActionPromptRecallKnowledge string
	//go:embed action_progress_thinking.gotmpl
	ActionPromptProgressThinking string
	//go:embed action_quick_research.gotmpl
	ActionPromptQuickResearch string
	//go:embed action_agent_search.gotmpl
	ActionPromptSearch string
	//go:embed action_agent_project_space_search.gotmpl
	ActionPromptProjectSpaceSearch string
	//go:embed action_codebase_search.gotmpl
	ActionPromptCodebaseSearch string
	//go:embed action_write_overview.gotmpl
	ActionPromptWriteOverview string
	//go:embed action_write_chapter.gotmpl
	ActionPromptWriteChapter string
	//go:embed action_git_clone.gotmpl
	ActionPromptGitClone string
	//go:embed action_browser_tool.gotmpl
	ActionBrowserToolChapter string
	//go:embed action_conclude.gotmpl
	ActionPromptConclude string
	//go:embed action_ask_user.gotmpl
	ActionPromptAskUser string
	//go:embed action_reuse_workflow.gotmpl
	ActionPromptReuseWorkflow string
)

func simpleToolObservation(name string, inputs, outputs map[string]any, err error) string {
	if err != nil {
		return fmt.Sprintf("Failed to invoking tool `%s`: %v", name, err)
	}

	var sb strings.Builder
	sb.WriteString("Invoked tool without error:\n")
	sb.WriteString(name)
	sb.WriteString(":\n")

	// Make inputs and outputs order stable for prompt cache.
	writeSortedMap := func(m map[string]any) {
		list := sort.StringSlice(lo.MapToSlice(m, func(k string, v any) string {
			return k + ": " + conv.JSONString(v) + "\n"
		}))
		sort.Sort(list)
		for idx := range list {
			sb.WriteString(list[idx])
		}
	}

	writeSortedMap(inputs)

	if len(outputs) > 0 {
		sb.WriteString("Outputs:\n")
		writeSortedMap(outputs)
	}
	return sb.String()
}

func ForgottenToolObservation(run *iris.AgentRunContext, name string, inputs, _ map[string]any, err error) string {
	if err != nil {
		return fmt.Sprintf("Failed to invoke tool `%s`: %v", name, err)
	}
	var sb strings.Builder
	sb.WriteString("Successfully invoked tool `")
	sb.WriteString(name)
	sb.WriteString("` with arguments:\n")
	for k, v := range inputs {
		sb.WriteString(k)
		sb.WriteString(": ")
		sb.WriteString(conv.JSONString(v))
		sb.WriteRune('\n')
	}
	sb.WriteString("Outputs are truncated due to context limit.\n")

	return sb.String()
}

func ToolObservation(run *iris.AgentRunContext, stepID string, name string, inputs, outputs map[string]any, err error) string {
	var logger iris.Logger
	if run != nil {
		logger = run.GetLogger()
	}
	var rawPromptTemplate string
	fallback := func() string {
		return simpleToolObservation(name, inputs, outputs, err)
	}
	switch name {
	case string(workspace.ActionFindIdentifier):
		rawPromptTemplate = ActionPromptFindIdentifier
	case string(workspace.ActionPatchFile), string(workspace.ActionSearchReplaceFile):
		rawPromptTemplate = ActionPromptPatchFile
	case string(workspace.ActionEditFile):
		rawPromptTemplate = ActionPromptEditFile
	case string(workspace.ActionListDirectory):
		rawPromptTemplate = ActionPromptReadDirectory
	case string(workspace.ActionReadFile):
		rawPromptTemplate = ActionPromptReadFile
	case string(workspace.ActionReadMarkDownFiles):
		rawPromptTemplate = ActionPromptReadMarkDownFiles
	case string(workspace.ActionCreateFile):
		rawPromptTemplate = ActionPromptCreateFile
	case string(workspace.ActionAppendFile):
		rawPromptTemplate = ActionPromptAppendFile
	case workspace.ActionWriteFileSmart:
		rawPromptTemplate = ActionPromptWriteFile
	case string(background.ActionReviewDefinition):
		rawPromptTemplate = ActionPromptReviewDefinition
	case string(background.ActionSearchEntity):
		rawPromptTemplate = ActionPromptSearchEntity
	case string(background.ActionSearchClass):
		rawPromptTemplate = ActionPromptSearchClass
	case string(background.ActionSearchFunction):
		rawPromptTemplate = ActionPromptSearchFunction
	case string(workspace.ActionGrepSearch):
		rawPromptTemplate = ActionPromptGrepSearch
	case string(workspace.ActionSearchFileName):
		rawPromptTemplate = ActionPromptSearchFileName
	case string(workspace.ActionGlobSearch):
		rawPromptTemplate = ActionPromptGlobSearch
	case string(workspace.ActionExecuteCommand), "execute_command":
		rawPromptTemplate = ActionPromptBash
	case workspace.ToolDeploy:
		rawPromptTemplate = ActionPromptDeploy
	case workspace.ToolDeployBackend:
		rawPromptTemplate = ActionPromptDeployBackend
	case controltool.ActionSequentialThinking:
		rawPromptTemplate = ActionPromptSequentialThinking
	case controltool.ActionThinking:
		rawPromptTemplate = ActionPromptThinking
	case controltool.ToolConclusion:
		rawPromptTemplate = ActionPromptConclude
	case string(workspace.ActionStrReplaceEditor):
		rawPromptTemplate = ActionPromptStrReplaceEditor
	case "recall_knowledge":
		rawPromptTemplate = ActionPromptRecallKnowledge
	case "progress_think":
		rawPromptTemplate = ActionPromptProgressThinking
	case websearchtool.ToolQuickResearch:
		rawPromptTemplate = ActionPromptQuickResearch
	case websearchtool.ToolSearch:
		rawPromptTemplate = ActionPromptSearch
	case websearchtool.ToolKnowledgeBaseSearch:
		rawPromptTemplate = ActionPromptProjectSpaceSearch
	case "codebase_search":
		rawPromptTemplate = ActionPromptCodebaseSearch
	case "write_overview":
		rawPromptTemplate = ActionPromptWriteOverview
	case "write_chapter":
		rawPromptTemplate = ActionPromptWriteChapter
	case "git_clone":
		rawPromptTemplate = ActionPromptGitClone
	case "ask_user":
		rawPromptTemplate = ActionPromptAskUser
	case "get_reusable_workflow":
		rawPromptTemplate = ActionPromptReuseWorkflow
	case "mcp_call":
		var callArgs agententity.MCPCallArgs
		err2 := mapstructure.Decode(inputs, &callArgs)
		if err2 != nil {
			if logger != nil {
				logger.Errorf("failed to decode mcp_call args: %v\n", err2)
			}
			return fallback()
		}
		// callArgs.Arguments convert to map[string]any
		var toolInputs map[string]any
		err2 = json.Unmarshal([]byte(callArgs.Arguments), &toolInputs)
		return simpleToolObservation(callArgs.Name, toolInputs, outputs, err)
	default:
		if strings.HasPrefix(name, "browser_") && name != "browser_use" {
			rawPromptTemplate = ActionBrowserToolChapter
		} else if strings.HasPrefix(name, "mcp:") {
			var extraDesc string
			// FIXME(cyx): import cycle
			const ExtraOutputDescKey = "aime_mcp_extra_desc"
			if outputs != nil {
				extraDesc = conv.DefaultAny[string](outputs[ExtraOutputDescKey])
				// Copy it, avoid deleting key permanently.
				outputs = lo.Assign(outputs)
				delete(outputs, ExtraOutputDescKey)
			}
			res := fallback() + extraDesc
			return res
		} else {
			return fallback()
		}
	}

	vars := map[string]any{
		"input":   lo.Ternary(inputs == nil, map[string]any{}, inputs),
		"output":  lo.Ternary(outputs == nil, map[string]any{}, outputs),
		"step_id": stepID,
	}
	if err != nil {
		vars["error"] = err.Error()
	}

	promptTemplate := prompt.NewPromptTemplate(prompt.Template{
		Template: rawPromptTemplate,
	})
	res, pErr := promptTemplate.ComposePrompt(vars)
	if pErr != nil {
		if logger != nil {
			logger.Errorf("failed to build action output prompt for action %s: %v, fallback to plain template\n", name, pErr)
		}
		return fallback()
	}
	observation := res.String()
	// there might be templates rendering empty string as result, which will cause the agent, fallback to simpleToolObservation
	if len(observation) == 0 {
		return fallback()
	}

	return observation
}

func WithReActToolHistory(
	run *iris.AgentRunContext,
	traceSteps []*iris.AgentRunStep,
	thoughtPrompt *template.Template,
	observationPrompt *template.Template,
	observationFormatFunc func(run *iris.AgentRunContext, step *iris.AgentRunStep) framework.ChatMessage,
	condenser condenser.Summarizer,
) agentprompt.ComposeVaryMessageOption {
	return func() ([]*framework.ChatMessage, error) {
		messages := make([]*framework.ChatMessage, 0)
		if len(traceSteps) > 0 {
			observations := lo.FlatMap(traceSteps, func(step *iris.AgentRunStep, idx int) []*framework.ChatMessage {
				var buf bytes.Buffer
				err := thoughtPrompt.Execute(&buf, step.Thought)
				if err != nil {
					run.GetLogger().Errorf("failed to execute thought prompt: %v", err)
					return nil
				}
				assistant := framework.ChatMessage{
					Role:    framework.RoleAssistant,
					Content: buf.String(),
				}
				observation := framework.ChatMessage{
					Role: framework.RoleUser,
				}
				var message string
				if observationFormatFunc != nil {
					observation = observationFormatFunc(run, step)
					message = observation.Content
				} else if step.Action != nil {
					message = ToolObservation(run, step.StepID, step.Action.Name(), step.Inputs, step.Outputs, step.Error)
				} else if step.Error != nil {
					message = step.Error.Error()
				}
				if len(message) == 0 {
					message = "No action is executed."
				}
				observation.Content = message
				if step.Thought.FunctionCall {
					assistant.ToolCalls = []framework.LLMToolFunction{
						{
							ID:         step.Thought.ToolCallID, // Gemini 返回的 tool call 没有 ID，只有 Name 来区分不同 tool call
							Name:       step.Action.Name(),
							Parameters: step.Inputs,
						},
					}
					observation.Role = framework.RoleTool
					observation.ToolCallID = step.Thought.ToolCallID
				}
				return []*framework.ChatMessage{
					&assistant,
					&observation,
				}
			})
			messages = append(messages, observations...)

			if lastBrowserStep, ok := browser.ContainedBrowserStep(traceSteps); ok {
				// 只取最新的浏览器状态
				if lastBrowserState := browser.GetBrowserState(run, lastBrowserStep.Outputs); lastBrowserState != nil {
					messages = append(messages, lastBrowserState...)
				}
			}
		}

		if condenser != nil {
			condensedMessage, err := condenser.Summarize(run, messages, false)
			if err != nil {
				return nil, err
			}
			messages = condensedMessage
		}

		return messages, nil
	}
}
