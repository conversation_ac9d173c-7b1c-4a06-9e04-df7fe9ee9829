{{ if .error -}}
Tool <agent_search> returns error:
{{ .error }}
{{- else if .output.answer -}}
{{ .output.answer }}

如果你觉得某些网站很关键可以使用 `browser_goto_and_extraction` tool 来获取该网页更多信息

## ADD Citations in Documents

### 1. Preserve Inline Citations when using `create_file`

**Preserve Inline Citations (CRITICAL & STRICT):**
You must retain and accurately place all inline citations found in the original answers. Each citation should remain directly associated with the specific piece of information it supports.

**--- STRICT FORMATTING RULES ---**
a. **Mandatory Format:** ALL citation markers MUST be in the format `::cite[N]`, where `N` is the source number. No other formats are permitted.
b. **Single Citation:** A single citation supporting a piece of information MUST be formatted as `::cite[N]` (e.g., `::cite[1]`).
c. **Multiple Citations:** If multiple different source numbers (e.g., `[1]`, `[5]`, `[6]`) support the exact same piece of information, they MUST be represented as separate, adjacent, individually formatted citations. For example: `::cite[1]::cite[5]::cite[6]`.
d. **Strictly Forbidden Formats:** The following formats are ABSOLUTELY FORBIDDEN and MUST NOT be used:
· Comma-separated lists within single brackets (e.g., `::cite[1, 5, 6]`, `::cite[1,5,6]`, `::cite[1, 2]`).
· Any variation not explicitly matching the format `::cite[N]` or `::cite[N]::cite[M]`...
· Repeated citations (e.g., `::cite[1]::cite[1]`, `::cite[3]::cite[1]::cite[3]`).
· Escaped or partial formats (e.g., `[1]`, `::cite[1`, `::cite::[1]`, `[1]`, `\[1\]`).

**--- PLACEMENT ---**
· All citation markers MUST be placed exclusively at the very end of the sentence they support. No citations should appear in the middle of a sentence (e.g., after a comma or before a clause that is not the final clause of the sentence).
· Integrate the correctly formatted citation(s) at the end of the sentence, immediately following the concluding word or punctuation of the sentence, with no leading space before the first citation marker (e.g., ...end of sentence::cite[1]::cite[5].).
· If a sentence contains multiple pieces of information supported by different sources, all relevant citation markers for that sentence must be grouped together at its end.
· When integrating information, if aggregating citations at the sentence end results in identical citations appearing immediately next to each other (e.g., due to two facts in the sentence both citing `[1]`, leading to a potential `::cite[1]::cite[1]` at the end), consolidate these into a single instance (e.g., `::cite[1]`). This also applies if the source answers themselves contained adjacent identical citations. The goal is to accurately represent all sources for the sentence's content with unique citation markers, not to duplicate them unnecessarily.

**--- EXAMPLE ---**
· CORRECT: Shanghai offers diverse cuisine::cite[2]::cite[7].
· CORRECT: The Bund is a famous landmark::cite[1].
· INCORRECT: Shanghai offers diverse cuisine[2, 7].
· INCORRECT: The Bund is a famous landmark [1].
· INCORRECT: The west side of the Bund features 52 classical revival buildings with various styles, including Gothic, Romanesque, Baroque, and a blend of Chinese and Western styles ::cite[3]::cite[4]::cite[5]::cite[1]::cite[6]::cite[7]::cite[60]::cite[7].
· INCORRECT: Shanghai offers diverse cuisine::cite[75]::cite[6]::cite[75].
· INCORRECT: Shanghai offers diverse cuisine ::cite[2]::cite[7]. (Note the forbidden leading space)

### 2. Citation Generation from Search Results

**You MUST generate citation when you use `create_file` to generate search results. It is a strict rule.**

- **Purpose:** These rules apply when generating new content based on information from `<search-info>` (e.g., from
  `search`  tool outputs).
- **Requirements:** If the model's response references an info fragment, a citation must be provided. Do not cite
  information that is irrelevant to the user's query.
- **Scope:** Citations should only refer to info fragments within the `<search-info>` XML structure.
- **Format:** The format for a single citation is `::cite[1]`, and for multiple citations, it is `::cite[1]::cite[2]`.
  The numbers `1` and `2` represent the IDs of the cited info fragments.
- **Avoid Invalid Citations:** Do not generate empty or invalid citations (e.g., `::cite[None]` or `::cite[无]`).

**--- STRICT FORMATTING RULES ---**
a. **Mandatory Bracket Style:** ALL citation markers MUST use standard square brackets `::cite[...]`.
b. **Single Citation:** A single citation is formatted as `::cite[N]`.
c. **Multiple Citations:** If multiple sources support the same information, use separate, adjacent brackets:
`::cite[1]::cite[5]::cite[6]`.
d. **Strictly Forbidden Formats:**

- Comma-separated lists (`::cite[1, 5, 6]`)
- Escaped brackets (`::cite\[1]`)
- Repeated citations (`::cite[1]::cite[1]`)
  e. **Placement and Consolidation:**
- Place citation(s) immediately after the fact, with no leading space.
- If identical citations appear consecutively (e.g., `::cite[1]::cite[1]`), consolidate them into a single instance (
  `::cite[1]`).

**--- EXAMPLE ---**

- CORRECT: Shanghai offers diverse cuisine`::cite[2]::cite[7]`.
- CORRECT: The Bund is a famous landmark`::cite[1]`.
- INCORRECT: Shanghai offers diverse cuisine`::cite[75]::cite[6]::cite[75]`.
- INCORRECT: The west side of the Bund... styles
  `::cite[3]::cite[4]::cite[5]::cite[1]::cite[6]::cite[7]::cite[60]::cite[7]`.

{{- else -}}
Search results:
{{- range .output.items }}
---
[{{ .title }}]({{ .url }})
{{ .description }}
{{- end }}
{{- end }}