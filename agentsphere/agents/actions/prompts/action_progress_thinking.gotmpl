{{ if .error -}}
Tool <think> returns error:
{{ .error }}
{{- else -}}
{{- $cnt := 0 -}}
{{- if .output.knowledge_renders -}}
{{- range .output.knowledge_renders -}}
{{- if or (not $.step_id) (eq .step_id $.step_id) -}}
{{- $cnt = add $cnt 1 -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{ if gt $cnt 0 -}}
# Here are some knowledge you can refer to:
{{range .output.knowledge_renders -}}
{{- if or (not $.step_id) (eq .step_id $.step_id) -}}
<knowledge>
{{ if eq .knowledge.category "" }}
{{ else if eq .knowledge.category "system" }}
KnowledgeType: system, system internal knowledge, lowest priority
{{ else if eq .knowledge.category "scenario" }}
KnowledgeType: scenario, scenario specific knowledge, highest priority
{{ else if eq .knowledge.category "tool" }}
KnowledgeType: tool, when calling related tools as reference
{{ else }}
KnowledgeType: unknown
{{- end }}
<use_this_knowledge_when>
{{.knowledge.used_when}}
</use_this_knowledge_when>
<content>
{{.knowledge.content}}
</content>
{{- end -}}
{{- end }}
</knowledge>
{{- end -}}
{{- if .output.experience -}}
[Historical Experiences]
Here are some key experiences from historical tasks. Please try to refer to them to complete the current task.
<experiences>
{{- range .output.experience }}
<experience>
{{ .content }}
</experience>
{{- end }}
</experiences>
{{- end -}}
{{ if and (le $cnt 0) (not .output.experience) -}}
Great, keep doing it. 
{{ end }}
You should follow the instruction in `next_stage` and make the progress via appropriate tools.
After you finish the intended next stage, remember to call the progress_think tool again to get the next stage instruction.
{{- end }}