package llmtool

import (
	"fmt"
	"sync"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience/filter"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
)

const ToolProgressThinking = "progress_think"

var (
	ToolProgressThinkingDescription = fmt.Sprintf(`This tool helps you methodically work through problems by tracking your current progress and determining the most appropriate next stage/phase. When you make some progress(E.g. you have completed/failed a phase or stage of the task, not a small step), remember to use this tool for progress updating and planning the next stage.
### Hints ###
#### How To Use This Tool

1. Begin by assessing the problem and estimating the number of steps needed
2. For each stage:
   - Tracks the current state of your problem-solving process using markdown checkboxes:
		- Use - [ ] for uncompleted tasks
		- Use - [x] for completed tasks
		- Indent subobjectives under their parent objectives with 4 spaces
		- Mark optional objectives with "(Optional)" at the end of the task description
   - Determine the most appropriate next stage based on current progress

#### The full format of conclusion must be:
%sparam="progress"
- [ ] Main Objective 1
    - [x] Completed Subobjective 1.1
    - [ ] Pending Subobjective 1.2 (In Progress)
- [ ] Main Objective 2
    - [ ] Subobjective 2.1
    - [ ] Subobjective 2.2 (Optional)
%s
%sparam="next_stage"
Specific stage to take next
%s
`, "```", "```", "```", "```")
)

// 3. Only mark as complete when a satisfactory solution has been reached
// - Focus only on tracking progress and planning the next stage
// - Keep your progress summaries concise but informative
// - Be specific about the next action to take
// - Adjust your estimated total steps as you gain more insight into the problem

type ProgressThinkingArgs struct {
	Progress  string `json:"progress" mapstructure:"progress" description:"a summary of what has been accomplished so far and what's the remaining work to be done"`
	NextStage string `json:"next_stage" mapstructure:"next_stage" description:"the specific stage that should be taken next to advance toward the solution"`
	//PrepareToCallTool string `json:"prepare_to_call_tool" mapstructure:"prepare_to_call_tool" description:"(optional) if you want to call a tool in next stage, you can optionally fill this field to get the knowledge of it is available"`
	// TODO(syx): merge the conclude tool to this tool
	// IsCompleted bool   `json:"is_completed" mapstructure:"is_completed" description:" Boolean, only mark as complete when a satisfactory solution has been reached"`
}

type ProgressThinkingOutput struct {
	Progress         string                        `json:"progress" mapstructure:"progress"`
	NextStage        string                        `json:"next_stage" mapstructure:"next_stage"`
	KnowledgeRenders []*knowledges.KnowledgeRender `json:"knowledge_renders" mapstructure:"knowledge_renders"`
	Experience       []filter.ExperienceItem       `json:"experience" mapstructure:"experience"`
	// TODO(syx): merge the conclude tool to this tool
	// IsCompleted bool   `json:"is_completed" mapstructure:"is_completed" description:" Boolean, only mark as complete when a satisfactory solution has been reached"`
}

// review the current progress, determine the next stage, retreive related knowledge
func NewProgressThinkingTool(kg knowledges.Knowledgebase, param knowledges.RetrieveParam) iris.Action {
	return actions.ToTool(ToolProgressThinking, ToolProgressThinkingDescription, func(c *iris.AgentRunContext, args ProgressThinkingArgs) (ProgressThinkingOutput, error) {
		group := errgroup.Group{}
		group.SetLimit(-1)

		var filteredExperiences []filter.ExperienceItem
		group.Go(func() error {
			defer func() {
				if r := recover(); r != nil {
					c.GetLogger().Errorf("experience retrieval panic: %v", r)
				}
			}()

			if !experience.EnabledExpInsights(c) {
				c.GetLogger().Infof("experience insights is disabled")
				return nil
			}

			experienceRetriever, err := experience.NewExperienceRetriever()
			if err != nil {
				c.GetLogger().Errorf("failed to create experience retriever: %v", err)
				return nil
			}
			scenario := iris.RetrieveStoreByKey[knowledges.Scenario](c, knowledges.ScenarioStoreKey)
			experienceTypes := []string{
				experience.Insights,
				experience.InsightsBadCase,
			}
			if scenario.Key == "data_analyze" {
				experienceTypes = append(experienceTypes, experience.InsightsScript)
			}

			if args.Progress == "" {
				c.GetLogger().Warn("no valid query input for experience retrieval")
				return nil
			}

			queryInput := fmt.Sprintf("Task Progress List:\n%s", args.Progress)

			c.GetLogger().Info("before RetrieveInsights")
			var executorAgent string
			if c.State.CurrentStep != nil {
				executorAgent = c.State.CurrentStep.ExecutorAgent
			} else {
				executorAgent = "unknown"
			}
			storeKey := fmt.Sprintf("retrieved_ids_%s", executorAgent)
			retrieverStore := iris.RetrieveStoreByKey[experience.RetrieverStore](c, storeKey)
			if retrieverStore.IsProgress {
				c.GetLogger().Info("skip experience retrieval because it is done by progress think")
				return nil
			}

			idsToExclude := retrieverStore.RetrievedIds
			if idsToExclude == nil {
				idsToExclude = []string{}
			}
			c.GetLogger().Infof("%v experiences already retrieved for %s", idsToExclude, storeKey)

			_, filteredExperiences, err = experienceRetriever.RetrieveInsights(
				c,
				queryInput,
				retrieverStore.OriginQuery,
				experience.MewtwoApplyType,
				experienceTypes,
				2,
				idsToExclude,
			)
			var retrievedIds = lo.Map(filteredExperiences, func(item filter.ExperienceItem, _ int) string {
				return item.ID
			})
			if err != nil {
				c.GetLogger().Errorf("failed to retrieve experience: %s", err)
				return nil
			}
			// 将新检索到的ID追加到store中，用于后续去重
			retrieverStore.RetrievedIds = append(idsToExclude, retrievedIds...)
			retrieverStore.IsProgress = true
			iris.UpdateStoreByKey(c, storeKey, retrieverStore)

			return nil
		})

		var (
			knowledgeItems   []knowledges.KnowledgeItem
			knowledgeRenders []*knowledges.KnowledgeRender
		)

		referenceStore := iris.RetrieveStoreByKey[planactentity.ReferenceStore](c, planactentity.ReferenceStoreKey)
		if len(referenceStore.SearchedRef.List) > 0 {
			param.WithCitation = true
		}
		if kg != nil {
			scenarioStore := iris.RetrieveStoreByKey[knowledges.Scenario](c, knowledges.ScenarioStoreKey)
			param.Scenario = scenarioStore
			mutex := sync.Mutex{}
			group.Go(func() error {
				kgs, _ := kg.RetrieveKnowledge(c, knowledges.KgRetrieveOption{
					Query:    args.NextStage,
					Strategy: knowledges.KgRetrieveStrategyLLM,
					Category: knowledges.KgRetrieveCategorySystem,
					Param:    param,
					Limit:    20,
				})
				mutex.Lock()
				knowledgeItems = append(knowledgeItems, kgs...)
				mutex.Unlock()
				return nil
			})
			group.Go(func() error {
				kgs, _ := kg.RetrieveKnowledge(c, knowledges.KgRetrieveOption{
					Query:    args.NextStage,
					Strategy: knowledges.KgRetrieveStrategyLLM,
					Category: knowledges.KgRetrieveCategoryScenario,
					Param:    param,
					Limit:    20,
				})
				mutex.Lock()
				knowledgeItems = append(knowledgeItems, kgs...)
				mutex.Unlock()
				return nil
			})
		}

		_ = group.Wait()
		knowledgeStore := iris.RetrieveStoreByKey[knowledges.Store](c, knowledges.StoreKey)
		for _, knowledge := range knowledgeItems {
			var (
				render *knowledges.KnowledgeRender
				exist  bool
			)
			render, exist = lo.Find(knowledgeStore.Renders, func(item *knowledges.KnowledgeRender) bool {
				return item.Knowledge.ID == knowledge.ID
			})
			if exist {
				if render.StepID != knowledges.SystemKnowledge {
					render.StepID = c.State.CurrentStep.StepID
				}
			} else {
				render = &knowledges.KnowledgeRender{
					Knowledge: knowledge,
					StepID:    c.State.CurrentStep.StepID,
				}
				knowledgeStore.Renders = append(knowledgeStore.Renders, render)
			}
			knowledgeRenders = append(knowledgeRenders, render)
		}
		iris.UpdateStoreByKey(c, knowledges.StoreKey, knowledgeStore)

		return ProgressThinkingOutput{
			Progress:         args.Progress,
			NextStage:        args.NextStage,
			KnowledgeRenders: knowledgeRenders,
			Experience:       filteredExperiences,
		}, nil
	})
}
