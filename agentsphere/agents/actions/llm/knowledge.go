package llmtool

import (
	"code.byted.org/gopkg/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
)

const (
	RecallKnowledgeToolName    = "recall_knowledge"
	RecallKnowledgeDescription = "Retrieve knowledge which may help you to complete your task based on your task plan."
)

type RecallKnowledgeToolArgs struct {
	Query string `json:"query" mapstructure:"query" description:"your query to recall knowledges, normally this could be your brief task plan, the brief task plan does not to be very detailed but it should contains the key points and steps. E.g: I will write a python script which uses matplotlib and pandas to plot a chart of the data in a file."`
}

type RecallKnowledgeToolResult struct {
	Enabled    bool                       `json:"enabled" mapstructure:"enabled"`
	Knowledges []knowledges.KnowledgeItem `json:"knowledges" mapstructure:"knowledges"`
}

func NewRecallKnowledgeTool(kg knowledges.Knowledgebase, param knowledges.RetrieveParam) *actions.Tool {
	return actions.ToTool(RecallKnowledgeToolName, RecallKnowledgeDescription, func(c *iris.AgentRunContext, args RecallKnowledgeToolArgs) (*RecallKnowledgeToolResult, error) {
		if kg == nil {
			return &RecallKnowledgeToolResult{
				Enabled: false,
			}, nil
		}
		knowledges, err := kg.RetrieveKnowledge(c, knowledges.KgRetrieveOption{
			Query: args.Query,
			Param: param,
			Limit: 20,
		})
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessage(err, "failed to recall knowledge"))
		}
		return &RecallKnowledgeToolResult{
			Enabled:    true,
			Knowledges: knowledges,
		}, nil
	})
}
