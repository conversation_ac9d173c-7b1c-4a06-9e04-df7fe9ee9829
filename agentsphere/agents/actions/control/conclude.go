package controltool

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

type ConclusionEvaluation string

const (
	ConclusionEvaluationSuccess          ConclusionEvaluation = "success"
	ConclusionEvaluationPartialCompleted ConclusionEvaluation = "partial_completed"
	ConclusionEvaluationInterrupted      ConclusionEvaluation = "user_interrupted"
	ConclusionEvaluationFailed           ConclusionEvaluation = "failed"
	ConclusionEvaluationFatal            ConclusionEvaluation = "fatal" // the task shall terminate
)

const (
	ToolConclusion            = "conclude"
	ToolConclusionDescription = `finish current task and provide a conclusion
### Hints ###
- If important results (files, images, deployed url, etc.) are generated during task execution, please list them all and attach them to the conclusion. 
- If code relevant project folders are generated during task execution, please list them all and attach them to the conclusion.
- Return ALL results in the conclusion with complete details and metadata for each entry."
- Never truncate results due to length
- Always include complete metadata
- Include important datasource urls in the reference section.
The full format of conclusion must be:
<tool name="conclude">
<conclusion>
{Your conclusion here, should contains all the important results and your explorations, expose everything you found or uncompleted works}
</conclusion>
<evaluation>
(success|partial_completed|failed)
</evaluation>
<reference>
- [description_1](relative_file_path)
  Authors: <AUTHORS>
  Date: [submission_date]
  ...other metadata
- [dataurl_1](url)
  Type: [type]
  ...other metadata
...
</reference>
<project_reference>
- path: [relative_project1_path]
- path: [relative_project2_path]
...
</project_reference>
</tool>

<IMPORTANT>
*You must try your best to complete as much as possible you can in the task, DO NOT return <evaluation>fail</evaluation> or <evaluation>partial_completed</evaluation> easily! Just as said before, everytime you give conclusion means your processing lifecycle is over, and even next time you handle the same task, you will forget everything you've done!
**If you finished most part of your work, even if there something miss or still a little work left, just return <evaluation>success</evaluation>, your job can be mark as success, good job to you!**
</IMPORTANT>
`
)

type ConclusionArgs struct {
	Conclusion       string               `json:"conclusion" mapstructure:"conclusion" description:"conclusion of the task; if partially completed or failed, provide the obstacles you meet and outcomes already achieved"`
	Evaluation       ConclusionEvaluation `json:"evaluation" mapstructure:"evaluation" description:"(required) evaluation of the task result, (success|partial_completed|failed)"`
	Reference        string               `json:"reference" mapstructure:"reference" description:"(optional) list links of important results (files, images, deployed url, etc.) in markdown format"`
	ProjectReference string               `json:"project_reference" mapstructure:"project_reference" description:"(optional) list paths of important projects in yaml format"`
}

type ConclusionOutput struct {
	Content          string                `json:"content" mapstructure:"content"`
	Evaluation       ConclusionEvaluation  `json:"evaluation" mapstructure:"evaluation"`
	Reference        iris.Reference        `json:"reference" mapstructure:"reference"`
	ProjectReference iris.ProjectReference `json:"project_reference" mapstructure:"project_reference"`
	Progress         string                `json:"progress" mapstructure:"progress"`
}

// NewConclusionTool returns a special conclusion tool that should finish current task and provide a conclusion
func NewConclusionTool() iris.Action {
	return actions.ToTool(ToolConclusion, ToolConclusionDescription, func(c *iris.AgentRunContext, args ConclusionArgs) (ConclusionOutput, error) {
		if len(args.Evaluation) == 0 {
			return ConclusionOutput{
				Content:          args.Conclusion,
				Evaluation:       ConclusionEvaluationPartialCompleted,
				Reference:        iris.ParseReference(args.Reference),
				ProjectReference: iris.ParseProjectReference(args.ProjectReference),
			}, nil
		}
		return ConclusionOutput{
			Content:          args.Conclusion,
			Evaluation:       args.Evaluation,
			Reference:        iris.ParseReference(args.Reference),
			ProjectReference: iris.ParseProjectReference(args.ProjectReference),
		}, nil
	})
}

var (
	ToolProgreActConclusion            = "conclude"
	ToolProgreActConclusionDescription = `finish current task and provide a conclusion
### Hints ###
- If important results (files, images, deployed url, etc.) are generated during task execution, please list them all and attach them to the conclusion. 
- If code relevant project folders are generated during task execution, please list them all and attach them to the conclusion.
- Return ALL results in the conclusion with complete details and metadata for each entry.
- Never truncate results due to length
- Always include complete metadata
- Include important datasource urls in the reference section.
Each parameter is required, and the format is as follows:

content: {Your conclusion here, should contain all the important results and your explorations, expose everything you found or uncompleted works}
evaluation: (success|partial_completed|failed)
reference: here is an example of reference format:
- [description_1](relative_file_path)
- [datasource_url_1](url)
reference: |
	- [description_1](relative_file_path)
	Authors: <AUTHORS>
	Date: [submission_date]  
	...other metadata
	- [description_2](deployed_url)
	Type: [type]
	Resolution: [resolution]
	...other metadata
project_reference: |
	<project_reference>
	- path: [relative_project1_path]
	- path: [relative_project2_path]
	</project_reference>
`
)

type ProgreActConclusionArgs struct {
	Content          string               `json:"content" mapstructure:"content" description:"main content of the task's conclusion; if partially completed or failed, provide the obstacles you meet and outcomes already achieved"`
	Evaluation       ConclusionEvaluation `json:"evaluation" mapstructure:"evaluation" description:"(required) evaluation of the task result, (success|partial_completed|failed)"`
	Reference        string               `json:"reference" mapstructure:"reference" description:"(optional) list links of important results (files, images, deployed url, etc.) in markdown format. For example, \"- [description_1](relative_file_path)\n- [description_2](deployed_url)\""`
	ProjectReference string               `json:"project_reference" mapstructure:"project_reference" description:"(optional) YAML-formatted list of important project relative paths, wrapped with <project_reference> tag"`
	Progress         string               `json:"-" mapstructure:"progress"`
}

// NewConclusionTool returns a special conclusion tool that should finish current task and provide a conclusion
func NewProgreActConclusionTool() iris.Action {
	return actions.ToTool(ToolProgreActConclusion, ToolProgreActConclusionDescription, func(c *iris.AgentRunContext, args ProgreActConclusionArgs) (ConclusionOutput, error) {
		return ConclusionOutput{
			Content:          args.Content,
			Progress:         args.Progress,
			Evaluation:       args.Evaluation,
			Reference:        iris.ParseReference(args.Reference),
			ProjectReference: iris.ParseProjectReference(args.ProjectReference),
		}, nil
	})
}
