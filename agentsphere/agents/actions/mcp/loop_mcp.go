package mcptool

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"code.byted.org/bcc/conf_engine/jsoniter"
	"github.com/sourcegraph/conc/panics"

	"code.byted.org/devgpt/kiwis/lib/mapstructure"

	"code.byted.org/lang/gg/gptr"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

const (
	ToolHelperID = "tool_helper"

	ToolCheckLoopTool            = "check_loop_tool"
	ToolCheckLoopToolDescription = "Check if a specific tool is available for loop execution. Supports MCP tools (with display_name starting with 'mcp:' in function description) and special tools (vision, lark_download). Use this BEFORE attempting to use loop_mcp_tool to avoid errors and time waste. You can pass either display_name or name for target_tool."
)

// CheckLoopToolArgs 检查循环工具可用性的输入参数
type CheckLoopToolArgs struct {
	TargetTool string `json:"target_tool" mapstructure:"target_tool" description:"Required. The tool name to check - can be either display_name from function description (starting with 'mcp:') or function name, or supported non-mcp tools like 'vision', 'lark_download'"`
}

// LoopToolInput 循环工具的输入参数
type LoopToolInput struct {
	TargetTool      string           `json:"target_tool" mapstructure:"target_tool" jsonschema:"title=Target Tool Name,description=The name of the tool to loop call (can be either display_name from function description starting with 'mcp:' or function name, or supported tools like vision/lark_download),required"`
	InputList       []map[string]any `json:"input_list" mapstructure:"input_list" jsonschema:"title=Input List,description=List of input parameters for each loop iteration,required"`
	MaxIterations   int              `json:"max_iterations,omitempty" mapstructure:"max_iterations" jsonschema:"title=Max Iterations,description=Maximum number of iterations (default: 100),minimum=1,maximum=1000"`
	ContinueOnError bool             `json:"continue_on_error,omitempty" mapstructure:"continue_on_error" jsonschema:"title=Continue On Error,description=Whether to continue execution when an iteration fails (default: false)"`
	BatchSize       int              `json:"batch_size,omitempty" mapstructure:"batch_size" jsonschema:"title=Batch Size,description=Number of iterations to process in each batch (default: 10),minimum=1,maximum=50"`
	DelayMs         int              `json:"delay_ms,omitempty" mapstructure:"delay_ms" jsonschema:"title=Delay Milliseconds,description=Delay between iterations in milliseconds (default: 100),minimum=0,maximum=10000"`
	Concurrency     int              `json:"concurrency,omitempty" mapstructure:"concurrency" jsonschema:"title=Concurrency,description=Number of concurrent workers (default: 1, max: 10),minimum=1,maximum=10"`
	TimeoutSeconds  int              `json:"timeout_seconds,omitempty" mapstructure:"timeout_seconds" jsonschema:"title=Timeout Seconds,description=Timeout in seconds (default: 1800, max: 1800),minimum=300,maximum=1800"`
}

// LoopToolOutput 循环工具的输出结果
type LoopToolOutput struct {
	Results         []map[string]any `json:"results" mapstructure:"results" jsonschema:"title=Results,description=Results from all iterations"`
	Errors          []string         `json:"errors,omitempty" mapstructure:"errors" jsonschema:"title=Errors,description=Errors occurred during iterations"`
	TotalIterations int              `json:"total_iterations" mapstructure:"total_iterations" jsonschema:"title=Total Iterations,description=Total number of iterations executed"`
	SuccessCount    int              `json:"success_count" mapstructure:"success_count" jsonschema:"title=Success Count,description=Number of successful iterations"`
	FailureCount    int              `json:"failure_count" mapstructure:"failure_count" jsonschema:"title=Failure Count,description=Number of failed iterations"`
	ExecutionTime   string           `json:"execution_time" mapstructure:"execution_time" jsonschema:"title=Execution Time,description=Total execution time"`
	TimedOut        bool             `json:"timed_out,omitempty" mapstructure:"timed_out" jsonschema:"title=Timed Out,description=Whether execution was terminated due to timeout"`
	TimeoutMessage  string           `json:"timeout_message,omitempty" mapstructure:"timeout_message" jsonschema:"title=Timeout Message,description=Timeout information if execution was terminated"`
}

// iterationResult 单次迭代的结果
type iterationResult struct {
	Index  int
	Result map[string]any
	Error  error
}

// NewLoopTool 创建循环工具
func NewLoopTool() iris.Action {
	return actions.NewTool(actions.NewToolOption{
		Name: McpToolName(ToolHelperID, "loop_mcp_tool"),
		Description: `Loop call a tool with multiple input parameters and return all results with concurrent execution support. 
Supports both MCP tools (with display_name starting with 'mcp:' in function description) and special non-MCP tools (vision, lark_download).
You can pass either display_name or function name for target_tool parameter.
This tool is useful when you need to:
1. Call the same tool multiple times with different parameters
2. Collect and aggregate results from multiple API calls
3. Process batch data efficiently with concurrent execution
4. Get comprehensive data by iterating through different queries
5. Handle large datasets with timeout and concurrency control

Example usage:
- Get multiple weather forecasts for different cities concurrently using MCP tools
- Analyze multiple images in parallel using the vision tool
- Validate multiple HTML pages using the lark_download tool
- Search multiple terms and collect all results in parallel
- Process a list of user queries with the same tool efficiently`,
		Input: framework.Schema{
			Type: framework.TypeObject,
			Properties: map[string]framework.Schema{
				"target_tool": {
					Type:        framework.TypeString,
					Description: "The name of the tool to loop call (can be either display_name from function description starting with 'mcp:' or function name, or supported tools like vision/lark_download)",
				},
				"input_list": {
					Type:        framework.TypeArray,
					Description: "List of input parameters for each loop iteration",
					Items: &framework.Schema{
						Type: framework.TypeObject,
					},
				},
				"max_iterations": {
					Type:        framework.TypeInteger,
					Description: "Maximum number of iterations (default: 100)",
					Minimum:     gptr.Of(1.0),
					Maximum:     gptr.Of(1000.0),
				},
				"continue_on_error": {
					Type:        framework.TypeBoolean,
					Description: "Whether to continue execution when an iteration fails (default: false)",
				},
				"batch_size": {
					Type:        framework.TypeInteger,
					Description: "Number of iterations to process in each batch (default: 10)",
					Minimum:     gptr.Of(1.0),
					Maximum:     gptr.Of(50.0),
				},
				"delay_ms": {
					Type:        framework.TypeInteger,
					Description: "Delay between iterations in milliseconds (default: 100)",
					Minimum:     gptr.Of(0.0),
					Maximum:     gptr.Of(10000.0),
				},
				"concurrency": {
					Type:        framework.TypeInteger,
					Description: "Number of concurrent workers (default: 1, max: 10)",
					Minimum:     gptr.Of(1.0),
					Maximum:     gptr.Of(10.0),
				},
				"timeout_seconds": {
					Type:        framework.TypeInteger,
					Description: "Timeout in seconds (default: 300, max: 1800 which is 30 minutes)",
					Minimum:     gptr.Of(1.0),
					Maximum:     gptr.Of(1800.0),
				},
			},
			Required: []string{"target_tool", "input_list"},
		},
		Output: framework.Schema{
			Type: framework.TypeObject,
			Properties: map[string]framework.Schema{
				"results": {
					Type:        framework.TypeArray,
					Description: "Results from all iterations",
					Items: &framework.Schema{
						Type: framework.TypeObject,
					},
				},
				"errors": {
					Type:        framework.TypeArray,
					Description: "Errors occurred during iterations",
					Items: &framework.Schema{
						Type: framework.TypeString,
					},
				},
				"total_iterations": {
					Type:        framework.TypeInteger,
					Description: "Total number of iterations executed",
				},
				"success_count": {
					Type:        framework.TypeInteger,
					Description: "Number of successful iterations",
				},
				"failure_count": {
					Type:        framework.TypeInteger,
					Description: "Number of failed iterations",
				},
				"execution_time": {
					Type:        framework.TypeString,
					Description: "Total execution time",
				},
				"timed_out": {
					Type:        framework.TypeBoolean,
					Description: "Whether execution was terminated due to timeout",
				},
				"timeout_message": {
					Type:        framework.TypeString,
					Description: "Timeout information if execution was terminated",
				},
			},
		},
		Impl: func(c *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
			res, err := executeLoopTool(c, step)
			return res, iris.NewRecoverable(err)
		},
	})
}

// executeLoopTool 执行循环工具逻辑
func executeLoopTool(run *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
	startTime := time.Now()

	var input LoopToolInput
	err := mapstructure.Decode(step.Inputs, &input)
	if err != nil {
		return nil, fmt.Errorf("failed to decode input: %v", err)
	}

	// 设置默认值
	if input.MaxIterations == 0 {
		input.MaxIterations = 100
	}
	if input.BatchSize == 0 {
		input.BatchSize = 10
	}
	if input.DelayMs == 0 {
		input.DelayMs = 100
	}
	if input.Concurrency == 0 {
		input.Concurrency = 1
	}
	if input.Concurrency > 10 {
		input.Concurrency = 10
	}
	if input.TimeoutSeconds == 0 {
		input.TimeoutSeconds = 1800 // 30分钟默认超时
	}

	// 参数验证
	if input.TargetTool == "" {
		return nil, fmt.Errorf("target_tool is required")
	}
	if len(input.InputList) == 0 {
		return nil, fmt.Errorf("input_list cannot be empty")
	}
	if len(input.InputList) > input.MaxIterations {
		return nil, fmt.Errorf("input list length (%d) exceeds max iterations (%d)", len(input.InputList), input.MaxIterations)
	}

	// 创建超时上下文
	ctx, cancel := context.WithTimeout(run, time.Duration(input.TimeoutSeconds)*time.Second)
	defer cancel()

	run.GetLogger().Infof("Starting loop tool execution for '%s' with %d iterations, %d workers, %d second timeout",
		input.TargetTool, len(input.InputList), input.Concurrency, input.TimeoutSeconds)

	// 获取目标工具的调用函数
	callFunc, ok := ProviderRegistry.loadAction(input.TargetTool)
	if !ok {
		return nil, fmt.Errorf("工具'%s'无法被loop_tool调用。请检查工具名称是否正确。如果确认工具名称无误，请直接调用该工具。", input.TargetTool)
	}

	// 使用并发执行
	results, errors, successCount, failureCount, timedOut, timeoutMessage := executeConcurrent(
		ctx, run, callFunc, input, len(input.InputList))

	executionTime := time.Since(startTime)

	if timedOut {
		run.GetLogger().Warnf("Loop tool timed out after %v: %d success, %d failures, message: %s",
			executionTime, successCount, failureCount, timeoutMessage)
	} else {
		run.GetLogger().Infof("Loop tool completed: %d success, %d failures, execution time: %v",
			successCount, failureCount, executionTime)
	}

	output := LoopToolOutput{
		Results:         results,
		Errors:          errors,
		TotalIterations: len(results),
		SuccessCount:    successCount,
		FailureCount:    failureCount,
		ExecutionTime:   executionTime.String(),
		TimedOut:        timedOut,
		TimeoutMessage:  timeoutMessage,
	}

	// 检查结果是否过大，如果是则保存到文件
	if len(results) > 0 {
		totalTokens := 0
		if res, err := jsoniter.MarshalToString(results); err == nil {
			totalTokens = prompt.CountToken(res)
		}
		if totalTokens > MaxResponseTokens {
			filePath, err := saveLargeResultsToFile(run, input.TargetTool, results, totalTokens)
			if err != nil {
				run.GetLogger().Errorf("保存大结果到文件失败: %v", err)
			} else {
				// 替换结果为文件保存信息
				resultSummary := fmt.Sprintf("循环工具执行完成，但结果过大(共%d个token)，已保存到文件以便查看。\n\n"+
					"结果统计:\n"+
					"- 总迭代次数: %d\n"+
					"- 成功次数: %d\n"+
					"- 失败次数: %d\n"+
					"- 执行时间: %s\n\n"+
					"详细结果已保存在文件: %s\n\n"+
					"要查看结果内容，您可以使用以下命令读取文件:\n"+
					"```bash\n"+
					"cat %s\n"+
					"```\n\n"+
					"该文件为标准JSON数组格式，包含所有结果。您也可以使用jq工具来查询特定内容:\n"+
					"```bash\n"+
					"# 查看第一个结果\n"+
					"cat %s | jq '.[0]'\n"+
					"# 查看所有结果中的特定字段\n"+
					"cat %s | jq '.[].your_field'\n"+
					"# 获取结果总数\n"+
					"cat %s | jq 'length'\n"+
					"```",
					totalTokens, len(results), successCount, failureCount, executionTime.String(), filePath, filePath, filePath, filePath, filePath)

				output.Results = []map[string]any{
					{
						"file_saved":     true,
						"file_path":      filePath,
						"total_tokens":   totalTokens,
						"result_summary": resultSummary,
						"message":        "结果过大已保存到文件，请使用命令行工具查看详细内容",
					},
				}
			}
		}
	}

	var outputMap map[string]any
	err = mapstructure.Decode(output, &outputMap)
	if err != nil {
		return nil, fmt.Errorf("failed to encode output: %v", err)
	}

	return outputMap, nil
}

// executeConcurrent 并发执行循环调用
func executeConcurrent(ctx context.Context, run *iris.AgentRunContext, callFunc func(*iris.AgentRunContext, map[string]any) (map[string]any, error),
	input LoopToolInput, totalTasks int) ([]map[string]any, []string, int, int, bool, string) {

	// 创建工作池
	taskChan := make(chan int, len(input.InputList))
	resultChan := make(chan iterationResult, len(input.InputList))

	// 填充任务
	for i := 0; i < len(input.InputList); i++ {
		taskChan <- i
	}
	close(taskChan)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < input.Concurrency; i++ {
		wg.Add(1)
		workerID := i
		go panics.Try(func() {
			defer wg.Done()
			worker(ctx, workerID, taskChan, resultChan, run, callFunc, input)
		})
	}

	// 等待所有工作完成或超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(resultChan)
		close(done)
	}()

	// 收集结果
	var results []map[string]any
	var errors []string
	successCount := 0
	failureCount := 0
	timedOut := false
	timeoutMessage := ""

	// 使用 map 来按顺序收集结果
	resultMap := make(map[int]iterationResult)
	var mu sync.Mutex

collectLoop:
	for {
		select {
		case <-ctx.Done():
			timedOut = true
			timeoutMessage = fmt.Sprintf("Execution timed out after %d seconds. Processed %d/%d iterations.",
				input.TimeoutSeconds, len(resultMap), len(input.InputList))
			break collectLoop
		case result, ok := <-resultChan:
			if !ok {
				// 所有结果都收集完毕
				break collectLoop
			}
			mu.Lock()
			resultMap[result.Index] = result
			mu.Unlock()
		case <-done:
			// 等待剩余结果
			for result := range resultChan {
				mu.Lock()
				resultMap[result.Index] = result
				mu.Unlock()
			}
			break collectLoop
		}
	}

	// 按索引顺序整理结果
	for i := 0; i < len(input.InputList); i++ {
		if result, exists := resultMap[i]; exists {
			if result.Error != nil {
				errorMsg := fmt.Sprintf("iteration %d failed: %v", i+1, result.Error)
				errors = append(errors, errorMsg)
				failureCount++

				if input.ContinueOnError {
					// 添加错误信息到结果中
					results = append(results, map[string]any{
						"error":     result.Error.Error(),
						"iteration": i + 1,
						"input":     input.InputList[i],
					})
				}
			} else {
				// 成功的结果
				if result.Result == nil {
					result.Result = make(map[string]any)
				}
				result.Result["iteration"] = i + 1
				results = append(results, result.Result)
				successCount++
			}
		} else if timedOut {
			// 超时未完成的任务
			break
		}
	}

	return results, errors, successCount, failureCount, timedOut, timeoutMessage
}

// worker 工作协程
func worker(ctx context.Context, workerID int, taskChan <-chan int, resultChan chan<- iterationResult,
	run *iris.AgentRunContext, callFunc func(*iris.AgentRunContext, map[string]any) (map[string]any, error),
	input LoopToolInput) {

	for {
		select {
		case <-ctx.Done():
			// 超时，退出
			return
		case taskIndex, ok := <-taskChan:
			if !ok {
				// 没有更多任务
				return
			}

			// 执行任务
			inputParams := input.InputList[taskIndex]

			run.GetLogger().Debugf("Worker %d processing iteration %d/%d for tool '%s'",
				workerID, taskIndex+1, len(input.InputList), input.TargetTool)

			result, err := callFunc(run, inputParams)

			// 发送结果
			select {
			case <-ctx.Done():
				return
			case resultChan <- iterationResult{
				Index:  taskIndex,
				Result: result,
				Error:  err,
			}:
			}

			// 添加延迟（如果配置了）
			if input.DelayMs > 0 {
				select {
				case <-ctx.Done():
					return
				case <-time.After(time.Duration(input.DelayMs) * time.Millisecond):
				}
			}
		}
	}
}

// NewCheckLoopTool 创建检查循环工具可用性的工具
func NewCheckLoopTool() iris.Action {
	return actions.NewTool(actions.NewToolOption{
		Name:        McpToolName(ToolHelperID, ToolCheckLoopTool),
		Description: ToolCheckLoopToolDescription,
		Input: framework.Schema{
			Type: framework.TypeObject,
			Properties: map[string]framework.Schema{
				"target_tool": {
					Type:        framework.TypeString,
					Description: "Required. The tool name to check - should match display_name from function description (starting with 'mcp:') or supported tools like 'vision', 'lark_download'",
				},
			},
			Required: []string{"target_tool"},
		},
		Output: framework.Schema{
			Type: framework.TypeObject,
			Properties: map[string]framework.Schema{
				"available": {
					Type:        framework.TypeBoolean,
					Description: "Whether the tool is available for loop execution",
				},
				"message": {
					Type:        framework.TypeString,
					Description: "Success message if tool is available",
				},
				"reason": {
					Type:        framework.TypeString,
					Description: "Reason why tool is not available (if applicable)",
				},
			},
		},
		Impl: func(run *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
			var args CheckLoopToolArgs
			err := mapstructure.Decode(step.Inputs, &args)
			if err != nil {
				return nil, fmt.Errorf("failed to decode input: %v", err)
			}
			res, err := CheckLoopTool(run, args)
			return res, iris.NewRecoverable(err)
		},
	})
}

// CheckLoopTool 检查指定工具是否可用于循环执行
func CheckLoopTool(c *iris.AgentRunContext, args CheckLoopToolArgs) (map[string]any, error) {
	// 检查工具是否在 registry 中可用
	if ProviderRegistry.actionsMap == nil {
		return map[string]any{
			"available": false,
			"reason":    "Tool registry not initialized",
		}, nil
	}

	// 检查工具是否存在于 actionsMap 中
	if _, exists := ProviderRegistry.loadAction(args.TargetTool); exists {
		return map[string]any{
			"available": true,
			"message":   fmt.Sprintf("Tool '%s' is available for loop execution", args.TargetTool),
		}, nil
	}

	return map[string]any{
		"available": false,
		"reason":    fmt.Errorf("工具'%s'无法被loop_tool调用。请检查工具名称是否正确。如果确认工具名称无误，请直接调用该工具。", args.TargetTool),
	}, nil
}

// saveLargeResultsToFile 将大结果保存到文件
func saveLargeResultsToFile(run *iris.AgentRunContext, targetTool string, results []map[string]any, totalTokens int) (string, error) {
	// 创建输出目录
	outputDir := "mcp_loop_outputs"
	if err := os.MkdirAll(outputDir, os.ModePerm); err != nil {
		return "", fmt.Errorf("创建输出目录失败: %v", err)
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("loop_results_%s_%s.json", targetTool, timestamp)
	filePath := filepath.Join(outputDir, fileName)

	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建结果文件失败: %v", err)
	}
	defer file.Close()

	// 将结果序列化为美化的JSON数组格式
	jsonBytes, err := jsoniter.MarshalIndent(results, "", "  ")
	if err != nil {
		return "", fmt.Errorf("序列化结果失败: %v", err)
	}

	// 写入文件
	if _, err := file.Write(jsonBytes); err != nil {
		return "", fmt.Errorf("写入结果文件失败: %v", err)
	}

	run.GetLogger().Infof("大结果已保存到文件: %s，包含%d个结果，总token数: %d", filePath, len(results), totalTokens)
	return filePath, nil
}
