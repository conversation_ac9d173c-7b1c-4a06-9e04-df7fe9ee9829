package mcptool

import (
	"encoding/json"
	"testing"

	gomcp "github.com/mark3labs/mcp-go/mcp"
	"gotest.tools/assert"

	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

func TestFromMCPSchema(t *testing.T) {
	var schema gomcp.ToolInputSchema
	json.Unmarshal([]byte(`{
  "properties": {
    "Comment": {
      "additionalProperties": false,
      "description": "Comment to create.",
      "properties": {
        "Content": {
          "description": "Content of the comment.",
          "type": "string"
        },
        "EndColumn": {
          "description": "File position: end column, starting from 1, inclusive, optional.",
          "type": "integer"
        },
        "EndLine": {
          "description": "File position: end line, starting from 1, inclusive.",
          "type": "integer"
        },
        "FilePath": {
          "description": "File position: file path.",
          "type": "string"
        },
        "Side": {
          "description": "File position: comment on the old side or new side of the merge request diff, default is new.",
          "enum": [
            "old",
            "new"
          ],
          "type": "string"
        },
        "StartColumn": {
          "description": "File position: start column, starting from 1, optional.",
          "type": "integer"
        },
        "StartLine": {
          "description": "File position: start line, starting from 1.",
          "type": "integer"
        },
        "ThreadId": {
          "description": "ID of the thread the comment belongs to. If ThreadId is set, file position fields are ignored.",
          "type": "string"
        }
      },
      "required": [
        "Content"
      ],
      "type": "object"
    },
    "Number": {
      "description": "The number of the merge request.",
      "type": "integer"
    },
    "RepoId": {
      "description": "The ID (e.g. 123456) or path (e.g. codebase/sdk) of the repository.",
      "type": "string"
    }
  },
  "required": [
    "RepoId",
    "Number",
    "Comment"
  ],
  "type": "object"
}`), &schema)
	converted, err := FromMCPSchema(schema)
	assert.NilError(t, err)
	assert.Equal(t, converted.Type, framework.TypeObject)
}
