package toolset

import (
	"fmt"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/interaction"
	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	agentsphereentity "code.byted.org/devgpt/kiwis/agentsphere/entity"

	"github.com/samber/lo"
)

type Toolset struct {
	Identifier  string
	Description string
	New         func(run *iris.AgentRunContext, ExecutionStep *actors.ExecutionStep) ([]iris.Action, error)

	// 是否暴露给 planner，某些工具会通过特殊的形式进行分配，比如模版经验。
	HiddenFromPlanner bool
}

func GetToolsets(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]Toolset, error) {
	toolsets := []Toolset{
		{
			Identifier:  "files",
			Description: "filesystem operations like reading files/images, save data/notes, update files, search in directory, etc.",
			New:         NewEditorToolset,
		},
		{
			Identifier:  "terminal",
			Description: "execute commands in terminal",
			New:         NewTerminalToolset,
		},
		{
			Identifier:  "research",
			Description: "a toolset that conducts a comprehensive investigation on a complex topic by exploring multiple facets concurrently and generating a single, synthesized report. Use this for broad, open-ended questions. This tool is slow but delivers a high-density, accurate report. Do NOT use this for tasks with sequential dependencies or for simple queries. For quick internal searches, use the search tool.",
			New:         NewResearchToolset,
		},
		{
			Identifier:  "search",
			Description: "a toolset that performs a quick, real-time search using search engines (Web Online & Bytedance Internal). This tool is the default and preferred choice for retrieving a wide range of information, including general company-wide internal documents. It is ideal for finding specific facts or for executing multi-step queries. For all queries about \"internal documents\" or \"company knowledge base\" that do not specify a private project space, this is the correct tool to use. It is fast but returns raw, unprocessed snippets. Do NOT use this for complex questions requiring deep analysis.",
			New:         NewSearchToolset,
		},
		{
			Identifier:        "browser_use",
			Description:       "visit specific URLs, navigate websites, interact with elements, and extract content. Limitation: Cannot extract multi pages, this toolset is slow for broad searching and too main actions, can not open binary files like docx, pptx, xlsx, etc. Can only be used for a specific URL.**CRITICAL RESTRICTION**: This tool is STRICTLY FORBIDDEN from accessing Feishu/Lark document links under ANY circumstances. Do NOT attempt to use browser tool with any Feishu or Lark URLs.",
			New:               NewBrowserToolset,
			HiddenFromPlanner: true,
		},
		{
			Identifier:  "project_space_search",
			Description: "a highly specialized tool designed exclusively for retrieving documents from a user's personal, private project space. This tool is NOT for general company-wide searches or public information. It should only be used when the user explicitly requests to search their own, private, or team-specific knowledge base using keywords like \"空间知识库,\". For any other type of internal search, use the search tool.",
			New:         NewKnowledgeBaseSearchToolset,
		},

		{
			Identifier:  "git",
			Description: "clone and work on repositories from codebase(code.byted.org and bits.bytedance.net/code), github with user ssh keys",
			New:         NewGitToolset,
		},
		{
			Identifier:  mcptool.Codebase.ID,
			Description: mcptool.Codebase.Description,
			New:         NewCodebaseToolset,
		},
		{
			Identifier:  "deploy",
			Description: "when the task is completed, deploy static files, HTML, and web pages so the user can visit",
			New:         NewDeployToolset,
		},

		{
			Identifier:  "lark_download",
			Description: `lark_download can download Lark document/sheet`,
			New:         NewLarkDownloadToolset,
		},
		// 飞书文档创建目前由 lark_creation agent tool 负责，但是过往经验中还会使用这个工具，因此需要兜底。
		// 注意：这个工具不暴露给 planner，正常流程 planner 不会分配这个工具。
		{
			Identifier:  mcptool.Lark.ID,
			Description: mcptool.Lark.Description,
			New: func(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
				return NewMCPToolset(run, mcptool.Lark.ID)
			},
			HiddenFromPlanner: true,
		},
		// visactor 工具, 给 chart agent tool 使用
		{
			Identifier:        "visactor",
			Description:       "VisActor is an open-source, enterprise-level data visualization solution. This toolset is used to generate VisActor data charts.",
			New:               NewVisActorToolset,
			HiddenFromPlanner: true,
		},
		// 特殊模版可能需要通过 ask_user 工具来获取用户输入以及人工确认。
		{
			Identifier:  interaction.AskUserActionDef.ActionName,
			Description: interaction.AskUserActionDef.ActionDescription,
			New: func(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
				return []iris.Action{interaction.NewAskUserAction(interaction.NewAskUserActionOption{
					Timeout:                     0,
					DownloadAttachmentsFilepath: ".",
				})}, nil
			},
			HiddenFromPlanner: true,
		},
	}

	// 项目空间场景，注入knowledge_graph toolset
	if agentsphereentity.CheckParamIsProjectSpace(run.Parameters[agentsphereentity.RuntimeParametersSpaceInfomation]) {
		run.GetLogger().Infof("[GetToolsets]-[knowledge_graph_debug]: project_space_scenario register knowledge_graph toolset")
		//知识图谱相关工具，目前支持"knowledge_graph_search_service_info"和"knowledge_graph_search_repo"工具
		knowledgeGraphToolSet := Toolset{
			Identifier:  "knowledge_graph",
			Description: "knowledge_graph includes code semantic retrieval, business document retrieval, service call chain information retrieval, and service interface document retrieval. Please use phrases with complete business meanings for the input parameters of the search. For example: Add a parameter to the create interface of the open api service.Currently it has 2 tools,\"knowledge_graph_search_service_info\" and \"knowledge_graph_search_repo\"",
			New:         NewKnowledgeGraphSearchToolset,
		}
		toolsets = append(toolsets, knowledgeGraphToolSet)
	}

	toolsets = append(toolsets, lo.FilterMap(mcptool.ProviderRegistry.Providers, func(provider *mcptool.MCPProvider, _ int) (Toolset, bool) {
		if provider.Type == mcptool.MCPSourceUserDefine {
			// 用户自定义的不放这里
			return Toolset{}, false
		}
		if provider.ID == mcptool.VisActorChartAssistant.ID {
			// visactor is only for internal usage
			return Toolset{}, false
		}
		if provider.ID == mcptool.Lark.ID {
			// lark变成agenttool
			return Toolset{}, false
		}

		// codebase 的创建 MR 工具替换成了内置实现，创建 MR 时带上 Aime
		if provider.ID == mcptool.Codebase.ID {
			return Toolset{}, false
		}

		return Toolset{
			Identifier:  provider.ID,
			Description: provider.Description,
			New: func(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
				return NewMCPToolset(run, provider.ID)
			},
		}, true
	})...)

	return toolsets, nil
}

func GetUserDefineToolsets(run *iris.AgentRunContext) ([]Toolset, error) {
	toolsets := []Toolset{}
	toolsets = append(toolsets, lo.FilterMap(mcptool.ProviderRegistry.Providers, func(provider *mcptool.MCPProvider, _ int) (Toolset, bool) {
		if provider.Type != mcptool.MCPSourceUserDefine {
			return Toolset{}, false
		}
		return Toolset{
			Identifier:  provider.ID,
			Description: provider.Description,
			New: func(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
				return NewMCPToolset(run, provider.ID)
			},
		}, true
	})...)
	return toolsets, nil
}

// MCP Loop tool, should be deprecated
func GetMCPLoopToolset(run *iris.AgentRunContext) []Toolset {
	toolsets := []Toolset{
		{
			Identifier:  mcptool.ToolHelper.ID,
			Description: mcptool.ToolHelper.Description,
			New: func(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
				return mcptool.ToolHelper.Tools, nil
			},
		},
	}
	return toolsets
}

func BuildTools(run *iris.AgentRunContext, toolsets []Toolset, step *actors.ExecutionStep, kg knowledges.Knowledgebase, agentName string) []iris.Action {
	tools := []iris.Action{}
	for _, toolset := range toolsets {
		toolsets, err := toolset.New(run, step)
		if err != nil {
			run.GetLogger().Warnf("failed to create toolset %s: %v", toolset.Identifier, err)
			continue
		}
		// only add toolset identifier to the tool description of mewtwo as only mewtwo may own user-defined tools
		toolsets = lo.Map(toolsets, func(tool iris.Action, _ int) iris.Action {
			return actions.NewActionProxy(tool).WithDescription(func() string {
				return fmt.Sprintf("[toolset:%s][display_name:%s] %s", toolset.Identifier, tool.Name(), tool.Description())
			})
		})
		tools = append(tools, toolsets...)
	}
	tools = append(tools, NewDefaultToolset(run, step, kg, agentName)...)
	tools = lo.UniqBy(tools, func(tool iris.Action) string {
		return tool.Name()
	})
	return tools
}
