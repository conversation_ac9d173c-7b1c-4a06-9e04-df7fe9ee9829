package toolset

import (
	"fmt"
	"strings"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/browseract"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/codebase"
	knowledgegraphtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/knowledge_graph"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/visactor"
	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	llmtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/llm"
	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience"
	expactions "code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges/experience/actions"
)

func NewEditorToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		workspace.NewGrepSearchAction(workspace.GrepSearchConfig{}),
		workspace.NewGlobSearchAction(),
		workspace.NewReadFileActionV2(workspace.ReadFileActionOption{MaxLines: 500}), // 500 lines for claude and gemini, 100 lines for doubao
		workspace.NewCreateFileAction(workspace.CreateFileOption{}),
		workspace.NewReadMarkDownFilesAction(),
		workspace.NewAppendFileAction(),
		// workspace.NewRawPatchFileV2Action(),
		workspace.NewSearchReplaceFileAction(),
		workspace.NewDeployAction(workspace.DeployOption{ProcessHTML: true}),
		workspace.NewDeployBackendAction(),
		workspace.NewExcelPreviewTool(),
		workspace.NewGeneralDataFilePreviewTool(),
		llmtool.NewVisionTool(),
	}, nil
}

func NewLarkDownloadToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		lark.NewLarkDownloadTool(),
	}, nil
}

func NewTerminalToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		workspace.NewExecuteCommandActionWithDefaultEnv(run, workspace.NewExecuteCommandActionOption{
			MaxColWidth:        20480, // provide a large col width (in characters) to avoid truncating lines
			ScrollbackLines:    300,
			MaxTokens:          20480,
			DisableStdioStream: true,
		}),
	}, nil
}

func NewResearchToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		websearchtool.NewQuickResearchTool(),
	}, nil
}

func NewSearchToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		websearchtool.NewSearchTool(),
	}, nil
}

func NewKnowledgeBaseSearchToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		websearchtool.NewKnowledgeBaseSearchTool(),
	}, nil
}

func NewGitToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		workspace.NewGitCloneAction(true),
	}, nil
}

func NewCodebaseToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	tools, err := mcptool.ProviderRegistry.ListTools(run, mcptool.Codebase.ID)
	if err != nil {
		return []iris.Action{}, err
	}
	tools = append(tools, codebase.NewSubmitMergeRequestAction(true))
	tool, err := GetBitsMRInfoTool(run)
	if err != nil {
		run.GetLogger().Errorf("failed to get bits mr info tool: %v", err)
	} else {
		tools = append(tools, tool)
	}
	return tools, nil
}

func GetBitsMRInfoTool(run *iris.AgentRunContext) (iris.Action, error) {
	bitsTools, err := mcptool.ProviderRegistry.ListTools(run, mcptool.BitsWorkFlow.ID)
	if err != nil {
		return nil, err
	}
	bitsMRInfoTool, b := lo.Find(bitsTools, func(item iris.Action) bool {
		return strings.Contains(item.Name(), "bits_workflow_get_mr_info")
	})
	if !b {
		return nil, fmt.Errorf("bits_workflow_get_mr_info tool not found")
	}
	return actions.NewActionProxy(bitsMRInfoTool).WithDescription(func() string {
		return "Retrieve MR detailed information by a Bits MR URL (only use this tool when the URL strictly follows the format: https://bits.bytedance.net/devops/{project_id}/code/detail/{cr_id})," +
			" including associated code repositories, branches, commits, and other data."
	}), nil
}

// Basic tools that are always available for every kind of task
func NewDefaultToolset(run *iris.AgentRunContext, step *actors.ExecutionStep, kg knowledges.Knowledgebase, agentName string) []iris.Action {
	tools := []iris.Action{
		// create notes and reports
		workspace.NewCreateFileAction(workspace.CreateFileOption{}),
		// command execution as a last resort
		workspace.NewExecuteCommandActionWithDefaultEnv(run, workspace.NewExecuteCommandActionOption{
			MaxColWidth:        20480, // provide a large col width (in characters) to avoid truncating lines
			ScrollbackLines:    300,
			MaxTokens:          20480,
			DisableStdioStream: true,
		}),
		// use progress thinking tool to review the current progress, determine the next step, retreive related knowledge
		llmtool.NewProgressThinkingTool(
			kg,
			knowledges.RetrieveParam{
				Agent:        agentName,
				Variant:      run.GetConfig().GetVariantByScene(agentName),
				Tools:        []string{},
				WithCitation: false,
			},
		),
		// finish the task.
		controltool.NewProgreActConclusionTool(),
	}
	//if experience.EnabledExpInsights(run) {
	//	tools = append(tools, experience.NewRetrieveExperienceAction())
	//}
	if experience.EnabledExpReusableWorkflow(run) {
		tools = append(tools, expactions.NewGetReusableWorkflowTool())
	}
	return tools
}

func NewMCPToolset(run *iris.AgentRunContext, providerID string) ([]iris.Action, error) {
	tools, err := mcptool.ProviderRegistry.ListTools(run, providerID)
	if err != nil {
		return []iris.Action{}, err
	}
	return tools, nil
}

func NewDeployToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	tools, err := browseract.NewBrowserToolset(run)
	if err != nil {
		return nil, err
	}

	// 仅过滤出 browser_goto_and_extraction
	tools = lo.Filter(tools, func(item iris.Action, _ int) bool {
		return strings.Contains(item.Name(), "browser_goto_and_extraction")
	})
	tools = append(tools, workspace.NewDeployAction(workspace.DeployOption{ProcessHTML: true}))
	tools = append(tools, workspace.NewDeployBackendAction())
	return tools, nil
}

func NewToolsRetrievalToolset(run *iris.AgentRunContext) []iris.Action {
	return []iris.Action{}
}

// NewPlaceholderToolset provides no tools, but can be used to hint the planner to assign tasks or used to retrieve knowledge
func NewPlaceholderToolset(run *iris.AgentRunContext) []iris.Action {
	return []iris.Action{}
}

func NewVisActorToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		visactor.NewGenerateVisactorChartTool(),
	}, nil
}

func NewBrowserToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	tools, err := browseract.NewBrowserToolset(run)
	if err != nil {
		return nil, err
	}
	return tools, nil
}

func NewKnowledgeGraphSearchToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		knowledgegraphtool.NewKnowledgeGraphSearchBamTool(),
		knowledgegraphtool.NewKnowledgeGraphSearchRepoTool(),
	}, nil
}
