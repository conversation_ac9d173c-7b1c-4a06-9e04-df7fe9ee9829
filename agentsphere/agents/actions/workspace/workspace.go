package workspace

import (
	"os/exec"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

type Workspace struct {
	Editor       *Editor
	Terminal     map[string]*Terminal
	Repositories map[string]*Repository
}

var _ iris.Workspace = (*Workspace)(nil)

func New(wd string, publisher *iris.AgentEventPublisher) *Workspace {
	return &Workspace{
		Editor:       NewEditor(wd, publisher),
		Terminal:     make(map[string]*Terminal),
		Repositories: make(map[string]*Repository),
	}
}

// AddRepository add an existing git repository to workspace
// Agent should clone the repository and add it to workspace
func (w *Workspace) AddRepository(path string) (*Repository, error) {
	// A repository can only be added once
	if r, ok := w.Repositories[path]; ok {
		return r, nil
	}

	repo, err := NewRepository(path)
	if err != nil {
		return nil, err
	}

	w.Repositories[path] = repo
	return repo, nil
}

func (w *Workspace) AddRawRepository(repo *Repository) {
	w.Repositories[repo.Directory] = repo
}

func (w *Workspace) GetRepositoryByRepoName(repoName string) *Repository {
	for _, repo := range w.Repositories {
		if repo.Meta == nil {
			continue
		}
		if repo.Meta.Name == repoName {
			return repo
		}
	}
	return nil
}

func (w *Workspace) GetRepository(path string) *Repository {
	return w.Repositories[path]
}

func (w *Workspace) AddTerminal(name string, t *Terminal) {
	w.Terminal[name] = t
}

func (w *Workspace) GetTerminal(run *iris.AgentRunContext, name string) *Terminal {
	if name == "" {
		name = "terminal"
	}
	if t, ok := w.Terminal[name]; ok {
		return t
	}
	// create if not exist
	t, err := NewTerminal(NewTerminalOption{
		Name:           name,
		Cmd:            exec.Command("bash"),
		PublishChannel: run.GetPublisher(),
		Logger:         run.GetLogger(),
	})
	if err != nil {
		run.GetLogger().Errorf("failed to create terminal: %v", err)
		return nil
	}
	w.AddTerminal(name, t)
	return t
}

func (w *Workspace) Close() {
	for _, t := range w.Terminal {
		t.Close()
	}
}

func GetWorkspace(run *iris.AgentRunContext) *Workspace {
	return run.GetRawWorkspace().(*Workspace)
}

func GetTerminal(run *iris.AgentRunContext, name string) *Terminal {
	return GetWorkspace(run).GetTerminal(run, name)
}

func GetEditor(run *iris.AgentRunContext) *Editor {
	return GetWorkspace(run).Editor
}

func GetEditorWithoutEventReport(run *iris.AgentRunContext) *Editor {
	editor := GetWorkspace(run).Editor
	editor.DisableEventReport()
	return editor
}
