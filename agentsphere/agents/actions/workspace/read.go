package workspace

import (
	"path/filepath"
	"strings"

	"github.com/go-enry/go-enry/v2"
)

type ReadToolArgs struct {
	Path   string `mapstructure:"file_path" json:"file_path" description:"The relative path of the file to read"`
	Offset int    `mapstructure:"offset" json:"offset" description:"The line number to start reading from. Only provide if the file is too large to read at once"`
	Limit  int    `mapstructure:"limit" json:"limit" description:"The number of lines to read. Only provide if the file is too large to read at once."`
}
type ReadToolOutput struct {
	FileRange FileRange `mapstructure:"file_range" json:"file_range"`
	Schema    string    `mapstructure:"schema" json:"schema"`
}

type FileType string

const (
	FileTypeDocument FileType = "document"
	FileTypeCode     FileType = "code"
	FileTypeData     FileType = "data"
	FileTypeImage    FileType = "image"
	FileTypeDefault  FileType = "default"
)

func (e *Editor) Read(args ReadToolArgs) (*ReadToolOutput, error) {
	fileType := e.getFileType(args.Path)
	switch fileType {
	case FileTypeCode:
		return &ReadToolOutput{}, nil
	case FileTypeData:
		return &ReadToolOutput{}, nil
	case FileTypeDocument:
		return &ReadToolOutput{}, nil
	case FileTypeImage:
		return &ReadToolOutput{}, nil
	default:
		return &ReadToolOutput{
			FileRange: FileRange{},
			Schema:    "",
		}, nil
	}
}

func (e *Editor) getFileType(path string) FileType {
	if enry.IsImage(path) {
		return FileTypeImage
	}
	ext := strings.ToLower(filepath.Ext(path))
	if lang, _ := enry.GetLanguageByExtension(ext); lang != "" {
		return FileTypeCode
	}
	if isDocumentation(ext) {
		return FileTypeDocument
	}
	if isData(ext, path) {
		return FileTypeData
	}
	return FileTypeDefault
}

func isDocumentation(ext string) bool {
	switch ext {
	case ".md", ".pdf", ".doc", ".docx":
		return true
	default:
		return false
	}
}

func isData(ext string, path string) bool {
	switch ext {
	case ".xls", ".xlsx", ".csv", ".tsv", ".json", ".jsonl", ".yaml", ".yml":
		return true
	case ".txt":
		return detectTxtFormat(path) != "txt"
	default:
		return false
	}
}
