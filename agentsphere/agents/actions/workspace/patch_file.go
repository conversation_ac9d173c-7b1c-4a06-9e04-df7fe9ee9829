package workspace

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/agext/levenshtein"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace/checker"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/port/autodiff"
)

// deployed on FaaS: https://cloud.bytedance.net/faas/function/4h5m480x/code?region=cn-north&x-resource-account=public
var defaultAutoDiffCli = autodiff.MustNewClient("https://copilot.byted.org")

const (
	diffSearchStart = "<<<<<<< SEARCH\n"
	diffSplitter    = "\n=======\n"
	diffReplaceEnd  = "\n>>>>>>> REPLACE"

	editFileReportBefore = 30
	editFileReportAfter  = 30
)

type SearchReplacePatch struct {
	Search  string
	Replace string
}

type PatchFileArgs struct {
	FilePath              string `mapstructure:"file_path" json:"file_path"`
	Search                string `mapstructure:"search" json:"search"`
	Replace               string `mapstructure:"replace" json:"replace"`
	RollbackOnSyntaxError bool   `mapstructure:"rollback_on_error" json:"-"` // invisible to llm
}

type RawPatchFileArgs struct {
	FilePath              string `mapstructure:"file_path" json:"file_path"`
	Diff                  string `mapstructure:"diff" json:"diff" description:"a SEARCH/REPLACE block"`
	RollbackOnSyntaxError bool   `mapstructure:"rollback_on_error" json:"-"` // invisible to llm
}

type PatchFileOutput struct {
	Succeed []PatchFileResult        `mapstructure:"succeed" json:"succeed"`
	Failed  []PatchFileItemWithError `mapstructure:"failed" json:"failed"`
}

type PatchFileResult struct {
	FilePath string `mapstructure:"file_path" json:"file_path"`
	Patch    string `mapstructure:"patch" json:"patch"`
}

type PatchFileItemWithError struct {
	FilePath string `mapstructure:"file_path" json:"file_path"`
	Search   string `mapstructure:"search" json:"search"`
	Replace  string `mapstructure:"replace" json:"replace"`
	Diff     string `mapstructure:"diff" json:"diff"`
	Error    string `mapstructure:"error" json:"error"`
}

func (e *Editor) PatchFile(ctx *iris.AgentRunContext, params PatchFileArgs) (*PatchFileOutput, error) {
	var (
		failedPatches  []PatchFileItemWithError
		succeedPatches []PatchFileResult
	)

	// Remove the extra new lines, or the extra lines will be removed when searching without indentations.
	params.Search = strings.Trim(params.Search, "\n")
	failed, succeed := e.patchFile(ctx, params.FilePath, params.Search, params.Replace, params.RollbackOnSyntaxError)
	if len(failed) > 0 {
		failedPatches = append(failedPatches, failed...)
	}
	if len(succeed) > 0 {
		succeedPatches = append(succeedPatches, succeed...)
	}

	if len(succeedPatches) == 0 {
		return &PatchFileOutput{
			Succeed: succeedPatches,
			Failed:  failedPatches,
		}, errors.Errorf("failed to patch file %s", params.FilePath)
	}
	return &PatchFileOutput{
		Succeed: succeedPatches,
		Failed:  failedPatches,
	}, nil
}

func (e *Editor) RawPatchFile(ctx *iris.AgentRunContext, params RawPatchFileArgs) (*PatchFileOutput, error) {
	var (
		failedPatches  []PatchFileItemWithError
		succeedPatches []PatchFileResult
	)

	// a raw patch may contain multiple search/replace block
	patches := parseDiffBlocks(params.Diff)
	if len(patches) == 0 {
		return nil, fmt.Errorf("no search/replace blocks found in diff: \n```\n%s\n```", params.Diff)
	}
	for _, patch := range patches {
		// Remove the extra new lines, or the extra lines will be removed when searching without indentations.
		failed, succeed := e.patchFile(ctx, params.FilePath, patch.Search, patch.Replace, params.RollbackOnSyntaxError)
		if len(failed) > 0 {
			failedPatches = append(failedPatches, failed...)
		}
		if len(succeed) > 0 {
			succeedPatches = append(succeedPatches, succeed...)
		}
	}

	if len(succeedPatches) == 0 {
		return &PatchFileOutput{
			Succeed: succeedPatches,
			Failed:  failedPatches,
		}, errors.Errorf("failed to patch file %s", params.FilePath)
	}
	return &PatchFileOutput{
		Succeed: succeedPatches,
		Failed:  failedPatches,
	}, nil
}

type EditFileArgs struct {
	FilePath   string `mapstructure:"file_path" json:"file_path"`
	Action     string `mapstructure:"action" json:"action"`
	AtLine     int    `mapstructure:"at_line" json:"at_line,omitempty" description:"if provided, the tool will try to search for the match around this line"`
	OldContent string `mapstructure:"old_content" json:"old_content"`
	Content    string `mapstructure:"content" json:"content"`
}

type EditFileResult struct {
	FilePath string     `mapstructure:"file_path" json:"file_path"`
	OldLines []FileLine `mapstructure:"old_lines" json:"old_lines"`
	NewLines []FileLine `mapstructure:"new_lines" json:"new_lines"`
	Errors   []string   `mapstructure:"errors" json:"errors"`
}

type EditFileOutput struct {
	Succeed []EditFileResult `mapstructure:"succeed" json:"succeed"`
	Failed  []EditFileResult `mapstructure:"failed" json:"failed"`
}

func (e Editor) EditFile(run *iris.AgentRunContext, args EditFileArgs) (output *EditFileOutput, err error) {
	logger := run.GetLogger()
	oldFile, err := e.ReadFile(ReadFileArgs{Path: args.FilePath})
	if err != nil {
		return nil, err
	}
	oldContent := oldFile.Content
	if args.AtLine == 0 {
		matches, err := e.search(oldFile.Content, args.OldContent)
		if err != nil {
			return nil, err
		}
		if len(matches) > 0 {
			args.AtLine = matches[0].StartLine
		}
	}

	insertMode := args.Action == "insert"
	startLine := args.AtLine
	if !insertMode {
		firstLineOfOldContent := strings.Split(args.OldContent, "\n")[0]
		surroundingLines := oldFile.Range(args.AtLine-10, args.AtLine+10)
		// 模型会给出要修改的 line，但有可能给出有偏差的行号。在附近几行根据模型给出的旧内容首行搜索
		similarities := lo.Map(surroundingLines, func(line FileLine, _ int) float64 {
			return levenshtein.Similarity(line.Content, firstLineOfOldContent, levenshtein.NewParams())
		})
		// By the mean time，模型不仅会给出有问题的行号，还会幻觉要修改的代码的内容，用相似度来近似匹配
		nearestLines := lo.Filter(surroundingLines, func(line FileLine, idx int) bool {
			return similarities[idx] > 0.8
		})
		if len(nearestLines) == 0 {
			return nil, fmt.Errorf("line %d does not contain `%s` in file %s", args.AtLine, firstLineOfOldContent, args.FilePath)
		}
		// sort by line distance and similarity
		slices.SortFunc(nearestLines, func(a FileLine, b FileLine) int {
			abs := func(x int) int {
				if x < 0 {
					return -x
				}
				return x
			}
			distance := abs(a.Line-args.AtLine) - abs(b.Line-args.AtLine)
			if distance == 0 {
				return int(similarities[lo.IndexOf(nearestLines, a)] - similarities[lo.IndexOf(nearestLines, b)])
			}
			return distance
		})
		if nearestLines[0].Line != args.AtLine {
			logger.Infof("corrected start_line to %d", nearestLines[0].Line)
			startLine = nearestLines[0].Line
		}
	}
	lineCount := len(strings.Split(args.OldContent, "\n"))
	endLine := max(startLine, startLine+lineCount-1)
	logger.Infof("start line: %d, end line: %d, old content lines: %d", startLine, endLine, lineCount)

	var edited string
	// NOTE: *File.Range is both inclusive while edit_file is left-closed right-open
	if insertMode {
		linesBefore := lo.Map(oldFile.Range(1, startLine-1), func(line FileLine, _ int) string { return line.Content })
		linesAfter := lo.Map(oldFile.Range(startLine, len(oldFile.Lines)), func(line FileLine, _ int) string { return line.Content })
		edited = strings.Join(linesBefore, "\n") + "\n" + args.Content + "\n" + strings.Join(linesAfter, "\n")
	} else {
		linesBefore := lo.Map(oldFile.Range(1, startLine-1), func(line FileLine, _ int) string { return line.Content })
		linesAfter := lo.Map(oldFile.Range(endLine+1, len(oldFile.Lines)), func(line FileLine, _ int) string { return line.Content })
		edited = strings.Join(linesBefore, "\n") + "\n" + args.Content + "\n" + strings.Join(linesAfter, "\n")
	}

	err = os.WriteFile(args.FilePath, []byte(edited), os.ModePerm)
	if err != nil {
		return nil, err
	}
	editedLinesCount := strings.Count(args.Content, "\n")
	editedFile, _ := e.readFile(args.FilePath)

	checkErrs := e.checkFile(run, args.FilePath)
	if len(checkErrs) > 0 {
		err = os.WriteFile(args.FilePath, []byte(oldContent), os.ModePerm)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to revert patch file")
		}
		return &EditFileOutput{
			Failed: []EditFileResult{
				{
					FilePath: args.FilePath,
					OldLines: oldFile.Range(startLine-editFileReportBefore, endLine+editFileReportAfter),
					NewLines: editedFile.Range(startLine-editFileReportBefore, startLine+editedLinesCount+editFileReportAfter),
					Errors:   lo.Map(checkErrs, func(err checker.CheckError, _ int) string { return err.Error() }),
				},
			},
		}, nil
	}
	err = e.formatFile(run, args.FilePath)
	if err != nil {
		return nil, err
	}

	// read file again and update cache
	editedFile, err = e.ReadFile(ReadFileArgs{Path: args.FilePath})
	if err == nil {
		// update file content in user side
		e.AddOpenedFile(&File{
			Path:     args.FilePath,
			RealPath: editedFile.RealPath,
			Content:  editedFile.Content,
			Lines:    lo.Map(strings.Split(editedFile.Content, "\n"), func(line string, i int) FileLine { return FileLine{Line: i + 1, Content: line} }),
		})
	}

	return &EditFileOutput{
		Succeed: []EditFileResult{
			{
				FilePath: args.FilePath,
				OldLines: oldFile.Range(startLine-editFileReportBefore, endLine+editFileReportAfter),
				NewLines: editedFile.Range(startLine-editFileReportBefore, startLine+editedLinesCount+editFileReportAfter),
			},
		},
	}, nil
}

func wrapRawPatch(filePath, search, replace, notice string) PatchFileItemWithError {
	return PatchFileItemWithError{
		FilePath: filePath,
		Search:   search,
		Replace:  replace,
		Error:    notice,
	}
}

func (e *Editor) uploadOriginalFile(run *iris.AgentRunContext, filePath string, content string) (err error) {
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[entity.PatchToolUploadStore](run, entity.PatchToolUploadKey)
	logger.Infof("RetrieveStoreByKey: %+v, %s", store, store.Files[filePath])

	if store.Artifact == nil {
		// TODO: backoff
		artifactKey := nextentity.ArtifactKeyFileBaseline
		artifact, err := run.GetArtifactService().NewFileArtifactWithKey(run, artifactKey, nextentity.FileArtifactTypeMetadata{})
		if err != nil {
			logger.Errorf("failed to create artifact: %v", err)
			return err
		}
		store.Artifact = artifact
	}

	if store.Files == nil {
		store.Files = make(map[string]struct{})
	}
	if _, ok := store.Files[filePath]; !ok {
		store.Files[filePath] = struct{}{}
		logger.Infof("upload original file: %s, size: %d", filePath, len(content))
		err = run.GetArtifactService().UploadFiles(run, store.Artifact, []iris.ArtifactFile{{
			Path:    filePath,
			Content: []byte(content),
		}})
		if err != nil {
			logger.Errorf("failed to upload file: %v", err)
			return err
		}
	}

	iris.UpdateStoreByKey(run, entity.PatchToolUploadKey, store)
	return nil
}

func (e *Editor) patchFile(run *iris.AgentRunContext, filePath, search, replace string, rollback bool) (failedPatches []PatchFileItemWithError, succeedPatches []PatchFileResult) {
	logger := run.GetLogger()
	f, err := e.ReadFile(ReadFileArgs{Path: filePath})
	if err != nil {
		logger.Errorf("failed to read file for patch file: %v", err)
		if os.IsNotExist(err) {
			return []PatchFileItemWithError{
				{
					FilePath: filePath,
					Search:   search,
					Replace:  replace,
					Error:    fmt.Sprintf(`File "%s" not exist, or is not a file, recheck your input path`, filePath),
				},
			}, nil
		}
		return []PatchFileItemWithError{
			{
				FilePath: filePath,
				Search:   search,
				Replace:  replace,
				Error:    fmt.Sprintf(`Read file "%s" failed: %s`, filePath, err),
			},
		}, nil
	}

	err = e.uploadOriginalFile(run, filePath, f.Content)
	if err != nil {
		// 记录错误，但不影响后续操作
		logger.Errorf("failed to upload original file: %v", err)
	}

	// if we can find an exact match (ignoring whitespace), use it.
	found, _ := e.search(f.Content, search)
	if len(found) == 0 {
		logger.Errorf("no matches found for search: %s", search)
		failedPatches = append(failedPatches, wrapRawPatch(filePath, search, replace, fmt.Sprintf("no matches found for search:\n%s", search)))
		return
	}
	if len(found) > 1 {
		foundAt := lo.Map(found, func(m SearchMatch, _ int) string { return strconv.Itoa(m.StartLine) })
		return []PatchFileItemWithError{
			{
				FilePath: filePath,
				Search:   search,
				Replace:  replace,
				Error:    fmt.Sprintf("multiple matches found for search at lines %s, please review the code, provide a few more origin code in the search block to make it unique.", strings.Join(foundAt, ",")),
			},
		}, nil
	}
	search = found[0].Match
	replaceList := strings.Split(strings.Trim(replace, "\n"), "\n")
	replaceWithIndent := ""
	for _, r := range replaceList {
		realLine, _ := strings.CutPrefix(r, found[0].RemoveIndent)
		replaceWithIndent += found[0].AppendIndent + realLine + "\n"
	}
	replaceWithIndent = strings.TrimRight(replaceWithIndent, "\n")
	diffs := diffSearchStart + search + diffSplitter + replaceWithIndent + diffReplaceEnd

	diffsResult, err := defaultAutoDiffCli.AnalyzeRawDiffs(run, filePath, f.Content, diffs)
	if err != nil {
		logger.Errorf("failed to analyze diffs: %v", err)
		failedPatches = append(failedPatches, wrapRawPatch(filePath, search, replace, "failed to analyze diffs"))
		return
	}

	if len(strings.TrimSpace(diffsResult.Diff)) == 0 {
		logger.Errorf("no diff found from auto diff")
		failedPatches = append(failedPatches, wrapRawPatch(filePath, search, replace, diffsResult.Message))
		return
	}

	if diffsResult.PatchedFileContent != "" {
		// 保存补丁后的内容到原文件
		newFilePath := filePath
		err = os.WriteFile(newFilePath, []byte(diffsResult.PatchedFileContent), os.ModePerm)
		if err != nil {
			logger.Errorf("failed to save patched content to new file: %v", err)
			failedPatches = append(failedPatches, wrapRawPatch(filePath, search, replace, err.Error()))
		} else {
			logger.Infof("patched content saved to: %s", newFilePath)
		}
	} else {
		err = e.runPatchCommand(run, diffsResult.Diff, false)
		if err != nil {
			logger.Errorf("failed to run patch command: %v\n\n the request parameters are \n\n filePath: %s\n\n fileContent: %s\n\n diffs: %s", err, filePath, f.Content, diffs)
			failedPatches = append(failedPatches, wrapRawPatch(filePath, search, replace, err.Error()))
			return
		}
	}

	checkErrs := e.checkFile(run, filePath)
	if len(checkErrs) > 0 {
		editedContent, _ := e.readFile(filePath)
		diff, msg := GetCheckErrorFeedback(run, editedContent.Content, checkErrs, diffsResult.EditLineRanges, CheckErrorFeedbackOption{
			ExtendLinesBefore: editFileReportBefore,
			ExtendLinesAfter:  editFileReportAfter,
			Rollback:          rollback,
		})
		if rollback {
			_ = os.WriteFile(f.RealPath, []byte(f.Content), os.ModePerm)
		}
		failedPatches = append(failedPatches, PatchFileItemWithError{
			FilePath: filePath,
			Diff:     diff,
			Error:    msg,
		})
		return
	}
	err = e.formatFile(run, filePath)
	if err != nil {
		return
	}

	// read file again and update cache
	f, err = e.ReadFile(ReadFileArgs{Path: filePath})
	if err == nil {
		// update file content in user side
		e.AddOpenedFile(&File{
			Path:     filePath,
			RealPath: f.RealPath,
			Content:  f.Content,
			Lines:    lo.Map(strings.Split(f.Content, "\n"), func(line string, i int) FileLine { return FileLine{Line: i + 1, Content: line} }),
		})
	}
	succeedPatches = append(succeedPatches, PatchFileResult{
		FilePath: filePath,
		Patch:    diffsResult.Diff,
	})

	return
}

func (e *Editor) formatFile(run *iris.AgentRunContext, filePath string) error {
	switch filepath.Ext(filePath) {
	case ".go":
		checker := checker.GolangChecker{WorkingDirectory: e.WorkingDirectory}
		return checker.Format(run, filePath)
	}
	return nil
}

func (e *Editor) checkFile(ctx context.Context, filePath string) (result []checker.CheckError) {
	start := time.Now()
	fileExt := filepath.Ext(filePath)
	var checkErr error

	defer func() {
		_ = metrics.AR.ToolCheckFileCost.WithTags(&metrics.ToolCheckFileTag{
			FileType:   fileExt,
			ErrorCount: len(result),
		}).Observe(float64(time.Since(start).Milliseconds()))
	}()

	switch fileExt {
	case ".go":
		chk := checker.GolangChecker{WorkingDirectory: e.WorkingDirectory}
		result, checkErr = chk.Check(ctx, filePath)
		if checkErr != nil {
			log.V1.Error("failed to check file: %v", checkErr)
		}
	case ".kt", ".kts":
		chk := checker.KotlinChecker{WorkingDirectory: e.WorkingDirectory}
		result, checkErr = chk.Check(ctx, filePath)
		if checkErr != nil {
			log.V1.Error("failed to check file: %v", checkErr)
		}
	case ".java":
		chk := checker.JavaChecker{WorkingDirectory: e.WorkingDirectory}
		result, checkErr = chk.Check(ctx, filePath)
		if checkErr != nil {
			log.V1.Error("failed to check file: %v", checkErr)
		}
	case ".js", ".jsx", ".ts", ".tsx":
		chk := checker.JavaScriptChecker{WorkingDirectory: e.WorkingDirectory}
		result, checkErr = chk.Check(ctx, filePath)
		if checkErr != nil {
			log.V1.Error("failed to check file: %v", checkErr)
		}
	case ".json":
		chk := checker.JSONChecker{WorkingDirectory: e.WorkingDirectory}
		result, checkErr = chk.Check(ctx, filePath)
		if checkErr != nil {
			log.V1.Error("failed to check file: %v", checkErr)
		}
	}
	return
}

func (e *Editor) runPatchCommand(run *iris.AgentRunContext, patchContent string, revert bool) error {
	var cmd *exec.Cmd
	if revert {
		cmd = exec.CommandContext(run, "patch", "--batch", "--reverse", "-p1", "--fuzz=5", "--no-backup-if-mismatch", "--suffix=.none")
	} else {
		cmd = exec.CommandContext(run, "patch", "--batch", "--fuzz=5", "-p1", "--no-backup-if-mismatch", "--suffix=.none")
	}

	fmt.Printf("%s:\n%s", cmd.String(), patchContent)

	cmd.Dir = e.WorkingDirectory
	cmd.Stdin = strings.NewReader(patchContent)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return errors.Wrapf(err, "failed to run patch command: %s", string(output))
	}

	run.GetLogger().Debugf("patch command output: %s", output)

	return nil
}

func parseDiffBlocks(diff string) []SearchReplacePatch {
	blocks := strings.Split(diff, diffReplaceEnd)
	if len(blocks) <= 1 {
		return nil
	}

	return lo.FilterMap(blocks, func(block string, _ int) (SearchReplacePatch, bool) {
		match := strings.Split(block, diffSplitter)
		if len(match) != 2 {
			return SearchReplacePatch{}, false
		}
		// remove empty lines before search
		search := strings.TrimLeft(match[0], "\n")
		search = strings.TrimPrefix(search, diffSearchStart)
		replace := strings.TrimSuffix(match[1], diffReplaceEnd)

		return SearchReplacePatch{Search: search, Replace: replace}, true
	})
}
