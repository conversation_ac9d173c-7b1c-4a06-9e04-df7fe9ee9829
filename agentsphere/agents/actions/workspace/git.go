package workspace

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	stdssh "golang.org/x/crypto/ssh"

	"code.byted.org/cld/safe-exec/command"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory/project_memory"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	gitignore "github.com/denormal/go-gitignore"
	"github.com/go-enry/go-enry/v2"
	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/config"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/format/diff"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/go-git/go-git/v5/plumbing/transport"
	"github.com/go-git/go-git/v5/plumbing/transport/ssh"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

var ErrNoCredential = errors.New("no credential found")

type RepoMeta struct {
	Name     string
	Platform entity.GitPlatform
	URL      string
}

type Repository struct {
	Directory     string    // sub directory path or "." for root
	Meta          *RepoMeta // will be empty if no origin is found
	Git           *git.Repository
	GitCredential transport.AuthMethod
	Artifact      *iris.PatchArtifact
	Memory        *project_memory.ProjectMemory
}

func NewRepository(directory string) (*Repository, error) {
	repo, err := git.PlainOpen(directory)
	if err != nil {
		return nil, err
	}
	remotes, err := repo.Remotes()
	if err != nil {
		return nil, err
	}
	var meta *RepoMeta
	origin, found := lo.Find(remotes, func(remote *git.Remote) bool { return remote.Config().Name == "origin" })
	if found && origin != nil {
		meta, err = parseGitURL(string(origin.Config().URLs[0])) // URLs will always be non-empty as documented in go-git
		if err != nil {
			return nil, errors.WithMessage(err, "failed to parse git url")
		}
	}

	return &Repository{
		Meta:      meta,
		Directory: directory,
		Git:       repo,
	}, nil
}

type CloneOption struct {
	Directory string
	URL       string
	Checkout  string // branch or commit to checkout

	Shallow       bool // if specified, only fetch the last commit of `Checkout`
	GitCredential transport.AuthMethod
	Terminal      *Terminal
	Progress      bool // if specified, write progress to the terminal
}

func (o CloneOption) WithSSHCredential(user, pass string) CloneOption {
	o.GitCredential = sshPass(user, pass)
	return o
}

func CloneRepository(ctx context.Context, opt CloneOption) (*Repository, error) {
	run, _ := ctx.(*iris.AgentRunContext)
	metadata, err := parseGitURL(opt.URL)
	if err != nil {
		return nil, err
	}
	repo, err := git.PlainInit(opt.Directory, false)
	if err != nil {
		return nil, err
	}

	// checkout to a temporary branch to avoid conflict, as the default branch(master) is already checked out as new born branch and git fetch will fail
	tmpBranchCmd := exec.Command("git", "checkout", "-b", uuid.New().String())
	tmpBranchCmd.Dir = opt.Directory
	out, err := tmpBranchCmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to checkout to a temporary branch: %w, %s", err, string(out))
	}

	remote, err := repo.CreateRemote(&config.RemoteConfig{
		Name: "origin",
		URLs: []string{opt.URL},
	})
	if err != nil {
		return nil, err
	}

	// if checkout is a commit, fetch the commit
	exact := plumbing.IsHash(opt.Checkout)

	var (
		refSpec     config.RefSpec
		checkoutRef plumbing.ReferenceName
	)
	if exact {
		refSpec = config.RefSpec(fmt.Sprintf("%s:%s", opt.Checkout, opt.Checkout))
	} else {
		// if checkout is not specified, checkout the default branch
		refs, err := remote.List(&git.ListOptions{
			Auth:    opt.GitCredential,
			Timeout: 300, // for very large repos, it may take a long time to list refs
		})
		if err != nil {
			return nil, err
		}

		toCheckoutRefs := strings.HasPrefix(opt.Checkout, "refs/")

		for _, ref := range refs {
			// if checkout is not specified, checkout the default branch
			if opt.Checkout == "" {
				if ref.Name() == "HEAD" || ref.Name() == "refs/heads/HEAD" {
					checkoutRef = ref.Target()
					opt.Checkout = ref.Target().Short()
					refSpec = config.RefSpec(fmt.Sprintf("+%s:%s", ref.Target().String(), ref.Target().String()))
					break
				}
				continue
			}

			// if wan't to checkout a specific ref, skip other refs
			if !toCheckoutRefs &&
				!strings.HasPrefix(ref.Name().String(), "refs/heads/") &&
				!strings.HasPrefix(ref.Name().String(), "refs/tags/") {
				// skip other refs
				continue
			}

			if ref.Name().Short() == opt.Checkout || ref.Name().String() == opt.Checkout {
				checkoutRef = ref.Name()
				refSpec = config.RefSpec(fmt.Sprintf("+%s:%s", ref.Name().String(), ref.Name().String()))
				break
			}
		}
		if checkoutRef == "" {
			return nil, fmt.Errorf("ref %s not found in remote %s", opt.Checkout, opt.URL)
		}
	}

	// check whether to use pvc cache
	usePVCCache, err := RepoUsePvcCache(ctx, opt)
	if err != nil {
		run.GetLogger().Errorf("failed to check whether %s use pvc cache: %w", opt.URL, err)
	} else {
		run.GetLogger().Infof("%s use pvc cache: %t", opt.URL, usePVCCache)
	}

	run.GetLogger().Infof("fetching %s with refSpec: %s", opt.URL, refSpec)

	gitFetchOptions := &git.FetchOptions{
		RemoteName: "origin",
		RefSpecs:   []config.RefSpec{refSpec},
		Depth:      lo.Ternary(opt.Shallow, 1, 0),
		Auth:       opt.GitCredential,
		Progress:   lo.Ternary(opt.Progress, opt.Terminal.StderrWriter(), nil),
	}

	if usePVCCache {
		err = GitFetchWithPvcCache(ctx, opt, gitFetchOptions)
		if err != nil {
			return nil, fmt.Errorf("failed to clone: %w", err)
		}
	} else {
		gitCommand := command.NewLocalCommandContext(ctx, "git", "fetch", gitFetchOptions.RemoteName)
		gitCommand.Env = os.Environ()
		gitCommand.Env = append(gitCommand.Env, fmt.Sprintf("GIT_SSH_COMMAND=sshpass -f %s ssh -o StrictHostKeyChecking=no", entity.PathUserCodebaseJWT))
		// Add proxy environment variables for GitHub repositories
		if strings.Contains(opt.URL, "github.com") {
			gitCommand.Env = append(gitCommand.Env,
				"https_proxy=http://sys-proxy-rd-relay.byted.org:3128",
				"http_proxy=http://sys-proxy-rd-relay.byted.org:3128",
			)
		}
		gitCommand.Dir = opt.Directory
		gitCommand.Args = append(gitCommand.Args, lo.Map(gitFetchOptions.RefSpecs, func(refSpec config.RefSpec, _ int) string {
			return refSpec.String()
		})...)

		if gitFetchOptions.Depth > 0 {
			gitCommand.Args = append(gitCommand.Args, "--depth", fmt.Sprintf("%d", gitFetchOptions.Depth))
		}
		if opt.Progress {
			gitCommand.Args = append(gitCommand.Args, "--progress")
			gitCommand.Stdout = opt.Terminal.StdoutWriter()
			gitCommand.Stderr = opt.Terminal.StderrWriter()
		}
		out, err := opt.Terminal.ExecuteSafeCmd(gitCommand, ExecuteCmdOption{DisableStdioStream: true, Timeout: 0})
		if err != nil {
			return nil, fmt.Errorf("failed to fetch: %w, %s", err, out)
		}
		run.GetLogger().Infof("fetch %s with refSpec %s success", opt.URL, refSpec)
	}
	run.GetLogger().Infof("fetch %s with refSpec success %s", opt.URL, refSpec)

	worktree, err := repo.Worktree()
	if err != nil {
		return nil, fmt.Errorf("failed to get worktree: %w", err)
	}

	checkoutOpts := &git.CheckoutOptions{}
	if exact {
		checkoutOpts.Hash = plumbing.NewHash(opt.Checkout)
	} else {
		checkoutOpts.Branch = checkoutRef
	}

	if usePVCCache {
		err = GitCheckoutWithPvcCache(ctx, opt, checkoutOpts)
		if err != nil {
			return nil, fmt.Errorf("failed to git checkout %s: %w", opt.Checkout, err)
		}
	} else {
		if err = worktree.Checkout(checkoutOpts); err != nil {
			run.GetLogger().Errorf("failed to checkout %s: %s, start manual checkout", opt.Checkout, err.Error())
			// 因为 go-git checkout 会不允许路径包含 `..\` 从而导致 checkout 失败，这里尝试手动 checkout
			// return fmt.Errorf("invalid path %q: cannot use '..'", p)
			// invalid path: "\"
			// https://github.com/go-git/go-git/blob/302dddeda962e4bb3477a8e4080bc6f5a253e2bb/worktree.go#L428-L457
			if strings.Contains(err.Error(), "invalid path") {
				// 先重置工作树状态，避免 go-git 和命令行 git 状态不一致
				resetCmd := exec.Command("git", "reset", "--hard")
				resetCmd.Dir = opt.Directory
				if _, resetErr := opt.Terminal.ExecuteCmd(resetCmd, ExecuteCmdOption{MaxDisplayLines: 100, Timeout: 300}); resetErr != nil {
					run.GetLogger().Warnf("failed to reset before checkout: %v", resetErr)
				}

				// 清理工作树，移除未跟踪的文件
				cleanCmd := exec.Command("git", "clean", "-fd")
				cleanCmd.Dir = opt.Directory
				if _, cleanErr := opt.Terminal.ExecuteCmd(cleanCmd, ExecuteCmdOption{MaxDisplayLines: 100, Timeout: 300}); cleanErr != nil {
					run.GetLogger().Warnf("failed to clean before checkout: %v", cleanErr)
				}

				cmd := exec.Command("git", "checkout", opt.Checkout)
				cmd.Dir = opt.Directory
				_, checkoutErr := opt.Terminal.ExecuteCmd(cmd, ExecuteCmdOption{MaxDisplayLines: 1000, Timeout: 300})
				if checkoutErr != nil {
					return nil, fmt.Errorf("failed to checkout %s:%w: %w", opt.Checkout, err, checkoutErr)
				}
			} else {
				return nil, fmt.Errorf("failed to checkout %s: %w", opt.Checkout, err)
			}
		}
	}
	run.GetLogger().Infof("checkout %s with checkoutOpts %s success", opt.URL, checkoutOpts)

	return &Repository{
		Directory:     opt.Directory,
		Meta:          metadata,
		Git:           repo,
		GitCredential: opt.GitCredential,
	}, nil
}

// Status returns git status of uncommitted files
func (r *Repository) Status() (s entity.GitStatus, err error) {
	worktree, err := r.Git.Worktree()
	if err != nil {
		return nil, err
	}
	status, err := worktree.Status()
	if err != nil {
		return nil, err
	}

	s = make(entity.GitStatus)
	for path, entry := range status {
		s[path] = entity.GitFileStatus{
			Status:  entry.Worktree,
			Path:    path,
			OldPath: lo.Ternary(entry.Worktree == git.Renamed, entry.Extra, ""),
		}
	}

	return
}

type RepoLanguage struct {
	Language   string  // language name
	Count      int     // count of files with this language
	Percentage float64 // percentage of files with this language, 0-1
}

// Languages infer languages from the repository, order by percentage of code files
// this will walk through files (excluding gitignore) and map languages from file extensions
func (r *Repository) Languages() (languages []RepoLanguage, err error) {
	languageCount := make(map[string]int)
	totalFiles := 0

	var ignoreRule gitignore.GitIgnore
	gitignorePath := filepath.Join(r.Directory, ".gitignore")
	if _, err := os.Stat(gitignorePath); err == nil {
		ignoreRule, err = gitignore.NewFromFile(gitignorePath)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to load .gitignore")
		}
	}

	err = filepath.Walk(r.Directory, func(path string, info os.FileInfo, walkErr error) error {
		if walkErr != nil {
			return walkErr
		}
		if info.IsDir() {
			if info.Name() == ".git" {
				return filepath.SkipDir
			}
			return nil
		}

		if ignoreRule != nil && ignoreRule.Ignore(path) {
			return nil
		}

		// Try to detect language by filename first
		language, safe := enry.GetLanguageByFilename(path)
		// If filename detection is not reliable or returns empty, try content-based detection
		if !safe || language == "" {
			content, readErr := os.ReadFile(path)
			if readErr != nil {
				return nil
			}
			language = enry.GetLanguage(path, content)
		}
		if language == "" {
			return nil
		}

		// Only count programming languages, ignore data files
		langType := enry.GetLanguageType(language)
		if langType != enry.Programming {
			return nil
		}

		languageCount[language]++
		totalFiles++
		return nil
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to walk repository files")
	}

	for lang, count := range languageCount {
		percentage := float64(count) / float64(totalFiles)
		languages = append(languages, RepoLanguage{
			Language:   lang,
			Count:      count,
			Percentage: percentage,
		})
	}

	sort.Slice(languages, func(i, j int) bool {
		return languages[i].Percentage > languages[j].Percentage
	})

	return languages, nil
}

type DiffPatchOption struct {
	From plumbing.Revision
	To   plumbing.Revision
}

func (r *Repository) DiffPatch(opt DiffPatchOption) (patch string, err error) {
	from, err := r.Git.ResolveRevision(opt.From)
	if err != nil {
		return "", err
	}
	to, err := r.Git.ResolveRevision(opt.To)
	if err != nil {
		return "", err
	}
	fromCommit, err := r.Git.CommitObject(*from)
	if err != nil {
		return "", err
	}
	toCommit, err := r.Git.CommitObject(*to)
	if err != nil {
		return "", err
	}
	p, err := fromCommit.Patch(toCommit)
	if err != nil {
		return "", err
	}
	return p.String(), nil
}

type DiffOption struct {
	From plumbing.Revision
	To   plumbing.Revision
}

func (r *Repository) DiffStatus(opt DiffOption) (s entity.GitStatus, err error) {
	from, err := r.Git.ResolveRevision(opt.From)
	if err != nil {
		return nil, err
	}
	to, err := r.Git.ResolveRevision(opt.To)
	if err != nil {
		return nil, err
	}
	fromCommit, err := r.Git.CommitObject(*from)
	if err != nil {
		return nil, err
	}
	toCommit, err := r.Git.CommitObject(*to)
	if err != nil {
		return nil, err
	}
	patch, err := fromCommit.Patch(toCommit)
	if err != nil {
		return nil, err
	}

	s = make(entity.GitStatus)
	for _, diff := range patch.FilePatches() {
		stat := getStatusFromDiff(diff)
		s[stat.Path] = stat
	}

	return
}

func (r *Repository) SetSSHCredential(user, pass string) {
	r.GitCredential = sshPass(user, pass)
}

// CurrentReference returns the current Git reference with type prefix
// Returns: "branchName:main", "tagName:v1.0.0", "commitHash:abc123...", or "" if error
func (r *Repository) CurrentReference() string {
	if r.Git == nil {
		return ""
	}

	head, err := r.Git.Head()
	if err != nil || head == nil {
		return ""
	}

	// Check if HEAD points to a branch
	if head.Name().IsBranch() {
		return "branchName:" + head.Name().Short()
	}

	// Check if HEAD points to a tag directly
	if head.Name().IsTag() {
		return "tagName:" + head.Name().Short()
	}

	// Check if HEAD points to a commit that has tags pointing to it (detached HEAD on tag)
	tagIter, err := r.Git.Tags()
	if err == nil {
		err = tagIter.ForEach(func(tagRef *plumbing.Reference) error {
			if tagRef == nil {
				return nil
			}

			// For lightweight tags, tagRef.Hash() is the commit hash
			if tagRef.Hash() == head.Hash() {
				return fmt.Errorf("found_tag:%s", tagRef.Name().Short())
			}

			// For annotated tags, we need to resolve the tag object to get the commit hash
			tagObj, err := r.Git.TagObject(tagRef.Hash())
			if err == nil && tagObj != nil && tagObj.Target == head.Hash() {
				return fmt.Errorf("found_tag:%s", tagRef.Name().Short())
			}

			return nil
		})
		if err != nil && strings.HasPrefix(err.Error(), "found_tag:") {
			tagName := strings.TrimPrefix(err.Error(), "found_tag:")
			return "tagName:" + tagName
		}
	}

	// If not a branch or tag, return as commit hash
	return "commitHash:" + head.Hash().String()
}

type CommitMessageOption struct {
	Title   string              // must be non-empty
	Body    string              // commit description
	Footers map[string][]string // `Co-authored-by`, `Signed-off-by`, `Change-Id`, etc
}

func (m CommitMessageOption) Format() string {
	var buf bytes.Buffer
	buf.WriteString(m.Title)
	if len(m.Body) > 0 {
		buf.WriteString("\n\n")
		buf.WriteString(m.Body)
	}
	if len(m.Footers) > 0 {
		for k, v := range m.Footers {
			if len(v) == 0 {
				continue
			}
			buf.WriteString("\n\n")
			for _, vv := range v {
				buf.WriteString(fmt.Sprintf("%s: %s\n", k, vv))
			}
		}
	}
	return buf.String()
}

type CommitOption struct {
	All     bool // whether to add modified/deleted files
	Message CommitMessageOption

	Author    *object.Signature
	CoAuthors []*object.Signature
	Parents   []string // parent commits, by default HEAD
}

func (r *Repository) Commit(opt CommitOption) (commit *object.Commit, err error) {
	worktree, err := r.Git.Worktree()
	if err != nil {
		return nil, err
	}
	parents := lo.Map(opt.Parents, func(p string, _ int) plumbing.Hash {
		return plumbing.NewHash(p)
	})

	if len(opt.CoAuthors) > 0 {
		opt.Message.Footers = lo.Assign(opt.Message.Footers, map[string][]string{
			"Co-authored-by": lo.Map(opt.CoAuthors, func(v *object.Signature, _ int) string {
				return fmt.Sprintf("%s <%s>", v.Name, v.Email)
			}),
		})
	}

	if opt.All {
		status, err := r.Status()
		if err != nil {
			return nil, err
		}
		for path, stat := range status {
			filePath := filepath.Join(r.Directory, path)
			fileInfo, err := os.Stat(filePath)
			if err != nil {
				return nil, err
			}
			if fileInfo.IsDir() {
				// 如果是文件夹，跳过处理
				continue
			}
			if len(stat.Path) > 0 {
				_, err = worktree.Add(stat.Path)
				if err != nil {
					return nil, err
				}
			}
			if len(stat.OldPath) > 0 {
				_, err = worktree.Remove(stat.OldPath)
				if err != nil {
					return nil, err
				}
			}
		}
	}

	hash, err := worktree.Commit(opt.Message.Format(), &git.CommitOptions{
		All:     false,
		Author:  opt.Author,
		Parents: parents,
	})
	if err != nil {
		return nil, err
	}
	return r.Git.CommitObject(hash)
}

type AmendCommitOption struct {
	// Committer the signature of the new commit, defaults to the current author
	Committer *object.Signature
	Message   *CommitMessageOption
}

// AmendCommit replace the tip of HEAD with new commit info.
// You should add necessary files before amending
func (r *Repository) AmendCommit(opt AmendCommitOption) (commit *object.Commit, err error) {
	// we don't use go-git's CommitOptions.Amend, since it have bugs with commits of multiple parents
	// and do not support no-edit to commit message, we still need to manually get the HEAD commit
	head, err := r.Git.Head()
	if err != nil {
		return nil, err
	}
	headCommit, err := r.Git.CommitObject(head.Hash())
	if err != nil {
		return nil, err
	}

	opts := &git.CommitOptions{
		Author: &headCommit.Author,
		Committer: lo.Ternary(opt.Committer != nil, opt.Committer, &object.Signature{
			Name:  headCommit.Author.Name,
			Email: headCommit.Author.Email,
			When:  time.Now(),
		}),
		Parents: headCommit.ParentHashes,
	}
	msg := lo.TernaryF(opt.Message != nil, func() string { return opt.Message.Format() }, func() string { return headCommit.Message })

	worktree, err := r.Git.Worktree()
	if err != nil {
		return nil, err
	}
	hash, err := worktree.Commit(msg, opts)
	if err != nil {
		return nil, err
	}
	return r.Git.CommitObject(hash)
}

func (r *Repository) Add(path string) (hash plumbing.Hash, err error) {
	worktree, err := r.Git.Worktree()
	if err != nil {
		return plumbing.ZeroHash, err
	}
	return worktree.Add(path)
}

func (r *Repository) AddAll() (err error) {
	worktree, err := r.Git.Worktree()
	if err != nil {
		return err
	}
	return worktree.AddWithOptions(&git.AddOptions{All: true})
}

type ReadGitFileOption struct {
	SHA  string // commit sha, default HEAD
	Path string
}

func (r *Repository) ReadFile(opt ReadGitFileOption) ([]byte, error) {
	sha := opt.SHA
	if sha == "" {
		head, err := r.Git.Head()
		if err != nil {
			return nil, err
		}
		sha = head.Hash().String()
	}

	commit, err := r.Git.CommitObject(plumbing.NewHash(sha))
	if err != nil {
		return nil, err
	}
	tree, err := commit.Tree()
	if err != nil {
		return nil, err
	}

	file, err := tree.File(opt.Path)
	if err != nil {
		return nil, err
	}
	reader, err := file.Blob.Reader()
	if err != nil {
		return nil, errors.Wrap(err, "failed to read blob")
	}
	defer reader.Close()

	return io.ReadAll(reader)
}

type CheckoutOption struct {
	// Checkout to a branch, mutually exclusive with Commit
	Branch string
	// Checkout to a commit, mutually exclusive with Branch
	Commit string

	// Create a new branch, equivalent to `git checkout -b`
	Create bool
	// after checkout, how the modified files should be reset
	// should be one of
	// `git.ResetModeSoft`: only set HEAD to the commit, keep all files in workspace
	// `git.ResetModeMixed`: default, keep uncommitted files in workspace
	// `git.ResetModeHard`: checkout and discard uncommitted files
	ResetMode git.ResetMode
	// Specify directories to checkout, if not specified, all files will be checked out
	Directories []string
}

func (r *Repository) Checkout(opt CheckoutOption) error {
	worktree, err := r.Git.Worktree()
	if err != nil {
		return err
	}

	err = worktree.Checkout(&git.CheckoutOptions{
		Hash:                      plumbing.NewHash(opt.Commit),
		Branch:                    plumbing.NewBranchReferenceName(opt.Branch),
		Create:                    opt.Create,
		Force:                     lo.Ternary(opt.ResetMode == git.HardReset, true, false),
		Keep:                      lo.Ternary(opt.ResetMode == git.SoftReset, true, false),
		SparseCheckoutDirectories: opt.Directories,
	})
	return err
}

type PushOption struct {
	// to push a commit to a branch: <sha>:refs/heads/<branch>
	// to push a branch to a branch: refs/heads/<source>:refs/heads/<target>
	RefSpec config.RefSpec
	Force   bool
}

func (r *Repository) Push(ctx context.Context, opt PushOption) error {
	if r.GitCredential == nil {
		return ErrNoCredential
	}
	return r.Git.PushContext(ctx, &git.PushOptions{
		RefSpecs: []config.RefSpec{
			opt.RefSpec,
		},
		Force: opt.Force,
		Auth:  r.GitCredential,
	})
}

func (r *Repository) SetArtifact(artifact *iris.PatchArtifact) {
	r.Artifact = artifact
}

func (r *Repository) SyncArtifact(run *iris.AgentRunContext) error {
	if r.Artifact == nil {
		return nil
	}
	artifacts, logger := run.GetArtifactService(), run.GetLogger()
	status, err := r.Status()
	if err != nil {
		return err
	}
	r.Artifact.Metadata.GitFilesStatus = status

	files := lo.Flatten(lo.MapToSlice(status, func(p string, status entity.GitFileStatus) []iris.ArtifactFile {
		filePath := filepath.Join(r.Directory, p)
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			logger.Errorf("failed to get file info %s: %v", filePath, err)
			return []iris.ArtifactFile{}
		}
		if fileInfo.IsDir() {
			// 如果是文件夹，跳过处理
			return []iris.ArtifactFile{}
		}
		content, err := os.ReadFile(filePath)
		if err != nil {
			logger.Errorf("failed to read file %s/%s: %v", r.Directory, p, err)
			return []iris.ArtifactFile{}
		}
		switch status.Status {
		case git.Untracked:
			fallthrough
		case git.Added:
			return []iris.ArtifactFile{{
				Path:    iris.ArtifactPathPrefix(iris.PatchArtifactNew, p),
				Content: content,
			}}
		case git.Deleted:
			oldContent, err := r.ReadFile(ReadGitFileOption{SHA: r.Artifact.Metadata.BaseCommitSHA, Path: p})
			if err != nil {
				logger.Errorf("failed to read old file %s/%s: %v", r.Directory, p, err)
				break
			}
			return []iris.ArtifactFile{{
				Path:    iris.ArtifactPathPrefix(iris.PatchArtifactOld, p),
				Content: oldContent,
			}}
		case git.Renamed:
			oldContent, err := r.ReadFile(ReadGitFileOption{SHA: r.Artifact.Metadata.BaseCommitSHA, Path: status.OldPath})
			if err != nil {
				logger.Errorf("failed to read old file %s/%s: %v", r.Directory, status.OldPath, err)
				break
			}
			return []iris.ArtifactFile{
				{
					Path:    iris.ArtifactPathPrefix(iris.PatchArtifactOld, status.OldPath),
					Content: oldContent,
				},
				{
					Path:    iris.ArtifactPathPrefix(iris.PatchArtifactNew, status.Path),
					Content: content,
				},
			}
		case git.Modified:
			oldContent, err := r.ReadFile(ReadGitFileOption{SHA: r.Artifact.Metadata.BaseCommitSHA, Path: p})
			if err != nil {
				logger.Errorf("failed to read old file %s/%s: %v", r.Directory, p, err)
				break
			}
			return []iris.ArtifactFile{
				{
					Path:    iris.ArtifactPathPrefix(iris.PatchArtifactOld, status.Path),
					Content: oldContent,
				},
				{
					Path:    iris.ArtifactPathPrefix(iris.PatchArtifactNew, status.Path),
					Content: content,
				},
			}
		}
		return []iris.ArtifactFile{}
	}))
	logger.Infof("sync git files to artifact(%s): %+v", r.Artifact.ArtifactID, lo.Map(files, func(file iris.ArtifactFile, _ int) string {
		return file.Path
	}))

	return artifacts.UploadFiles(run, r.Artifact, files)
}

type PushArtifactOption struct {
	RemoteBranch string
}

type CommitArtifactOption struct {
	CommitOption *CommitOption
	PushOption   *PushArtifactOption
}

func (r *Repository) CommitArtifact(run *iris.AgentRunContext, opt CommitArtifactOption) error {
	if r.Artifact == nil {
		return nil
	}
	artifacts, logger := run.GetArtifactService(), run.GetLogger()
	status, err := r.Status()
	if err != nil {
		logger.Errorf("failed to get git status: %v", err)
	}
	r.Artifact.Metadata.GitFilesStatus = status

	var commit *object.Commit
	if opt.CommitOption != nil {
		logger.Infof("create commit: %+v", opt.CommitOption)
		commit, err = r.Commit(*opt.CommitOption)
		if err != nil {
			return errors.Wrap(err, "failed to commit")
		}
		hash := commit.Hash.String()
		r.Artifact.Metadata.PatchCommitSHA = hash
		r.Artifact.Metadata.PatchCommitMessage = commit.Message
		logger.Infof("created commit: %s\n%s", hash, commit.Message)
	}

	if commit != nil && opt.PushOption != nil {
		err = r.Push(run, PushOption{
			RefSpec: config.RefSpec(fmt.Sprintf("%s:refs/heads/%s", commit.Hash.String(), opt.PushOption.RemoteBranch)),
		})
		if err != nil {
			return errors.Wrap(err, "failed to push commit")
		}
		r.Artifact.Metadata.BranchName = opt.PushOption.RemoteBranch

		logger.Infof("artifact pushed to branch: %s", r.Artifact.ArtifactID, opt.PushOption.RemoteBranch)
	}

	return artifacts.CommitArtifact(run, r.Artifact)
}

var (
	codebaseRepoRe = regexp.MustCompile(`(code|review|git).byted.org[:/](.*)`)
	githubRepoRe   = regexp.MustCompile(`github.com[:/](.*)`)
)

func parseGitURL(url string) (meta *RepoMeta, err error) {
	meta = &RepoMeta{
		Platform: entity.GitPlatformUnknown,
		URL:      url,
	}
	match := codebaseRepoRe.FindStringSubmatch(url)
	if len(match) > 0 {
		meta.Platform = entity.GitPlatformCodebase
		meta.Name = match[2]
	} else if githubRepoRe.MatchString(url) {
		meta.Platform = entity.GitPlatformGithub
		match = githubRepoRe.FindStringSubmatch(url)
		meta.Name = match[1]
	}

	meta.Name = cleanRepoName(meta.Name)
	return meta, nil
}

// cleanRepoName removes .git suffix from repository name
func cleanRepoName(repoName string) string {
	repoName = strings.TrimSuffix(repoName, ".git")
	repoName = strings.TrimSuffix(repoName, "/")
	return repoName
}

func sshPass(user, pass string) transport.AuthMethod {
	return &ssh.PasswordCallback{
		User: user,
		Callback: func() (string, error) {
			return pass, nil
		},
		HostKeyCallbackHelper: ssh.HostKeyCallbackHelper{
			HostKeyCallback: func(hostname string, remote net.Addr, key stdssh.PublicKey) error {
				return nil
			},
		},
	}
}

func getStatusFromDiff(diff diff.FilePatch) (status entity.GitFileStatus) {
	status = entity.GitFileStatus{
		Status: git.Modified,
	}
	from, to := diff.Files()
	if from == nil {
		status.Status = git.Added
		status.Path = to.Path()
		status.NewMode = lo.ToPtr(to.Mode())
		return
	} else if to == nil {
		status.Status = git.Deleted
		status.Path = from.Path()
		status.OldMode = lo.ToPtr(from.Mode())
		return
	}

	if from.Path() != to.Path() {
		status.Status = git.Renamed
		status.OldPath = from.Path()
	}
	status.Path = to.Path()
	status.OldMode = lo.ToPtr(from.Mode())
	status.NewMode = lo.ToPtr(to.Mode())
	return status
}
