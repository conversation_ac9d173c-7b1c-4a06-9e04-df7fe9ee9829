package workspace

import (
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/stretchr/testify/assert"
)

func Test_parseGitURL(t *testing.T) {
	type args struct {
		url string
	}
	tests := []struct {
		name     string
		args     args
		wantMeta *RepoMeta
		wantErr  bool
	}{
		{
			name: "codebase ssh",
			args: args{
				url: "******************:devgpt/kiwis.git",
			},
			wantMeta: &RepoMeta{
				Platform: "codebase",
				Name:     "devgpt/kiwis",
				URL:      "******************:devgpt/kiwis.git",
			},
		},
		{
			name: "codebase http",
			args: args{
				url: "https://code.byted.org/devgpt/kiwis.git",
			},
			wantMeta: &RepoMeta{
				Platform: "codebase",
				Name:     "devgpt/kiwis",
				URL:      "https://code.byted.org/devgpt/kiwis.git",
			},
		},
		{
			name: "codebase mirror",
			args: args{
				url: "<EMAIL>:devgpt/kiwis.git",
			},
			wantMeta: &RepoMeta{
				Platform: "codebase",
				Name:     "devgpt/kiwis",
				URL:      "<EMAIL>:devgpt/kiwis.git",
			},
		},
		{
			name: "github http",
			args: args{
				url: "https://github.com/go-git/go-git.git",
			},
			wantMeta: &RepoMeta{
				Platform: "github",
				Name:     "go-git/go-git",
				URL:      "https://github.com/go-git/go-git.git",
			},
		},
		{
			name: "github ssh",
			args: args{
				url: "**************:go-git/go-git.git",
			},
			wantMeta: &RepoMeta{
				Platform: "github",
				Name:     "go-git/go-git",
				URL:      "**************:go-git/go-git.git",
			},
		},
		{
			name: "with extra suffix /",
			args: args{
				url: "github.com:go-git/go-git/",
			},
			wantMeta: &RepoMeta{
				Platform: "github",
				Name:     "go-git/go-git",
				URL:      "github.com:go-git/go-git/",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotMeta, err := parseGitURL(tt.args.url)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseGitURL() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotMeta, tt.wantMeta) {
				t.Errorf("parseGitURL() = %v, want %v", gotMeta, tt.wantMeta)
			}
		})
	}
}

// Helper function to create test signature
func testSignature() *object.Signature {
	return &object.Signature{
		Name:  "Test User",
		Email: "<EMAIL>",
		When:  time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
	}
}

// Helper function to create initial commit in repository
func createInitialCommit(t *testing.T, repo *git.Repository, tempDir string) plumbing.Hash {
	worktree, err := repo.Worktree()
	if err != nil {
		t.Fatalf("Failed to get worktree: %v", err)
	}

	// Create a test file
	testFile := filepath.Join(tempDir, "test.txt")
	if err := os.WriteFile(testFile, []byte("test content"), 0o644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Add and commit
	if _, err := worktree.Add("test.txt"); err != nil {
		t.Fatalf("Failed to add file: %v", err)
	}

	commitHash, err := worktree.Commit("Initial commit", &git.CommitOptions{
		Author: testSignature(),
	})
	if err != nil {
		t.Fatalf("Failed to commit: %v", err)
	}

	return commitHash
}

func TestRepository_CurrentReference(t *testing.T) {
	tests := []struct {
		name     string
		setup    func(t *testing.T) *Repository
		expected string
	}{
		{
			name: "nil git repository",
			setup: func(t *testing.T) *Repository {
				return &Repository{Git: nil}
			},
			expected: "",
		},
		{
			name: "empty repository (no commits)",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				repo, err := git.PlainInit(tempDir, false)
				if err != nil {
					t.Fatalf("Failed to init repo: %v", err)
				}

				return &Repository{Git: repo, Directory: tempDir}
			},
			expected: "",
		},
		{
			name: "on branch",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				repo, err := git.PlainInit(tempDir, false)
				if err != nil {
					t.Fatalf("Failed to init repo: %v", err)
				}

				createInitialCommit(t, repo, tempDir)

				return &Repository{Git: repo, Directory: tempDir}
			},
			expected: "branchName:master",
		},
		{
			name: "on feature branch",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				repo, err := git.PlainInit(tempDir, false)
				if err != nil {
					t.Fatalf("Failed to init repo: %v", err)
				}

				createInitialCommit(t, repo, tempDir)

				worktree, err := repo.Worktree()
				if err != nil {
					t.Fatalf("Failed to get worktree: %v", err)
				}

				branchName := plumbing.NewBranchReferenceName("feature/test")
				if err := worktree.Checkout(&git.CheckoutOptions{
					Branch: branchName,
					Create: true,
				}); err != nil {
					t.Fatalf("Failed to checkout feature branch: %v", err)
				}

				return &Repository{Git: repo, Directory: tempDir}
			},
			expected: "branchName:feature/test",
		},
		{
			name: "on annotated tag",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				repo, err := git.PlainInit(tempDir, false)
				if err != nil {
					t.Fatalf("Failed to init repo: %v", err)
				}

				commitHash := createInitialCommit(t, repo, tempDir)

				// Create annotated tag
				tagName := "v1.0.0"
				if _, err := repo.CreateTag(tagName, commitHash, &git.CreateTagOptions{
					Tagger:  testSignature(),
					Message: "Version 1.0.0",
				}); err != nil {
					t.Fatalf("Failed to create tag: %v", err)
				}

				// Checkout the tag (creates detached HEAD pointing to commit)
				worktree, err := repo.Worktree()
				if err != nil {
					t.Fatalf("Failed to get worktree: %v", err)
				}

				if err := worktree.Checkout(&git.CheckoutOptions{
					Hash: commitHash,
				}); err != nil {
					t.Fatalf("Failed to checkout tag: %v", err)
				}

				return &Repository{Git: repo, Directory: tempDir}
			},
			expected: "tagName:v1.0.0",
		},
		{
			name: "on lightweight tag",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				repo, err := git.PlainInit(tempDir, false)
				if err != nil {
					t.Fatalf("Failed to init repo: %v", err)
				}

				commitHash := createInitialCommit(t, repo, tempDir)

				// Create lightweight tag
				tagRef := plumbing.NewTagReferenceName("v2.0.0")
				ref := plumbing.NewHashReference(tagRef, commitHash)
				if err := repo.Storer.SetReference(ref); err != nil {
					t.Fatalf("Failed to create lightweight tag: %v", err)
				}

				// Checkout the tag commit (creates detached HEAD)
				worktree, err := repo.Worktree()
				if err != nil {
					t.Fatalf("Failed to get worktree: %v", err)
				}

				if err := worktree.Checkout(&git.CheckoutOptions{
					Hash: commitHash,
				}); err != nil {
					t.Fatalf("Failed to checkout tag commit: %v", err)
				}

				return &Repository{Git: repo, Directory: tempDir}
			},
			expected: "tagName:v2.0.0",
		},
		{
			name: "detached HEAD on commit (no tag)",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				repo, err := git.PlainInit(tempDir, false)
				if err != nil {
					t.Fatalf("Failed to init repo: %v", err)
				}

				// Create first commit
				commitHash1 := createInitialCommit(t, repo, tempDir)

				// Create second commit
				worktree, err := repo.Worktree()
				if err != nil {
					t.Fatalf("Failed to get worktree: %v", err)
				}

				testFile := filepath.Join(tempDir, "test2.txt")
				if err := os.WriteFile(testFile, []byte("second commit"), 0o644); err != nil {
					t.Fatalf("Failed to create second test file: %v", err)
				}

				if _, err := worktree.Add("test2.txt"); err != nil {
					t.Fatalf("Failed to add second file: %v", err)
				}

				commitHash2, err := worktree.Commit("Second commit", &git.CommitOptions{
					Author: testSignature(),
				})
				if err != nil {
					t.Fatalf("Failed to create second commit: %v", err)
				}

				// Checkout first commit (creates detached HEAD, no tag points to it)
				if err := worktree.Checkout(&git.CheckoutOptions{
					Hash: commitHash1,
				}); err != nil {
					t.Fatalf("Failed to checkout first commit: %v", err)
				}

				// Verify we're not on the latest commit
				if commitHash1 == commitHash2 {
					t.Fatal("Expected different commit hashes")
				}

				return &Repository{Git: repo, Directory: tempDir}
			},
			expected: "commit_hash", // Will be validated as commitHash:hash format
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := tt.setup(t)
			result := repo.CurrentReference()

			// Special handling for commit hash cases
			if tt.expected == "commit_hash" {
				// Should start with "commitHash:" and have valid hash
				if !strings.HasPrefix(result, "commitHash:") {
					t.Errorf("Expected result to start with 'commitHash:', got: %s", result)
					return
				}
				hash := strings.TrimPrefix(result, "commitHash:")
				if !plumbing.IsHash(hash) {
					t.Errorf("Expected valid Git hash after 'commitHash:', got: %s", hash)
					return
				}
			} else if result != tt.expected {
				t.Errorf("CurrentReference() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestRepository_Languages(t *testing.T) {
	tests := []struct {
		name         string
		setup        func(t *testing.T) *Repository
		expected     []RepoLanguage
		expectError  bool
		errorMessage string
	}{
		{
			name: "empty directory",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()
				return &Repository{Directory: tempDir}
			},
			expected:    []RepoLanguage{},
			expectError: false,
		},
		{
			name: "mixed programming languages",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				// Create files for different languages
				testFiles := map[string]string{
					"main.go":     "package main\n\nfunc main() {\n\tprintln(\"Hello World\")\n}",
					"script.js":   "console.log('Hello World');",
					"app.py":      "print('Hello World')",
					"style.css":   "body { margin: 0; }",
					"index.html":  "<html><body>Hello World</body></html>",
					"utils.go":    "package utils\n\nfunc Add(a, b int) int {\n\treturn a + b\n}",
					"helper.py":   "def greet(name):\n    return f'Hello {name}'",
					"config.json": "{\"name\": \"test\"}",
					"data.txt":    "some text data",
				}

				for filename, content := range testFiles {
					filePath := filepath.Join(tempDir, filename)
					if err := os.WriteFile(filePath, []byte(content), 0o644); err != nil {
						t.Fatalf("Failed to create test file %s: %v", filename, err)
					}
				}

				return &Repository{Directory: tempDir}
			},
			expected: []RepoLanguage{
				{Language: "Python", Count: 2, Percentage: 0.4},
				{Language: "Go", Count: 2, Percentage: 0.4},
				{Language: "JavaScript", Count: 1, Percentage: 0.2},
			},
			expectError: false,
		},
		{
			name: "with gitignore",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				// Create .gitignore file
				gitignoreContent := "*.log\n*.tmp\nnode_modules/\nbuild/\n"
				gitignorePath := filepath.Join(tempDir, ".gitignore")
				if err := os.WriteFile(gitignorePath, []byte(gitignoreContent), 0o644); err != nil {
					t.Fatalf("Failed to create .gitignore: %v", err)
				}

				// Create test files
				testFiles := map[string]string{
					"main.go":   "package main",
					"app.py":    "print('hello')",
					"debug.log": "debug info", // should be ignored
					"temp.tmp":  "temp data",  // should be ignored
					"script.js": "console.log()",
				}

				for filename, content := range testFiles {
					filePath := filepath.Join(tempDir, filename)
					if err := os.WriteFile(filePath, []byte(content), 0o644); err != nil {
						t.Fatalf("Failed to create test file %s: %v", filename, err)
					}
				}

				// Create ignored directory
				nodeModulesDir := filepath.Join(tempDir, "node_modules")
				if err := os.MkdirAll(nodeModulesDir, 0o755); err != nil {
					t.Fatalf("Failed to create node_modules dir: %v", err)
				}
				ignoredFile := filepath.Join(nodeModulesDir, "package.js")
				if err := os.WriteFile(ignoredFile, []byte("// ignored"), 0o644); err != nil {
					t.Fatalf("Failed to create ignored file: %v", err)
				}

				return &Repository{Directory: tempDir}
			},
			expected: []RepoLanguage{
				{Language: "JavaScript", Count: 2, Percentage: 0.5},
				{Language: "Python", Count: 1, Percentage: 0.25},
				{Language: "Go", Count: 1, Percentage: 0.25},
			},
			expectError: false,
		},
		{
			name: "only non-programming files",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				// Create only non-programming files
				testFiles := map[string]string{
					"data.json":  "{\"key\": \"value\"}",
					"style.css":  "body { margin: 0; }",
					"index.html": "<html></html>",
					"data.txt":   "plain text",
					"config.xml": "<root></root>",
				}

				for filename, content := range testFiles {
					filePath := filepath.Join(tempDir, filename)
					if err := os.WriteFile(filePath, []byte(content), 0o644); err != nil {
						t.Fatalf("Failed to create test file %s: %v", filename, err)
					}
				}

				return &Repository{Directory: tempDir}
			},
			expected:    []RepoLanguage{},
			expectError: false,
		},
		{
			name: "single language dominance",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				// Create many Go files and one Python file
				goFiles := []string{"main.go", "utils.go", "handler.go", "model.go", "service.go"}
				for _, filename := range goFiles {
					filePath := filepath.Join(tempDir, filename)
					content := "package main\n\nfunc example() {}"
					if err := os.WriteFile(filePath, []byte(content), 0o644); err != nil {
						t.Fatalf("Failed to create Go file %s: %v", filename, err)
					}
				}

				// One Python file
				pythonFile := filepath.Join(tempDir, "script.py")
				if err := os.WriteFile(pythonFile, []byte("print('hello')"), 0o644); err != nil {
					t.Fatalf("Failed to create Python file: %v", err)
				}

				return &Repository{Directory: tempDir}
			},
			expected: []RepoLanguage{
				{Language: "Go", Count: 5, Percentage: float64(5) / 6},
				{Language: "Python", Count: 1, Percentage: float64(1) / 6},
			},
			expectError: false,
		},
		{
			name: "nested directories",
			setup: func(t *testing.T) *Repository {
				tempDir := t.TempDir()

				// Create nested directory structure
				dirs := []string{
					"src",
					"src/main",
					"src/utils",
					"tests",
				}

				for _, dir := range dirs {
					if err := os.MkdirAll(filepath.Join(tempDir, dir), 0o755); err != nil {
						t.Fatalf("Failed to create directory %s: %v", dir, err)
					}
				}

				// Create files in nested directories
				testFiles := map[string]string{
					"src/main/app.py":     "print('main')",
					"src/utils/helper.go": "package utils",
					"tests/test.py":       "import unittest",
					"main.go":             "package main",
					"test.go":             "package main_test",
				}

				for filePath, content := range testFiles {
					fullPath := filepath.Join(tempDir, filePath)
					if err := os.WriteFile(fullPath, []byte(content), 0o644); err != nil {
						t.Fatalf("Failed to create nested file %s: %v", filePath, err)
					}
				}

				return &Repository{Directory: tempDir}
			},
			expected: []RepoLanguage{
				{Language: "Go", Count: 3, Percentage: 0.6},
				{Language: "Python", Count: 2, Percentage: 0.4},
			},
			expectError: false,
		},
		{
			name: "invalid directory",
			setup: func(t *testing.T) *Repository {
				return &Repository{Directory: "/nonexistent/directory"}
			},
			expected:     nil,
			expectError:  true,
			errorMessage: "failed to walk repository files",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := tt.setup(t)
			result, err := repo.Languages()

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if !strings.Contains(err.Error(), tt.errorMessage) {
					t.Errorf("Expected error message to contain %q, got: %v", tt.errorMessage, err)
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Check each result
			assert.ElementsMatch(t, result, tt.expected)

			// Verify results are sorted by percentage (descending)
			for i := 1; i < len(result); i++ {
				if result[i-1].Percentage < result[i].Percentage {
					t.Errorf("Results not sorted by percentage: %s (%.2f%%) should be before %s (%.2f%%)",
						result[i].Language, result[i].Percentage,
						result[i-1].Language, result[i-1].Percentage)
				}
			}
		})
	}
}

// BenchmarkRepository_Languages benchmarks the Languages function on the current repository
func BenchmarkRepository_Languages(b *testing.B) {
	// Use the current directory as the repository
	currentDir, err := os.Getwd()
	if err != nil {
		b.Fatalf("Failed to get current directory: %v", err)
	}

	// Navigate to the repository root (kiwis directory)
	repoDir := filepath.Join(currentDir, "../../../..")
	if _, err := os.Stat(filepath.Join(repoDir, ".git")); os.IsNotExist(err) {
		b.Skip("No .git directory found, skipping benchmark")
	}

	repo := &Repository{Directory: repoDir}

	// Run a single test first to ensure it works
	result, err := repo.Languages()
	if err != nil {
		b.Fatalf("Languages function failed: %v", err)
	}

	// Log some stats about what we're benchmarking
	b.Logf("Benchmarking repository with %d languages detected", len(result))
	for i, lang := range result {
		if i >= 5 { // Only show top 5
			break
		}
		b.Logf("  %d. %s: %d files (%.1f%%)", i+1, lang.Language, lang.Count, lang.Percentage*100)
	}

	// Reset timer before actual benchmark
	b.ResetTimer()

	// Run the actual benchmark
	for i := 0; i < b.N; i++ {
		_, err := repo.Languages()
		if err != nil {
			b.Fatalf("Languages function failed during benchmark: %v", err)
		}
	}
}

// BenchmarkRepository_Languages_Small benchmarks on a smaller test repository
func BenchmarkRepository_Languages_Small(b *testing.B) {
	tempDir := b.TempDir()

	// Create a moderate number of files for benchmarking
	testFiles := map[string]string{
		"cmd/main.go":          "package main\n\nfunc main() {}",
		"internal/service.go":  "package internal\n\ntype Service struct {}",
		"internal/handler.go":  "package internal\n\nfunc Handler() {}",
		"pkg/utils.go":         "package pkg\n\nfunc Utils() {}",
		"pkg/models.go":        "package pkg\n\ntype Model struct {}",
		"scripts/deploy.py":    "#!/usr/bin/env python3\nprint('deploying')",
		"scripts/test.py":      "import unittest\nclass Test(unittest.TestCase): pass",
		"web/app.js":           "console.log('app started');",
		"web/components.js":    "function Component() {}",
		"web/styles.css":       "body { margin: 0; }",
		"docker/Dockerfile":    "FROM alpine\nRUN echo hello",
		"config/app.yaml":      "name: test\nversion: 1.0",
		"docs/README.md":       "# Project\nThis is a test project",
		"tests/unit_test.go":   "package tests\nimport \"testing\"",
		"tests/integration.py": "def test_integration(): pass",
	}

	// Create directory structure
	dirs := []string{"cmd", "internal", "pkg", "scripts", "web", "docker", "config", "docs", "tests"}
	for _, dir := range dirs {
		if err := os.MkdirAll(filepath.Join(tempDir, dir), 0o755); err != nil {
			b.Fatalf("Failed to create directory %s: %v", dir, err)
		}
	}

	// Create files
	for filePath, content := range testFiles {
		fullPath := filepath.Join(tempDir, filePath)
		if err := os.WriteFile(fullPath, []byte(content), 0o644); err != nil {
			b.Fatalf("Failed to create file %s: %v", filePath, err)
		}
	}

	repo := &Repository{Directory: tempDir}

	// Verify it works
	result, err := repo.Languages()
	if err != nil {
		b.Fatalf("Languages function failed: %v", err)
	}

	b.Logf("Small benchmark: %d languages detected", len(result))
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := repo.Languages()
		if err != nil {
			b.Fatalf("Languages function failed during benchmark: %v", err)
		}
	}
}
