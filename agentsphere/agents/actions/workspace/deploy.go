package workspace

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

const (
	ToolDeploy            = "deploy_frontend"
	ToolDeployDescription = `Specify a directory to deploy a static site to user. The directory can be a relative path (e.g. '.'). The directory should only contain essential files, verify the files list before deploy them.
For frontend projects based on frameworks like Vue or React, first compile the project into static files, and then use this tool to deploy.
The deployment will use index.html as the main entry point. Only include necessary files for the static site (html, css, js, images, etc.)`

	ToolDeployBackend            = "deploy_backend"
	ToolDeployBackendDescription = `Specify a directory to deploy a backend service to user. The directory can be a relative path (e.g. '.'). The directory should only contain essential files, verify the files list before deploy them.
Only supports FastAPI applications created by aime_create_fastapi_app.`

	// MaxDeployFiles is the maximum number of files allowed for deployment
	MaxDeployFiles = 300
)

type DeployArgs struct {
	Directory string `json:"directory" mapstructure:"directory" description:"the directory to deploy, it should contain an index.html as the entry point"`
}

type DeployBackendArgs struct {
	Directory string `json:"directory" mapstructure:"directory" description:"the directory to deploy, it should contain a FastAPI application (app.py or main.py)"`
}

type DeployOutput struct {
	ArtifactID string          `json:"artifact_id" mapstructure:"artifact_id"`
	Files      []string        `json:"files" mapstructure:"files"`
	URL        string          `json:"url" mapstructure:"url"`
	APIInfo    *BackendAPIInfo `json:"api_info,omitempty" mapstructure:"api_info,omitempty"`
	Console    []string        `json:"console,omitempty" mapstructure:"console,omitempty"`
}

type DeployOption struct {
	ProcessHTML bool
}

func NewDeployAction(opt DeployOption) iris.Action {
	return actions.ToTool(ToolDeploy, ToolDeployDescription, func(c *iris.AgentRunContext, args DeployArgs) (*DeployOutput, error) {
		output, err := DeployDirectory(c, args, opt)
		return output, iris.NewRecoverable(err)
	})
}

func DeployDirectory(c *iris.AgentRunContext, args DeployArgs, opt DeployOption) (*DeployOutput, error) {
	artifactService := c.GetArtifactService()
	if artifactService.IsNil() {
		return nil, errors.New("artifact service is nil")
	}

	editor := GetEditor(c)
	if editor == nil {
		return nil, errors.New("editor is nil")
	}

	stat, err := os.Stat(args.Directory)
	if err != nil {
		return nil, fmt.Errorf("%s does not exist: %v", args.Directory, err)
	}
	// model may want to deploy a single file, but forgot that references to images/css/js are not included
	// we force all deploy tool calls to be a directory
	if !stat.IsDir() {
		return nil, fmt.Errorf("%s is not a directory: %v", args.Directory, err)
	}

	fileList, err := editor.ReadDirectory(ReadDirectory{
		Path:      args.Directory,
		Recursive: true,
		MaxDepth:  1, // check if html is present in the first level of the directory
	})
	if err != nil {
		return nil, fmt.Errorf("failed to read directory %s: %v", args.Directory, err)
	}

	hasHTMLFile := lo.SomeBy(fileList.Files, func(file FileEntry) bool {
		return !file.IsDir && strings.HasSuffix(file.Name, ".html")
	})

	if !hasHTMLFile {
		return nil, fmt.Errorf("static site deployment requires at least one HTML file in %s", args.Directory)
	}

	if opt.ProcessHTML {
		addCitationLink(c, fileList)
	}

	artifact, err := artifactService.NewFileArtifact(c, nextentity.FileArtifactTypeMetadata{})
	if err != nil {
		return nil, err
	}
	absDirectory := args.Directory
	if !filepath.IsAbs(absDirectory) {
		absDirectory = filepath.Join(editor.WorkingDirectory, absDirectory)
	}
	files := lo.FilterMap(fileList.AllFiles, func(file FileEntry, _ int) (iris.ArtifactFileReader, bool) {
		filePath := filepath.Join(editor.WorkingDirectory, file.Path)
		fileInfo, err := os.Stat(filePath)
		if err != nil || fileInfo.IsDir() || fileInfo.Size() == 0 {
			return iris.ArtifactFileReader{}, false
		}
		f, err := os.Open(filePath)
		if err != nil {
			return iris.ArtifactFileReader{}, false
		}
		// upload path should be relative to the directory to deploy
		relPath, err := filepath.Rel(absDirectory, filePath)
		if err != nil {
			relPath = file.Path // use path relative to working directory instead
		}

		return iris.ArtifactFileReader{
			Path:   relPath,
			Size:   fileInfo.Size(),
			Reader: f,
		}, true
	})
	defer func() {
		for _, file := range files {
			if closer, ok := file.Reader.(io.Closer); ok {
				closer.Close()
			}
		}
	}()
	// the number of files should be less than MaxDeployFiles
	// the number of static files in kiwis-frontend is ~150
	// projects larger than this is out of model's capability, and may bring large pressure to our server
	if len(files) > MaxDeployFiles {
		return nil, fmt.Errorf("too many files(%d) to deploy (maybe trying to upload the whole codebase or node_modules?), please reduce the number of files", len(files))
	}

	err = artifactService.UploadFilesStream(c, artifact, files)
	if err != nil {
		return nil, err
	}
	err = artifactService.CommitArtifact(c, artifact)
	if err != nil {
		return nil, err
	}

	deployment, err := artifactService.CreateStaticSiteDeployment(c, artifact.ID())
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create deployment")
	}

	if _, ok := c.Parameters["deploy_id"]; !ok {
		c.Parameters["deploy_id"] = map[string]any{}
	}
	c.Parameters["deploy_id"].(map[string]any)[deployment.URL] = artifact.ID()

	output := &DeployOutput{
		ArtifactID: artifact.ID(),
		Files:      lo.Map(files, func(file iris.ArtifactFileReader, _ int) string { return file.Path }),
		URL:        deployment.URL,
	}

	// get browser console
	console, err := getBrowserConsole(c, deployment.URL)
	if err != nil {
		c.GetLogger().Errorf("failed to get browser console: %v", err)
	} else {
		c.GetLogger().Infof("browser console: %v", console)
		output.Console = console
	}

	return output, nil
}

func DeploySingleFile(c *iris.AgentRunContext, argFile string, opt DeployOption) (*DeployOutput, error) {
	artifactService := c.GetArtifactService()
	if artifactService.IsNil() {
		return nil, errors.New("artifact service is nil")
	}
	editor := GetEditor(c)
	if editor == nil {
		return nil, errors.New("editor is nil")
	}
	artifact, err := artifactService.NewFileArtifact(c, nextentity.FileArtifactTypeMetadata{})
	if err != nil {
		return nil, err
	}
	filePath := filepath.Join(editor.WorkingDirectory, argFile)
	fileInfo, err := os.Stat(filePath)
	if err != nil || fileInfo.IsDir() || fileInfo.Size() == 0 {
		return nil, err
	}
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}
	// todo: 上游citation相关pe ready后，也补充addCitationLink的调用

	// use stream to upload files as there might be binaries like pdfs
	err = artifactService.UploadFilesStream(c, artifact, []iris.ArtifactFileReader{
		{
			Path:   argFile,
			Size:   fileInfo.Size(),
			Reader: bytes.NewReader(content),
		},
	})
	if err != nil {
		return nil, err
	}
	c.GetLogger().Infof("successfully upload file: %s", argFile)
	err = artifactService.CommitArtifact(c, artifact)
	if err != nil {
		return nil, err
	}
	c.GetLogger().Infof("successfully commit artifact: %s", artifact.ID())
	deployment, err := artifactService.CreateStaticSiteDeployment(c, artifact.ID())
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create deployment")
	}
	c.GetLogger().Infof("successfully create deployment: %s", deployment.DeploymentID)

	if _, ok := c.Parameters["deploy_id"]; !ok {
		c.Parameters["deploy_id"] = map[string]any{}
	}
	c.Parameters["deploy_id"].(map[string]any)[deployment.URL] = artifact.ID()

	return &DeployOutput{
		ArtifactID: artifact.ID(),
		Files:      []string{argFile},
		URL:        deployment.URL,
	}, nil
}

func addCitationLink(c *iris.AgentRunContext, fileList *ReadDirectoryResult) {
	store := iris.RetrieveStoreByKey[entity.ReferenceStore](c, entity.ReferenceStoreKey)
	refs := make(map[string]string)
	for i, r := range store.SearchedRef.List {
		key := fmt.Sprintf("[%v]", i+1)
		refs[key] = r.URI
	}
	c.GetLogger().Debugf("deploy tool, get refs: %v", refs)
	if len(refs) == 0 {
		return
	}

	for _, file := range fileList.AllFiles {
		if strings.HasSuffix(file.Name, ".html") {
			processOneHTML(c, file, refs)
		}
	}
}

func processOneHTML(c *iris.AgentRunContext, fileEntry FileEntry, refs map[string]string) {
	filePath := filepath.Join(GetEditor(c).WorkingDirectory, fileEntry.Path)

	c.GetLogger().Debugf("deploy tool, process one html file: %s", filePath)

	// read file
	file, err := GetEditor(c).ReadFile(ReadFileArgs{Path: filePath})
	if err != nil {
		c.GetLogger().Error("deploy tool, failed to read file `%v`, err: %v", filePath, err)
		return
	}

	// 先把<script>内容抠出来存一份，里面的[1][2][3]不处理
	re := regexp.MustCompile(`(?s)<script[^>]*>(.*?)</script>`)
	matches := re.FindAllString(file.Content, -1)
	scriptContentTmpReplace := make(map[string]string)
	for i, match := range matches {
		tmpReplaceTo := fmt.Sprintf("@@@AIME@@@SCRIPT@@@%v@@@", i)
		scriptContentTmpReplace[tmpReplaceTo] = match
		file.Content = strings.Replace(file.Content, match, tmpReplaceTo, -1)
	}

	file.Content = limitCitationNum(file.Content)

	// 扣[1][2][3]并对matches去重
	pattern := `\[(\d+)\]`
	re = regexp.MustCompile(pattern)
	matches = re.FindAllString(file.Content, -1)
	uniqueMatches := map[string]bool{}
	for _, match := range matches {
		uniqueMatches[match] = true
	}

	// 逐个加<a>
	for match := range uniqueMatches {
		if refLink, ok := refs[match]; ok {
			toReplace := fmt.Sprintf("<a href=\"%v\" target=\"_blank\" class=\"[color:#5fc3a7] hover:[color:#3d9882]\">%v</a>", refLink, match)
			file.Content = strings.Replace(file.Content, match, toReplace, -1)
		}
	}

	// 把<script>放回去
	for tmpReplaceTo, match := range scriptContentTmpReplace {
		file.Content = strings.Replace(file.Content, tmpReplaceTo, match, -1)
	}

	// 把html写回去
	if _, err := GetEditor(c).CreateFile(c, CreateFileArgs{
		Content: file.Content,
		Path:    fileEntry.Path,
	}, CreateFileOption{RemoveOuterBackquotes: false}); err != nil {
		c.GetLogger().Error("deploy tool, failed to update file `%v`, err: %v", filePath, err)
	}
}

func limitCitationNum(content string) string {
	// 正则表达式匹配连续的引用格式 [1]
	re := regexp.MustCompile(`(\[\d+\])+`)
	updatedContent := re.ReplaceAllStringFunc(content, func(match string) string {
		// 提取引用编号
		numRe := regexp.MustCompile(`\[(\d+)\]`)
		matches := numRe.FindAllStringSubmatch(match, -1) // 这里改用 match 而不是 content
		// 提取所有数字
		numbers := make([]int, 0, len(matches))
		for _, numMatch := range matches {
			if len(numMatch) > 1 {
				num, err := strconv.Atoi(numMatch[1])
				if err != nil {
					return match // 如果转换失败，返回原始匹配
				}
				numbers = append(numbers, num)
			}
		}
		// 去重、排序并限制数量
		numbers = lo.Uniq(numbers)
		sort.Ints(numbers)
		numbers = lo.Slice(numbers, 0, 3) // 只取前三个数字
		// 构建新的引用字符串
		var builder strings.Builder
		for _, num := range numbers {
			builder.WriteString("[")
			builder.WriteString(strconv.Itoa(num))
			builder.WriteString("]")
		}
		return builder.String()
	})
	return updatedContent
}

// extractTextContent extracts text content from MCP Content array
func extractTextContent(contents []mcp.Content) []string {
	result := make([]string, 0, len(contents))
	for _, content := range contents {
		if content == nil {
			continue
		}

		if textContent, ok := content.(mcp.TextContent); ok {
			result = append(result, textContent.Text)
		}
	}
	return result
}

func getBrowserConsole(run *iris.AgentRunContext, viewURL string) ([]string, error) {
	browserClient, err := browser.NewBrowserMCPClient(run)
	if err != nil {
		run.GetLogger().Errorf("failed to new a browser mcp service: %v", err)
		return nil, err
	}

	ctx, cancel := context.WithTimeout(run, 10*time.Minute)
	defer cancel()

	// 1. First goto the URL
	gotoRequest := mcp.CallToolRequest{}
	gotoRequest.Params.Name = "browser_goto"
	gotoRequest.Params.Arguments = map[string]any{
		"url": viewURL,
	}
	gotoResult, err := browserClient.Client.CallTool(ctx, gotoRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to goto url: %v", err)
		return nil, err
	}

	// Check if goto operation had errors
	if gotoResult.IsError {
		err := errors.New("browser goto operation failed")
		run.GetLogger().Errorf("Browser goto returned error: %v", err)
		return nil, err
	}

	// 2. Get console
	consoleRequest := mcp.CallToolRequest{}
	consoleRequest.Params.Name = "browser_get_console"
	consoleRequest.Params.Arguments = map[string]any{}
	result, err := browserClient.Client.CallTool(ctx, consoleRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to get console: %v", err)
		return nil, err
	}

	// Check if console operation had errors
	if result.IsError {
		err := errors.New("browser get console operation failed")
		run.GetLogger().Errorf("Browser get console returned error: %v", err)
		return nil, err
	}

	// Extract text content from result
	textContents := extractTextContent(result.Content)
	run.GetLogger().Infof("Successfully got console text result, result: %v", textContents)

	return textContents, nil
}

func NewDeployBackendAction() iris.Action {
	return actions.ToTool(ToolDeployBackend, ToolDeployBackendDescription, func(c *iris.AgentRunContext, args DeployBackendArgs) (*DeployOutput, error) {
		output, err := DeployBackendDirectory(c, args)
		return output, iris.NewRecoverable(err)
	})
}

func DeployBackendDirectory(c *iris.AgentRunContext, args DeployBackendArgs) (*DeployOutput, error) {
	artifactService := c.GetArtifactService()
	if artifactService.IsNil() {
		return nil, errors.New("artifact service is nil")
	}

	editor := GetEditor(c)
	if editor == nil {
		return nil, errors.New("editor is nil")
	}

	stat, err := os.Stat(args.Directory)
	if err != nil {
		return nil, fmt.Errorf("%s does not exist: %v", args.Directory, err)
	}
	// model may want to deploy a single file, but forgot that references to other files are not included
	// we force all deploy tool calls to be a directory
	if !stat.IsDir() {
		return nil, fmt.Errorf("%s is not a directory: %v", args.Directory, err)
	}

	fileList, err := editor.ReadDirectory(ReadDirectory{
		Path:      args.Directory,
		Recursive: true,
		MaxDepth:  5, // check for backend app structure
	})
	if err != nil {
		return nil, fmt.Errorf("failed to read directory %s: %v", args.Directory, err)
	}

	// 1. Detect technology stack
	techStack, err := detectTechStack(c, args.Directory, fileList)
	if err != nil {
		return nil, err
	}

	// 2. Validate requirements for the detected tech stack
	if err := techStack.ValidateRequirements(c, args.Directory, fileList); err != nil {
		return nil, fmt.Errorf("%v in %s", err, args.Directory)
	}

	artifact, err := artifactService.NewFileArtifact(c, nextentity.FileArtifactTypeMetadata{})
	if err != nil {
		return nil, err
	}
	absDirectory := args.Directory
	if !filepath.IsAbs(absDirectory) {
		absDirectory = filepath.Join(editor.WorkingDirectory, absDirectory)
	}

	// 3. Filter and prepare files for upload using tech stack specific rules
	files := lo.FilterMap(fileList.AllFiles, func(file FileEntry, _ int) (iris.ArtifactFileReader, bool) {
		// Filter out files based on tech stack specific rules
		if techStack.ShouldExcludeFile(file.Path) {
			return iris.ArtifactFileReader{}, false
		}

		filePath := filepath.Join(editor.WorkingDirectory, file.Path)
		fileInfo, err := os.Stat(filePath)
		if err != nil || fileInfo.IsDir() || fileInfo.Size() == 0 {
			return iris.ArtifactFileReader{}, false
		}
		f, err := os.Open(filePath)
		if err != nil {
			return iris.ArtifactFileReader{}, false
		}
		// upload path should be relative to the directory to deploy
		relPath, err := filepath.Rel(absDirectory, filePath)
		if err != nil {
			relPath = file.Path // use path relative to working directory instead
		}

		return iris.ArtifactFileReader{
			Path:   relPath,
			Size:   fileInfo.Size(),
			Reader: f,
		}, true
	})
	defer func() {
		for _, file := range files {
			if closer, ok := file.Reader.(io.Closer); ok {
				closer.Close()
			}
		}
	}()
	// the number of files should be less than MaxDeployFiles
	// projects larger than this is out of model's capability, and may bring large pressure to our server
	if len(files) > MaxDeployFiles {
		return nil, fmt.Errorf("too many files(%d) to deploy (maybe trying to upload the whole codebase?), please reduce the number of files", len(files))
	}

	// 4. Upload files to artifact and commit
	err = artifactService.UploadFilesStream(c, artifact, files)
	if err != nil {
		return nil, err
	}
	err = artifactService.CommitArtifact(c, artifact)
	if err != nil {
		return nil, err
	}

	// 5. Create backend deployment from artifact
	deployment, err := artifactService.CreateBackendDeployment(c, artifact.ID())
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create backend deployment. Please make sure your backend code is compatible with Python 3.8")
	}

	// remove the /docs from the url
	deployment.URL = strings.TrimSuffix(deployment.URL, "/docs")

	if _, ok := c.Parameters["deploy_id"]; !ok {
		c.Parameters["deploy_id"] = map[string]any{}
	}
	c.Parameters["deploy_id"].(map[string]any)[deployment.URL] = artifact.ID()

	// 6. Fetch API information from the deployed backend
	var apiInfo *BackendAPIInfo
	if deployment.URL != "" {
		c.GetLogger().Infof("Attempting to fetch API info from deployed backend: %s", deployment.URL)
		if info, err := FetchBackendAPIInfo(c, deployment.URL); err != nil {
			c.GetLogger().Warnf("Failed to fetch API info from backend: %v", err)
		} else {
			apiInfo = info
			c.GetLogger().Infof("Successfully fetched API info: %d endpoints found", len(info.Endpoints))
		}
	}

	return &DeployOutput{
		ArtifactID: artifact.ID(),
		Files:      lo.Map(files, func(file iris.ArtifactFileReader, _ int) string { return file.Path }),
		URL:        deployment.URL,
		APIInfo:    apiInfo,
	}, nil
}

// OpenAPI/Swagger related structures
type OpenAPISpec struct {
	OpenAPI    string                 `json:"openapi"`
	Info       OpenAPIInfo            `json:"info"`
	Paths      map[string]OpenAPIPath `json:"paths"`
	Components OpenAPIComponents      `json:"components,omitempty"`
}

type OpenAPIInfo struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Version     string `json:"version"`
}

type OpenAPIPath struct {
	Get    *OpenAPIOperation `json:"get,omitempty"`
	Post   *OpenAPIOperation `json:"post,omitempty"`
	Put    *OpenAPIOperation `json:"put,omitempty"`
	Delete *OpenAPIOperation `json:"delete,omitempty"`
	Patch  *OpenAPIOperation `json:"patch,omitempty"`
}

type OpenAPIOperation struct {
	Summary     string                     `json:"summary"`
	OperationID string                     `json:"operationId"`
	Parameters  []OpenAPIParameter         `json:"parameters,omitempty"`
	RequestBody *OpenAPIRequestBody        `json:"requestBody,omitempty"`
	Responses   map[string]OpenAPIResponse `json:"responses"`
	Tags        []string                   `json:"tags,omitempty"`
}

type OpenAPIParameter struct {
	Name        string                 `json:"name"`
	In          string                 `json:"in"`
	Required    bool                   `json:"required,omitempty"`
	Schema      map[string]interface{} `json:"schema,omitempty"`
	Description string                 `json:"description,omitempty"`
}

type OpenAPIRequestBody struct {
	Content  map[string]OpenAPIMediaType `json:"content"`
	Required bool                        `json:"required,omitempty"`
}

type OpenAPIMediaType struct {
	Schema map[string]interface{} `json:"schema"`
}

type OpenAPIResponse struct {
	Description string                      `json:"description"`
	Content     map[string]OpenAPIMediaType `json:"content,omitempty"`
}

type OpenAPIComponents struct {
	Schemas map[string]map[string]interface{} `json:"schemas,omitempty"`
}

// APIEndpoint represents a structured API endpoint
type APIEndpoint struct {
	Path        string            `json:"path"`
	Method      string            `json:"method"`
	Summary     string            `json:"summary"`
	OperationID string            `json:"operationId"`
	Parameters  []APIParameter    `json:"parameters,omitempty"`
	RequestBody *APIRequestBody   `json:"requestBody,omitempty"`
	Responses   map[string]string `json:"responses"`
	Tags        []string          `json:"tags,omitempty"`
}

type APIParameter struct {
	Name        string `json:"name"`
	In          string `json:"in"`
	Required    bool   `json:"required"`
	Type        string `json:"type"`
	Description string `json:"description,omitempty"`
}

type APIRequestBody struct {
	Required    bool              `json:"required"`
	ContentType string            `json:"contentType"`
	Schema      map[string]string `json:"schema,omitempty"`
}

// BackendAPIInfo represents the complete API information
type BackendAPIInfo struct {
	Title       string        `json:"title"`
	Description string        `json:"description"`
	Version     string        `json:"version"`
	BaseURL     string        `json:"baseUrl"`
	Endpoints   []APIEndpoint `json:"endpoints"`
	Usage       string        `json:"usage"`
}

// String returns a formatted string representation of the API info
func (api *BackendAPIInfo) String() string {
	var sb strings.Builder

	// Header
	sb.WriteString(fmt.Sprintf("%s (v%s)\n", api.Title, api.Version))
	if api.Description != "" {
		sb.WriteString(fmt.Sprintf("%s\n", api.Description))
	}
	sb.WriteString(fmt.Sprintf("Base URL: %s\n", api.BaseURL))
	sb.WriteString(fmt.Sprintf("Total Endpoints: %d\n\n", len(api.Endpoints)))

	// Group endpoints by path
	pathGroups := make(map[string][]APIEndpoint)
	for _, endpoint := range api.Endpoints {
		pathGroups[endpoint.Path] = append(pathGroups[endpoint.Path], endpoint)
	}

	// Sort paths for consistent output
	var paths []string
	for path := range pathGroups {
		paths = append(paths, path)
	}
	sort.Strings(paths)

	// Display endpoints
	for _, path := range paths {
		endpoints := pathGroups[path]
		sb.WriteString(fmt.Sprintf("%s\n", path))

		for _, endpoint := range endpoints {
			sb.WriteString(endpoint.String(api.BaseURL))
			sb.WriteString("\n")
		}
		sb.WriteString("\n")
	}

	return sb.String()
}

// String returns a formatted string representation of an API endpoint
func (endpoint *APIEndpoint) String(baseURL string) string {
	var sb strings.Builder

	// Method and summary
	sb.WriteString(fmt.Sprintf("  %s %s\n", endpoint.Method, endpoint.Summary))

	// Parameters
	if len(endpoint.Parameters) > 0 {
		sb.WriteString("    Parameters:\n")
		for _, param := range endpoint.Parameters {
			required := ""
			if param.Required {
				required = " (required)"
			}
			sb.WriteString(fmt.Sprintf("      %s (%s in %s)%s", param.Name, param.Type, param.In, required))
			if param.Description != "" {
				sb.WriteString(fmt.Sprintf(" - %s", param.Description))
			}
			sb.WriteString("\n")
		}
	}

	// Request body
	if endpoint.RequestBody != nil {
		required := ""
		if endpoint.RequestBody.Required {
			required = " (required)"
		}
		sb.WriteString(fmt.Sprintf("    Request Body: %s%s\n", endpoint.RequestBody.ContentType, required))
	}

	// Responses
	if len(endpoint.Responses) > 0 {
		sb.WriteString("    Responses:\n")
		// Sort status codes for consistent output
		var codes []string
		for code := range endpoint.Responses {
			codes = append(codes, code)
		}
		sort.Strings(codes)

		for _, code := range codes {
			description := endpoint.Responses[code]
			sb.WriteString(fmt.Sprintf("      %s: %s\n", code, description))
		}
	}

	// Usage example
	sb.WriteString(fmt.Sprintf("    Usage: curl -X %s \"%s%s", endpoint.Method, baseURL, endpoint.Path))

	// Add query parameters example
	if hasQueryParams(endpoint.Parameters) {
		sb.WriteString("?")
		var queryParams []string
		for _, param := range endpoint.Parameters {
			if param.In == "query" {
				queryParams = append(queryParams, fmt.Sprintf("%s=${%s}", param.Name, strings.ToUpper(param.Name)))
			}
		}
		sb.WriteString(strings.Join(queryParams, "&"))
	}

	sb.WriteString("\"")

	// Add request body example
	if endpoint.RequestBody != nil && endpoint.RequestBody.ContentType == "application/json" {
		sb.WriteString(" -H \"Content-Type: application/json\" -d '${JSON_DATA}'")
	}

	return sb.String()
}

// hasQueryParams checks if there are any query parameters
func hasQueryParams(params []APIParameter) bool {
	for _, param := range params {
		if param.In == "query" {
			return true
		}
	}
	return false
}

// FetchBackendAPIInfo fetches and parses the OpenAPI specification from a deployed backend
func FetchBackendAPIInfo(c *iris.AgentRunContext, baseURL string) (*BackendAPIInfo, error) {
	openAPIURL := baseURL + "/openapi.json"

	c.GetLogger().Infof("Fetching OpenAPI spec from: %s", openAPIURL)

	client := &http.Client{
		Timeout: 100 * time.Second,
	}

	resp, err := client.Get(openAPIURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch OpenAPI spec: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch OpenAPI spec: status code %d", resp.StatusCode)
	}

	var spec OpenAPISpec
	if err := json.NewDecoder(resp.Body).Decode(&spec); err != nil {
		return nil, fmt.Errorf("failed to parse OpenAPI spec: %v", err)
	}

	apiInfo := &BackendAPIInfo{
		Title:       spec.Info.Title,
		Description: spec.Info.Description,
		Version:     spec.Info.Version,
		BaseURL:     baseURL,
		Endpoints:   []APIEndpoint{},
	}

	for path, pathItem := range spec.Paths {
		endpoints := parsePathItem(path, pathItem)
		apiInfo.Endpoints = append(apiInfo.Endpoints, endpoints...)
	}

	apiInfo.Usage = apiInfo.String()

	return apiInfo, nil
}

// parsePathItem converts OpenAPI path item to structured endpoints
func parsePathItem(path string, pathItem OpenAPIPath) []APIEndpoint {
	var endpoints []APIEndpoint

	methods := map[string]*OpenAPIOperation{
		"GET":    pathItem.Get,
		"POST":   pathItem.Post,
		"PUT":    pathItem.Put,
		"DELETE": pathItem.Delete,
		"PATCH":  pathItem.Patch,
	}

	for method, operation := range methods {
		if operation == nil {
			continue
		}

		endpoint := APIEndpoint{
			Path:        path,
			Method:      method,
			Summary:     operation.Summary,
			OperationID: operation.OperationID,
			Parameters:  []APIParameter{},
			Responses:   make(map[string]string),
			Tags:        operation.Tags,
		}

		// Parse parameters
		for _, param := range operation.Parameters {
			apiParam := APIParameter{
				Name:        param.Name,
				In:          param.In,
				Required:    param.Required,
				Description: param.Description,
			}

			// Extract type from schema
			if param.Schema != nil {
				if paramType, ok := param.Schema["type"]; ok {
					if typeStr, ok := paramType.(string); ok {
						apiParam.Type = typeStr
					}
				}
			}

			endpoint.Parameters = append(endpoint.Parameters, apiParam)
		}

		// Parse request body
		if operation.RequestBody != nil {
			requestBody := &APIRequestBody{
				Required: operation.RequestBody.Required,
				Schema:   make(map[string]string),
			}

			// Find the first content type
			for contentType := range operation.RequestBody.Content {
				requestBody.ContentType = contentType
				break
			}

			endpoint.RequestBody = requestBody
		}

		// Parse responses
		for statusCode, response := range operation.Responses {
			endpoint.Responses[statusCode] = response.Description
		}

		endpoints = append(endpoints, endpoint)
	}

	return endpoints
}

// TechStack represents a specific technology stack for deployment
type TechStack interface {
	// Detect checks if the directory contains this technology stack
	Detect(c *iris.AgentRunContext, directory string, fileList *ReadDirectoryResult) bool

	// ValidateRequirements validates that all required files are present
	ValidateRequirements(c *iris.AgentRunContext, directory string, fileList *ReadDirectoryResult) error

	// ShouldExcludeFile determines if a file should be excluded from deployment
	ShouldExcludeFile(filePath string) bool

	// GetName returns the name of the technology stack
	GetName() string
}

// FastAPIAppDeploy implements TechStack for FastAPI applications
type FastAPIAppDeploy struct{}

func (f *FastAPIAppDeploy) GetName() string {
	return "FastAPI"
}

func (f *FastAPIAppDeploy) Detect(c *iris.AgentRunContext, directory string, fileList *ReadDirectoryResult) bool {
	// Check for FastAPI-specific files
	hasMainPy := false
	hasRequirementsTxt := false
	hasRunSh := false

	for _, file := range fileList.Files {
		if !file.IsDir {
			switch file.Name {
			case "main.py":
				hasMainPy = true
			case "requirements.txt":
				hasRequirementsTxt = true
			case "run.sh":
				hasRunSh = true
			}
		}
	}

	return hasMainPy && hasRequirementsTxt && hasRunSh
}

func (f *FastAPIAppDeploy) ValidateRequirements(c *iris.AgentRunContext, directory string, fileList *ReadDirectoryResult) error {
	requiredFiles := map[string]bool{
		"main.py":          false,
		"requirements.txt": false,
		"run.sh":           false,
	}

	// Check which required files are present
	for _, file := range fileList.Files {
		if !file.IsDir {
			if _, exists := requiredFiles[file.Name]; exists {
				requiredFiles[file.Name] = true
			}
		}
	}

	// Collect missing files
	var missingFiles []string
	for fileName, found := range requiredFiles {
		if !found {
			missingFiles = append(missingFiles, fileName)
		}
	}

	if len(missingFiles) > 0 {
		return fmt.Errorf("missing required files: %v. FastAPI deployment requires main.py, requirements.txt "+
			"and run.sh. Make sure you use `aime_create_fastapi_app` to create a FastAPI application and DO NOT EDIT THE `run.sh` FILE in fastapi app which is used for deployment", missingFiles)
	}

	// Check Python 3.8 compatibility for requirements.txt
	return f.checkPython38Compatibility(c, directory)
}

func (f *FastAPIAppDeploy) ShouldExcludeFile(filePath string) bool {
	// Normalize path separators
	normalizedPath := filepath.ToSlash(filePath)

	// Check if path contains any excluded directories
	excludedDirs := []string{
		".git/",
		"__pycache__/",
		"venv/",
		"env/",
		".venv/",
		"node_modules/",
		"dist/",
		"build/",
		".pytest_cache/",
		"htmlcov/",
		".mypy_cache/",
		".tox/",
	}

	for _, excludedDir := range excludedDirs {
		if strings.Contains(normalizedPath, excludedDir) {
			return true
		}
	}

	// Check for specific file patterns
	excludedPatterns := []string{
		".pyc",
		".pyo",
		".pyd",
		".coverage",
		".DS_Store",
		".gitignore",
		".dockerignore",
	}

	for _, pattern := range excludedPatterns {
		if strings.HasSuffix(normalizedPath, pattern) {
			return true
		}
	}

	return false
}

func (f *FastAPIAppDeploy) checkPython38Compatibility(c *iris.AgentRunContext, directory string) error {
	editor := GetEditor(c)
	if editor == nil {
		return errors.New("editor is nil")
	}

	// Check if requirements.txt exists
	requirementsPath := filepath.Join(directory, "requirements.txt")
	absRequirementsPath := requirementsPath
	if !filepath.IsAbs(absRequirementsPath) {
		absRequirementsPath = filepath.Join(editor.WorkingDirectory, absRequirementsPath)
	}

	if _, err := os.Stat(absRequirementsPath); os.IsNotExist(err) {
		// No requirements.txt file, skip compatibility check
		c.GetLogger().Infof("No requirements.txt found, skipping Python 3.8 compatibility check")
		return nil
	}

	c.GetLogger().Infof("Checking Python 3.8 compatibility for requirements.txt")

	// Use pip install --dry-run --python-version 3.8 --no-deps to check compatibility
	cmd := exec.Command("pip", "install", "--dry-run", "--python-version", "3.8", "--no-deps", "-r", absRequirementsPath)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		c.GetLogger().Errorf("Python 3.8 compatibility check failed: %v", err)
		c.GetLogger().Errorf("stderr: %s", stderr.String())
		return fmt.Errorf("requirements.txt contains dependencies that are not compatible with Python 3.8: %v\nError output: %s", err, stderr.String())
	}

	c.GetLogger().Infof("Python 3.8 compatibility check passed")
	return nil
}

// detectTechStack detects the technology stack of the directory
func detectTechStack(c *iris.AgentRunContext, directory string, fileList *ReadDirectoryResult) (TechStack, error) {
	techStacks := []TechStack{
		&FastAPIAppDeploy{},
		// Add more tech stacks here in the future
	}

	for _, techStack := range techStacks {
		if techStack.Detect(c, directory, fileList) {
			c.GetLogger().Infof("Detected technology stack: %s", techStack.GetName())
			return techStack, nil
		}
	}

	return nil, fmt.Errorf("no supported technology stack detected in %s. Currently supported: FastAPI", directory)
}
