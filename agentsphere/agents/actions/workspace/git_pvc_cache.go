package workspace

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"github.com/go-git/go-git/v5"
)

func RepoUsePvcCache(ctx context.Context, opt CloneOption) (bool, error) {
	// 解析仓库信息
	repoPath, err := ParseRepoPath(opt.URL)
	if err != nil {
		return false, err
	}
	// 仅缓存内部仓库
	if repoPath.Platform != entity.GitPlatformCodebase {
		return false, nil
	}

	// 获取仓库base name，拼接缓存路径
	repoNameEscaped := strings.ReplaceAll(strings.ReplaceAll(repoPath.RepoName, "/", "-"), "_", "-")
	cacheDir := fmt.Sprintf("/workspace/.bigrepo_warmup/%s", repoNameEscaped)
	if _, err = os.Stat(cacheDir); os.IsNotExist(err) {
		logs.V1.CtxWarn(ctx, "bigrepo cache dir is not exists")
		return false, nil
	}

	// 不支持下载到当前目录
	dir := strings.TrimSuffix(opt.Directory, "/")
	workspace := strings.TrimSuffix(os.Getenv("IRIS_WORKSPACE_PATH"), "/")
	if !path.IsAbs(dir) {
		dir = path.Join(workspace, dir)
	}
	if dir == workspace {
		return false, fmt.Errorf("only support git clone to subdir")
	}

	err = setupGitAlternates(ctx, opt, cacheDir, dir)
	if err != nil {
		return false, err
	}

	cmd := exec.CommandContext(ctx, "bash", "-c", fmt.Sprintf("rm -rf .git/config.lock .git/index.lock .git/shallow.lock .git/info/sparse-checkout.lock"))
	cmd.Dir = dir
	_, err = opt.Terminal.ExecuteCmd(cmd, ExecuteCmdOption{MaxDisplayLines: 1000, Timeout: 0})
	if err != nil {
		return false, err
	}
	return true, nil
}

func GitFetchWithPvcCache(ctx context.Context, opt CloneOption, fetchOptions *git.FetchOptions) error {
	refSpec := fetchOptions.RefSpecs[0].String()
	args := []string{"fetch", "-u", fetchOptions.RemoteName, refSpec}
	// 使用git alternates方式浅克隆会卡几分钟，强制深克隆
	//if fetchOptions.Depth == 1 {
	//	args = append(args, "--depth=1")
	//}

	cmd := exec.CommandContext(ctx, "git", args...)
	cmd.Dir = opt.Directory
	cmd.Env = os.Environ()
	cmd.Env = append(cmd.Env, fmt.Sprintf("GIT_SSH_COMMAND=sshpass -f '%s' ssh -o StrictHostKeyChecking=no", entity.PathUserCodebaseJWT))
	out, err := opt.Terminal.ExecuteCmd(cmd, ExecuteCmdOption{MaxDisplayLines: 1000, Timeout: 0})
	if err != nil {
		return fmt.Errorf("failed to git fetch %s: output=%s err=%w", refSpec, out, err)
	}
	return nil
}

func GitCheckoutWithPvcCache(ctx context.Context, opt CloneOption, checkoutOptions *git.CheckoutOptions) error {
	reference := ""
	if !checkoutOptions.Hash.IsZero() {
		reference = checkoutOptions.Hash.String()
	} else {
		reference = strings.TrimPrefix(strings.TrimPrefix(checkoutOptions.Branch.String(), "refs/heads/"), "refs/remotes/origin/")
	}

	cmd := exec.CommandContext(ctx, "bash", "-c", `
		git restore --staged .
		git restore .
		git clean -df
`)
	cmd.Dir = opt.Directory
	// 忽略失败
	opt.Terminal.ExecuteCmd(cmd, ExecuteCmdOption{MaxDisplayLines: 1000, Timeout: 0})

	cmd = exec.CommandContext(ctx, "git", "checkout", reference)
	cmd.Dir = opt.Directory
	cmd.Env = os.Environ()
	cmd.Env = append(cmd.Env, fmt.Sprintf("GIT_SSH_COMMAND=sshpass -f '%s' ssh -o StrictHostKeyChecking=no", entity.PathUserCodebaseJWT))
	out, err := opt.Terminal.ExecuteCmd(cmd, ExecuteCmdOption{MaxDisplayLines: 1000, Timeout: 0})
	if err != nil {
		return fmt.Errorf("failed to git checkout %s: output=%s err=%w", reference, out, err)
	}
	return nil
}

func setupGitAlternates(ctx context.Context, opt CloneOption, cacheDir string, dir string) error {
	alternatesContent := ""
	alternatesContent = fmt.Sprintln(alternatesContent + filepath.Join(cacheDir, ".git", "objects"))
	filename := filepath.Join(dir, ".git", "objects", "info", "alternates")
	err := os.WriteFile(filename, []byte(alternatesContent), 0600)
	if err != nil {
		return fmt.Errorf("fail to write git alternates file: %+v", err)
	}
	return nil
}

type GetBigrepoWarmupResponse struct {
	Code    int64  `json:"code"`
	Message string `json:"message"`
}

func RepoInWarmupList(ctx context.Context, sessionID string, repoName string) bool {
	cubeApiServerURL := "https://stratocube-test.byted.org"
	if env.IsProduct() {
		cubeApiServerURL = "https://stratocube-online.byted.org"
	}
	if iris.CurrentRegion() == iris.RegionI18n {
		cubeApiServerURL = "https://stratocube-online-tt.byted.org"
	}

	paths := fmt.Sprintf("/api/v1/bigrepo_warmup_config/get?tenant_id=1003&repo_name=%s", repoName)
	hertzClient, err := hertz.NewClient(cubeApiServerURL, hertz.NewHTTPClientOption{
		Timeout: 10 * time.Second,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "Fail to new hertz client for session %s: %+v", sessionID, err)
		return false
	}

	resp, err := hertzClient.DoJSONReq(ctx, http.MethodGet, paths, hertz.ReqOption{
		Body:    nil,
		Headers: nil,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "Fail to get bigrepo warmup config for session %s: %+v", sessionID, err)
		return false
	}

	if resp.StatusCode() != 200 {
		logs.V1.CtxError(ctx, "Fail to get bigrepo warmup config for session %s: code = %d", sessionID, resp.StatusCode)
		return false
	}

	var warmupResp GetBigrepoWarmupResponse
	err = json.Unmarshal(resp.Body(), &warmupResp)
	if err != nil {
		logs.V1.CtxError(ctx, "Fail to unmarshal bigrepo warmup config for session %s: %+v", sessionID, err)
		return false
	}

	if warmupResp.Code != 0 {
		logs.V1.CtxError(ctx, "Fail to get bigrepo warmup config for session %s: %s", sessionID, warmupResp.Message)
		return false
	}
	return true
}
