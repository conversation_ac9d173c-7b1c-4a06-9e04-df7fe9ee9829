package workspace

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	safe_exec "code.byted.org/cld/safe-exec"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
)

type BuiltinActionID string

const (
	ActionReadFile          BuiltinActionID = "read_file"
	ActionListDirectory     BuiltinActionID = "read_directory"
	ActionCreateFile        BuiltinActionID = "create_file"
	ActionAppendFile        BuiltinActionID = "append_file"
	ActionPatchFile         BuiltinActionID = "patch_file"
	ActionEditFile          BuiltinActionID = "edit_file"
	ActionSearchReplaceFile BuiltinActionID = "search_replace_file"
	ActionSearchFileName    BuiltinActionID = "search_file_name"
	ActionFindIdentifier    BuiltinActionID = "find_identifier"
	ActionGrepSearch        BuiltinActionID = "grep_search"
	ActionExecuteCommand    BuiltinActionID = "bash"
	ActionGlobSearch        BuiltinActionID = "glob_search"
	ActionReadMarkDownFiles BuiltinActionID = "read_markdown_files"

	ReadFileDescription       = "Read a text file content between [start_line] and [end_line] in file. Each call can browse up to {{.MaxLines}} lines of content. [file_path] is the relative path of the file. Do **NOT** use this tool to read binary files (png, pdf) or the complex data files (excel, jsonl). Instead, you can read the first few lines and last few lines to infer the data schema and then load the data files by scripts."
	AppendFileDescription     = "Append content to the end of the file. Note this does not add a new leading newline."
	FindIdentifierDescription = "Finds where an [identifier] occurs in the file. [file_path] is the relative path of the file. Recommended!"
	ListDirectoryDescription  = "Lists files and directories in a given path. The path parameter must be a relative path not an absolute path."
	GrepSearchDescription     = "Search for a [pattern] in a file or directory. [path_or_directory] is the relative path of the directory or file. Recommended!"
	PatchFileDescription      = `Use this tool (patch_file) to modify code to solve the issue.
### Hints ###
The SEARCH/REPLACE block should according to the original code, indent correctly, and do not include line numbers. It must use the following format:
1. The start of search block: <<<<<<< SEARCH
2. A contiguous chunk of lines to search for in the existing source code
3. The dividing line: =======
4. The lines to replace into the source code
5. The end of the replace block: >>>>>>> REPLACE
Once you want to modify the code you MUST: 
	* Include *ALL* the code being searched and replaced!
	* Every *SEARCH* section must *EXACTLY MATCH* the existing source code, character for character, including all comments, docstrings, etc.
	* '<<<<<<< SEARCH', '=======' and  '>>>>>>> REPLACE' symbols must be on a line by themselves and cannot be indented.
	* All code modifications must be expressed in the REPLACE format above (including delete an insert). We will find the source code with the highest matching degree in the original file and replace it. Please provide sufficient and unique old line(s) from snippet to facilitate matching.
	* If the code patch you provide is successfully applied, the differences before and after the code modification will be returned.
	* The paths of modified files must be the relative paths of the repo.
	* Make sure the patch you provide is indented correctly especially in python programs: The indentation of old lines is exactly the same as the original code, and the indentation of new lines is correct.
	* All patches must be based on the original code viewed by the tools, and fabricated code patch(es) is prohibited.
	* Previously successfully applied patches will modify the code, and new patches must be applied based on the current code. Please review the relevant code again then provide new patches.
	* The old line(s) can not be empty! The old lines must be the code in the snippet you are responsible for, and must be exactly the same!`
	EditFileDescription          = "Edit the file content in the given range (including start_line and end_line). if `insert` is true, end_line is ignored and the content will be inserted before the start line."
	SearchReplaceFileDescription = "Search and replace content of a given file. This is performed line level search and replace, so the search content should be the exact line(s) in the original file."
	SearchFileNameDescription    = "Search in the directory recursively for given file name."
	CreateFileDescription        = "Use this tool (create_file) to create a new file. [file_path] is the relative path of the new file, and [content] is the full content of the created file."
	ExecuteCommandDescription    = `Execute a one-time bash command in a non-interactive shell and wait for its output in the workspace with optional timeout, ensuring proper handling and security measures.

Before executing the command, please follow these steps:

1. Directory Verification:
   - If the command will create new directories or files, first verify the parent directory exists and is the correct location
   - For example, before running "mkdir foo/bar", first check that "foo" exists and is the intended parent directory

2. Command Execution:
   - Always quote file paths that contain spaces with double quotes (e.g., cd "path with spaces/file.txt")
   - Examples of proper quoting:
     - cd "/Users/<USER>/My Documents" (correct)
     - cd /Users/<USER>/My Documents (incorrect - will fail)
     - python "/path/with spaces/script.py" (correct)
     - python /path/with spaces/script.py (incorrect - will fail)
   - After ensuring proper quoting, execute the command.
   - Capture the output of the command.`
	GlobSearchDescription = `Fast file pattern matching tool that works with any codebase size, it can efficiently analyze and explore the directory structure of code repositories. Supports glob patterns like "*" for all files, "**/*.js" for all javascript files, or "src/**/*.ts" for all typescript files in the src directory.
Returns matching file paths sorted by modification time in descending order, with the most recently modified files appearing first. Use this tool when you need to find files by name patterns.`
	ReadMarkDownFilesDescription = "**FOR READING MARKDOWN OR MD FILES, ALWAYS USE THIS TOOL INSTEAD OF read_file.** This tool is specifically designed and optimized for markdown content. Read all provided markdown file (which ends with .md) content. file_paths is the list of relative path of file."

	PatchFileExample = "\nThe whole tool call format is:\n## Action (patch_file)\n\n```param=\"file_path\"\nsrc/pages/index.tsx\n```\n\n```param=\"diff\"\n<<<<<<< SEARCH\nimport { useState } from 'react';\n=======\nimport { useState } from 'react';\nimport { useEffect } from 'react';\n>>>>>>> REPLACE\n```\n"
)

// ALL editor actions are recoverable

type ReadFileActionOption struct {
	MaxLines int
}

func NewReadFileAction(opts ...ReadFileActionOption) iris.Action {
	opt, _ := util.AssignStructNonZero(opts...)
	desc := strings.ReplaceAll(ReadFileDescription, "{{.MaxLines}}", strconv.Itoa(lo.Ternary(opt.MaxLines == 0, 200, opt.MaxLines)))
	return actions.ToTool(string(ActionReadFile), desc, func(c *iris.AgentRunContext, args ReadFileRangeArgs) (*FileRange, error) {
		f, err := GetEditor(c).ReadFileRange(args)
		return f, iris.NewRecoverable(err)
	})
}

func NewReadFileActionV2(opts ...ReadFileActionOption) iris.Action {
	opt, _ := util.AssignStructNonZero(opts...)
	desc := strings.ReplaceAll(ReadFileDescription, "{{.MaxLines}}", strconv.Itoa(lo.Ternary(opt.MaxLines == 0, 200, opt.MaxLines)))
	return actions.ToTool(string(ActionReadFile), desc, func(c *iris.AgentRunContext, args ReadFileRangeArgs) (*FileRange, error) {
		f, err := GetEditor(c).ReadFileRangeOptimzeForLLM(args)
		return f, iris.NewRecoverable(err)
	})
}

func NewReadMarkDownFilesAction() iris.Action {
	return actions.ToTool(string(ActionReadMarkDownFiles), ReadMarkDownFilesDescription, func(c *iris.AgentRunContext, args ReadMarkdownFilesArgs) (*MarkDownFiles, error) {
		f, err := GetEditor(c).ReadMarkdownFiles(args)
		return f, iris.NewRecoverable(err)
	})
}

func NewFindIdentifierAction() iris.Action {
	return actions.ToTool(string(ActionFindIdentifier), FindIdentifierDescription, func(c *iris.AgentRunContext, args FindIdentifierArgs) (*FindIdentifierOutput, error) {
		f, err := GetEditor(c).FindIdentifier(args)
		return f, iris.NewRecoverable(err)
	})
}

func NewCreateFileAction(opt CreateFileOption) iris.Action {
	return actions.ToTool(string(ActionCreateFile), CreateFileDescription, func(c *iris.AgentRunContext, args CreateFileArgs) (*CreateFileOutput, error) {
		output, err := GetEditor(c).CreateFile(c, args, opt)
		return output, iris.NewRecoverable(err)
	})
}

type AppendFileArgs struct {
	FilePath string `json:"file_path" mapstructure:"file_path" description:"file path"`
	NewLine  bool   `json:"new_line" mapstructure:"new_line" description:"whether to insert a new line before the content so the appended content starts on a new line"`
	Content  string `json:"content" mapstructure:"content" description:"content to append to the file, whitespaces will be trimmed"`
}

func NewAppendFileAction() iris.Action {
	return actions.ToTool(string(ActionAppendFile), AppendFileDescription, func(c *iris.AgentRunContext, args AppendFileArgs) (any, error) {
		return nil, iris.NewRecoverable(GetEditor(c).WriteFile(WriteFileArgs{
			FilePath: args.FilePath,
			Content:  lo.Ternary(args.NewLine, "\n"+args.Content, args.Content),
			Append:   true,
		}))
	})
}

type ListDirectoryOutputs struct {
	Files     []FileEntry `mapstructure:"files" json:"files"`
	Formatted string      `mapstructure:"formatted" json:"formatted"`
	Recursive bool        `mapstructure:"recursive" json:"recursive"`
}

func NewListDirectoryAction() iris.Action {
	return actions.ToTool(string(ActionListDirectory), ListDirectoryDescription, func(c *iris.AgentRunContext, args ListDirectoryArgs) (*ListDirectoryOutputs, error) {
		result, err := GetEditor(c).ListDirectory(args)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		return &ListDirectoryOutputs{
			Files:     result.Files,
			Formatted: result.String(),
			Recursive: result.Recursive,
		}, nil
	})
}

func NewPatchFileAction() iris.Action {
	return actions.ToTool(string(ActionPatchFile), "Patch file", func(c *iris.AgentRunContext, args PatchFileArgs) (*PatchFileOutput, error) {
		res, err := GetEditor(c).PatchFile(c, PatchFileArgs{
			FilePath:              args.FilePath,
			Search:                args.Search,
			Replace:               args.Replace,
			RollbackOnSyntaxError: true,
		})
		return res, iris.NewRecoverable(err)
	})
}

func NewRawPatchFileAction() iris.Action {
	return actions.ToTool(string(ActionPatchFile), PatchFileDescription, func(c *iris.AgentRunContext, args RawPatchFileArgs) (*PatchFileOutput, error) {
		res, err := GetEditor(c).RawPatchFile(c, RawPatchFileArgs{
			FilePath:              args.FilePath,
			Diff:                  args.Diff,
			RollbackOnSyntaxError: true,
		})
		c.GetLogger().Info("raw patch file", "res", res, "err", err)
		return res, iris.NewRecoverable(err)
	})
}

// NewRawPatchFileV2Action uses new markdown like tool call format
func NewRawPatchFileV2Action() iris.Action {
	return actions.ToTool(string(ActionPatchFile), PatchFileDescription+PatchFileExample, func(c *iris.AgentRunContext, args RawPatchFileArgs) (*PatchFileOutput, error) {
		res, err := GetEditor(c).RawPatchFile(c, RawPatchFileArgs{
			FilePath:              args.FilePath,
			Diff:                  args.Diff,
			RollbackOnSyntaxError: false,
		})
		c.GetLogger().Info("raw patch file", "res", res, "err", err)
		return res, iris.NewRecoverable(err)
	})
}

func NewSearchFileNameAction() iris.Action {
	return actions.ToTool(string(ActionSearchFileName), SearchFileNameDescription, func(c *iris.AgentRunContext, args SearchFileNameArgs) (*SearchFileNameOutput, error) {
		res, err := GetEditor(c).SearchFileName(args)
		return res, iris.NewRecoverable(err)
	})
}

type GrepSearchConfig struct {
	LinesBefore int `mapstructure:"lines_before" json:"lines_before"`
	LinesAfter  int `mapstructure:"lines_after" json:"lines_after"`
}
type GrepSearchArgs struct {
	PathOrDirectory string `json:"path_or_directory" mapstructure:"path_or_directory"`
	Pattern         string `json:"pattern" mapstructure:"pattern"`
	IgnoreCase      bool   `json:"ignore_case" mapstructure:"ignore_case"`
	RegexMode       bool   `json:"regex_mode" mapstructure:"regex_mode"`
}

func NewGrepSearchAction(config GrepSearchConfig) iris.Action {
	return actions.ToTool(string(ActionGrepSearch), GrepSearchDescription, func(c *iris.AgentRunContext, args GrepSearchArgs) (*GrepSearchOutput, error) {
		res, err := GetTerminal(c, "").ExecGrepSearch(c, GrepSearchRangeArgs{
			PathOrDirectory: args.PathOrDirectory,
			Pattern:         args.Pattern,
			IgnoreCase:      args.IgnoreCase,
			RegexMode:       args.RegexMode,
			LinesBefore:     config.LinesBefore,
			LinesAfter:      config.LinesAfter,
		})
		return res, iris.NewRecoverable(err)
	})
}

func NewGrepSearchRangeAction() iris.Action {
	return actions.ToTool(string(ActionGrepSearch), GrepSearchDescription, func(c *iris.AgentRunContext, args GrepSearchRangeArgs) (*GrepSearchOutput, error) {
		res, err := GetTerminal(c, "").ExecGrepSearch(c, args)
		return res, iris.NewRecoverable(err)
	})
}

// TODO(lhj): temporarily implement as a different action to prevent affecting npe fix agent
func NewEditFileAction() iris.Action {
	return actions.ToTool(string(ActionEditFile), EditFileDescription, func(c *iris.AgentRunContext, args EditFileArgs) (*EditFileOutput, error) {
		res, err := GetEditor(c).EditFile(c, args)
		return res, iris.NewRecoverable(err)
	})
}

func NewSearchReplaceFileAction() iris.Action {
	return actions.ToTool(string(ActionSearchReplaceFile), SearchReplaceFileDescription, func(c *iris.AgentRunContext, args SearchReplaceFileArgs) (*PatchFileOutput, error) {
		res, err := GetEditor(c).SearchReplaceFile(c, args)
		return res, iris.NewRecoverable(err)
	})
}

type ExecuteCommandArgs struct {
	Cmd     string `json:"command" mapstructure:"command" description:"(required) command to execute"`
	Timeout int    `json:"timeout,omitempty" mapstructure:"timeout" description:"Optional timeout in seconds. Default is 600s and Up to 14400s. Use -1 for background servers"`
}

type ExecuteCommandOutput struct {
	Output    string `json:"output" mapstructure:"output"`
	Truncated bool   `json:"truncated" mapstructure:"truncated"`
}

type NewExecuteCommandActionOption struct {
	Environment        map[string]string
	ScrollbackLines    int
	MaxColWidth        int
	MaxTokens          int  // apply to the whole output
	DisableStdioStream bool // disable streaming report of stdout/stderr
	// EnabledCommand is a list of commands that are allowed to be executed
	// default is all commands are allowed
	EnabledCommand []string
	// Variant to integrate with Claude defined tools' description and schema
	// https://docs.anthropic.com/en/docs/agents-and-tools/computer-use#understand-anthropic-defined-tools
	Variant string
}

const (
	BashMaxTimeout     = 14400 // 4 hours
	BashDefaultTimeout = 600   // 10 minutes
)

func NewExecuteCommandAction(opt NewExecuteCommandActionOption) iris.Action {
	desc := ExecuteCommandDescription

	return actions.ToTool(string(ActionExecuteCommand), desc, func(c *iris.AgentRunContext, args ExecuteCommandArgs) (*ExecuteCommandOutput, error) {
		logger := c.GetLogger()
		// check if command is enabled
		if len(opt.EnabledCommand) > 0 {
			// check prefix of cmd contains any of the enabled commands
			enabled := false
			for _, cmd := range opt.EnabledCommand {
				if strings.HasPrefix(args.Cmd, cmd) {
					enabled = true
					break
				}
			}
			if !enabled {
				return &ExecuteCommandOutput{Output: fmt.Sprintf("command `%s` is not enabled", args.Cmd)}, nil
			}
		}

		firstCommand := extractFirstCommand(args.Cmd)
		start := time.Now()

		timeout := BashDefaultTimeout
		if args.Timeout == -1 || args.Timeout > 0 {
			timeout = args.Timeout
		}
		if timeout > BashMaxTimeout {
			timeout = BashMaxTimeout
		}

		cmd := safe_exec.CommandContext(c, "bash", "-c", args.Cmd)
		// inherit all environment variables
		cmd.SetEnv(os.Environ())
		out, err := GetTerminal(c, "").ExecuteSafeCmd(cmd, ExecuteCmdOption{
			Environment:        opt.Environment,
			Timeout:            timeout,
			DisableStdioStream: opt.DisableStdioStream,
		})

		// Record the timing metrics
		_ = metrics.AR.ToolBashCmdCost.WithTags(&metrics.ToolBashCmdTag{
			Command: firstCommand,
			Timeout: timeout,
			Success: err == nil,
		}).Observe(float64(time.Since(start).Milliseconds()))

		lines := strings.Split(out, "\n")
		linesCount := len(lines)
		maxColWidth := lo.Ternary(opt.MaxColWidth == 0, 4096, opt.MaxColWidth) // default 4kb for one line

		var truncated bool

		// truncate lines first to reduce the number of lines to process
		var lineTruncatePrefix string
		if opt.ScrollbackLines > 0 && linesCount > opt.ScrollbackLines {
			lines = lines[linesCount-opt.ScrollbackLines:]
			lineTruncatePrefix = fmt.Sprintf("...[%d lines truncated]...\n", linesCount-opt.ScrollbackLines)
			truncated = true
		}
		for idx, line := range lines {
			if len(line) > maxColWidth {
				lines[idx] = line[:maxColWidth] + fmt.Sprintf("...[truncated %d bytes]", len(line)-maxColWidth)
				truncated = true
			}
		}
		// rebuild out string after all line-level truncations
		out = lineTruncatePrefix + strings.Join(lines, "\n")
		// truncate the output if it exceeds the max token, as model may read a large jsonl file such that col width * lines count > MaxToken
		if opt.MaxTokens > 0 {
			first, last, squeezed := prompt.SqueezeToken(out, opt.MaxTokens/2)
			if squeezed {
				out = fmt.Sprintf("%s\n\n...[truncated %d bytes due to length limit]...\n%s", first, len(out)-len(first)-len(last), last)
				truncated = true
				logger.Infof("squeezed output due to length limit: %d bytes -> %d bytes", len(out), len(first)+len(last))
			}
		}

		if err != nil {
			return &ExecuteCommandOutput{Output: fmt.Sprintf("failed to execute command: %s\n%s", err.Error(), out)}, nil
		}
		return &ExecuteCommandOutput{Output: strings.TrimSpace(out), Truncated: truncated}, nil
	})
}

func NewExecuteCommandActionWithDefaultEnv(run *iris.AgentRunContext, opt NewExecuteCommandActionOption) iris.Action {
	llmBaseURL := lo.Ternary(run.GetEnv(entity.RuntimeEnvironLLMBaseURL) == "", run.GetEnv(entity.RuntimeEnvironAPIBaseURL), run.GetEnv(entity.RuntimeEnvironLLMBaseURL))
	env := lo.Assign(opt.Environment, map[string]string{
		"GIT_AUTHOR_NAME":     run.User.Username,
		"GIT_AUTHOR_EMAIL":    run.User.Username + "@bytedance.com",
		"GIT_COMMITTER_NAME":  run.User.Username,
		"GIT_COMMITTER_EMAIL": run.User.Username + "@bytedance.com",
		"GIT_SSH_COMMAND":     fmt.Sprintf("sshpass -f '%s' ssh -o StrictHostKeyChecking=no", entity.PathUserCodebaseJWT),
		"OPENAI_API_KEY":      run.State.SessionID,
		"OPENAI_BASE_URL":     fmt.Sprintf("%s/llmproxy/user", llmBaseURL),
		"USER_CLOUD_JWT":      run.GetEnv(entity.RuntimeEnvironUserCloudJWT),
	})
	return NewExecuteCommandAction(NewExecuteCommandActionOption{
		Environment:        env,
		ScrollbackLines:    opt.ScrollbackLines,
		MaxColWidth:        opt.MaxColWidth,
		MaxTokens:          opt.MaxTokens,
		EnabledCommand:     opt.EnabledCommand,
		Variant:            opt.Variant,
		DisableStdioStream: opt.DisableStdioStream,
	})
}

func NewGlobSearchAction() iris.Action {
	return actions.ToTool(string(ActionGlobSearch), GlobSearchDescription, func(c *iris.AgentRunContext, args GlobSearchArgs) (*GlobSearchOutput, error) {
		res, err := GetEditor(c).ExecGlobSearch(args)
		return res, iris.NewRecoverable(err)
	})
}

const (
	ActionWriteFileSmart            = "write_file"
	ActionWriteFileSmartDescription = "Use this tool (write_file) to create a new file and write content based on the content description you give to the file. [file_path] is the relative path of the new file, and [content_detail_description] is the very detailed description of the file content(but no need to be the specific content) with full context, such as language, pseudocode, skeleton, framework, section, outline."
)

type expander func(run *iris.AgentRunContext, filename, desc string) (string, error)

func NewWriteFileSmartAction(expand expander) iris.Action {
	return actions.ToTool(ActionWriteFileSmart, ActionWriteFileSmartDescription, func(run *iris.AgentRunContext, args WriteFileSmartArgs) (output *WriteFileSmartOutput, err error) {
		defer func() {
			if err != nil {
				run.GetLogger().Warnf("failed to run write file smart action: %v", err)
				err = iris.NewRecoverable(err)
			}
		}()

		content, err := expand(run, args.FilePath, args.ContentDetailDescription)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to expand content")
		}

		if _, err := GetEditor(run).CreateFile(run,
			CreateFileArgs{
				Path:    args.FilePath,
				Content: content,
			}, CreateFileOption{RemoveOuterBackquotes: false}); err != nil {
			return nil, errors.WithMessage(err, "failed to create file")
		}

		return &WriteFileSmartOutput{
			Content: content,
		}, nil
	})
}

type WriteFileSmartArgs struct {
	FilePath                 string `mapstructure:"file_path" json:"file_path" description:"(required) file path"`
	ContentDetailDescription string `mapstructure:"content_detail_description" json:"content_detail_description" description:"(required) content detail description with full context"`
}

type WriteFileSmartOutput struct {
	Content string `mapstructure:"content" json:"content"`
}

// extractFirstCommand extracts the first main command from a bash command string
func extractFirstCommand(cmd string) string {
	tokens := strings.Fields(strings.TrimSpace(cmd))
	if len(tokens) == 0 {
		return "unknown"
	}
	return tokens[0]
}
