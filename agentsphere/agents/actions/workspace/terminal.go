package workspace

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"os"
	"os/exec"
	"runtime"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"al.essio.dev/pkg/shellescape"
	safe_exec "code.byted.org/cld/safe-exec/command"
	"code.byted.org/gopkg/pkg/errors"
	"github.com/creack/pty"
	"github.com/fatih/color"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/conc/panics"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

type NewTerminalOption struct {
	Name           string
	Cmd            *exec.Cmd // cmd with stdin/stdout/stderr NOT set
	PublishChannel *iris.AgentEventPublisher
	Logger         iris.Logger
}

type Terminal struct {
	Name string

	// ensure only one command is running
	// and prevent terminal from being copied (pty)
	mu      sync.Mutex
	ptmx    *os.File
	cmd     *exec.Cmd
	closed  *atomic.Bool
	channel *iris.AgentEventPublisher
	logger  iris.Logger
}

var _ io.Writer = (*Terminal)(nil)

type ExecuteCmdOption struct {
	// ALL terminal lines will be sent to server and we shall not store more than MaxDisplayLines
	// This won't affect the returned output
	MaxDisplayLines int64
	// disable streaming report of stdout/stderr
	DisableStdioStream bool
	// Environment variables to set for the command
	Environment map[string]string
	Timeout     int
}

// OutputMessage 用于保持stdout和stderr的时间顺序
type OutputMessage struct {
	timestamp time.Time
	channel   string
	data      []byte
}

// Execute runs a command in a standalone process (non-interactively)
// this helper is for agent tools, not for users
func (t *Terminal) ExecuteCmd(cmd *exec.Cmd, opt ExecuteCmdOption) (out string, err error) {
	t.mu.Lock()
	defer t.mu.Unlock()
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return "", errors.WithMessage(err, "failed to get stdout pipe")
	}
	stderr, err := cmd.StderrPipe()
	if err != nil {
		return "", errors.WithMessage(err, "failed to get stderr pipe")
	}
	if len(opt.Environment) > 0 {
		for k, v := range opt.Environment {
			cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", k, v))
		}
	}
	if runtime.GOOS == "linux" {
		// avoid overwriting existing settings (e.g., Credential)
		if cmd.SysProcAttr == nil {
			cmd.SysProcAttr = &syscall.SysProcAttr{}
		}
		cmd.SysProcAttr.Setpgid = true
	}

	err = cmd.Start()
	if err != nil {
		return "", errors.WithMessagef(err, "failed to run command `%s`", cmd.String())
	}

	// command may contain passwords or credentials, don't log in production
	// t.logger.Infof("running command `%s`", t.formatCommand(cmd))
	t.channel.ReportShellStdio(iris.EventShellStdio{
		Terminal: t.Name,
		Channel:  "stdin",
		Data:     t.formatCommand(cmd),
	})

	buf := strings.Builder{}
	wg := new(sync.WaitGroup)
	lines := atomic.Int64{}
	timedout := false

	// 🔧 保持时间顺序的方案：使用带时间戳的消息队列
	msgChan := make(chan OutputMessage, 1000) // 缓冲队列
	var msgWg sync.WaitGroup
	msgWg.Add(1)

	// 全局行数计数器，用于MaxDisplayLines限制
	globalLineCount := &atomic.Int64{}

	// 消息收集和排序goroutine
	go func() {
		defer msgWg.Done()
		messages := []OutputMessage{}

		// 收集所有消息
		for msg := range msgChan {
			messages = append(messages, msg)
		}

		// 按时间戳排序（虽然通常已经是有序的）
		sort.Slice(messages, func(i, j int) bool {
			return messages[i].timestamp.Before(messages[j].timestamp)
		})

		// 按时间顺序写入最终缓冲区
		lineCount := 0
		for _, msg := range messages {
			buf.Write(msg.data)
			lineCount++
		}
		lines.Store(int64(lineCount))
	}()

	wg.Add(2)
	go panics.Try(func() {
		// stdout处理：发送到消息队列，支持退格处理
		t.processStreamWithCarriageReturn(stdout, msgChan, "stdout", globalLineCount, opt)
		wg.Done()
	})
	go panics.Try(func() {
		// stderr处理：发送到消息队列，支持退格处理
		t.processStreamWithCarriageReturn(stderr, msgChan, "stderr", globalLineCount, opt)
		wg.Done()
	})
	go panics.Try(func() {
		if opt.Timeout <= 0 {
			return
		}
		time.AfterFunc(time.Duration(opt.Timeout)*time.Second, func() {
			timedout = true
			if cmd.Process == nil {
				t.logger.Warnf("command [%s] has no process, cannot kill.", cmd.String())
				return
			}
			if runtime.GOOS == "linux" {
				_ = syscall.Kill(-cmd.Process.Pid, syscall.SIGINT)  // kill the whole process group, created by Setpgid
				time.Sleep(time.Second * 3)                         // try to graceful shutdown
				_ = syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL) // kill the whole process group, created by Setpgid
			} else {
				_ = syscall.Kill(-cmd.Process.Pid, syscall.SIGINT)
				time.Sleep(time.Second * 3) // try to graceful shutdown
				if err := cmd.Process.Kill(); err != nil {
					t.logger.Warnf("execute command timeout, not killed: %+v", err)
				} else {
					t.logger.Infof("execute command timeout, killed: %v, pid: %d", cmd.String(), cmd.Process.Pid)
				}
			}
		})
	})
	// -1表示后台运行，直接返回进程信息
	if opt.Timeout == -1 {
		go panics.Try(func() {
			wg.Wait()
			cmd.Wait()
		})
		return fmt.Sprintf("Command started in background with PID: %d", cmd.Process.Pid), nil
	}
	// if scanner reads to EOF, the child process have been exited and closes child end of pipe
	// wait scanner first to ensure all stdout and stderr are read, and call cmd.Wait to cleanup
	wg.Wait()
	close(msgChan) // 关闭消息队列
	msgWg.Wait()   // 等待消息处理完成

	if timedout {
		buf.WriteString("\n\n[timeout exceeded, killed]")
	}
	if lines.Load() > opt.MaxDisplayLines {
		t.logger.Infof("output truncated to %d lines", opt.MaxDisplayLines)
		t.channel.ReportShellStdio(iris.EventShellStdio{
			Terminal: t.Name,
			Channel:  "stderr",
			Data:     fmt.Sprintf("[...%d lines of command result truncated..]\n", lines.Load()-opt.MaxDisplayLines),
		})
	}
	// stdout and stderr are automatically closed by cmd.Wait
	return buf.String(), cmd.Wait()
}

func (t *Terminal) ExecuteSafeCmd(cmd safe_exec.Cmd, opt ExecuteCmdOption) (out string, err error) {
	t.mu.Lock()
	defer t.mu.Unlock()
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return "", errors.WithMessage(err, "failed to get stdout pipe")
	}
	stderr, err := cmd.StderrPipe()
	if err != nil {
		return "", errors.WithMessage(err, "failed to get stderr pipe")
	}
	if len(opt.Environment) > 0 {
		cmd.AppendEnv(lo.MapToSlice(opt.Environment, func(k string, v string) string {
			return fmt.Sprintf("%s=%s", k, v)
		}))
	}
	err = cmd.Start()
	if err != nil {
		return "", errors.WithMessagef(err, "failed to run command `%s`", cmd.String())
	}

	buf := strings.Builder{}
	wg := conc.NewWaitGroup()
	lines := atomic.Int64{}
	timedout := false
	var cmdErr error
	lo.ForEach(
		[]struct {
			channel string
			reader  io.Reader
		}{
			{channel: "stdout", reader: stdout},
			{channel: "stderr", reader: stderr},
		},
		func(item struct {
			channel string
			reader  io.Reader
		}, _ int,
		) {
			channel, reader := item.channel, item.reader
			wg.Go(func() {
				// 使用支持退格处理的读取方法
				t.processStreamForSafeCmd(reader, &buf, &lines, channel, opt)
			})
		})
	// -1表示后台运行，给10秒时间收集初始输出
	if opt.Timeout == -1 {
		go panics.Try(func() {
			wg.Wait()
			cmd.Wait()
		})

		// 等待10秒收集输出
		time.Sleep(10 * time.Second)

		output := buf.String()
		backgroundMsg := "Command started in background and here's the output of first 10 seconds:"

		if output != "" {
			return fmt.Sprintf("%s\n\n%s", backgroundMsg, output), nil
		}
		return backgroundMsg, nil
	} else {
		if opt.Timeout > 0 {
			time.AfterFunc(time.Duration(opt.Timeout)*time.Second, func() {
				timedout = true
				cmd.Kill()
			})
		}
		wg.Go(func() {
			cmdErr = cmd.Wait()
		})
	}

	wg.Wait()
	if timedout {
		buf.WriteString(fmt.Sprintf("\n\n[timeout %d seconds exceeded, killed]", opt.Timeout))
		buf.WriteString("[consider: continue executing, increase timeout, fix command syntax, break down command, or run in background with -1 timeout]")
	}

	return buf.String(), cmdErr
}

// ExecuteAllCmd runs all commands in order
func (t *Terminal) ExecuteAllCmd(cmds ...*exec.Cmd) error {
	for _, cmd := range cmds {
		_, err := t.ExecuteCmd(cmd, ExecuteCmdOption{})
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *Terminal) PrintCommandPrompt(cmd *exec.Cmd) error {
	t.channel.ReportShellStdio(iris.EventShellStdio{
		Terminal: t.Name,
		Channel:  "stdout",
		Data:     t.formatCommand(cmd),
	})
	return nil
}

func (t *Terminal) StdoutWriter() io.Writer {
	return &TerminalWriter{output: "stdout", t: t}
}

func (t *Terminal) StderrWriter() io.Writer {
	return &TerminalWriter{output: "stderr", t: t}
}

// Write directly write to (interactive) terminal
func (t *Terminal) Write(bytes []byte) (int, error) {
	t.mu.Lock()
	defer t.mu.Unlock()
	written, err := t.ptmx.Write(bytes)
	if err != nil {
		return written, errors.Wrap(err, "failed to write to terminal")
	}
	return written, nil
}

func (t *Terminal) WriteString(str string) error {
	_, err := t.Write([]byte(str))
	return err
}

func (t *Terminal) Close() {
	// only close once
	if !t.closed.CompareAndSwap(false, true) {
		return
	}
	if t.cmd.Process != nil {
		// bash will proxy the signal to the child processes for them to exit
		t.cmd.Process.Signal(os.Interrupt)
		// however child processes may ignore the signal or too busy to handle it
		// force kill the processes after 3 seconds
		go panics.Try(func() {
			<-time.After(time.Second * 3)
			if err := t.cmd.Process.Kill(); err != nil {
				fmt.Printf("not killed: %+v\n", err)
			} else {
				fmt.Printf("killed terminal: %v, pid: %d\n", t.cmd.ProcessState, t.cmd.Process.Pid)
			}
		})
		t.cmd.Process.Wait()
	}
	t.ptmx.Close()
}

func (t *Terminal) Scan() {
	scanner := bufio.NewScanner(t.ptmx)
	for scanner.Scan() {
		if t.closed.Load() {
			break
		}
		t.channel.ReportShellStdio(iris.EventShellStdio{
			Terminal: t.Name,
			Channel:  "stdout",
			Data:     scanner.Text() + "\n",
		})
	}
}

func (t *Terminal) formatCommand(cmd *exec.Cmd) string {
	args := lo.Map(lo.Slice(cmd.Args, 1, len(cmd.Args)), func(arg string, _ int) string {
		return shellescape.Quote(arg)
	})
	// force enable color formatting, ignore NO_COLOR settings
	promptColor, commandColor := color.New(color.FgHiGreen), color.New(color.FgHiGreen)
	promptColor.EnableColor()
	commandColor.EnableColor()
	return fmt.Sprintf("%s %s %s\n", promptColor.Sprint("λ"), commandColor.Sprint(cmd.Args[0]), strings.Join(args, " "))
}

func NewTerminal(opt NewTerminalOption) (*Terminal, error) {
	f, err := pty.Start(opt.Cmd)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to start terminal")
	}

	t := &Terminal{
		Name:    opt.Name,
		ptmx:    f,
		cmd:     opt.Cmd,
		channel: opt.PublishChannel,
		logger:  opt.Logger,
		closed:  new(atomic.Bool),
	}
	go t.Scan()

	return t, nil
}

type TerminalWriter struct {
	output string // `stdout` or `stderr`
	t      *Terminal
}

var _ io.Writer = (*TerminalWriter)(nil)

func (t *TerminalWriter) Write(p []byte) (n int, err error) {
	t.t.channel.ReportShellStdio(iris.EventShellStdio{
		Terminal: t.t.Name,
		Channel:  t.output,
		Data:     string(p),
	})
	return len(p), nil
}

// StreamProcessor 流处理器配置
type StreamProcessor struct {
	// 输出目标
	Buffer      *strings.Builder   // 用于 ExecuteSafeCmd
	MessageChan chan OutputMessage // 用于 ExecuteCmd

	// 计数器
	Lines       *atomic.Int64 // 用于 ExecuteSafeCmd
	GlobalLines *atomic.Int64 // 用于 ExecuteCmd
	LocalLines  *int          // 用于 ExecuteCmd

	// 其他参数
	Channel  string
	Opt      ExecuteCmdOption
	Terminal *Terminal
}

// processStreamGeneric 通用的流处理函数，支持退格处理
// 通过 StreamProcessor 配置不同的输出行为
func (t *Terminal) processStreamGeneric(pipe io.Reader, processor *StreamProcessor) {
	reader := bufio.NewReader(pipe)
	// 预分配空间，减少扩容
	currentLine := make([]byte, 0, 1024)      // 预分配1KB空间
	lastReportedLine := make([]byte, 0, 1024) // 预分配1KB空间

	// 使用更大的缓冲区进行批量读取
	buffer := make([]byte, 4096) // 4KB缓冲区

	for {
		// 批量读取以提高性能
		n, err := reader.Read(buffer)
		if err != nil {
			if err != io.EOF {
				t.logger.Warnf("%s读取错误: %v", processor.Channel, err)
			}
			// 处理最后一行（可能没有换行符）
			if len(currentLine) > 0 {
				t.handleLineComplete(currentLine, processor, &lastReportedLine)
			}
			break
		}

		// 高效处理读取到的数据
		data := buffer[:n]
		for i := 0; i < len(data); i++ {
			b := data[i]

			// 处理 \r 字符
			if b == '\r' {
				// 检查下一个字符是否为 \n
				if i+1 < len(data) && data[i+1] == '\n' {
					// \r\n 组合，正常处理
					currentLine = append(currentLine, b)
					continue
				}
				// 单独的 \r，清空当前行，准备接收新内容
				currentLine = currentLine[:0]
				continue
			}

			// 处理 \n 字符
			if b == '\n' {
				currentLine = append(currentLine, b)
				t.handleLineComplete(currentLine, processor, &lastReportedLine)

				// 重置当前行，保持预分配的空间
				currentLine = currentLine[:0]
				continue
			}

			// 普通字符，添加到当前行
			currentLine = append(currentLine, b)
		}
	}
}

// handleLineComplete 处理完整的行
func (t *Terminal) handleLineComplete(currentLine []byte, processor *StreamProcessor, lastReportedLine *[]byte) {
	// 根据处理器类型执行不同的操作
	if processor.Buffer != nil {
		// ExecuteSafeCmd 模式：写入缓冲区
		processor.Buffer.Write(currentLine)
		if processor.Lines.Load() <= processor.Opt.MaxDisplayLines && !processor.Opt.DisableStdioStream {
			data := string(currentLine)
			if len(currentLine) > 0 && currentLine[len(currentLine)-1] != '\n' {
				data += "\n"
			}
			t.channel.ReportShellStdio(iris.EventShellStdio{
				Terminal: t.Name,
				Channel:  processor.Channel,
				Data:     data,
			})
		}
		processor.Lines.Add(1)
	} else if processor.MessageChan != nil {
		// ExecuteCmd 模式：发送到消息通道
		*processor.LocalLines++
		processor.MessageChan <- OutputMessage{
			timestamp: time.Now(),
			channel:   processor.Channel,
			data:      append([]byte{}, currentLine...), // 复制一份避免并发问题
		}

		if processor.Opt.MaxDisplayLines <= 0 || processor.GlobalLines.Load() <= processor.Opt.MaxDisplayLines {
			if !processor.Opt.DisableStdioStream {
				// 检查是否需要发送更新（处理退格情况）
				if !bytes.Equal(currentLine, *lastReportedLine) {
					t.channel.ReportShellStdio(iris.EventShellStdio{
						Terminal: t.Name,
						Channel:  processor.Channel,
						Data:     string(currentLine),
					})
					// 优化内存复制：重用预分配的空间
					*lastReportedLine = (*lastReportedLine)[:0]
					*lastReportedLine = append(*lastReportedLine, currentLine...)
				}
			}
		}
		processor.GlobalLines.Add(1)
	}
}

// processStreamForSafeCmd 为 ExecuteSafeCmd 提供支持退格处理的流处理
func (t *Terminal) processStreamForSafeCmd(reader io.Reader, buf *strings.Builder, lines *atomic.Int64, channel string, opt ExecuteCmdOption) {
	processor := &StreamProcessor{
		Buffer:   buf,
		Lines:    lines,
		Channel:  channel,
		Opt:      opt,
		Terminal: t,
	}
	t.processStreamGeneric(reader, processor)
}

// processStreamWithCarriageReturn 处理包含 \r 的流，实现终端退格效果
// 当连续读取 \r 且没有 \n 时，保留最新的内容
func (t *Terminal) processStreamWithCarriageReturn(pipe io.Reader, msgChan chan OutputMessage, channel string, globalLineCount *atomic.Int64, opt ExecuteCmdOption) {
	localLineCount := 0
	processor := &StreamProcessor{
		MessageChan: msgChan,
		GlobalLines: globalLineCount,
		LocalLines:  &localLineCount,
		Channel:     channel,
		Opt:         opt,
		Terminal:    t,
	}
	t.processStreamGeneric(pipe, processor)
}
