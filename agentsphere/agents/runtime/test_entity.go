package runtime

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	larktool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	planact_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/agent"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/client"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/port/lark"
	customopenai "code.byted.org/devgpt/kiwis/port/openai"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

type testTask struct {
	require.TestingT
	record     *TestCaseRecord
	caseObj    testCase
	cloudToken string
	request    entity.RunAgentRequest
}

type TestCaseRecord struct {
	Name      string
	StartTime time.Time
	EndTime   time.Time
	Status    string
	Fail      bool
	Output    bytes.Buffer
	SessionID string
	lock      *sync.Mutex
}
type CaseType string

const (
	E2ECaseType    CaseType = "e2e"
	ModuleCaseType CaseType = "module"
)

type E2EAssertion func(ctx Context, session *Session)
type E2ECase struct {
	Name       string
	Message    string
	Role       nextagent.SessionRole
	Assertion  E2EAssertion
	Assertions []E2EAssertion
}

func (e *E2ECase) description() string {
	return e.Message
}

func (e *E2ECase) name() string {
	return e.Name
}

func (e *E2ECase) setName(name string) {
	e.Name = name
}

func (e *E2ECase) run(ctx Context) {
	session := ctx.CreateSession(ctx, e.Role)
	session.SendMessage(e.Message)
	err := session.WaitCompleted()
	assert.Nil(ctx, err)
	if e.Assertion != nil {
		e.Assertion(ctx, session)
	}
	for _, assertion := range e.Assertions {
		assertion(ctx, session)
	}
}

func (e *E2ECase) caseType() string {
	return string(E2ECaseType)
}

type ModuleCase struct {
	Name    string
	Execute func(ctx Context, runContext *iris.AgentRunContext)
}

func (e *ModuleCase) description() string {
	return e.name()
}

func (e *ModuleCase) name() string {
	return e.Name
}

func (e *ModuleCase) setName(name string) {
	e.Name = name
}
func (e *ModuleCase) run(ctx Context) {
	agentContext := ctx.CreateAgentContext()
	e.Execute(ctx, agentContext)
}

func (e *ModuleCase) caseType() string {
	return string(ModuleCaseType)
}

type Session struct {
	Session    *nextagent.Session
	API        *client.RuntimeAPIClient
	Token      string
	ctx        *TestingContext
	AgentRunID string
}

func (s *Session) SendMessage(msg string) {
	s.ctx.Helper()
	_, err := s.API.CreateMessage(context.Background(), &nextagent.CreateMessageRequest{
		SessionID: s.Session.ID,
		Content:   msg,
	}, s.Token)
	assert.Nil(s.ctx, err)
}

func (s *Session) WaitCompleted() error {
	timeoutTimer := time.After(time.Hour)
	for {
		select {
		case <-timeoutTimer:
			return errors.New("timeout")
		case <-time.After(5 * time.Second):
			session, err := s.API.GetSession(context.Background(), &nextagent.GetSessionRequest{
				SessionID: s.Session.ID,
			}, s.Token)
			if err != nil {
				return err
			}
			switch session.Session.Status {
			case nextagent.SessionStatusCanceled, nextagent.SessionStatusError:
				// TODO: cover all status cases.
				return errors.Errorf("session status: %s", session.Session.Status)
			case nextagent.SessionStatusIdle:
				return nil
			}
		}
	}
}

func (s *Session) GetArtifacts() ([]*nextagent.Artifact, error) {
	listArtifactsResponse, err := s.API.ListArtifactsV2(context.Background(), &nextagent.ListArtifactsRequest{
		SessionID: lo.ToPtr(s.Session.ID),
	})
	if err != nil {
		return nil, err
	}
	return listArtifactsResponse.Artifacts, nil
}

func (s *Session) GetTools() ([]string, error) {
	// TODO: implement
	return nil, nil
}

func (s *Session) GetAgentRun() *AgentRun {
	return s.ctx.remoteRuntime.GetRun(s.AgentRunID)
}

func (s *Session) Stop() {
	_, err := s.API.UpdateSession(context.Background(), &nextagent.UpdateSessionRequest{
		SessionID: s.Session.ID,
		Status:    lo.ToPtr(nextagent.SessionStatusCanceled),
	}, s.Token)
	if err != nil {
		s.ctx.logger.Errorf("failed to stop session: %v", err)
	}
}

type Context interface {
	context.Context
	require.TestingT
	CreateSession(ctx context.Context, role nextagent.SessionRole) *Session
	CreateAgentContext() *iris.AgentRunContext
	Log(s string)
	Logf(format string, args ...any)
	Helper()
	GroundTruthCheck(result string, groundTrue string) *GroundTruthCheckResult
	GetLLM() (framework.LLM, error)
	ReadFile(file string) ([]byte, error)
	ReadDir(s string) ([]os.DirEntry, error)
	ReadLarkDocs(ctx context.Context, url string) []*larkdocx.Block
}

type GroundTruthCheckResult struct {
	Score       int
	IsAccurate  bool
	ErrorDetail string
	MissingInfo string
	Suggestions string
	RawResponse string
}

func (g *GroundTruthCheckResult) String() string {
	builder := strings.Builder{}
	builder.WriteString(fmt.Sprintf("\n=== Ground Truth Check ===\n"))
	builder.WriteString(fmt.Sprintf("Score: %d/10\n", g.Score))
	builder.WriteString(fmt.Sprintf("Is Accurate: %t\n", g.IsAccurate))
	builder.WriteString(fmt.Sprintf("Error Detail: %s\n", g.ErrorDetail))
	builder.WriteString(fmt.Sprintf("Missing Info: %s\n", g.MissingInfo))
	builder.WriteString(fmt.Sprintf("Suggestions: %s\n", g.Suggestions))
	return builder.String()
}

type testCase interface {
	run(ctx Context)
	name() string
	setName(name string)
	caseType() string
	description() string
}

var (
	cases    []testCase
	dedupMap = map[string]struct{}{}
)

func RegistryE2ECase(cs ...*E2ECase) {
	for _, c := range cs {
		registryE2ECase(c)
	}
}

func RegistryModuleCase(cs ...*ModuleCase) {
	for _, c := range cs {
		registryE2ECase(c)
	}
}

func registryE2ECase(c testCase) {
	callerPkgName := getCallerPackageName(3)
	if len(callerPkgName) > 0 {
		callerPkgName = strings.TrimPrefix(callerPkgName, "code.byted.org/devgpt/kiwis/agentsphere/runtime/eval/")
		c.setName(callerPkgName + "." + c.name())
	}
	if _, ok := dedupMap[c.name()]; ok {
		panic("duplicate Case: " + c.name())
	}
	dedupMap[c.name()] = struct{}{}
	cases = append(cases, c)
}

func getCallerPackageName(skip int) string {
	pc, _, _, ok := runtime.Caller(skip)
	if !ok {
		return ""
	}
	fn := runtime.FuncForPC(pc)
	if fn == nil {
		return ""
	}
	fullName := fn.Name()
	lastSlash := strings.LastIndex(fullName, "/")
	if lastSlash == -1 {
		lastSlash = 0
	}
	dot := strings.Index(fullName[lastSlash:], ".")
	if dot == -1 {
		return ""
	}
	return fullName[:lastSlash+dot]
}

func getCases() []testCase {
	return cases
}

var _ Context = (*TestingContext)(nil)

type TestingContext struct {
	context.Context
	remoteRuntime *RemoteRuntime
	c             testCase
	api           *client.RuntimeAPIClient
	token         string
	logger        iris.Logger
	uri           string
	request       entity.RunAgentRequest
	record        *TestCaseRecord
	helperPCs     map[uintptr]struct{} // functions to be skipped when writing file/line info
	helperNames   map[string]struct{}  // helperPCs converted to function names
}

func (t *TestingContext) ReadDir(s string) ([]os.DirEntry, error) {
	return os.ReadDir(s)
}

func (t *TestingContext) ReadFile(file string) ([]byte, error) {
	// TODO: might use rpc to read file.
	return os.ReadFile(file)
}

func (t *TestingContext) GroundTruthCheck(result string, groundTruth string) *GroundTruthCheckResult {
	llm, err := t.GetLLM()
	if err != nil {
		t.Errorf("Failed to get LLM: %v", err)
		return &GroundTruthCheckResult{
			Score:       0,
			IsAccurate:  false,
			ErrorDetail: fmt.Sprintf("LLM调用失败: %v", err),
		}
	}
	prompt := fmt.Sprintf(`请仔细检查以下搜索结果与事实的准确性。

搜索结果：
%s

事实：
%s

请从以下几个方面进行评估：
1. 事实准确性：搜索结果中的事实是否与给定事实一致
2. 信息完整性：是否有遗漏的重要信息
3. 错误信息：是否包含错误或误导性的信息
4. 逻辑一致性：信息之间是否存在逻辑矛盾

请严格按照以下格式回复（不要添加其他内容）：
准确性评分：[0-10分，10分为完全准确]
是否准确：[是/否]
错误详情：[如果有错误，请详细说明具体错误；如果准确，请写"无错误"]
遗漏信息：[如果有遗漏，请详细说明遗漏内容；如果无遗漏，请写"无遗漏"]
改进建议：[如果有改进建议，请详细说明；如果无需改进，请写"无需改进"]`, result, groundTruth)

	response, err := llm.ChatCompletion(context.Background(), []*framework.ChatMessage{
		{
			Role:    "user",
			Content: prompt,
		},
	}, framework.LLMCompletionOption{
		Model:       "gpt-4o-2024-05-13",
		Temperature: 0.1,
	})

	if err != nil {
		t.Errorf("Failed to call LLM for ground truth check: %v", err)
		return &GroundTruthCheckResult{
			Score:       0,
			IsAccurate:  false,
			ErrorDetail: fmt.Sprintf("LLM调用失败: %v", err),
		}
	}

	if len(response.Content) == 0 {
		t.Errorf("No response from LLM for ground truth check")
		return &GroundTruthCheckResult{
			Score:       0,
			IsAccurate:  false,
			ErrorDetail: "LLM未返回响应",
		}
	}

	checkResult := response.Content
	t.Logf("Ground Truth Check Result:\n%s", checkResult)

	parsedResult := parseGroundTruthResponse(checkResult)
	return parsedResult
}

func parseGroundTruthResponse(response string) *GroundTruthCheckResult {
	result := &GroundTruthCheckResult{
		RawResponse: response,
		Score:       0,
		IsAccurate:  false,
		ErrorDetail: "解析失败",
		MissingInfo: "解析失败",
		Suggestions: "解析失败",
	}
	lines := strings.Split(response, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "准确性评分") {
			if scoreStr := extractNumber(line); scoreStr != "" {
				if score, err := strconv.Atoi(scoreStr); err == nil {
					result.Score = score
				}
			}
		}
		if strings.Contains(line, "是否准确") {
			if strings.Contains(line, "是") {
				result.IsAccurate = true
			}
		}
		if strings.Contains(line, "错误详情") {
			if detail := extractContent(line, "错误详情："); detail != "" {
				result.ErrorDetail = detail
			}
		}
		if strings.Contains(line, "遗漏信息") {
			if missing := extractContent(line, "遗漏信息："); missing != "" {
				result.MissingInfo = missing
			}
		}
		if strings.Contains(line, "改进建议") {
			if suggestion := extractContent(line, "改进建议："); suggestion != "" {
				result.Suggestions = suggestion
			}
		}
	}
	return result
}

func extractNumber(s string) string {
	re := regexp.MustCompile(`\d+`)
	matches := re.FindString(s)
	return matches
}

func extractContent(s, prefix string) string {
	if idx := strings.Index(s, prefix); idx != -1 {
		content := strings.TrimSpace(s[idx+len(prefix):])
		content = strings.Trim(content, "[]")
		return content
	}
	return ""
}

func (t *TestingContext) GetLLM() (framework.LLM, error) {
	llmToken := lo.Ternary(
		t.request.Environ != nil && t.request.Environ[entity.RuntimeEnvironLLMToken] != "",
		t.request.Environ[entity.RuntimeEnvironLLMToken],
		os.Getenv(entity.RuntimeEnvironLLMToken),
	)
	llm, err := framework.NewCustomOpenAILLM(
		llmToken,
		fmt.Sprintf("%s/llmproxy", t.remoteRuntime.config.LLMBaseURL),
		func(ctx context.Context, req *customopenai.ChatCompletionsRequest, reqOpt *hertz.ReqOption) context.Context {
			reqOpt.Headers = lo.Assign(reqOpt.Headers, map[string]string{
				framework.LLMTagKey:     conv.DefaultAny[string](req.ExtraOptions["tag"]),
				framework.LLMTraceIDKey: conv.DefaultAny[string](ctx.Value(framework.LLMTraceIDKey)),
			})
			return ctx
		},
	)
	return llm, err
}

func (t *TestingContext) Logf(format string, args ...any) {
	s := t.decorate(fmt.Sprintf("Info: "+format+"\n", args...), 2)
	t.logger.Info(s)
	t.record.lock.Lock()
	defer t.record.lock.Unlock()
	t.record.Output.WriteString(s)
}

func (t *TestingContext) Helper() {
	// The code is referenced from testing.T.Helper().
	t.record.lock.Lock()
	defer t.record.lock.Unlock()
	if t.helperPCs == nil {
		t.helperPCs = make(map[uintptr]struct{})
	}
	// repeating code from callerName here to save walking a stack frame
	var pc [1]uintptr
	n := runtime.Callers(2, pc[:]) // skip runtime.Callers + Helper
	if n == 0 {
		panic("testing: zero callers found")
	}
	if _, found := t.helperPCs[pc[0]]; !found {
		t.helperPCs[pc[0]] = struct{}{}
		t.helperNames = nil // map will be recreated next time it is needed
	}
}

const maxStackLen = 50

// frameSkip searches, starting after skip frames, for the first caller frame
// in a function not marked as a helper and returns that frame.
// The search stops if it finds a tRunner function that
// was the entry point into the test and the test is not a subtest.
// This function must be called with c.mu held.
func (t *TestingContext) frameSkip(skip int) runtime.Frame {
	// If the search continues into the parent test, we'll have to hold
	// its mu temporarily. If we then return, we need to unlock it.
	shouldUnlock := false
	defer func() {
		if shouldUnlock {
			t.record.lock.Unlock()
		}
	}()
	var pc [maxStackLen]uintptr
	// Skip two extra frames to account for this function
	// and runtime.Callers itself.
	n := runtime.Callers(skip+2, pc[:])
	if n == 0 {
		panic("testing: zero callers found")
	}
	frames := runtime.CallersFrames(pc[:n])
	var firstFrame, frame runtime.Frame
	for more := true; more; {
		frame, more = frames.Next()
		if frame.Function == "runtime.gopanic" {
			continue
		}
		if firstFrame.PC == 0 {
			firstFrame = frame
		}
		// If more helper PCs have been added since we last did the conversion
		if t.helperNames == nil {
			t.helperNames = make(map[string]struct{})
			for pc := range t.helperPCs {
				t.helperNames[pcToName(pc)] = struct{}{}
			}
		}
		if _, ok := t.helperNames[frame.Function]; !ok {
			// Found a frame that wasn't inside a helper function.
			return frame
		}
	}
	return firstFrame
}

func pcToName(pc uintptr) string {
	pcs := []uintptr{pc}
	frames := runtime.CallersFrames(pcs)
	frame, _ := frames.Next()
	return frame.Function
}

// decorate prefixes the string with the file and line of the call site
// and inserts the final newline if needed and indentation spaces for formatting.
// This function must be called with c.mu held.
func (t *TestingContext) decorate(s string, skip int) string {
	frame := t.frameSkip(skip)
	file := frame.File
	line := frame.Line
	if file != "" {
		if index := strings.LastIndexAny(file, `/\`); index >= 0 {
			file = file[index+1:]
		}
	} else {
		file = "???"
	}
	if line == 0 {
		line = 1
	}
	buf := new(strings.Builder)
	// Every line is indented at least 4 spaces.
	buf.WriteString("    ")
	fmt.Fprintf(buf, "%s:%d: ", file, line)
	lines := strings.Split(s, "\n")
	if l := len(lines); l > 1 && lines[l-1] == "" {
		lines = lines[:l-1]
	}
	for i, line := range lines {
		if i > 0 {
			// Second and subsequent lines are indented an additional 4 spaces.
			buf.WriteString("\n        ")
		}
		buf.WriteString(line)
	}
	buf.WriteByte('\n')
	return buf.String()
}

func (t *TestingContext) Errorf(format string, args ...interface{}) {
	t.logger.Errorf("[UnitTest] Test case %s failed: %s", t.c.name(), fmt.Sprintf(format, args...))
	t.record.lock.Lock()
	t.record.Fail = true
	t.record.lock.Unlock()
	t.record.Output.WriteString(t.decorate(fmt.Sprintf("Error: "+format, args...), 2))
}

func (t *TestingContext) FailNow() {
	t.logger.Errorf("[UnitTest] Test case %s failed immediately", t.c.name())
	t.record.lock.Lock()
	t.record.Fail = true
	t.record.lock.Unlock()
	runtime.Goexit()
}

func (t *TestingContext) Log(s string) {
	t.logger.Info(s)
	t.record.lock.Lock()
	defer t.record.lock.Unlock()
	t.record.Output.WriteString(t.decorate(fmt.Sprintf("Info: "+s), 2))
}

func (t *TestingContext) CreateSession(ctx context.Context, role nextagent.SessionRole) *Session {
	t.Helper()
	createSessionResponse, err := t.api.CreateSession(ctx, &nextagent.CreateSessionRequest{
		Role: &role,
	}, t.token)
	assert.Nil(t, err)
	sessionID := createSessionResponse.Session.ID
	t.logger.Infof("session created: %s", sessionID)
	agentRunID, err := t.api.BindAgent(ctx, &entity.BindAgentRequest{
		MainContainerSessionID: os.Getenv("TESTING_SESSION"),
		SessionID:              sessionID,
		URI:                    t.uri,
	})
	assert.Nil(t, err)
	value := "/workspace/iris_" + sessionID
	_ = os.MkdirAll(value, 0744)
	_ = os.Chdir(value)
	err = t.api.ReportStatus(ctx, &agentsphere.UpdateRuntimeStateRequest{
		SessionID: sessionID,
		URI:       t.uri,
		PID:       int32(os.Getpid()),
		Status:    string(entity.SessionStatusRunning),
	})
	assert.Nil(t, err)
	t.logger.Infof("agent runtime is registered for session: %s", sessionID)
	t.Logf("created session: %s", sessionID)
	t.record.lock.Lock()
	// Only support one session for now.
	t.record.SessionID = sessionID
	t.record.lock.Unlock()
	return &Session{
		AgentRunID: agentRunID,
		Session:    createSessionResponse.Session,
		API:        t.api,
		Token:      t.token,
		ctx:        t,
	}
}
func (t *TestingContext) ReadLarkDocs(ctx context.Context, url string) []*larkdocx.Block {
	t.Helper()
	larkClient := larktool.GetLarkClient()
	_, fileType, token, _, err := larktool.PrepareLarkURL(larkClient, url, t.request.Environ[entity.RunTimeLarkUserAccessToken])
	assert.Nil(t, err)
	option := lark.Option{
		UserAccessToken: t.request.Environ[entity.RunTimeLarkUserAccessToken],
	}
	switch fileType {
	case "docx":
		blocks, err := larkClient.GetLarkDocxBlock(t, token, "", option)
		assert.Nil(t, err)
		return blocks
	default:
		assert.FailNow(t, "not supported file type: %s", fileType)
	}
	return nil
}

var initBackgroundServicesOnce sync.Once

func (t *TestingContext) CreateAgentContext() *iris.AgentRunContext {
	agentRunContext, err := t.remoteRuntime.CreateRunContext(context.Background(), entity.RunAgentRequest{
		AgentName:      t.request.AgentName,
		AssignmentID:   t.request.AssignmentID,
		RunID:          t.request.RunID,
		SessionID:      t.request.SessionID,
		User:           t.request.User,
		UserInfo:       t.request.UserInfo,
		Input:          t.request.Input,
		Config:         t.request.Config,
		AgentRunConfig: t.request.AgentRunConfig,
		Parameters:     t.request.Parameters,
		Environ:        t.request.Environ,
	})
	if err != nil {
		require.Nil(t, err)
	}
	_ = agentRunContext.CreateStep()
	initBackgroundServicesOnce.Do(func() {
		planact_agent.InitializeBackgroundServices(agentRunContext)
	})
	return agentRunContext
}
