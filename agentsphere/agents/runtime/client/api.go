package client

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/pkg/errors"
	"github.com/cenk/backoff"
	"github.com/hashicorp/go-multierror"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/iter"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
)

type RuntimeAPIClient struct {
	baseURL     string
	cli         *hertz.Client
	logger      iris.Logger
	sessionType string
}

func NewRuntimeAPIClient(baseURL string, logger iris.Logger, sessionType string) (*RuntimeAPIClient, error) {
	cli, err := hertz.NewClient(baseURL, hertz.NewHTTPClientOption{
		Timeout:             60 * time.Second,
		MaxIdleConnDuration: 1 * time.Minute,
		UseSD:               false,
		EnableStream:        true,
	})
	if err != nil {
		return nil, err
	}

	return &RuntimeAPIClient{
		baseURL:     baseURL,
		cli:         cli,
		logger:      logger,
		sessionType: sessionType,
	}, nil
}

func (c *RuntimeAPIClient) ReportStatus(ctx context.Context, req *agentsphere.UpdateRuntimeStateRequest) error {
	reportPath := fmt.Sprintf("/runtime/sessions/%s/state", req.SessionID)
	var res agentsphere.UpdateRuntimeStateResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, reportPath, hertz.ReqOption{
		Body:            req,
		Result:          &res,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to report status: %v", err)
	} else {
		c.logger.Infof("reported status: %+v, response: %v", req, res)
	}
	return err
}

func (c *RuntimeAPIClient) RequireConnection(ctx context.Context, req entity.RequireConnectionRequest) error {
	connectPath := fmt.Sprintf("/runtime/sessions/%s/connect", req.SessionID)
	var res entity.RequireConnectionResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, connectPath, hertz.ReqOption{
		Body:            req,
		Result:          &res,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	return err
}

func (c *RuntimeAPIClient) GetArtifact(ctx context.Context, req *agentsphere.GetArtifactRequest) (*agentsphere.GetArtifactResponse, error) {
	var resp agentsphere.GetArtifactResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodGet, fmt.Sprintf("/internal/artifacts/%s", req.ID), hertz.ReqOption{
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to get artifact[id=%s]: %v", req.ID, err)
	}

	return &resp, err
}

// Deprecated: it's not used in agent.
func (c *RuntimeAPIClient) ListArtifacts(ctx context.Context, req *agentsphere.ListArtifactsRequest) (*agentsphere.ListArtifactsResponse, error) {
	query := url.Values{}
	if req.AssignmentID != nil {
		query.Add("assignment_id", *req.AssignmentID)
	}
	if req.SessionID != nil {
		query.Add("session_id", *req.SessionID)
	}
	if req.ArtifactKey != nil {
		query.Add("key", *req.ArtifactKey)
	}
	var resp agentsphere.ListArtifactsResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodGet, fmt.Sprintf("/internal/artifacts?%s", query.Encode()), hertz.ReqOption{
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	return &resp, err
}

func (c *RuntimeAPIClient) ListArtifactsV2(ctx context.Context, req *nextagent.ListArtifactsRequest) (*nextagent.ListArtifactsResponse, error) {
	query := url.Values{}
	if req.ReplayID != nil {
		query.Add("replay_id", *req.ReplayID)
	}
	if req.SessionID != nil {
		query.Add("session_id", *req.SessionID)
	}
	if req.Display != nil {
		query.Add("display", strconv.FormatBool(*req.Display))
	}
	var resp nextagent.ListArtifactsResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodGet, "/internal/artifacts/list?"+query.Encode(), hertz.ReqOption{
		Result:          &resp,
		Body:            req,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	return &resp, err
}

func (c *RuntimeAPIClient) CreateArtifact(ctx context.Context, req *agentsphere.CreateArtifactRequest) (*agentsphere.CreateArtifactResponse, error) {
	var resp agentsphere.CreateArtifactResponse
	req.SessionType = &c.sessionType

	err := backoff.Retry(func() error {
		resp, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, "/internal/artifacts", hertz.ReqOption{
			Body:            req,
			Result:          &resp,
			ExpectedCode:    http.StatusOK,
			SetTTEnvHeaders: true,
		})
		return errors.WithMessagef(err, "failed to create artifact, status code: %d, error: %v", resp.StatusCode(), err)
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5))

	return &resp, err
}

func (c *RuntimeAPIClient) UpdateArtifact(ctx context.Context, req *agentsphere.UpdateArtifactRequest) (*agentsphere.UpdateArtifactResponse, error) {
	var resp agentsphere.UpdateArtifactResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPut, fmt.Sprintf("/internal/artifacts/%s", req.ID), hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to update artifact: %v", err)
	}

	return &resp, err
}

func (c *RuntimeAPIClient) UploadArtifactFile(ctx context.Context, req *agentsphere.UploadArtifactRequest) (*agentsphere.UploadArtifactResponse, error) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	defer w.Close()

	err := w.WriteField("path", req.Path)
	if err != nil {
		return nil, err
	}
	fw, err := w.CreateFormFile("content", req.Path)
	if err != nil {
		return nil, err
	}
	_, err = fw.Write(req.Content)
	if err != nil {
		return nil, err
	}

	err = w.Close()
	if err != nil {
		return nil, err
	}

	var resp agentsphere.UploadArtifactResponse
	err = backoff.Retry(func() error {
		resp, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, fmt.Sprintf("/internal/artifacts/%s/upload", req.ID), hertz.ReqOption{
			Headers:         map[string]string{"Content-Type": w.FormDataContentType()},
			Body:            b.Bytes(),
			Result:          &resp,
			ExpectedCode:    http.StatusOK,
			Timeout:         300 * time.Second,
			SetTTEnvHeaders: true,
		})
		return errors.WithMessagef(err, "failed to upload file, status code: %d, error: %v", resp.StatusCode(), err)
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5))
	if err != nil {
		return nil, err
	}

	return &resp, err
}

func (c *RuntimeAPIClient) UploadArtifactFileStream(ctx context.Context, req *agentsphere.UploadArtifactStreamRequest, r io.Reader) (*agentsphere.UploadArtifactResponse, error) {
	var resp agentsphere.UploadArtifactResponse
	// 512MB is the max size for uploading file stream considering potential OOM
	// we currently don't have such a scenario to upload a single file larger than 512MB
	// the error should be LLM friendly so it can notify the user
	if req.Size > 512*1024*1024 {
		return nil, fmt.Errorf("file size (%d bytes) is too large, max size is 512MB", req.Size)
	}
	// read the content before uploading so we can retry if the upload fails
	content, err := io.ReadAll(r)
	if err != nil {
		return nil, err
	}
	err = backoff.Retry(func() error {
		resp, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost,
			fmt.Sprintf("/internal/artifacts/%s/upload/stream?path=%s&size=%d", req.ID, req.Path, req.Size),
			hertz.ReqOption{
				Headers: map[string]string{
					"Content-Type":      "application/octet-stream",
					"Transfer-Encoding": "chunked",
				},
				Body:            bytes.NewReader(content),
				Result:          &resp,
				ExpectedCode:    http.StatusOK,
				Timeout:         300 * time.Second,
				SetTTEnvHeaders: true,
			})
		return errors.WithMessagef(err, "failed to upload file, status code: %d, error: %v", resp.StatusCode(), err)
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5))
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

func (c *RuntimeAPIClient) RetrieveArtifactFiles(ctx context.Context, req *agentsphere.RetrieveArtifactFilesRequest) (*agentsphere.RetrieveArtifactFilesResponse, error) {
	var resp agentsphere.RetrieveArtifactFilesResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, fmt.Sprintf("/internal/artifacts/%s/files", req.ID), hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, err
	}

	resp.Files = lo.Map(resp.Files, func(file *agentsphere.ArtifactFile, _ int) *agentsphere.ArtifactFile {
		content, err := base64.StdEncoding.DecodeString(file.Content)
		if err != nil {
			return file
		}
		return &agentsphere.ArtifactFile{
			Path:    lo.Ternary(file.Path == "", lo.FromPtr(file.Name), file.Path),
			Content: string(content),
		}
	})

	return &resp, err
}

type ArtifactFile struct {
	Path    string `json:"path"`
	Content []byte `json:"content"`
}

type UploadAgentArtifactOption struct {
	// if id is specified, will upload to existing artifact
	ID    string              `json:"id"`
	Type  entity.ArtifactType `json:"type"`
	Key   string              `json:"key"`
	Files []ArtifactFile      `json:"files"`
}

func (c *RuntimeAPIClient) UploadAgentArtifact(ctx context.Context, opt UploadAgentArtifactOption) error {
	artifactID := opt.ID
	if opt.ID == "" {
		res, err := c.CreateArtifact(c.injectKEnvToCtx(ctx), &agentsphere.CreateArtifactRequest{
			Type: string(opt.Type),
			Key:  opt.Key,
		})
		if err != nil {
			return err
		}
		artifactID = res.Artifact.ID
	}

	iter := iter.Iterator[ArtifactFile]{
		MaxGoroutines: 8,
	}

	errors := &multierror.Error{}
	mu := sync.Mutex{}
	iter.ForEach(opt.Files, func(file *ArtifactFile) {
		_, err := c.UploadArtifactFile(c.injectKEnvToCtx(ctx), &agentsphere.UploadArtifactRequest{
			ID:      artifactID,
			Path:    file.Path,
			Content: file.Content,
		})
		if len(file.Content) > 0 && err != nil {
			c.logger.Errorf("failed to upload file: %v", err)
			mu.Lock()
			multierror.Append(errors, err)
			mu.Unlock()
			return
		}
	})

	return errors.ErrorOrNil()
}

type ArtifactFileReader struct {
	Path   string    `json:"path"`
	Reader io.Reader `json:"content"`
	Size   int64     `json:"size"`
}

type UploadAgentArtifactStreamOption struct {
	// if id is specified, will upload to existing artifact
	ID    string               `json:"id"`
	Type  entity.ArtifactType  `json:"type"`
	Key   string               `json:"key"`
	Files []ArtifactFileReader `json:"files"`
}

func (c *RuntimeAPIClient) UploadAgentArtifactStream(ctx context.Context, opt UploadAgentArtifactStreamOption) error {
	artifactID := opt.ID
	if opt.ID == "" {
		res, err := c.CreateArtifact(c.injectKEnvToCtx(ctx), &agentsphere.CreateArtifactRequest{
			Type: string(opt.Type),
			Key:  opt.Key,
		})
		if err != nil {
			return err
		}
		artifactID = res.Artifact.ID
	}

	iter := iter.Iterator[ArtifactFileReader]{
		MaxGoroutines: 8,
	}

	errors := &multierror.Error{}
	mu := sync.Mutex{}
	iter.ForEach(opt.Files, func(file *ArtifactFileReader) {
		_, err := c.UploadArtifactFileStream(c.injectKEnvToCtx(ctx), &agentsphere.UploadArtifactStreamRequest{
			ID:   artifactID,
			Path: file.Path,
			Size: file.Size,
		}, file.Reader)
		if file.Size > 0 && err != nil {
			c.logger.Errorf("failed to upload file stream: %v", err)
			mu.Lock()
			multierror.Append(errors, err)
			mu.Unlock()
			return
		}
	})

	return errors.ErrorOrNil()
}

// CreateNextServerDeployment 创建部署
// /api/agents/v2/internal/deployments
func (c *RuntimeAPIClient) CreateNextServerDeployment(ctx context.Context, req nextagent.CreateDeploymentRequest) (*nextagent.CreateDeploymentResponse, error) {
	var resp nextagent.CreateDeploymentResponse

	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, "/internal/deployments", hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		Timeout:         10 * time.Minute,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to create deployment: %v", err)
		return nil, err
	}

	return &resp, err
}

func (c *RuntimeAPIClient) GetPrompt(ctx context.Context, req *nextagent.DownloadPromptVersionRequest) (*nextagent.DownloadPromptVersionResponse, error) {
	var resp nextagent.DownloadPromptVersionResponse
	query := url.Values{}
	if req.PromptID != nil {
		query.Add("prompt_id", *req.PromptID)
	}
	if req.Version != nil {
		query.Add("version", strconv.Itoa(int(*req.Version)))
	}

	err := backoff.Retry(func() error {
		_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodGet, fmt.Sprintf("/internal/prompt/version/download?%s", query.Encode()), hertz.ReqOption{
			Result:          &resp,
			ExpectedCode:    http.StatusOK,
			SetTTEnvHeaders: true,
		})
		if err != nil {
			c.logger.Errorf("failed to get prompt: %v", err)
			return err
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5))

	if err != nil {
		return nil, err
	}

	return &resp, nil
}

const ctxKeyENV = "K_ENV"

func (c *RuntimeAPIClient) injectKEnvToCtx(ctx context.Context) context.Context {
	_, ok := ctxvalues.Env(ctx)
	if ok {
		return ctx
	}
	env := os.Getenv(entity.RuntimeTCEEnv)
	if env != "" {
		ctx = context.WithValue(ctx, ctxKeyENV, env)
	}
	return ctx
}

type UploadTemplateExperienceFileRequest struct {
	TemplateID string
	Path       string
	Size       int64
	FileReader io.Reader
}

type UploadTemplateExperienceFileResult struct {
	File TemplateExperienceFile `json:"file"`
}

type TemplateExperienceFile struct {
	FileID     string `json:"file_id"`
	TemplateID string `json:"template_id"`
	Name       string `json:"name"`
	Type       string `json:"type"`
}

// 上传模版经验中用到的文件
func (c *RuntimeAPIClient) UploadTemplateExperienceFile(ctx context.Context, opt UploadTemplateExperienceFileRequest) (*UploadTemplateExperienceFileResult, error) {
	res := new(UploadTemplateExperienceFileResult)
	_, err := c.cli.DoJSONReq(
		c.injectKEnvToCtx(ctx), http.MethodPost,
		fmt.Sprintf("/templates/%s/upload/stream?path=%s&size=%d",
			url.PathEscape(opt.TemplateID), url.PathEscape(opt.Path), opt.Size),
		hertz.ReqOption{
			ExpectedCode:    http.StatusOK,
			Body:            opt.FileReader,
			Result:          res,
			Notes:           "upload_template_experience_file",
			SetTTEnvHeaders: true,
		})
	if err != nil {
		return nil, err
	}
	return res, nil
}

type DownloadTemplateExperienceFileStreamRequest struct {
	FileID string `json:"file_id"`
}

type DownloadTemplateExperienceFileStreamResult struct {
	FileReader io.Reader
}

// 下载模版经验中用到的文件
func (c *RuntimeAPIClient) DownloadTemplateExperienceFileStream(ctx context.Context, opt DownloadTemplateExperienceFileStreamRequest) (io.ReadCloser, error) {
	var res io.ReadCloser
	_, err := c.cli.DoJSONReq(
		c.injectKEnvToCtx(ctx), http.MethodGet,
		fmt.Sprintf("/templates/file/%s/raw", opt.FileID),
		hertz.ReqOption{
			ExpectedCode:    http.StatusOK,
			Body:            nil,
			Result:          &res,
			Notes:           "download_template_experience_file_stream",
			SetTTEnvHeaders: true,
		})
	if err != nil {
		return nil, err
	}
	return res, nil
}

type UpdateTemplateExperienceOption struct {
	// path param.
	TemplateID string `json:"-"`

	Status int64   `json:"status"`
	Error  *string `json:"error"`

	ProgressPlan *string `json:"progress_plan"`
	ExpSOP       *string `json:"exp_sop"`
}

func (c *RuntimeAPIClient) UpdateTemplateExperience(ctx context.Context, opt UpdateTemplateExperienceOption) error {
	_, err := c.cli.DoJSONReq(
		c.injectKEnvToCtx(ctx), http.MethodPut,
		fmt.Sprintf("/templates/%s/experience", opt.TemplateID),
		hertz.ReqOption{
			ExpectedCode:    http.StatusOK,
			Body:            opt,
			Result:          nil,
			Notes:           "update_template_experience",
			SetTTEnvHeaders: true,
		})
	if err != nil {
		return err
	}
	return nil
}

func (c *RuntimeAPIClient) RefreshToken(ctx context.Context, req entity.RefreshTokenRequest) (*entity.RefreshTokenResponse, error) {
	var res entity.RefreshTokenResponse
	_, err := c.cli.DoJSONReq(
		c.injectKEnvToCtx(ctx), http.MethodPost,
		"/runtime/token/refresh",
		hertz.ReqOption{
			ExpectedCode:    http.StatusOK,
			Body:            req,
			Result:          &res,
			Notes:           "refresh_codebase_user_jwt",
			SetTTEnvHeaders: true,
		})
	if err != nil {
		return nil, err
	}
	return &res, nil
}

func (c *RuntimeAPIClient) RecallDataset(ctx context.Context, req *nextagent.RecallDatasetRequest) (*nextagent.RecallDatasetResponse, error) {
	var resp nextagent.RecallDatasetResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, fmt.Sprintf("/internal/datasets/%s/recall", req.DatasetID), hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to recall dataset: %v", err)
		return nil, err
	}

	return &resp, err
}

func (c *RuntimeAPIClient) GetDatasetDocument(ctx context.Context, req *nextagent.GetDocumentRequest) (*nextagent.GetDocumentResponse, error) {
	var resp nextagent.GetDocumentResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodGet, fmt.Sprintf("/internal/datasets/%s/documents/%s", req.DatasetID, req.DocumentID), hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to get document from dataset: %v", err)
		return nil, err
	}

	return &resp, err
}

func (c *RuntimeAPIClient) ListKnowledge(ctx context.Context, req *nextagent.ListKnowledgeRequest) (*nextagent.ListKnowledgeResponse, error) {
	var resp nextagent.ListKnowledgeResponse

	path := fmt.Sprintf("/internal/knowledge?knowledge_set_version_id=%s", req.KnowledgesetVersionID)
	if req.Tag != nil {
		path = fmt.Sprintf("%s&tag=%s", path, *req.Tag)
	}
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodGet, path, hertz.ReqOption{
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to create deployment: %v", err)
		return nil, err
	}

	return &resp, err
}

func (c *RuntimeAPIClient) GetKnowledgeSetInfo(run *iris.AgentRunContext, knowledgeSetIds []string) (*nextagent.ListKnowledgesetResponse, error) {
	var resp nextagent.ListKnowledgesetResponse
	_, err := c.cli.DoJSONReq(run, http.MethodGet, fmt.Sprintf("/internal/knowledgeset?ids=%s", strings.Join(knowledgeSetIds, ",")), hertz.ReqOption{
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to create deployment: %v", err)
		return nil, err
	}
	return &resp, err
}

func (c *RuntimeAPIClient) IngestTraces(ctx context.Context, req *agentsphere.IngestTracesRequest) error {
	return backoff.Retry(func() error {
		_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, "/runtime/traces/ingest", hertz.ReqOption{
			Body:            req,
			ExpectedCode:    http.StatusOK,
			SetTTEnvHeaders: true,
		})
		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 5))
}

func (c *RuntimeAPIClient) BindAgent(ctx context.Context, req *entity.BindAgentRequest) (string, error) {
	type bindAgentResponse struct {
		AgentRunID string `json:"agent_run_id"`
	}
	result := &bindAgentResponse{}
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, "/runtime/bind", hertz.ReqOption{
		Body:            req,
		ExpectedCode:    http.StatusOK,
		Result:          result,
		SetTTEnvHeaders: true,
	})
	if err != nil {
		c.logger.Errorf("failed to bind agent: %v", err)
		return "", err
	}
	return result.AgentRunID, nil
}
func (c *RuntimeAPIClient) GetRunAgentContext(ctx context.Context, options ...Option) (*entity.RunAgentRequest, error) {
	result := &entity.RunAgentRequest{}
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodGet, "/runtime/run_agent_context", resolveOptions(hertz.ReqOption{
		ExpectedCode:    http.StatusOK,
		Result:          result,
		SetTTEnvHeaders: true,
	}, options...))
	if err != nil {
		c.logger.Errorf("failed to bind agent: %v", err)
		return nil, err
	}
	return result, nil
}
func (c *RuntimeAPIClient) CreateSession(ctx context.Context, req *nextagent.CreateSessionRequest, token string) (*nextagent.CreateSessionResponse, error) {
	var resp nextagent.CreateSessionResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, "/sessions", hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
		Headers: map[string]string{
			"Authorization": token,
		},
	})
	if err != nil {
		c.logger.Errorf("failed to create session: %v", err)
		return nil, err
	}
	return &resp, err
}

type Option func(option *hertz.ReqOption)

func WithTokenOption(token string) Option {
	return func(option *hertz.ReqOption) {
		if option.Headers == nil {
			option.Headers = map[string]string{}
		}
		option.Headers["Authorization"] = token
	}
}

func WithTesting() Option {
	return func(option *hertz.ReqOption) {
		if option.Headers == nil {
			option.Headers = map[string]string{}
		}
		option.Headers["x-testing"] = "1"
	}
}

func resolveOptions(option hertz.ReqOption, options ...Option) hertz.ReqOption {
	for _, opt := range options {
		opt(&option)
	}
	return option
}

func (c *RuntimeAPIClient) CreateMessage(ctx context.Context, req *nextagent.CreateMessageRequest, options ...Option) (*nextagent.CreateMessageResponse, error) {
	var resp nextagent.CreateMessageResponse
	option := hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
	}
	option = resolveOptions(option, options...)
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodPost, fmt.Sprintf("/sessions/%s/message", req.SessionID), option)
	if err != nil {
		c.logger.Errorf("failed to create message: %v", err)
		return nil, err
	}
	return &resp, err
}

func (c *RuntimeAPIClient) GetSession(ctx context.Context, req *nextagent.GetSessionRequest, token string) (*nextagent.GetSessionResponse, error) {
	var resp nextagent.GetSessionResponse
	_, err := c.cli.DoJSONReq(c.injectKEnvToCtx(ctx), http.MethodGet, fmt.Sprintf("/sessions/%s", req.SessionID), hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
		Headers: map[string]string{
			"Authorization": token,
		},
	})
	if err != nil {
		c.logger.Errorf("failed to get session: %v", err)
		return nil, err
	}
	return &resp, err
}

func (c *RuntimeAPIClient) UpdateSession(ctx context.Context, req *nextagent.UpdateSessionRequest, token string) (*nextagent.UpdateSessionResponse, error) {
	var resp nextagent.UpdateSessionResponse
	_, err := c.cli.DoJSONReq(ctx, http.MethodPatch, fmt.Sprintf("/sessions/%s", req.SessionID), hertz.ReqOption{
		Body:            req,
		Result:          &resp,
		ExpectedCode:    http.StatusOK,
		SetTTEnvHeaders: true,
		Headers: map[string]string{
			"Authorization": token,
		},
	})
	if err != nil {
		c.logger.Errorf("failed to get session: %v", err)
		return nil, err
	}
	return &resp, err
}
