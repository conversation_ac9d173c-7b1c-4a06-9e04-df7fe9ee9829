package runtime

import (
	"bytes"
	"context"
	"fmt"
	"net/url"
	"runtime/debug"
	"slices"
	"time"

	"sync"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/client"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"github.com/samber/lo"
)

const (
	testCaseStatusRunning = "running"
	testCaseCompleted     = "completed"
	testCaseQueued        = "queued"
)

func (r *RemoteRuntime) startTestQueueWorker() {
	r.queueOnce.Do(func() {
		r.queueCond = sync.NewCond(&r.queueLock)
		go func() {
			for {
				r.queueLock.Lock()
				for len(r.testQueue) == 0 {
					r.queueCond.Wait()
				}
				task := r.testQueue[0]
				r.testQueue = r.testQueue[1:]
				r.queueLock.Unlock()
				executionTest := func(c *TestingContext, execution func(_ Context)) {
					c.record.Status = testCaseStatusRunning
					c.record.StartTime = time.Now()
					stopChan := make(chan struct{})
					var panicError string
					go func() {
						defer func() {
							if recoverErr := recover(); recoverErr != nil {
								panicError = fmt.Sprintf("[UnitTest] Test case %s panic: %v, stack: %v", c.c.name(), recoverErr, string(debug.Stack()))
								r.logger.Errorf("[UnitTest] Test case %s panic: %v, stack: %v", c.c.name(), recoverErr, string(debug.Stack()))
							}
							close(stopChan)
						}()
						cloudToken, err := r.cloudOAuthClient.RefreshJwtToken(c, task.cloudToken)
						if err != nil {
							c.Errorf("refresh cloud token failed: %v", err)
							return
						}
						c.token = "Byte-Cloud-JWT " + cloudToken
						agentContext, err := r.api.GetRunAgentContext(c, client.WithTokenOption(c.token))
						if err != nil {
							c.Errorf("get agent context failed: %v", err)
							return
						}
						c.request = *agentContext
						execution(c)
					}()
					<-stopChan
					c.record.lock.Lock()
					c.record.Status = testCaseCompleted
					c.record.EndTime = time.Now()
					if len(panicError) > 0 {
						c.record.Fail = true
						c.record.Output.WriteString(panicError)
						r.logger.Errorf("[UnitTest] Test case %s failed: %v", c.c.name(), panicError)
					}
					c.record.lock.Unlock()
				}
				executionTest(&TestingContext{
					Context:       context.Background(),
					remoteRuntime: r,
					c:             task.caseObj,
					api:           r.api,
					logger:        r.logger,
					uri:           r.ControllerBusURI(),
					record:        task.record,
				}, task.caseObj.run)
			}
		}()
	})
}

func (r *RemoteRuntime) ListTestCases(_ context.Context, _ entity.ListTestCaseRequest) (entity.ListTestCaseResponse, error) {
	r.logger.Info("list test cases")
	cases := getCases()
	return entity.ListTestCaseResponse{
		Cases: lo.Map(cases, func(item testCase, _ int) *entity.Case {
			return &entity.Case{
				Name:        item.name(),
				Description: item.description(),
				Type:        item.caseType(),
			}
		}),
	}, nil
}

func (r *RemoteRuntime) RunTestCases(_ context.Context, p entity.RunTestCaseRequest) (entity.RunTestCaseResponse, error) {
	r.logger.Info("run test cases: ", p.Cases)
	allCases := getCases()
	caseMap := make(map[string]testCase)
	for _, c := range allCases {
		caseMap[c.name()] = c
	}

	var selected []testCase
	for _, name := range p.Cases {
		c, ok := caseMap[name]
		if !ok {
			r.logger.Warnf("Test case not found: %s", name)
			continue
		}
		selected = append(selected, c)
	}
	if len(selected) == 0 {
		return entity.RunTestCaseResponse{}, nil
	}
	r.startTestQueueWorker()
	r.queueLock.Lock()
	for _, c := range selected {
		record := &TestCaseRecord{
			Name:      c.name(),
			StartTime: time.Time{},
			EndTime:   time.Time{},
			Status:    testCaseQueued,
			Fail:      false,
			Output:    bytes.Buffer{},
			lock:      &sync.Mutex{},
		}
		r.testRecords = append(r.testRecords, record)
		r.testQueue = append(r.testQueue, &testTask{
			record:     record,
			caseObj:    c,
			cloudToken: p.Token,
			request:    p.Request,
		})
	}
	r.queueCond.Broadcast()
	r.queueLock.Unlock()

	return entity.RunTestCaseResponse{}, nil
}

func (r *RemoteRuntime) GetTestExecutions(ctx context.Context, p entity.GetTestExecutionsRequest) (entity.GetTestExecutionsResponse, error) {
	r.logger.Info("get test executions")
	r.queueLock.Lock()
	parse, err := url.Parse(r.config.APIBaseURL)
	var serverHostURL = "https://aime-boe.bytedance.net/chat/"
	if err == nil && parse.Host == "aime.bytedance.net" {
		serverHostURL = "https://aime.bytedance.net/chat/"
	}
	execs := make([]entity.TestCaseExecution, 0, len(r.testRecords))
	for _, rec := range r.testRecords {
		sessionURL := ""
		if rec.SessionID != "" {
			sessionURL = serverHostURL + rec.SessionID
		}
		execs = append(execs, entity.TestCaseExecution{
			Name:       rec.Name,
			StartTime:  rec.StartTime,
			EndTime:    rec.EndTime,
			Status:     rec.Status,
			Failed:     rec.Fail,
			Output:     rec.Output.String(),
			SessionURL: sessionURL,
		})
	}
	r.queueLock.Unlock()
	slices.Reverse(execs)
	return entity.GetTestExecutionsResponse{Executions: execs}, nil
}
