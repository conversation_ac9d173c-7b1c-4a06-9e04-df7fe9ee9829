package runtime

import (
	"bytes"
	"sync"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"github.com/sirupsen/logrus"
)

func TestTestingContext_Helper(t *testing.T) {
	c := &TestingContext{
		request: entity.RunAgentRequest{},
		logger:  logrus.StandardLogger(),
		c: &E2ECase{
			Name: "test",
		},
		record: &TestCaseRecord{
			Output: bytes.Buffer{},
			lock:   &sync.Mutex{},
		},
	}
	Helper(c)
	logContent := c.record.Output.String()
	t.Logf("\n%s", logContent)
}

func Helper(c *TestingContext) {
	c.Helper()
	c.Logf("testing helper")
	c.Log("testing helper")
	c.<PERSON>("tesing helper")
}
