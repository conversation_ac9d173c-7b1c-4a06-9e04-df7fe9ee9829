package runtime

import (
	"bytes"
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"html"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/jwt_manager"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/context_storage"
	irisimpl "code.byted.org/devgpt/kiwis/agentsphere/agents/iris/impl"
	remotetool "code.byted.org/devgpt/kiwis/agentsphere/agents/iris/remote_tool"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/client"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/trace"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	remotebus "code.byted.org/devgpt/kiwis/agentsphere/runtime/eventbus/remote"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/cloud_oauth"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/signal"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	customopenai "code.byted.org/devgpt/kiwis/port/openai"

	bt "code.byted.org/bytedtrace/interface-go"
	codfishmodel "code.byted.org/codebase/codfish/core/model"
	codfish "code.byted.org/codebase/codfish/core/sdk"
	sdk "code.byted.org/codebase/codfish/sdk/peripheral"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logid"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/conc/panics"
	"github.com/sourcegraph/jsonrpc2"
	"gopkg.in/yaml.v3"
)

// AgentRun is the runtime representation for an agent run.
// It is used to manage the run context and state.
type AgentRun struct {
	Bus                iris.EventBus
	Ctx                *iris.AgentRunContext
	State              *iris.AgentRunState
	Publisher          *iris.AgentEventPublisher
	RemoteToolExecutor *remotetool.RemoteToolExecutor

	Context  context.Context
	cancel   func() // cancel context
	canceled atomic.Bool
}

type AgentRuntime interface {
	Run(ctx context.Context, opt iris.AgentRunOption) error
}

type RemoteRuntime struct {
	config           entity.RuntimeDaemonConfig
	addr             string // ip:port
	bus              string // controller bus id
	codfish          *codfish.PeripheralClient
	api              *client.RuntimeAPIClient
	artifactService  *ArtifactService
	logger           *logrus.Logger
	cloudOAuthClient *cloud_oauth.OAuthClient // 字节云OAuth客户端
	tools            map[string]iris.Action
	agents           map[string]iris.ConfigurableAgent
	runs             map[string]*AgentRun
	serverConns      map[string]*remotebus.RemoteBus
	abortedConns     map[string][]*remotebus.RemoteBus
	mu               sync.RWMutex
	uuid             uuid.Generator
	ctxStorage       iris.ContextStorage
	// For Local Testing.
	testRecords []*TestCaseRecord
	testQueue   []*testTask
	queueLock   sync.Mutex
	queueCond   *sync.Cond
	queueOnce   sync.Once
}

var _ AgentRuntime = &RemoteRuntime{}

type RuntimeOption struct {
	Addr             string
	Workspace        string
	SessionID        string
	Config           entity.RuntimeDaemonConfig
	API              *client.RuntimeAPIClient
	Logger           *logrus.Logger
	CloudOAuthClient *cloud_oauth.OAuthClient // 字节云OAuth客户端
}

func NewRemoteRuntime(opt RuntimeOption) (*RemoteRuntime, error) {
	cli, err := sdk.NewClient(sdk.InitOptions{
		Name:      entity.RuntimeClientName,
		Role:      codfishmodel.RoleUserPeripheral,
		RPC:       sdk.RPCWS,
		Endpoint:  lo.Ternary(opt.Addr != "", codfish.ToEndpointURL(opt.Addr), ""),
		Workspace: lo.Ternary(opt.Workspace == "", os.TempDir(), opt.Workspace),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create runtime")
	}
	id, err := cli.CreateSession()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create eventbus for controller")
	}
	cli.Session = id
	idGen := uuid.GetDefaultGenerator(nil)

	// TODO: artifact service should be recreated per request as multiple runs may dispatch to this workspace
	artifactService := NewArtifactService(opt.API, lo.Ternary(opt.Config.Debug, "", opt.SessionID))
	runtime := &RemoteRuntime{
		addr:             opt.Addr,
		bus:              id,
		codfish:          cli,
		logger:           opt.Logger,
		config:           opt.Config,
		artifactService:  artifactService,
		tools:            make(map[string]iris.Action),
		agents:           make(map[string]iris.ConfigurableAgent),
		runs:             make(map[string]*AgentRun),
		serverConns:      make(map[string]*remotebus.RemoteBus),
		abortedConns:     make(map[string][]*remotebus.RemoteBus),
		uuid:             idGen,
		api:              opt.API,
		ctxStorage:       context_storage.NewContextStorage(context_storage.DefaultStorageDir, opt.Logger),
		cloudOAuthClient: opt.CloudOAuthClient,
	}
	runtime.Init()

	return runtime, nil
}

func (r *RemoteRuntime) Init() {
	// TODO: migrate all places that create files with os.CreateFile
	// currently make all files created by runtime to be readable by all users
	// except for log files
	if runtime.GOOS == "linux" {
		syscall.Umask(0)
	}
	r.logger.SetFormatter(&client.RuntimeLogFormatter{})
	r.logger.SetReportCaller(true)
	r.logger.AddHook(client.NewArgosLogHook(logrus.InfoLevel, ""))
	// for debug and eval purposes, log content directly to runtime
	r.codfish.RouteSessionAPISync(iris.AgentRunEventLogTrace.String(), r.handleLogFn(logrus.TraceLevel))
	r.codfish.RouteSessionAPISync(iris.AgentRunEventLogDebug.String(), r.handleLogFn(logrus.DebugLevel))
	r.codfish.RouteSessionAPISync(iris.AgentRunEventLogInfo.String(), r.handleLogFn(logrus.InfoLevel))
	r.codfish.RouteSessionAPISync(iris.AgentRunEventLogWarning.String(), r.handleLogFn(logrus.WarnLevel))
	r.codfish.RouteSessionAPISync(iris.AgentRunEventLogError.String(), r.handleLogFn(logrus.ErrorLevel))
	r.codfish.RouteSessionAPISync(iris.AgentRunEventLogFatal.String(), r.handleLogFn(logrus.FatalLevel))
	r.codfish.RouteSessionAPI(codfishmodel.SessionOutput, codfish.ToRPCHandler(r.handleOutput))
	r.codfish.RouteSessionAPISync(entity.RPCAgentEvent.String(), func(ctx context.Context, conn *jsonrpc2.Conn, req *jsonrpc2.Request) (interface{}, error) {
		var event iris.AgentRunEvent[string]
		err := json.Unmarshal(*req.Params, &event)
		if err != nil {
			r.logger.Errorf("failed to decode event: %s", string(*req.Params))
			return nil, nil
		}
		switch event.Event {
		case iris.AgentRunEventLogTrace:
			r.logger.Trace(event.Data)
		case iris.AgentRunEventLogDebug:
			r.logger.Debug(event.Data)
		case iris.AgentRunEventLogInfo:
			r.logger.Info(event.Data)
		case iris.AgentRunEventLogWarning:
			r.logger.Warn(event.Data)
		case iris.AgentRunEventLogError:
			r.logger.Error(event.Data)
		case iris.AgentRunEventLogFatal:
			r.logger.Fatal(event.Data)
		}
		return nil, nil
	})

	r.codfish.RouteSessionAPI(entity.RPCRunAgent.String(), codfish.CtxToRPCHandler(r.HandleRunAgent))
	r.codfish.RouteSessionAPI(entity.RPCAbortAgent.String(), codfish.CtxToRPCHandler(r.AbortAgent))
	r.codfish.RouteSessionAPI(entity.RPCPauseAgent.String(), codfish.CtxToRPCHandler(r.PauseAgent))
	r.codfish.RouteSessionAPI(entity.RPCAgentSendMessage.String(), codfish.CtxToRPCHandler(r.AddMessage))
	r.codfish.RouteSessionAPISync(entity.RPCSubmitToolCallResults.String(), codfish.CtxToRPCHandler(r.SubmitToolCallResults))
	r.codfish.RouteSessionAPI(entity.RPCGetAgentEventStream.String(), codfish.CtxToRPCHandler(r.GetAgentEventStream))
	r.codfish.RouteSessionAPI(entity.RPCRetrieveEvents.String(), codfish.CtxToRPCHandler(r.RetrieveEvents))
	r.codfish.RouteSessionAPI(entity.RPCSyncPatchArtifact.String(), codfish.CtxToRPCHandler(r.SyncPatchArtifact))
	r.codfish.RouteSessionAPISync(entity.RPCAgentGenerateExperience.String(), codfish.CtxToRPCHandler(r.GenerateExperience))
	r.codfish.RouteSessionAPISync(entity.RPCPing.String(), codfish.CtxToRPCHandler(r.Ping))
	r.codfish.RouteSessionAPISync(entity.RPCTestListCases.String(), codfish.CtxToRPCHandler(r.ListTestCases))
	r.codfish.RouteSessionAPISync(entity.RPCTestRunCases.String(), codfish.CtxToRPCHandler(r.RunTestCases))
	r.codfish.RouteSessionAPISync(entity.RPCTestGetExecutions.String(), codfish.CtxToRPCHandler(r.GetTestExecutions))
	// health check server connections in production, disable in eval mode
	if !r.config.Debug {
		go r.keepalive()
	}
}

func (r *RemoteRuntime) Ping(ctx context.Context, opts entity.PingRequest) (res *entity.PingResponse, err error) {
	now := time.Now()
	diff := now.Sub(opts.Timestamp)
	return &entity.PingResponse{
		Message: fmt.Sprintf("pong, diff: %s", diff.String()),
	}, nil
}

func (r *RemoteRuntime) RegisterAgent(agent iris.ConfigurableAgent) {
	r.agents[agent.Name()] = agent
}

func (r *RemoteRuntime) RegisterAction(action iris.Action) {
	r.tools[action.Name()] = action
}

// Run implements AgentRuntime.
func (r *RemoteRuntime) Run(ctx context.Context, opt iris.AgentRunOption) error {
	return nil
}

func (r *RemoteRuntime) Ready() error {
	r.codfish.Ready()
	r.logger.Infof("runtime listening on %s", r.ControllerBusURI())
	return nil
}

func (r *RemoteRuntime) CreateRunContext(ctx context.Context, opts entity.RunAgentRequest) (run *iris.AgentRunContext, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("failed to create run context: %v, trace: %s", e, string(debug.Stack()))
			r.logger.Errorf("panic in CreateRunContext: %v, trace: %s", e, string(debug.Stack()))
		}
	}()
	agent, ok := r.agents[opts.AgentName]
	if !ok {
		r.logger.Errorf("agent %s not found, registered agents: %v", opts.AgentName, lo.Keys(r.agents))
		return nil, fmt.Errorf("agent %s not found", opts.AgentName)
	}
	runConfig := getAgentRunConfig(opts.AgentRunConfig, opts.Config)
	err = agent.ValidateConfig(runConfig)
	if err != nil {
		return nil, errors.WithMessage(err, "invalid agent run config")
	}
	promptLoader := prompt.NewHTTPPromptLoader(func(ctx context.Context, promptID string, version int) (string, error) {
		prompt, err := r.api.GetPrompt(ctx, &nextagent.DownloadPromptVersionRequest{
			PromptID: lo.ToPtr(promptID),
			Version:  lo.ToPtr(int32(version)),
		})
		if err != nil {
			return "", err
		}
		if prompt.PromptVersion == nil {
			return "", fmt.Errorf("prompt %s version %d not found", promptID, version)
		}
		return lo.FromPtr(prompt.PromptVersion.Content), nil
	}, lo.TernaryF(opts.AgentRunConfig != nil, func() *entity.PromptConfig {
		return opts.AgentRunConfig.PromptConfig
	}, func() *entity.PromptConfig {
		return &entity.PromptConfig{}
	}))
	knowledgeConfig := lo.TernaryF(opts.AgentRunConfig != nil, func() *entity.KnowledgesetConfig { return opts.AgentRunConfig.KnowledgesetConfig }, func() *entity.KnowledgesetConfig { return &entity.KnowledgesetConfig{} })
	if opts.Parameters == nil {
		opts.Parameters = map[entity.RuntimeParameterKey]any{}
	}
	opts.Parameters[entity.RuntimeParametersKnowledgeSetConfig] = knowledgeConfig

	ctx, cancel := context.WithCancel(ctx)
	// trace span has set log id, no need to set again
	if ctxvalues.LogIDDefault(ctx) == "" {
		ctx = ctxvalues.SetLogID(ctx, logid.GenLogID())
	}
	env := os.Getenv(entity.RuntimeTCEEnv)
	if env != "" {
		ctx = context.WithValue(ctx, "K_ENV", env)
	}

	conversation := iris.NewConversation()
	if opts.Input != nil && !opts.Restart {
		msg := &iris.Message{
			ID:            opts.Input.ID,
			Content:       opts.Input.Content.Content,
			From:          iris.MessageFromUser,
			CreatedAt:     time.Now(),
			StructContent: map[string]any{},
			Attachments: lo.Map(opts.Input.Attachments, func(attachment entity.AttachmentMeta, _ int) iris.Attachment {
				return iris.Attachment{
					ArtifactID: attachment.ArtifactID,
					Path:       attachment.Filename,
				}
			}),
			Mentions: lo.FilterMap(opts.Input.Mentions, func(mention *entity.Mention, _ int) (iris.Mention, bool) {
				if mention == nil {
					return nil, false
				}
				m := iris.GetMention(*mention)
				if m == nil {
					r.logger.Errorf("unrecognized mention %+v", mention)
					return nil, false
				}
				return m, true
			}),
		}
		conversation.AddUserMessage(msg)
		// (syx): It should be dirty so the agent can recognize the new input
		// conversation.ClearDirty()
	}

	// create event bus for agent run
	bus, _ := remotebus.Connect(r.serverURI(), remotebus.WithName("publisher"))

	// these logs will be transported to server through event bus
	agentLogger := r.GetAgentLogger(opts.RunID, bus)
	eventLogger := r.GetEventLogger(opts.RunID, bus)
	publisher := iris.NewAgentEventPublisher(eventLogger, r.ctxStorage, opts.Restart)
	remoteToolExecutor := remotetool.NewRemoteToolExecutor(r.uuid, publisher, agentLogger)

	// intercept events and log in debug mode
	if r.config.Debug {
		r.codfish.ConnectSession(bus.ID())
		publisher.AddClient(bus)
	}
	bus.Conn().Ready()

	state := &iris.AgentRunState{
		AssignmentID: opts.AssignmentID,
		SessionID:    opts.SessionID,
		RunID:        opts.RunID,
		AgentID:      opts.AgentName,
		Agent:        agent,
		Status:       iris.AgentRunStatusCreated,
		Memory:       memory.NewAgentMemory(),
		Conversation: conversation,
		Signals:      signal.NewNotifier(),
		Variables:    make(map[string]any),
		Store:        make(map[string]any),
	}

	backgroundService := irisimpl.NewBackgroundServiceManager()
	llmToken := lo.Ternary(
		opts.Environ != nil && opts.Environ[entity.RuntimeEnvironLLMToken] != "",
		opts.Environ[entity.RuntimeEnvironLLMToken],
		os.Getenv(entity.RuntimeEnvironLLMToken),
	)
	llm, err := framework.NewCustomOpenAILLM(
		llmToken,
		fmt.Sprintf("%s/llmproxy", r.config.LLMBaseURL),
		func(ctx context.Context, req *customopenai.ChatCompletionsRequest, reqOpt *hertz.ReqOption) context.Context {
			agentLogger.Infof("llm service request with function call: %s", conv.JSONFormatString(req.Tools))
			reqOpt.Headers = lo.Assign(reqOpt.Headers, map[string]string{
				entity.SessionIDHeader:  opts.SessionID,
				framework.LLMTagKey:     conv.DefaultAny[string](req.ExtraOptions["tag"]),
				framework.LLMTraceIDKey: conv.DefaultAny[string](ctx.Value(framework.LLMTraceIDKey)),
			})
			reqOpt.SetTTEnvHeaders = true
			req.ExtraOptions["session_id"] = opts.SessionID
			return ctx
		},
	)
	if err != nil {
		cancel()
		return nil, errors.WithMessage(err, "failed to build LLM client")
	}

	// Create agent tracer.
	tracer := agentrace.NewAgentRuntimeTracer(
		agentrace.NewFileStoarge(r.getTraceFilePath(opts.RunID)),
		map[string]any{
			"run_id":     opts.RunID,
			"session_id": opts.SessionID,
			"agent_id":   opts.AgentName,
		},
	)
	ctx = agentrace.SetRuntimeTracerToContext(ctx, tracer)
	var spanOpts []bt.StartSpanOption
	if traceSpan := bt.GetSpanFromContext(ctx); traceSpan != nil {
		spanOpts = append(spanOpts, bt.FollowsFrom(traceSpan.GetContext()))
	}
	traceSpan, ctx := bt.StartCustomSpan(ctx, "agent_run", agent.Name(), spanOpts...)
	_, ctx = agentrace.StartRuntimeRootSpan(ctx, opts.RunID, "agent_run", agent.Name(), agentrace.WithSpanID(opts.RunID), agentrace.WithByteTraceSpan(&traceSpan))

	artifactService := NewArtifactService(r.api, opts.SessionID)
	environ := iris.NewRunEnviron()
	if opts.Environ != nil {
		environ.Replace(opts.Environ)
	}
	telemetryTracer := telemetry.NewRuntimeTracer(r.api, agentLogger)
	telemetry := telemetry.NewCollector(agentLogger, telemetry.NewCollectorOption{
		SessionID:  opts.SessionID,
		User:       opts.User.Username,
		Environ:    environ,
		Config:     &iris.AgentRunConfig{AgentRunConfig: *runConfig},
		Parameters: opts.Parameters,
	})

	run = iris.NewRunContext(ctx, bus, llm, agentLogger, state, opts.Parameters, environ, runConfig, promptLoader,
		publisher, r.uuid, remoteToolExecutor, backgroundService, artifactService, r.ctxStorage, NewAPIService(r.api), telemetry, telemetryTracer)

	agentRun := &AgentRun{
		Bus:                bus,
		Ctx:                run,
		State:              state,
		Publisher:          publisher,
		RemoteToolExecutor: remoteToolExecutor,
		Context:            ctx,
		cancel:             cancel,
	}
	r.mu.Lock()
	r.runs[opts.RunID] = agentRun
	r.mu.Unlock()

	if opts.Restart {
		// 先取出来，防止后续更新
		_, _, err := r.ctxStorage.Recover(ctx, iris.StorageTypeAgentRunContextStore)
		if err != nil {
			r.logger.Errorf("failed to recover agent run ctx store: %v", err)
		}
		oldRunContext, exist, err := r.ctxStorage.Recover(ctx, iris.StorageTypeAgentRunContext)
		if err != nil {
			r.logger.Errorf("failed to recover run context: %v", err)
		} else if !exist {
			r.logger.Errorf("run context not found")
		} else {
			c, ok := oldRunContext.(*iris.AgentRunContext)
			if !ok {
				r.logger.Errorf("assert old run context failed")
			} else {
				// 覆盖新的 agent context 数据
				run.RestartFromOldRunCtx(c)
				r.logger.Infof("success recover run context, old ctx has new message: %v, new ctx have new message: %v", c.State.Conversation.HasNewMessage(), run.State.Conversation.HasNewMessage())
			}
		}
	} else {
		err = r.initAgentMemory(run)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to init agent memory")
		}
	}

	err = r.requestConnection(ctx, opts.SessionID, opts.RunID)
	if err != nil && !r.config.Debug {
		return nil, err
	}

	err = r.initWorkspace(run)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to init workspace components")
	}

	run.User = opts.User
	run.UserInfo = lo.FromPtr(opts.UserInfo)

	return run, nil
}

func (r *RemoteRuntime) getTraceFilePath(runID string) string {
	return filepath.Join(r.config.LogDir, fmt.Sprintf("iris-trace-%s.jsonl", runID))
}

func (r *RemoteRuntime) HandleRunAgent(ctx context.Context, opts entity.RunAgentRequest) (res entity.RunAgentResponse, err error) {
	// start the agent in a goroutine and send the response ack to the server
	// so we can know if runtime really receives the request
	go panics.Try(func() {
		r.RunAgent(ctx, opts)
	})
	return entity.RunAgentResponse{
		Code: 0,
	}, nil
}

// RunAgent starts the agent or restarts the agent after hibernation
func (r *RemoteRuntime) RunAgent(ctx context.Context, opts entity.RunAgentRequest) (res any, err error) {
	defer func() {
		_ = metrics.AR.RunAgentThroughput.WithTags(&metrics.RunAgentTag{
			AgentName: opts.AgentName,
			Failed:    err != nil && !errors.Is(err, context.Canceled) && !errors.Is(err, context.DeadlineExceeded),
		}).Add(1)
	}()
	var meta *codfishmodel.RPCMeta
	if !opts.Restart {
		// 存储
		if meta, ok := ctx.Value(codfishmodel.CtxKeyRPCMeta).(*codfishmodel.RPCMeta); ok {
			err = r.ctxStorage.Store(ctx, iris.StorageTypeAgentRequestMeta, iris.StoreOption{RPCMeta: meta})
			if err != nil {
				r.logger.Errorf("failed store rpc meta: %v", err)
			}
		}
	} else {
		// 恢复
		reqMeta, exist, err := r.ctxStorage.Recover(ctx, iris.StorageTypeAgentRequestMeta)
		if err != nil {
			r.logger.Errorf("failed recover rpc meta: %v", err)
		} else if !exist {
			r.logger.Errorf("rpc meta not found")
		} else {
			nmeta, ok := reqMeta.(*codfishmodel.RPCMeta)
			if !ok {
				r.logger.Errorf("assert rpc meta failed")
			} else {
				meta = nmeta
			}
		}
	}
	ctx, span := trace.StartServerSpan(ctx, r.logger, entity.RPCRunAgent.String(), meta)
	// runtime 没有 boe 环境，因此都是上报到线上
	// 由于后面是 long running 运行模式，所以这里直接结束 span
	// 异步开启的 custom span 可以通过指定 option 来进行关联，参考文档 https://bytedance.larkoffice.com/wiki/wikcnbXE01npxIhrxX9zobKxOsh
	// 如果不指定，则会默认和 server span 一个层级，关联到统一 logid 上
	trace.FinishServerSpan(span, err)
	// 参考示例, 通过指定 FollowsFrom 完成父节点关联
	/* span 只有调用后 finish 才会上报，因此需要保证必须执行 finish, 关注 defer 的时机
	asyncSpan, ctx := bt.StartCustomSpan(ctx, "async", "test", bt.FollowsFrom(span.GetContext()))
	defer asyncSpan.Finish()
	*/

	wd, _ := os.Getwd()
	r.logger.Infof("run agent %s with run id %s", opts.AgentName, opts.RunID)
	r.logger.Infof("working directory: %s", wd)
	r.logger.Infof("config: %s", conv.JSONString(opts.AgentRunConfig))
	if opts.AgentRunConfig != nil && opts.AgentRunConfig.KnowledgesetConfig != nil {
		r.logger.Infof("knowledge set config: %+v", conv.JSONString(opts.AgentRunConfig.KnowledgesetConfig))
	}
	r.logger.Infof("parameters: %v", opts.Parameters)
	r.logger.Infof("user: %+v", opts.User)
	r.logger.Infof("user info: %+v", opts.UserInfo)
	r.logger.Infof("input: %s", conv.JSONString(opts.Input))
	_ = r.LogEventsDebugURL(opts.RunID)

	r.mu.Lock()
	_, ok := lo.FindKeyBy(r.runs, func(key string, run *AgentRun) bool {
		if opts.AssignmentID == "" {
			return run.State.RunID == opts.RunID
		}
		return run.State.AssignmentID == opts.AssignmentID || run.State.RunID == opts.RunID
	})
	r.mu.Unlock()
	if ok {
		r.logger.Infof("agent run %s (assignment %s) already started", opts.RunID, opts.AssignmentID)
		return nil, nil
	}

	run, err := r.CreateRunContext(ctx, opts)
	if err != nil {
		return nil, err
	}

	// 第一次启动
	if !opts.Restart {
		// 保存上下文到存储中
		go func() {
			defer func() {
				if e := recover(); e != nil {
					r.logger.Errorf("failed to store run agent request: err: %v, stack: %v", e, debug.Stack())
				}
			}()
			nerr := run.CtxStorage.Store(ctx, iris.StorageTypeRunAgentRequest, iris.StoreOption{RunAgentRequest: &opts})
			if nerr != nil {
				r.logger.Errorf("failed to store run agent request: %v", nerr)
			}
		}()
	}

	defer run.Close()
	defer func() {
		if p := recover(); p != nil {
			err = fmt.Errorf("%v\nstack: %s", p, debug.Stack())
			r.logger.Errorf("panic: %v\nstack: %s", p, debug.Stack())
		}
		_ = metrics.AR.AgentRunFinishedThroughput.WithTags(&metrics.RunAgentTag{
			AgentName: opts.AgentName,
			Failed:    err != nil && !errors.Is(err, context.Canceled) && !errors.Is(err, context.DeadlineExceeded),
		}).Add(1)
		if err != nil {
			run.State.Status = iris.AgentRunStatusFailed
		} else {
			run.State.Status = iris.AgentRunStatusCompleted
		}
		r.logger.Infof("%s(%s) run finished: %v", opts.AgentName, opts.RunID, err)
		// complete with finished event
		// upload logs before reporting finished, to avoid log lost
		_ = run.GetTracer(run).Close()
		r.uploadLogs(run)
		run.GetPublisher().ReportFinished(opts.RunID, err)
		if opts.ExitOnFinish {
			c, cancel := context.WithTimeout(run, 5*time.Minute)
			defer cancel()
			run.GetPublisher().WaitReported(c)
			time.Sleep(time.Second * 5)
			os.Exit(0)
		}
		r.mu.Lock()
		for _, conn := range r.abortedConns[opts.RunID] {
			conn.Close()
		}
		r.mu.Unlock()
	}()

	r.logger.Infof("initialization finished for %s(%s)", opts.AgentName, opts.RunID)

	// refresh git token for vscode sidecar
	run.RegisterBackgroundService(jwt_manager.NewJWTManager(run, r.api, r.cloudOAuthClient, r.logger))

	// Reset knowledge recall timing at the start of agent execution
	run.ResetKnowledgeRecallTiming()

	err = run.State.Agent.Run(run)

	// Emit total knowledge recall time after agent execution completes
	totalKnowledgeRecallTime := run.GetTotalKnowledgeRecallTime()
	if totalKnowledgeRecallTime > 0 {
		telemetry.EmitKnowledgeRecallTotal(run, opts.AgentName, totalKnowledgeRecallTime)
	}

	return nil, err
}

func (r *RemoteRuntime) AbortAgent(ctx context.Context, opts entity.AbortRunRequest) (res any, err error) {
	_ = metrics.AR.AbortAgentThroughput.Add(1)
	ctx, span := trace.StartServerSpan(ctx, r.logger, entity.RPCAbortAgent.String(), nil)
	defer trace.FinishServerSpan(span, err)
	run := r.GetRun(opts.RunID)
	if run == nil {
		return nil, errors.New("agent run not found")
	}
	run.cancel()
	run.canceled.Store(true)
	return nil, nil
}

func (r *RemoteRuntime) PauseAgent(ctx context.Context, opts entity.PauseRunRequest) (res any, err error) {
	_ = metrics.AR.PauseAgentThroughput.Add(1)
	ctx, span := trace.StartServerSpan(ctx, r.logger, entity.RPCPauseAgent.String(), nil)
	defer trace.FinishServerSpan(span, err)
	// TODO(cyx): implement pause, currently it does the same thing as AbortAgent.
	run := r.GetRun(opts.RunID)
	if run == nil {
		return nil, errors.New("agent run not found")
	}
	if opts.Exit {
		run.cancel()
		run.canceled.Store(true)
	} else {
		if run.State != nil && run.State.Signals != nil {
			run.State.Signals.Notify(iris.SignalCancelAgentRun)
		}
	}

	return nil, nil
}

func (r *RemoteRuntime) AddMessage(ctx context.Context, opts entity.AddMessageRequest) (res entity.AddMessageResponse, err error) {
	ctx, span := trace.StartServerSpan(ctx, r.logger, entity.RPCAgentSendMessage.String(), nil)
	defer trace.FinishServerSpan(span, err)
	var run *AgentRun
	// spin for 2s to wait if add message is called before run is created
	backoff.Retry(func() error {
		run = r.GetRun(opts.RunID)
		if run == nil {
			return fmt.Errorf("agent run %s not found", opts.RunID)
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewConstantBackOff(20*time.Millisecond), 100))
	if run == nil {
		return entity.AddMessageResponse{}, fmt.Errorf("agent run %s not found", opts.RunID)
	}
	run.State.Conversation.AddUserMessage(&iris.Message{
		ID:            opts.Input.ID,
		Content:       opts.Input.Content.Content,
		From:          iris.MessageFromUser,
		CreatedAt:     time.Now(),
		StructContent: map[string]any{},
		Attachments: lo.Map(opts.Input.Attachments, func(attachment entity.AttachmentMeta, _ int) iris.Attachment {
			return iris.Attachment{
				ArtifactID: attachment.ArtifactID,
				Path:       attachment.Filename,
			}
		}),
		Mentions: lo.FilterMap(opts.Input.Mentions, func(mention *entity.Mention, _ int) (iris.Mention, bool) {
			if mention == nil {
				return nil, false
			}
			m := iris.GetMention(*mention)
			if m == nil {
				return nil, false
			}
			return m, true
		}),
	})
	r.logger.Infof("refresh environ for run %s, environ: %+v", opts.RunID, lo.MapToSlice(opts.Environ, func(key string, value string) string {
		return fmt.Sprintf("%s=%s", key, lo.Ternary(value != "", "<hidden>", "<empty>"))
	}))
	run.Ctx.Environ.Replace(lo.Assign(run.Ctx.Environ.ToMap(), opts.Environ))
	messages := run.State.Conversation.GetMessages()
	r.RefreshJWT(run.Ctx)
	return entity.AddMessageResponse{
		Messages: lo.Map(messages, func(msg *iris.Message, _ int) *entity.Message {
			return &entity.Message{
				Content:   entity.MessageContent{Content: msg.Content},
				CreatedAt: msg.CreatedAt,
				Attachments: lo.Map(msg.Attachments, func(attachment iris.Attachment, _ int) entity.AttachmentMeta {
					return entity.AttachmentMeta{
						ArtifactID: attachment.ArtifactID,
						Filename:   attachment.Path,
					}
				}),
			}
		}),
	}, nil
}

func (r *RemoteRuntime) GetAgentEventStream(ctx context.Context, opts entity.GetAgentEventStreamRequest) (res *entity.GetAgentEventStreamResult, err error) {
	ctx, span := trace.StartServerSpan(ctx, r.logger, entity.RPCGetAgentEventStream.String(), nil)
	defer trace.FinishServerSpan(span, err)
	run := r.GetRun(opts.RunID)
	if run == nil {
		r.logger.Errorf("agent run %s not found for get agent event stream", opts.RunID)
		return nil, fmt.Errorf("agent run %s not found", opts.RunID)
	}
	bus, err := r.RegisterSubscriber(opts.RunID)
	if err != nil {
		return nil, err
	}
	if opts.Server {
		r.mu.Lock()
		// if new server connects, shutdown the old one
		// there's a race condition inside jsonrpc library and calling close() while reading from the connection will panic
		// we only stop sending new messages to the server and close connection after agent is idle
		if r.serverConns[opts.RunID] != nil {
			conn := r.serverConns[opts.RunID]
			conn.Shutdown()
			run.Publisher.RemoveClient(conn.ID())
			r.abortedConns[opts.RunID] = append(r.abortedConns[opts.RunID], conn)
		}
		r.serverConns[opts.RunID] = bus
		r.mu.Unlock()
	}
	return &entity.GetAgentEventStreamResult{BusURI: r.busURI(bus.ID())}, nil
}

func (r *RemoteRuntime) RetrieveEvents(ctx context.Context, opts entity.RetrieveEventsRequest) (res any, err error) {
	ctx, span := trace.StartServerSpan(ctx, r.logger, entity.RPCRetrieveEvents.String(), nil)
	defer trace.FinishServerSpan(span, err)
	run := r.GetRun(opts.RunID)
	r.logger.Infof("retrieving events for run %s since offset %d, reporting to %s", opts.RunID, opts.Since, opts.ReportTo)
	if run == nil {
		return nil, fmt.Errorf("agent run %s not found", opts.RunID)
	}
	run.Publisher.ReportSince(opts.ReportTo, opts.Since)
	return map[string]any{"status": "ok"}, nil
}

func (r *RemoteRuntime) SubmitToolCallResults(ctx context.Context, opts entity.SubmitToolCallResultRequest) (res any, err error) {
	ctx, span := trace.StartServerSpan(ctx, r.logger, entity.RPCSubmitToolCallResults.String(), nil)
	defer trace.FinishServerSpan(span, err)
	run := r.GetRun(opts.RunID)
	if run == nil {
		return nil, fmt.Errorf("agent run %s not found", opts.RunID)
	}
	run.Ctx.Environ.Replace(lo.Assign(run.Ctx.Environ.ToMap(), opts.Environ))
	r.RefreshJWT(run.Ctx)
	return run.RemoteToolExecutor.SubmitToolCallResults(opts)
}

func (r *RemoteRuntime) SyncPatchArtifact(ctx context.Context, opts entity.SyncPatchArtifactRequest) (res any, err error) {
	ctx, span := trace.StartServerSpan(ctx, r.logger, entity.RPCSyncPatchArtifact.String(), nil)
	defer trace.FinishServerSpan(span, err)
	ctx, cancel := context.WithTimeout(context.Background(), 300*time.Second)
	defer cancel()
	return r.artifactService.SyncPatchArtifact(ctx, opts)
}

func (r *RemoteRuntime) GetAgentLogger(runID string, bus iris.EventBus) iris.Logger {
	path := filepath.Join(r.config.LogDir, fmt.Sprintf("agent-%s.log", runID))
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, entity.LogPerm)
	if err != nil {
		r.logger.Errorf("failed to create log file: %v", err)
	}
	logger := client.NewAgentLogger(f)
	logger.SetLevel(logrus.DebugLevel) // log debug info in log files
	logger.AddHook(client.NewEventBusLogHook(bus))
	logger.AddHook(client.NewArgosLogHook(logrus.InfoLevel, runID))
	return logger
}

func (r *RemoteRuntime) GetEventLogger(runID string, bus iris.EventBus) *logrus.Logger {
	path := filepath.Join(r.config.LogDir, fmt.Sprintf("iris-events-%s.log", runID))
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, entity.LogPerm)
	if err != nil {
		r.logger.Errorf("failed to create event log file: %v", err)
	}
	logger := client.NewAgentLogger(f)

	if file, err := os.OpenFile(filepath.Join(r.config.LogDir, entity.RuntimeEventsFile), os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644); err == nil {
		logger.AddHook(client.NewEventJsonlHook(file))
	} else {
		logger.Infof("failed to open iris_events.jsonl: %v", err)
	}
	return logger
}

func (r *RemoteRuntime) ControllerBusURI() string {
	return r.busURI(r.bus)
}

// event bus for internal communication or runtime-server communication
func (r *RemoteRuntime) busURI(id string) string {
	return fmt.Sprintf("iris://%s/?id=%s&workspace=%s", r.addr, id, os.Getenv(entity.RuntimeEnvironWorkspacePath))
}

func (r *RemoteRuntime) serverURI() string {
	return fmt.Sprintf("iris://%s", r.addr)
}

// RegisterSubscriber registers a new subscriber to event publisher
// the bus is in event publisher's perspective
// To receive events from this bus, call `ConnectPublisher` with returned bus.ID()
// For event bus communication, we need two buses, one for each side (since a bus can not subscribe to itself):
// Publisher <-> EventBus(RegisterSubscriber) -> EventBus(ConnectPublisher) <-> Subscriber
func (r *RemoteRuntime) RegisterSubscriber(runID string) (*remotebus.RemoteBus, error) {
	r.mu.RLock()
	run := r.runs[runID]
	r.mu.RUnlock()
	if run == nil {
		r.logger.Errorf("agent run %s not found while register subscriber", runID)
		return nil, fmt.Errorf("agent run %s not found", runID)
	}
	bus, err := remotebus.Connect(r.serverURI(), remotebus.WithName("publisher"))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to connect to runtime process")
	}
	run.Publisher.AddClient(bus)
	bus.Conn().Ready()
	return bus, nil
}

// ConnectPublisher connects to the event bus created by `RegisterSubscriber`
func (r *RemoteRuntime) ConnectPublisher(busID string) (*remotebus.RemoteBus, error) {
	bus, err := remotebus.Connect(r.busURI(busID), remotebus.WithName("subscriber"))
	if err != nil {
		r.logger.Errorf("failed to connect to runtime process: %v", err)
		bus.Close()
		return nil, err
	}
	bus.Conn().Ready()
	return bus, nil
}

func (r *RemoteRuntime) handleLogFn(level logrus.Level) codfish.RPCHandler {
	return func(ctx context.Context, conn *jsonrpc2.Conn, req *jsonrpc2.Request) (interface{}, error) {
		var content string
		err := json.Unmarshal(*req.Params, &content)
		if err != nil {
			content = string(*req.Params)
		}
		r.logger.Log(level, content)
		// only send logs to server in debug mode to prevent excessive logs consuming memory in production
		if !r.config.Debug {
			return nil, nil
		}
		err = r.codfish.BroadcastSession(r.bus, fmt.Sprintf("log/%s", level.String()), req.Params)
		return nil, err
	}
}

func (r *RemoteRuntime) handleOutput(data codfishmodel.OutputData) (struct{}, error) {
	switch data.Channel {
	case codfishmodel.OutputChannel(logrus.TraceLevel.String()):
		r.logger.Trace(data.Message)
	case codfishmodel.OutputChannel(logrus.DebugLevel.String()):
		r.logger.Debug(data.Message)
	case codfishmodel.OutputChannel(logrus.InfoLevel.String()):
		r.logger.Info(data.Message)
	case codfishmodel.OutputChannel(logrus.WarnLevel.String()):
		r.logger.Warn(data.Message)
	case codfishmodel.OutputChannel(logrus.ErrorLevel.String()):
		r.logger.Error(data.Message)
	}
	return struct{}{}, nil
}

func (r *RemoteRuntime) initWorkspace(run *iris.AgentRunContext) error {
	defaultTerminal := "terminal"
	logger, publisher := run.GetLogger(), run.GetPublisher()
	dir, _ := os.Getwd()
	ws := workspace.New(dir, publisher)
	// skip log dir
	ws.Editor.IgnorePaths = []string{
		r.config.LogDir,
	}
	// create default terminal
	term, err := workspace.NewTerminal(workspace.NewTerminalOption{
		Name:           defaultTerminal,
		Cmd:            exec.CommandContext(run, "/bin/bash"),
		PublishChannel: publisher,
		Logger:         logger,
	})
	if err != nil {
		logger.Errorf("failed to create terminal: %v", err)
		return errors.WithMessage(err, "failed to create terminal")
	}
	ws.AddTerminal(defaultTerminal, term)
	run.SetWorkspace(ws)

	return nil
}

func (r *RemoteRuntime) initAgentMemory(run *iris.AgentRunContext) error {
	run.State.Memory = memory.NewAgentMemory()
	return nil
}

func (r *RemoteRuntime) uploadLogs(run *iris.AgentRunContext) {
	logger := run.GetLogger() // get agent logger
	if r.artifactService.IsNil() {
		logger.Errorf("failed to connected artifact service")
		return
	}
	// We do not use AgentRunContext as ctx, since it may be cancelled before we finish uploading logs
	uploadCtx := context.Background()
	artifact, err := r.artifactService.NewLogsArtifact(uploadCtx, entity.LogsArtifactMetadata{})
	if err != nil {
		logger.Errorf("failed to create logs artifact: %v", err)
		return
	}

	files := map[string]string{
		entity.RuntimeDaemonLogFile: filepath.Join(r.config.LogDir, entity.RuntimeDaemonLogFile),
		entity.RuntimeEventsFile:    filepath.Join(r.config.LogDir, entity.RuntimeEventsFile),
		entity.RuntimeAgentLogFile:  filepath.Join(r.config.LogDir, fmt.Sprintf("agent-%s.log", run.State.RunID)),
		entity.RuntimeTraceFile:     filepath.Join(r.config.LogDir, fmt.Sprintf("iris-trace-%s.jsonl", run.State.RunID)),
	}

	err = r.artifactService.UploadFiles(uploadCtx, artifact, lo.MapToSlice(files, func(key string, path string) iris.ArtifactFile {
		content, _ := os.ReadFile(path)
		return iris.ArtifactFile{
			Path:    key,
			Content: content,
		}
	}))
	if err != nil {
		logger.Errorf("failed to upload logs: %v", err)
		return
	}

	err = r.artifactService.CommitArtifact(uploadCtx, artifact)
	if err != nil {
		logger.Errorf("failed to commit logs artifact: %v", err)
		return
	}
}

// server may be down for a while, try to request until a server is available
func (r *RemoteRuntime) requestConnection(ctx context.Context, sessionID, runID string) error {
	if os.Getenv("TESTING_SESSION") == sessionID {
		r.logger.Infof("testing session, skip to request connection")
		// Debug session.
		return nil
	}
	ticker := backoff.NewTicker(
		backoff.NewConstantBackOff(time.Second * 1),
	)
	defer ticker.Stop()
	timeout := time.After(time.Minute * 30)
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			err := r.api.RequireConnection(context.Background(), entity.RequireConnectionRequest{
				SessionID: sessionID,
				RunID:     runID,
			})
			if err != nil {
				r.logger.Infof("failed to connect to server: %v, session %s, run %s", err, sessionID, runID)
			}
			// debug mode will not block on server event stream connection
			if err == nil || r.config.Debug {
				r.logger.Infof("connected to server: session %s, run %s", sessionID, runID)
				return nil
			}
		case <-timeout:
			r.logger.Errorf("failed to request connection: timeout, session %s, run %s", sessionID, runID)
			return errors.Errorf("no server is responding to connection request: timed out")
		}
	}
}

// keepalive periodically checks if server connection is alive
// if not, remove the connection and request a new connection
func (r *RemoteRuntime) keepalive() {
	defer func() {
		if err := recover(); err != nil {
			r.logger.Errorf("keepalive paniced, restart keep alive: %v", err)
			go r.keepalive()
		}
	}()

	for range time.Tick(10 * time.Second) {
		r.mu.RLock()
		serverConns := make(map[string]*remotebus.RemoteBus)
		for runID, bus := range r.serverConns {
			serverConns[runID] = bus
		}
		r.mu.RUnlock()
		if len(serverConns) != 0 {
			r.logger.Tracef("health checking server connections: %d", len(serverConns))
		}

		wg := conc.NewWaitGroup()
		for runID, bus := range serverConns {
			wg.Go(func() {
				r.mu.RLock()
				run := r.runs[runID]
				r.mu.RUnlock()
				if run.Context.Err() == nil {
					ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
					defer cancel()
					_, err := bus.Conn().CtxCallSession(ctx, bus.ID(), entity.RPCKeepAlive.String(), conv.JSONString(entity.KeepAliveRequest{RunID: runID}), "")
					if err != nil {
						run.Publisher.RemoveClient(bus.ID())
						r.logger.Errorf("server disconnected for run %s: %v, attempting to reconnect", runID, err)
						// we don't remove this connection, so that if no server is connecting, this automatically retries
						err = r.requestConnection(run.Context, run.State.SessionID, run.State.RunID)
						if err != nil {
							r.logger.Errorf("failed to reconnect to server: %v", err)
						}
					}
				} else {
					run.Publisher.RemoveClient(bus.ID())
					r.mu.Lock()
					delete(r.serverConns, runID)
					r.mu.Unlock()
					r.logger.Infof("run %s finished: %s", runID, run.Context.Err())
				}
			})
		}
		wg.Wait()

		r.mu.RLock()
		for _, conns := range r.abortedConns {
			for _, conn := range conns {
				conn.Close()
			}
		}
		r.abortedConns = make(map[string][]*remotebus.RemoteBus)
		r.mu.RUnlock()
	}
}

func (r *RemoteRuntime) GetRun(id string) *AgentRun {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.runs[id]
}

func (r *RemoteRuntime) LogEventsDebugURL(runID string) error {
	containerID := os.Getenv("HOSTNAME")
	provider := "docker"
	if strings.HasPrefix(containerID, "cube-") {
		provider = "stratocube"
		containerID = strings.TrimSuffix(containerID, "-sts-0")
	}
	debugURL := fmt.Sprintf("%s?run_id=%s&container_id=%s&uri=%s&provider=%s",
		r.config.APIBaseURL+"/runtime/log/html",
		url.QueryEscape(runID),
		containerID,
		url.QueryEscape(r.ControllerBusURI()),
		provider,
	)
	r.logger.Infof("\n🌟 DEBUG URL 🌟: \033[1;30;46m %s \033[0m\n", debugURL)

	return nil
}

func (r *RemoteRuntime) GetCtxStorage() iris.ContextStorage {
	return r.ctxStorage
}

func (r *RemoteRuntime) RefreshJWT(run *iris.AgentRunContext) {
	if run == nil {
		return
	}
	service, ok := iris.FindServiceOf[*jwt_manager.JWTManager](run.GetBackgroundServiceManager())
	if !ok {
		return
	}
	service.RefreshCodebaseJWTFile()
}

func getAgentRunConfig(runConfig *entity.AgentRunConfig, backupConfig *config.AgentRunConfig) *config.AgentRunConfig {
	if runConfig == nil {
		return backupConfig
	}
	customConfig := runConfig.CustomConfig
	if customConfig == nil {
		return backupConfig
	}

	var agentConfig config.AgentRunConfig
	// All json string are valid yaml, we use yaml tag for config currently, so here we use yaml.Unmarshal.
	err := yaml.Unmarshal(*customConfig, &agentConfig)
	// err := json.Unmarshal(*customConfig, &agentConfig)
	if err != nil {
		return backupConfig
	}

	return &agentConfig
}

// 如果上传失败，可能直接存储到 SOP 经验里面，不上传到 TOS。限制下文件大小，避免影响数据库，10KB。
const maxTemplateExpRefFileSize = 10 * 1024 * 1024

func (r *RemoteRuntime) GenerateExperience(ctx context.Context, opts entity.GenerateExperienceRequest) (res *entity.GenerateExperienceResponse, err error) {
	defer func() {
		if err != nil {
			r.logger.Errorf("failed to generate experience: %v", err)
		}
	}()

	run := r.GetRun(opts.RunID)
	if run == nil {
		return nil, errors.Errorf("agent run %s not found", opts.RunID)
	}
	traceFilename := r.getTraceFilePath(opts.RunID)

	logger := run.Ctx.GetLogger()

	logger.Infof("generating experience for template id: %s", opts.TemplateID)

	intermediateDir := filepath.Join(r.config.LogDir, "exp")
	_ = os.Mkdir(intermediateDir, 0750)

	var (
		wg = sync.WaitGroup{}

		queryTemplate    *entity.ExpUserQueryTemplate
		queryTemplateErr error

		progressPlan    *entity.ProgressPlan
		progressPlanErr error

		expSOP    *entity.ExpSOP
		expSOPErr error
	)

	modelConfig := run.Ctx.GetConfig().GetModelByScene("gen_exp_sop")

	trajectory, err := genexp.ParseTraceFile(traceFilename)
	if err != nil {
		logger.Errorf("failed to parse task trajectory: %v", err)
		return nil, errors.WithMessage(err, "failed to parse task trajectory")
	}

	logger.Infof("parsed task trajectory: %d rounds", len(trajectory))

	if opts.UserQueryTemplate == nil {
		logger.Infof("generating user query template")
		backoff.Retry(func() error {
			queryTemplate, queryTemplateErr = genexp.GenerateUserQueryTemplate(ctx, genexp.GenerateUserQueryTemplateOption{
				Trajectory: trajectory,
				ResultDir:  intermediateDir,
				Model:      run.Ctx.GetConfig().GetModelByScene("gen_exp_query_template"),
				LLM:        run.Ctx.GetLLM(),
			})
			return queryTemplateErr
		}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Millisecond*200), 3))
		if queryTemplateErr != nil {
			logger.Errorf("failed to generate user query template: %v", queryTemplateErr)
			return nil, queryTemplateErr
		}
		os.WriteFile(filepath.Join(intermediateDir, "user_query_template.json"), []byte(conv.JSONFormatString(queryTemplate)), 0644)
	} else {
		queryTemplate = opts.UserQueryTemplate
		logger.Infof("using provided user query template(%s): %s", queryTemplate.Name, queryTemplate.UserQuery)
	}

	if opts.Async {
		ctx = context.WithoutCancel(ctx)
	}

	if opts.GenerateTypes.ProgressPlan {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := panics.Try(func() {
				logger.Infof("generating progress plan experience")
				backoff.Retry(func() error {
					progressPlan, progressPlanErr = genexp.GenerateTemplateExpProgressPlan(ctx, genexp.GenerateTemplateExpProgressPlanOption{
						Trajectory: trajectory,
						ResultDir:  intermediateDir,
						Model:      modelConfig,
						LLM:        run.Ctx.GetLLM(),
					})
					if progressPlanErr != nil {
						logger.Errorf("failed to generate progress plan: %v", progressPlanErr)
						return progressPlanErr
					}
					logger.Infof("generated progress plan experience")
					return nil
				}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Second*5), 3))
			}).AsError(); err != nil {
				logger.Errorf("gen progress plan panic: %v", err)
				progressPlanErr = err
			}
			os.WriteFile(filepath.Join(intermediateDir, "progress_plan.json"), []byte(conv.JSONFormatString(progressPlan)), 0644)
		}()
	}

	if opts.GenerateTypes.SOP {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := panics.Try(func() {
				logger.Infof("generating SOP experience")
				backoff.Retry(func() error {
					expSOP, expSOPErr = genexp.GenerateTemplateExpSOPWithScores(ctx, genexp.GenerateTemplateExpSOPOption{
						Trajectory:       trajectory,
						ResultDir:        intermediateDir,
						Model:            modelConfig,
						SummarizeActor:   true,
						SaveIntermediate: true,

						UserQueryTemplate: queryTemplate,

						LLM: run.Ctx.GetLLM(),
					}, 3)
					if expSOPErr != nil {
						logger.Errorf("failed to generate SOP: %v", expSOPErr)
						return expSOPErr
					}
					logger.Infof("generated SOP experience")
					return nil
				}, backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Second*5), 3))
			}).AsError(); err != nil {
				logger.Errorf("failed to generate SOP: %v", err)
				expSOPErr = err
			}

			// Upload files to server.
			if expSOP != nil {
				for _, step := range expSOP.PlanSteps {
					for _, phase := range step.Phases {
						for idx, ref := range phase.References {
							if len(ref.Error) != 0 {
								logger.Warnf("ref file content error: %s", ref.Error)
								continue
							}
							res, err := r.api.UploadTemplateExperienceFile(ctx, client.UploadTemplateExperienceFileRequest{
								TemplateID: opts.TemplateID,
								Path:       ref.Filepath,
								Size:       int64(len(ref.Content)),
								FileReader: strings.NewReader(ref.Content),
							})
							if err != nil {
								err = errors.WithMessage(err, "failed to upload file")
								logger.Warnf("failed to upload file: %v", err)
								if len(ref.Content) <= maxTemplateExpRefFileSize {
									logger.Infof("preserve file content in sop, size %d", len(ref.Content))
									continue
								}
								phase.References[idx].Error = err.Error()
								ref.Content = ""
								continue
							}
							logger.Infof("uploaded template experience file: %s", res.File)
							phase.References[idx].ID = res.File.FileID
							phase.References[idx].Content = ""
						}
					}
					scoreReport := map[string]interface{}{
						"session_id":  run.State.SessionID,
						"template_id": opts.TemplateID,
						"score":       expSOP.EvaluateResult.Score,
					}
					for _, aspect := range expSOP.EvaluateResult.Aspects {
						if !lo.Contains([]string{"consistency", "efficiency", "correctness"}, strings.ToLower(aspect.Aspect)) {
							continue
						}
						scoreReport[strings.ToLower(aspect.Aspect)] = aspect.Score
					}
					run.Ctx.GetTelemetry().Collect("template_exp_sop_gen_final_score", scoreReport)
				}
				os.WriteFile(filepath.Join(intermediateDir, "sop.json"), []byte(conv.JSONFormatString(expSOP)), 0644)
			}
		}()
	}

	if !opts.Async {
		wg.Wait()
		if queryTemplateErr != nil {
			return nil, queryTemplateErr
		}
		if progressPlanErr != nil {
			return nil, progressPlanErr
		}
		if expSOPErr != nil {
			return nil, expSOPErr
		}
		res := &entity.GenerateExperienceResponse{
			ExpSOP:            expSOP,
			UserQueryTemplate: queryTemplate,
			ProgressPlan:      nil,
		}
		if progressPlan != nil {
			res.ProgressPlan = &progressPlan.Plan
		}
		return res, nil
	}

	// Submit generated results to server.
	go func() {
		if !opts.GenerateTypes.ProgressPlan && !opts.GenerateTypes.SOP {
			logger.Infof("no experience to submit")
			return
		}
		if err := panics.Try(func() {
			wg.Wait()

			updateTemplate := func(opt client.UpdateTemplateExperienceOption) {
				if err := r.api.UpdateTemplateExperience(ctx, opt); err != nil {
					logger.Errorf("failed to submit template experience: %v", err)
				} else {
					logger.Infof("submitted template experience: %d", opt.Status)
				}
			}

			if expSOPErr != nil {
				updateTemplate(client.UpdateTemplateExperienceOption{
					TemplateID: opts.TemplateID,
					Status:     1,
					Error:      lo.ToPtr(expSOPErr.Error()),
				})
				return
			}

			opt := client.UpdateTemplateExperienceOption{
				TemplateID:   opts.TemplateID,
				Status:       0,
				Error:        nil,
				ProgressPlan: nil,
				ExpSOP:       lo.ToPtr(conv.JSONFormatString(expSOP)),
			}
			if progressPlan != nil {
				buf := bytes.NewBuffer(nil)
				encoder := xml.NewEncoder(buf)
				encoder.Indent("", "  ")
				encoder.Encode(progressPlan)
				plan := buf.String()
				// xml does not provide an unescape function, so we use html version
				opt.ProgressPlan = lo.ToPtr(html.UnescapeString(plan))
			}
			updateTemplate(opt)
		}).AsError(); err != nil {
			logger.Errorf("gen exp panic: %v", err)
			return
		}
	}()

	return &entity.GenerateExperienceResponse{
		UserQueryTemplate: queryTemplate,
	}, nil
}
