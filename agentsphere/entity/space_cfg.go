package entity

import (
	"encoding/json"
)

// from next_dev_service
type SpaceDevServiceInfo struct {
	UniqueID     string `json:"unique_id" description:"a {product}.{subsystem}.{module} format string to uniformaly named a service, like toutiao.article.item"`                                                 // psm
	Type         string `json:"type" description:"type of the service, such as tce, faas etc"`                                                                                                                   // project type
	Name         string `json:"name" description:"name of the service, it is a {product}.{subsystem}.{module} format string to uniformaly named a service, like toutiao.article.item. It is usually called PSM"` // project name
	ControlPlane string `json:"control_plane" description:"control plane of the service, such as cn, i18n etc"`                                                                                                  // control plane
}

func NewSpaceDevServiceInfo(prjType, name, controlPlane string) *SpaceDevServiceInfo {
	return &SpaceDevServiceInfo{
		Type:         prjType,
		Name:         name,
		ControlPlane: controlPlane,
	}
}

// from next_code_repo
type SpaceCodeRepoInfo struct {
	RepoName string `json:"repo_name" description:"name of the repo"` // repo name
	RepoID   string `json:"repo_id" description:"id of the repo"`     // repo ID
}

func NewSpaceCodeRepoInfo(repoName, repoID string) *SpaceCodeRepoInfo {
	return &SpaceCodeRepoInfo{
		RepoName: repoName,
		RepoID:   repoID,
	}
}

type SpaceInfo struct {
	SpaceID        string                 `json:"space_id" description:"unique id of the space"`                                             //space_id
	IsProjectSpace bool                   `json:"is_project_space" description:"identify if the space is a project space or personal space"` //idenfy if the space is a public space, personal space does not have below infos
	MeegoList      []string               `json:"meego_list" description:"links of the meego space configured in the space"`                 // meego space list configured for space, from next_platform_config
	DevServiceList []*SpaceDevServiceInfo `json:"dev_service_list" description:"services configured in the space"`                           // services configured for space
	RepoList       []*SpaceCodeRepoInfo   `json:"repo_list" description:"repositories configured in the space"`                              // repos configured for space
}

func NewSpaceInfo(spaceId string, isProjectSpace bool, opts ...func(*SpaceInfo)) *SpaceInfo {
	info := &SpaceInfo{
		SpaceID:        spaceId,
		IsProjectSpace: isProjectSpace,
		MeegoList:      []string{},
		DevServiceList: []*SpaceDevServiceInfo{},
		RepoList:       []*SpaceCodeRepoInfo{},
	}

	for _, opt := range opts {
		opt(info)
	}

	return info
}

func NewSpaceInfoFromParameter(param any) (*SpaceInfo, error) {
	info := &SpaceInfo{}
	buf, err := json.Marshal(param)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(buf, &info)

	return info, err
}

func CheckParamIsProjectSpace(param any) bool {
	isProjectSpace := false
	spaceInfo, err := NewSpaceInfoFromParameter(param)
	if err != nil {
		return isProjectSpace
	}

	if spaceInfo != nil {
		isProjectSpace = spaceInfo.IsProjectSpace
	}

	return isProjectSpace
}

func (si *SpaceInfo) AddMeegoList(meegos []string) {
	si.MeegoList = append(si.MeegoList, meegos...)
}

func (si *SpaceInfo) AddDevService(devSvc *SpaceDevServiceInfo) {
	si.DevServiceList = append(si.DevServiceList, devSvc)
}

func (si *SpaceInfo) AddCodeRepo(repo *SpaceCodeRepoInfo) {
	si.RepoList = append(si.RepoList, repo)
}

func (si *SpaceInfo) ToString() string {
	strByte, err := json.Marshal(si)
	if err != nil {
		return err.Error()
	}

	return string(strByte)
}
