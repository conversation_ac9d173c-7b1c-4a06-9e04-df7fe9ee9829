package runtimeserver

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextruntime"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/hertz"
)

type RuntimeServerClient struct {
	baseURL string
	cli     *hertz.Client
}

func NewRuntimeServerClientFromConf(conf *config.AgentSphereConfig) (*RuntimeServerClient, error) {
	cli, err := hertz.NewClient(conf.RuntimeServerConfig.APIBaseURL, hertz.NewHTTPClientOption{
		Timeout: time.Second * 12,
	})
	if err != nil {
		return nil, err
	}
	return &RuntimeServerClient{
		baseURL: conf.RuntimeServerConfig.APIBaseURL,
		cli:     cli,
	}, nil
}

func NewRuntimeServerClient(baseURL string) (*RuntimeServerClient, error) {
	cli, err := hertz.NewClient(baseURL, hertz.NewHTTPClientOption{
		Timeout: time.Second * 10,
	})
	if err != nil {
		return nil, err
	}
	return &RuntimeServerClient{
		baseURL: baseURL,
		cli:     cli,
	}, nil
}

// ValidateMCP calls the single MCP validation endpoint
func (c *RuntimeServerClient) ValidateMCP(ctx context.Context, req *nextruntime.ValidateMCPRequest) (*nextruntime.ValidateMCPResponse, error) {
	var resp nextruntime.ValidateMCPResponse
	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/api/runtime/v1/mcp/validate", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Body:         req,
		Result:       &resp,
		// SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// ValidateMCPBatch calls the batch MCP validation endpoint
func (c *RuntimeServerClient) ValidateMCPBatch(ctx context.Context, req *nextruntime.ValidateMCPBatchRequest) (*nextruntime.ValidateMCPBatchResponse, error) {
	var resp nextruntime.ValidateMCPBatchResponse
	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/api/runtime/v1/mcp/validate/batch", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Body:         req,
		Result:       &resp,
		// SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// ListMCPTools calls the single MCP tools list endpoint
func (c *RuntimeServerClient) ListMCPTools(ctx context.Context, req *nextruntime.ListMCPToolsRequest) (*nextruntime.ListMCPToolsResponse, error) {
	var resp nextruntime.ListMCPToolsResponse
	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/api/runtime/v1/mcp/tools", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Body:         req,
		Result:       &resp,
		// SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// ListMCPToolsBatch calls the batch MCP tools list endpoint
func (c *RuntimeServerClient) ListMCPToolsBatch(ctx context.Context, req *nextruntime.ListMCPToolsBatchRequest) (*nextruntime.ListMCPToolsBatchResponse, error) {
	var resp nextruntime.ListMCPToolsBatchResponse
	_, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/api/runtime/v1/mcp/tools/batch", hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Body:         req,
		Result:       &resp,
		// SetTTEnvHeaders: true,
	})
	if err != nil {
		return nil, err
	}
	return &resp, nil
}
